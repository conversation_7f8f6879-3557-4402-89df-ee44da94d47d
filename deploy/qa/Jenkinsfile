podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat',
            ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'dev', description: 'Branch being deployed'),
       string(name: 'targetBranch', defaultValue: 'dev', description: 'Target branch against which if a PR is being raised')])])

  currentBuild.description = "branch ${params.branchName}"
  node(POD_LABEL) {
    container('helm') {
      withCredentials([[$class       : 'FileBinding',
                        credentialsId: 'sling-test-kubeconfig',
                        variable     : 'KUBECONFIG'],
                       [$class       : 'StringBinding',
                        credentialsId: 'sd-charts-github-api-token',
                        variable     : 'API_TOKEN']]) {
        stage('Add Helm repository') {
          sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
              label: 'Add stable helm repo'
          sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
              label: 'Add helm repo'
          sh script: 'helm repo list', label: 'List available helm repos'
        }
        withCredentials([[$class       : 'StringBinding',
                          credentialsId: 'test-env-postgres-password',
                          variable     : 'POSTGRES_PASSWORD'],
                         [$class       : 'StringBinding',
                          credentialsId: 'test-env-rabbitmq-password',
                          variable     : 'RABBITMQ_PASSWORD'],
                          [$class       : 'StringBinding',
                          credentialsId: 'sd-meeting-rails-master-key',
                          variable     : 'RAILS_MASTER_KEY'],
                          [$class       : 'StringBinding',
                          credentialsId: 'test-email-credential-encryption-secret',
                          variable     : 'CALENDAR_CREDENTIAL_ENCRYPTION_SECRET'],
			                    [$class       : 'StringBinding',
                          credentialsId: 'email-credential-encryption-iv',
                          variable     : 'CALENDAR_CREDENTIAL_ENCRYPTION_IV']
                         ]) {
          stage('Deploy') {
            echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-meetings:${params.dockerImageTag}"
            sh script: "helm upgrade --install sd-meetings sd-charts/sd-meetings " +
                "--set " +
                "image.tag=${params.dockerImageTag}," +
                "appConfig.postgres.password=${POSTGRES_PASSWORD}," +
                "appConfig.rabbitmq.password=${RABBITMQ_PASSWORD}," +
                "appConfig.credentials.masterKey=${RAILS_MASTER_KEY}," +
                "appConfig.calendar.credential.encryption.secret='${CALENDAR_CREDENTIAL_ENCRYPTION_SECRET}'," +
                "appConfig.calendar.credential.encryption.iv='${CALENDAR_CREDENTIAL_ENCRYPTION_IV}'," +
                "appConfig.kylas.uiHost=http://app-qa.sling-dev.com," +
                "appConfig.kylas.apiHost=https://api-qa.sling-dev.com," +
                "deployment.annotations.buildNumber=${currentBuild.number} " +
                "--wait",
                label: 'Install helm release'
          }
        }
      }
    }
    container('curl') {
      stage('Refresh Gateway routes') {
        sh script: 'curl -X POST \\\n' +
            '  http://api-qa.sling-dev.com/actuator/gateway/refresh \\\n' +
            '  -H \'Accept: application/json\' \\\n' +
            '  -H \'Host: api-qa.sling-dev.com\' \\\n' +
            '  -H \'cache-control: no-cache\'', label: 'Force refresh routes cache'
      }
    }
    // try {
    //   if (params.branchName != 'dev') {
    //     stage('Approval for Sell Do QA Deployment') {
    //       userInput = input(id: 'confirm', message: 'Do you wish to deploy to Sell Do QA environment?',
    //         parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy to Sell Do QA environment', name: 'confirm']])
    //     }
    //   }
    //   stage('Start Sell Do QA Deployment') {
    //     build job: '../../selldo/sd-meetings/deploy-to-qa',
    //       parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: params.dockerImageTag],
    //                    [$class: 'StringParameterValue', name: 'triggeredByJob', value: "selldo deploy-to-qa : #${BUILD_NUMBER}"],
    //                    [$class: 'StringParameterValue', name: 'branchName', value: params.branchName]],
    //       wait: false
    //   }
    // }
    // catch (err) {
    //  def user = err.getCauses()[0].getUser()
    //   userInput = false
    //   echo "Aborted by: [${user}]"
    // }
  }
}
