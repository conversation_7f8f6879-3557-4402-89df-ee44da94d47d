class CreateMeetings < ActiveRecord::Migration[6.0]
	def change
		create_table :meetings do |t|
			t.string :title, null: false
			t.string :description
			t.datetime :from, null: false
			t.datetime :to
			t.boolean :all_day, default: false
			t.string :location
			t.bigint :tenant_id, null: false
			t.bigint :owner_id, foriegn_key: true, null: false
			t.bigint :created_by_id, foriegn_key: true, null: false
			t.bigint :updated_by_id, foriegn_key: true, null: false
			t.timestamps
		end
	end
end
