class CreateConnectedAccounts < ActiveRecord::Migration[6.0]
  def change
    create_table :connected_accounts do |t|
      t.bigint 'tenant_id', null: false
      t.string 'access_token'
      t.string 'refresh_token'
      t.bigint 'expires_at'
      t.string 'email'
      t.text 'scopes', default: [], array: true
      t.string 'provider_name'
      t.boolean 'active'
      t.string 'last_history_id'
      t.string 'subscription_id'
      t.bigint 'subscription_expiry'

      t.references :user, index: true, foreign_key: true
      t.timestamps
    end
  end
end
