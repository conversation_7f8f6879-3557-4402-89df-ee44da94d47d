class CreateShareRules < ActiveRecord::Migration[6.1]
  def change
    create_table :share_rules do |t|
      t.bigint :tenant_id, null: false
      t.references :created_by, null: false, foriegn_key: { to_table: :users }
      t.references :updated_by, null: false, foriegn_key: { to_table: :users }
      t.jsonb :actions
      t.string :name
      t.string :description
      t.bigint :meeting_id
      t.bigint :from_id
      t.string :from_type
      t.bigint :to_id
      t.string :to_type
      t.boolean :system_default, default: false
      t.boolean :share_all_records, default: false

      t.timestamps
    end
  end
end
