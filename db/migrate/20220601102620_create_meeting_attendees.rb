class CreateMeetingAttendees < ActiveRecord::Migration[6.0]
  def change
    create_table :meeting_attendees do |t|
      t.datetime :checked_in_at, precision: 6
      t.string :checked_in_latitude
      t.string :checked_in_longitude
      t.datetime :checked_out_at, precision: 6
      t.string :checked_out_latitude
      t.string :checked_out_longitude
      t.references :meeting, null: false, foreign_key: true
      t.timestamps
    end
  end
end
