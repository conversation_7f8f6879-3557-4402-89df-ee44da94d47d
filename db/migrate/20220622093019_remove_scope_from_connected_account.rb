class RemoveScopeFromConnectedAccount < ActiveRecord::Migration[6.0]
  def change
    remove_column :connected_accounts, :scopes, :text, if_exists: true
    rename_column :connected_accounts, :provider_reference, :calendar_id
    change_column_default :connected_accounts, :sync_type, from: {"kylas_to_source"=>false, "source_to_kylas"=>false}, to: {"kylas_to_calendar"=>false, "calendar_to_kylas"=>false}
  end
end