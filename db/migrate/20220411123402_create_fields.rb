class CreateFields < ActiveRecord::Migration[6.0]
  def change
    create_table :fields do |t|
      t.string :internal_name, null: false
      t.string :display_name, null: false
      t.string :field_type, null: false
      t.boolean :is_standard, default: true
      t.boolean :is_sortable, default: false
      t.boolean :is_filterable, default: false
      t.boolean :is_internal, default: false
      t.boolean :is_required, default: false
      t.boolean :active, default: true
      t.bigint :tenant_id, null:false
      t.timestamps
    end
  end
end
