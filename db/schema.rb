# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_02_28_123458) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "connected_accounts", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "access_token"
    t.bigint "expires_at"
    t.string "email"
    t.string "provider_name"
    t.boolean "active"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "calendar_id"
    t.jsonb "sync_type", default: {"calendar_to_kylas"=>false, "kylas_to_calendar"=>false}
    t.date "subscription_renewal_date"
    t.string "provider_subscription_resource_id"
    t.string "next_sync_token"
    t.index ["user_id"], name: "index_connected_accounts_on_user_id"
  end

  create_table "fields", force: :cascade do |t|
    t.string "internal_name", null: false
    t.string "display_name", null: false
    t.string "field_type", null: false
    t.boolean "is_standard", default: true
    t.boolean "is_sortable", default: false
    t.boolean "is_filterable", default: false
    t.boolean "is_internal", default: false
    t.boolean "is_required", default: false
    t.boolean "active", default: true
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "description"
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.index ["tenant_id", "internal_name"], name: "unique_fields_by_tenant", unique: true
  end

  create_table "look_ups", force: :cascade do |t|
    t.string "entity", null: false
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id", null: false
    t.string "email"
    t.string "public_id"
    t.bigint "owner_id"
    t.index ["owner_id"], name: "index_look_ups_on_owner_id"
    t.index ["tenant_id", "email"], name: "index_look_ups_on_tenant_id_and_email"
  end

  create_table "look_ups_meetings", id: false, force: :cascade do |t|
    t.bigint "meeting_id", null: false
    t.bigint "look_up_id", null: false
  end

  create_table "meeting_attendances", force: :cascade do |t|
    t.datetime "checked_in_at", precision: 6
    t.string "checked_in_latitude"
    t.string "checked_in_longitude"
    t.datetime "checked_out_at", precision: 6
    t.string "checked_out_latitude"
    t.string "checked_out_longitude"
    t.bigint "meeting_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "user_id"
    t.boolean "is_checked_in_outside_geofence", default: false
    t.boolean "is_checked_out_outside_geofence", default: false
    t.index ["meeting_id"], name: "index_meeting_attendances_on_meeting_id"
  end

  create_table "meeting_look_ups", force: :cascade do |t|
    t.boolean "related"
    t.boolean "participant"
    t.bigint "meeting_id", null: false
    t.bigint "look_up_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "rsvp_response"
    t.string "rsvp_message"
    t.boolean "organizer", default: false
    t.index ["look_up_id"], name: "index_meeting_look_ups_on_look_up_id"
    t.index ["meeting_id"], name: "index_meeting_look_ups_on_meeting_id"
  end

  create_table "meetings", force: :cascade do |t|
    t.string "title", null: false
    t.string "description"
    t.datetime "from", null: false
    t.datetime "to"
    t.boolean "all_day", default: false
    t.string "location"
    t.bigint "tenant_id", null: false
    t.bigint "owner_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "time_zone_id"
    t.string "status"
    t.string "public_id"
    t.datetime "conducted_at", precision: 6
    t.bigint "conducted_by_id"
    t.datetime "cancelled_at", precision: 6
    t.bigint "cancelled_by_id"
    t.string "medium", limit: 15
    t.string "provider_link"
    t.string "provider_meeting_id"
    t.jsonb "custom_field_values", default: {}
    t.bigint "imported_by_id"
    t.decimal "location_latitude", precision: 10, scale: 7
    t.decimal "location_longitude", precision: 10, scale: 7
    t.index ["from"], name: "index_meetings_on_from"
    t.index ["tenant_id"], name: "index_meetings_on_tenant_id"
  end

  create_table "notes", force: :cascade do |t|
    t.text "description", null: false
    t.bigint "created_by_id", null: false
    t.bigint "meeting_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.index ["meeting_id"], name: "index_notes_on_meeting_id"
  end

  create_table "outlook_change_logs", force: :cascade do |t|
    t.string "resource_id", null: false
    t.string "change_key", null: false
    t.index ["resource_id", "change_key"], name: "index_outlook_change_logs_on_resource_id_and_change_key", unique: true
  end

  create_table "picklist_values", force: :cascade do |t|
    t.string "internal_name", null: false
    t.string "display_name", null: false
    t.boolean "disabled", default: false
    t.bigint "tenant_id", null: false
    t.bigint "picklist_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["picklist_id"], name: "index_picklist_values_on_picklist_id"
  end

  create_table "picklists", force: :cascade do |t|
    t.string "internal_name", null: false
    t.string "display_name", null: false
    t.bigint "tenant_id", null: false
    t.bigint "field_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["field_id"], name: "index_picklists_on_field_id"
  end

  create_table "share_rules", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.jsonb "actions"
    t.string "name"
    t.string "description"
    t.bigint "meeting_id"
    t.bigint "from_id"
    t.string "from_type"
    t.bigint "to_id"
    t.string "to_type"
    t.boolean "system_default", default: false
    t.boolean "share_all_records", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "index_share_rules_on_created_by_id"
    t.index ["updated_by_id"], name: "index_share_rules_on_updated_by_id"
  end

  create_table "teams", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name"
    t.bigint "user_ids", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["id", "tenant_id"], name: "index_teams_on_id_and_tenant_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "geofence_config"
    t.index ["id", "tenant_id"], name: "index_users_on_id_and_tenant_id"
  end

  add_foreign_key "connected_accounts", "users"
  add_foreign_key "meeting_attendances", "meetings"
  add_foreign_key "notes", "meetings"
  add_foreign_key "picklist_values", "picklists"
  add_foreign_key "picklists", "fields"
end
