{"openapi": "3.0.1", "info": {"title": "API V1", "version": "v1"}, "paths": {"/v1/meetings/fields": {"post": {"summary": "Create custom field", "tags": ["Meeting Fields"], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Create custom field"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid Field"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": [{"displayName": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "filterable": {"type": "boolean"}, "sortable": {"type": "boolean"}, "standard": {"type": "boolean"}, "required": {"type": "boolean"}, "pickLists": {"type": "array", "items": {"type": "object", "properties": [{"displayName": {"type": "string"}}], "uniqueItems": true}}}], "required": ["displayName", "type", "filterable", "sortable", "standard", "required"]}}}}, "x-access-policy": {"action": "create", "policy": "custom_fields", "resource": "field"}}, "get": {"summary": "List fields", "tags": ["Meeting Fields"], "parameters": [{"name": "custom-only", "in": "query", "schema": {"type": "boolean"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meetings List"}, "401": {"description": "Authentication failed"}}}}, "/v1/meetings/fields/{id}/activate": {"put": {"summary": "Activate Custom Field", "tags": ["Meeting Fields"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Field Activated"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid Field"}}, "x-access-policy": {"action": "activate", "policy": "custom_fields", "resource": "field"}}}, "/v1/meetings/fields/{id}/deactivate": {"put": {"summary": "Deactivate Custom Field", "tags": ["Meeting Fields"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Field Deactivated"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid Field"}}}}, "/v1/meetings/fields/{id}": {"get": {"summary": "Field", "tags": ["Get Field"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Get Field"}, "401": {"description": "Authentication failed"}}}, "put": {"summary": "Update fields", "tags": ["Fields"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Update Fields"}, "401": {"description": "Authentication failed"}, "404": {"description": "Field Not Found"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": [{"displayName": {"type": "string"}, "picklist": {"type": "object"}, "description": {"type": "string"}, "type": {"type": "string"}, "filterable": {"type": "boolean"}, "sortable": {"type": "boolean"}, "standard": {"type": "boolean"}, "required": {"type": "boolean"}}], "required": ["displayName", "description"]}}}}}}, "/v1/meetings/fields/create_fields": {"post": {"summary": "Create Fields", "tags": ["Create Fields for Existing Tenants Migration"], "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string"}}, {"name": "userId", "in": "query", "schema": {"type": "string"}}, {"name": "name", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Create Fields"}}}}, "/v1/meetings/fields/update_fields": {"post": {"summary": "Update Fields", "tags": ["Update Fields for Existing Tenants Migration"], "parameters": [{"name": "fromTenantId", "in": "query", "schema": {"type": "string"}}, {"name": "toTenantId", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Update Fields"}}}}, "/v48348807127D18DF/meetings/health": {"get": {"summary": "meeting from database", "tags": ["Meetings"], "responses": {"200": {"description": "Database is up"}, "404": {"description": "Entity not present"}, "503": {"description": "Database is down"}}}}, "/v1/meetings/layout": {"get": {"summary": "Meeting Layout", "tags": ["Meeting Create and Edit Layout"], "parameters": [{"name": "view", "in": "query", "required": true, "description": "create or edit layout", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Create or Edit Layout"}, "401": {"description": "Authentication failed"}, "404": {"description": "Not Found"}}}}, "/v1/meetings/layout/list": {"get": {"summary": "Meeting Fields List", "tags": ["Meeting Fields Listing API"], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Fields List"}, "401": {"description": "Authentication failed"}, "404": {"description": "Not Found"}}}}, "/v1/meetings": {"post": {"summary": "Creates a Meeting", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "meeting created"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "time"}, "to": {"type": "time"}, "all_day": {"type": "boolean"}, "location": {"type": "string"}, "related_to": {"type": "object"}, "participants": {"type": "object"}, "medium": {"type": "string"}}, "required": ["title", "from", "to", "all_day"]}}}}}}, "/v1/meetings/{id}": {"get": {"summary": "Retrieves a Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting found", "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "datetime"}, "to": {"type": "datetime"}, "all_day": {"type": "boolean"}, "location": {"type": "string"}, "related_to": {"type": "object"}, "participants": {"type": "array", "items": {"type": "object"}}}}}}}, "404": {"description": "meeting not found"}}}, "put": {"summary": "Update a Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting updated"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "time"}, "to": {"type": "time"}, "all_day": {"type": "boolean"}, "location": {"type": "string"}, "related_to": {"type": "object"}, "participants": {"type": "object"}}, "required": ["title", "from", "to", "all_day"]}}}}}, "delete": {"summary": "Delete a Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting deleted"}, "401": {"description": "authentication failed"}}}}, "/v1/meetings/{id}/cancel": {"post": {"summary": "Cancel a Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting cencelled"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}}}, "/v1/meetings/{id}/conduct": {"post": {"summary": "Mark meeting as conducted", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting conducted"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}}}, "/v1/meetings/{id}/checkin": {"post": {"summary": "Check in Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Checked In"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid Request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"location": {"type": "string"}, "checkedInDetails": {"type": "object", "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}}}}}}}}}}, "/v1/meetings/{id}/checkout": {"post": {"summary": "Check out Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Checked Out"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid Request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"checkedOutDetails": {"type": "object", "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}}}}}}}}}}, "/v1/meetings/search": {"post": {"summary": "Searches meetings", "tags": ["Meetings"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Searched"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/meetings/{id}/rsvp": {"post": {"summary": "Rsvp Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Meeting Rsvp"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"rsvpResponse": {"type": "string"}, "rsvpMessage": {"type": "string"}, "notifyOrganiser": {"type": "boolean"}}, "required": ["rsvpResponse", "rsvpMessage", "notify<PERSON><PERSON><PERSON>"]}}}}}}, "/v1/meetings/{id}/p_rsvp": {"post": {"summary": "Public Rsvp Meeting", "tags": ["Meetings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Meeting Rsvp"}, "401": {"description": "authentication failed"}, "422": {"description": "invalid request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"rsvpResponse": {"type": "string"}, "rsvpMessage": {"type": "string"}, "pid": {"type": "string"}}, "required": ["rsvpResponse", "rsvpMessage", "pid"]}}}}}}, "/v1/meetings/delete": {"delete": {"summary": "Delete bulk meetings", "tags": ["Meetings"], "parameters": [{"name": "ids", "in": "query", "collectionFormat": "multi", "schema": {"type": "array"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meetings deleted"}, "401": {"description": "authentication failed"}}}}, "/v1/meetings/lookup": {"get": {"summary": "Meetings Lookup", "tags": ["Meetings"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meetings Lookup Response"}, "401": {"description": "Authentication failed"}}}}, "/v1/meetings/webhooks/microsoft": {"post": {"summary": "Microsoft Calendar webhook", "tags": ["Meetings"], "parameters": [{"name": "validationToken", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Always returns 200"}}}}, "/v1/meetings/bulk-checkout": {"post": {"summary": "Bulk Checkout", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Checked out"}, "401": {"description": "Unauthorized"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}, "meetingIds": {"type": "array"}}}}}}}}, "/v1/meetings/{meeting_id}/notes": {"get": {"summary": "Get Notes for Meeting", "tags": ["Meetings"], "parameters": [{"name": "meeting_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Notes Searched"}, "401": {"description": "authentication failed"}}}, "post": {"summary": "Create Notes for Meeting", "tags": ["Meetings"], "parameters": [{"name": "meeting_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Notes created"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"description": {"type": "string"}}, "required": ["description"]}}}}}}, "/v1/meetings/{meeting_id}/notes/{id}": {"delete": {"summary": "Delete Notes of Meeting", "tags": ["Meetings"], "parameters": [{"name": "meeting_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting Notes deleted"}, "401": {"description": "authentication failed"}}}}, "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}": {"put": {"summary": "Update Picklist Value", "tags": ["Custom Picklist Values"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "picklist_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Custom picklist value updated successfully"}, "401": {"description": "Authentication Failed or Unauthorised user"}, "422": {"description": "Invalid Picklist Value"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["id", "name", "displayName"], "properties": {"id": {"type": "integer", "description": "Picklist Value ID"}, "name": {"type": "string", "description": "Picklist Value Internal Name"}, "displayName": {"type": "string", "description": "Picklist Value Updated Display Name"}, "disabled": {"type": "boolean", "description": "Current Status of Value"}}}}}}}, "delete": {"summary": "Delete Picklist Value", "tags": ["Custom Picklist Values"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "picklist_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Custom Picklist Value deleted successfully"}, "400": {"description": "Picklist value associated with meeting"}, "401": {"description": "Authentication Failed or Unauthorised user"}, "422": {"description": "Invalid Picklist Value"}}}}, "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}/enable": {"put": {"summary": "Enable Picklist Value", "tags": ["Custom Picklist Values"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "picklist_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Custom Picklist Value Enabled successfully"}, "401": {"description": "Authentication Failed or Unauthorised user"}, "422": {"description": "Invalid Picklist Value"}}}}, "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}/disable": {"put": {"summary": "Disable Picklist Value", "tags": ["Custom Picklist Values"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "picklist_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Custom Picklist Value Disabled successfully"}, "401": {"description": "Authentication Failed or Unauthorised user"}, "422": {"description": "Invalid Picklist Value"}}}}, "/v1/meetings/share-rules/{id}": {"get": {"summary": "Get Share Rule", "tags": ["Meeting Share Rules"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Get Share Rule"}, "401": {"description": "Authentication Failed"}, "404": {"description": "Share rule does not exist"}}}, "delete": {"summary": "Delete Share Rule", "tags": ["Meeting Share Rules"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Share deleted successfully"}, "401": {"description": "Authentication Failed"}, "404": {"description": "Share rule does not exist"}}}}, "/v1/meetings/share-rules/search": {"post": {"summary": "Share Rules Search", "tags": ["Meeting Share Rules"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Share Rule Search Response"}, "401": {"description": "Authentication Failed"}, "422": {"description": "Invalid request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/meetings/{id}/share": {"post": {"summary": "Create share rule for single meeting", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "share rule created"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "to": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "actions": {"type": "object"}}}}}}}}, "/v1/meetings/share": {"post": {"summary": "Create share rule for all meetings", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "share rule created"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "to": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "actions": {"type": "object"}}}}}}}}, "/v1/meetings/{id}/share/{share_rule_id}": {"put": {"summary": "Updates share rule for single meeting", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "share_rule_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "share rule updated"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "to": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "actions": {"type": "object"}}}}}}}}, "/v1/meetings/share/{share_rule_id}": {"put": {"summary": "Updates share rule for all meetings", "tags": ["Meetings"], "security": [{"bearerAuth": []}], "parameters": [{"name": "share_rule_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "share rule updated"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "from": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "to": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}}}, "actions": {"type": "object"}}}}}}}}, "/v1/meetings/webhooks/google": {"post": {"summary": "Google calendar webhook", "tags": ["Meetings"], "responses": {"200": {"description": "Always returns 200"}}}}}, "servers": [{"url": "http://{defaultHost}", "variables": {"defaultHost": {"default": "localhost:3000"}}}, {"url": "https://{defaultHost}", "variables": {"defaultHost": {"default": "127.0.0.1:3000"}}}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}