# This file is used by Rack-based servers to start the application.

require_relative 'config/environment'

run Rails.application

# Loading Middleware for exposing monitoring metrics to Prometheus
require 'rack'
require 'prometheus/middleware/collector'
require 'prometheus/middleware/exporter'

use Rack::Deflater
use Prometheus::Middleware::Collector
use Prometheus::Middleware::Exporter, { path: '/prometheus' }
