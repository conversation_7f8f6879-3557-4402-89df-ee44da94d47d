# frozen_string_literal: true

LOOKUP_LEAD = 'lead'
LOOKUP_DEAL = 'deal'
LOOKUP_CONTACT = 'contact'
LOOKUP_COMPANY = 'company'
LOOKUP_USER = 'user'
LOOKUP_EXTERNAL = 'external'
LOOKUP_TIMEZONE = 'timezone'
PARENT_ENTITY_TYPES = [LO<PERSON>UP_LEAD, LOOKUP_DEAL, <PERSON>O<PERSON><PERSON>_CONTACT, LOOKUP_COMPANY]
LOOKUP_TYPES = [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_COMPANY, LOOKUP_USER, <PERSON>O<PERSON><PERSON>_TIMEZONE, LOOKUP_EXTERNAL].freeze
MEETING_EXCHANGE = 'ex.meeting'
LEAD_EXCHANGE = 'ex.sales'
USER_EXCHANGE = 'ex.iam'
DEAL_EXCHANGE = 'ex.deal'
CONTACT_EXCHANGE = 'ex.sales'
COMPANY_EXCHANGE = 'ex.company'
WORKFLOW_EXCHANGE = 'ex.workflow'
FIELD_SALES_EXCHANGE = 'ex.fieldSales'
USER_NAME_UPDATED_EVENT = 'user.name.updated'
USER_EMAIL_UPDATED_EVENT = 'user.email.updated'
LEAD_UPDATED_EVENT = 'sales.lead.updated'
LEAD_DELETED_EVENT = 'sales.lead.deleted'
DEAL_NAME_UPDATED_EVENT = 'deal.name.updated'
DEAL_REASSIGNED_EVENT = 'deal.reassigned'
DEAL_DELETED_EVENT = 'deal.deleted'
CONTACT_UPDATED_EVENT = 'sales.contact.updated'
COMPANY_REASSIGNED_EVENT = 'company.reassigned'
CONTACT_DELETED_EVENT = 'sales.contact.deleted'
COMPANY_NAME_UPDATED_EVENT = 'company.name.updated'
COMPANY_DELETED_EVENT = 'company.deleted.v2'
TENANT_CREATED_EVENT = 'iam.tenant.created'
MEETING_SCHEDULER_EVENT = 'scheduler.3am'
FIELD_SALES_CHECKOUT_MEETINGS_EVENT = 'fieldSales.meetings.checkout'
FIELD_SALES_CHECKOUT_MEETINGS_QUEUE = 'q.fieldSales.meetings.checkout.meetings'
FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_EVENT = 'fieldSales.executive.geofence.updated'
FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_QUEUE = 'q.fieldSales.executive.geofence.updated.meetings'
FIELD_SALES_USER_ADDED_EVENT  = 'fieldSales.user.added'
FIELD_SALES_USER_ADDED_QUEUE = 'q.fieldSales.user.added.meetings'
FIELD_SALES_USER_REMOVED_EVENT = 'fieldSales.user.removed'
FIELD_SALES_USER_REMOVED_QUEUE = 'q.fieldSales.user.removed.meetings'
TEAM_UPDATED_V2_EVENT = 'team.updated.v2'
TEAM_UPDATED_V2_QUEUE = 'q.team.updated.v2.meetings'

WORKFLOW_MEETING_UPDATED_EVENT = 'workflow.meeting.update'
WORKFLOW_MEETING_UPDATED_QUEUE = 'q.workflow.meeting.update.meetings'

USAGE_LIMIT_CHANGED_EVENT = 'usage.limit.changed'
USAGE_LIMIT_CHANGED_QUEUE = 'q.usage.limit.changed.meetings'

CREATE_ALL_MEETINGS_SHARE_RULES_EVENT = 'iam.create.all.meetings.shareRules'
CREATE_ALL_MEETINGS_SHARE_RULES_QUEUE = "q.#{CREATE_ALL_MEETINGS_SHARE_RULES_EVENT}.meetings"
DELETE_ALL_MEETINGS_SHARE_RULES_EVENT = 'iam.delete.all.meetings.shareRules'
DELETE_ALL_MEETINGS_SHARE_RULES_QUEUE = "q.#{DELETE_ALL_MEETINGS_SHARE_RULES_EVENT}.meetings"

GOOGLE_PROVIDER = 'GOOGLE'
MICROSOFT_TEAMS_PROVIDER = 'MICROSOFT'
MS_RENEWAL_DURATION = 2
OFFLINE = 'OFFLINE'
CONNECTED_ACCOUNT_SYNC_EVENT = 'iam.calendarOauth.connected'
CONNECTED_ACCOUNT_SYNC_QUEUE = 'q.iam.calendarOauth.connected.meeting'
DEACTIVATE_CONNECTED_ACCOUNT_EVENT = 'iam.calendarOauth.disconnected'
DEACTIVATE_CONNECTED_ACCOUNT_QUEUE = 'q.iam.calendarOauth.disconnected.meeting'
ONLINE_MEETING_CREATE_EVENT = 'Create'
ONLINE_MEETING_UPDATE_EVENT = 'Update'
ONLINE_MEETING_DELETE_EVENT = 'Delete'
ONLINE_MEETING_CANCEL_EVENT = 'Cancel'
KYLAS = 'Kylas'

USER_NAME_UPDATED_QUEUE = 'q.user.name.updated'
USER_EMAIL_UPDATED_QUEUE = 'q.user.email.updated'
LEAD_UPDATED_QUEUE = 'q.sales.lead.updated'
LEAD_DELETED_QUEUE = 'q.meetings.sales.lead.deleted'
DEAL_NAME_UPDATED_QUEUE = 'q.deal.name.updated'
DEAL_REASSIGNED_QUEUE = 'q.deal.reassigned.meetings'
DEAL_DELETED_QUEUE = 'q.meetings.deal.deleted'
CONTACT_UPDATED_QUEUE = 'q.sales.contact.updated'
CONTACT_DELETED_QUEUE = 'q.meetings.sales.contact.deleted'
COMPANY_NAME_UPDATED_QUEUE = 'q.company.name.updated'
COMPANY_REASSIGNED_QUEUE = 'q.company.reassigned.meetings'
TENANT_CREATED_QUEUE = 'q.meetings.iam.tenant.created'
MEETING_SCHEDULER_QUEUE = 'q.scheduler.3am.meetings'
COMPANY_DELETED_QUEUE = 'q.company.deleted.meetings'

USAGE_SCHEDULER_EVENT = 'scheduler.collect.usage'
USAGE_SCHEDULER_QUEUE = 'q.meeting.scheduler.collect.usage'
USAGE_PUBLISHER_EVENT = 'tenant.usage.collected'
USAGE_PUBLISHER_QUEUE = 'q.meeting.tenant.usage.collected'
SCHEDULER_EXCHANGE = 'ex.scheduler'
MEETING_SCHEDULED_RELATED_TO_ENTITY_EVENT = 'meeting.scheduled.relatedTo.entity'
SCHEDULER_REMINDER_SCAN_EVENT = 'scheduler.reminder.scan'
SCHEDULER_REMINDER_SCAN_QUEUE = 'q.meeting.scheduler.reminder.scan'
MEETING_SCHEDULED_IN_MINUTES_EVENT = 'meeting.scheduled.inMinutes'
FIELD_TYPES = %w[TEXT_FIELD TOGGLE DATE_PICKER DATETIME_PICKER PICK_LIST ENTITY_LOOKUP LOOK_UP NUMBER PARAGRAPH_TEXT MEETING_INVITEES ENTITY_PICKLIST URL CHECKBOX RICH_TEXT MEETING_ORGANIZER GPS_COORDINATES].freeze

SORTABLE_FIELD_TYPES = %w[TEXT_FIELD DATE_PICKER DATETIME_PICKER NUMBER PICK_LIST].freeze
NOTE_FILTERABLE_FIELDS = %w[createdAt createdBy]

EXCLUDED_MEETING_LAYOUT_FIELDS = %w(id tenant_id).freeze
EXCLUDED_MEETING_FIELDS = %w(tenant_id).freeze
PICKLIST_FIELD_TYPES = %w(PICK_LIST ENTITY_LOOKUP ENTITY_PICKLIST).freeze

MEETING_INVITEE_LOOKUP_URL = '/search/meeting-invitee/lookup?q=key:'
USER_LOOKUP_URL = '/users/lookup?q=name:'
USER_OWNER_URL = '/users/meeting-invitee/lookup?q='
RELATED_LEAD_LOOKUP_URL = '/search/lead/lookup?q=firstName:'
RELATED_DEAL_LOOKUP_URL = '/search/deal/lookup?q=name:'
RELATED_CONTACT_LOOKUP_URL = '/search/contact/lookup?q=firstName:'
RELATED_COMPANY_LOOKUP_URL = '/companies/lookup?view=meeting&q=comp:'
ASSOCIATED_TEAM_LOOKUP_URL = '/teams/lookup?q=name:'

ACTIVATE = 'activate'
DEACTIVATE = 'deactivate'
SCHEDULED = 'scheduled'
CONDUCTED = 'conducted'
CANCELLED = 'cancelled'
MISSED = 'missed'
RSVP_YES = 'YES'
RSVP_NO = 'NO'
RSVP_MAYBE = 'MAY_BE'
PARTICIPANTS_TYPE = 'participants'
RELATED_TO_TYPE = 'related_to'
MEETING_CUSTOM_FIELD = 'MEETING_CUSTOM_FIELD'
STATUS_CREATED = 'CREATED'
IMPORT_MESSSAGE = 'Meeting created successfully'
AUTO_DISCONNECTED = 'AUTO'

DEACTIVATE_CUSTOM_FIELD_PLANS = ['embark', 'explore-monthly', 'explore-annual'].freeze
JWT_KEY = 'test'

VALIDATION_FAILED_ERROR = 'Validation failed: '

if Rails.env.development? || Rails.env.test?
  SERVICE_SALES = 'http://localhost:8082'
  SERVICE_IAM = 'http://localhost:8081'
  SERVICE_DEAL = 'http://localhost:8090'
  SERVICE_SEARCH = 'http://localhost:8083'
  SERVICE_CONFIG = 'http://localhost:8089'
  SERVICE_FIELD_SALES = 'http://localhost:9007'
  APP_KYLAS_HOST = 'http://localhost'
  API_KYLAS_HOST = "http://localhost"
elsif Rails.env.staging?
  SERVICE_SALES = 'http://sd-sales'
  SERVICE_IAM = 'http://sd-iam'
  SERVICE_DEAL = 'http://sd-deal'
  SERVICE_SEARCH = 'http://sd-search'
  SERVICE_CONFIG = "http://sd-config"
  SERVICE_FIELD_SALES = 'http://sd-field-sales'
  APP_KYLAS_HOST = ENV['APP_KYLAS_HOST']
  API_KYLAS_HOST = ENV['API_KYLAS_HOST']
elsif Rails.env.production?
  SERVICE_SALES = 'http://sd-sales'
  SERVICE_IAM = 'http://sd-iam'
  SERVICE_DEAL = 'http://sd-deal'
  SERVICE_SEARCH = 'http://sd-search'
  SERVICE_CONFIG = "http://sd-config"
  SERVICE_FIELD_SALES = 'http://sd-field-sales'
  APP_KYLAS_HOST = ENV['APP_KYLAS_HOST']
  API_KYLAS_HOST = ENV['API_KYLAS_HOST']
end

EMAIL_REGEXP = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

USER = 'USER'
TEAM = 'TEAM'
ALLOWED_MEETING_SHARE_ACTIONS = ['read']
DEFAULT_VIEW = 'default'
SHARE_VIEW = 'share'
CHECKIN_VIEW = 'checkin'

MEETING = 'MEETING'

SUCCESS = 'SUCCESS'
FAILED = 'FAILED'
