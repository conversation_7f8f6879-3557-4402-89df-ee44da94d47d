class RabbitmqConnection
  @@channel = nil

  def self.get_channel routing_key
    routing_key = routing_key.gsub('.','_')
    if RabbitmqConnection.class_variable_defined?("@@#{routing_key}")
      return  self.class_variable_get("@@#{routing_key}")
    else
      rabbitmq_connection = Bunny.new(
        host: <PERSON>N<PERSON>['RABBITMQ_HOST'],
        user: <PERSON>N<PERSON>['RABBITMQ_USER'],
        pass: ENV['RABBITMQ_PASSWORD'],
        vhost: ENV['RABBITMQ_VIRTUAL_HOST']).start
      channel = rabbitmq_connection.create_channel
      channel.prefetch(1)
      self.class_variable_set("@@#{routing_key}", channel)
      self.class_variable_get("@@#{routing_key}")
    end
  end

  def self.get_exchange exchange_name, routing_key = 'meeting'
    get_channel(routing_key).topic(exchange_name, durable: true)
  end

  def self.subscribe exchange_name, routing_key, queue_name
    exchange = get_exchange(exchange_name, routing_key)
    get_channel(routing_key).queue(queue_name, auto_delete: false, durable: true).bind(exchange, routing_key: routing_key).subscribe do |delivery_info, metadata, payload|
      begin
        yield(payload, metadata.to_hash)
      rescue Exception => e
        Rails.logger.info "RabbitMQ exception: routing_key: #{routing_key} | message: #{e.to_s}"
        if e.to_s.downcase.include?('activerecord') || e.to_s.downcase.include?('connection')
          ActiveRecord::Base.connection.close if ActiveRecord::Base.connection
          ActiveRecord::Base.connection_pool.with_connection do
            yield payload
          end
        end
      ensure
        ActiveRecord::Base.clear_active_connections!
      end
    end
  end
end
