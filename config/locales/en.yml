# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  meetings:
    invalid_medium: 'Invalid medium'
  error:
    not_found:
      view: 'View not found.'
      meeting: 'Meeting not found.'
      note: 'Note not found.'
      participant: 'Participant not found.'
      field: 'Field not found.'
      share_rule: 'Share rule not found.'
      tenant_usage: 'Tenant usage information not found.'
    meeting_expired: 'Meeting expired.'
    meeting_cancelled: 'Meeting cancelled.'
    note_create_not_allowed: 'Note creation is not allowed.'
    note_access_not_allowed: 'Do not have access to notes.'
    connected_account_not_found: 'Connected Account not found.'
    picklist_value_cannot_be_deleted: 'Picklist value cannot be deleted.'
    provider_internal_server: 'Provider internal server error.'
    provider_forbidden: 'Invalid provider.'
    provider_unauthorized: 'Unauthorized provider.'
    provider_invalid: 'Invalid provider.'
    provider_not_found: 'Provider not found.'
    invalid:
      cancel_meeting: 'Invalid meeting record for cancelling meeting.'
      rsvp_response: 'Invalid RSVP response.'
      rsvp_response_blank: 'RSVP response is blank.'
      meeting_participant: 'Invalid meeting participant.'
      participant: 'Invalid participant.'
      meeting: '%{error}'
      sync_type: 'Invalid sync type.'
      checked_out_details: 'Invalid checked out details.'
      meeting_attendance: 'Invalid meeting attendace.'
      custom_field: 'Invalid value %{value} for custom field - %{field}.'
      inactive_fields: 'Inactive fields - %{fields}'
      non_existant_field: 'Invalid custom fields - %{fields}'
      note: 'Invalid note.'
      meeting_action: 'Invalid meeting action.'
      sortable_field_or_order: 'Invalid sortable field or order.'
      non_filterable_field: 'This field is non-filterable.'
      rule: 'Invalid rule.'
      tenant: 'Invalid tenant.'
      related_to: 'Invalid related to user.'
      rsvp_token: 'Invalid rsvp token.'
      field: 'Invalid field.'
      owner_participant: 'Owner is not participant.'
      organizer_is_not_a_participant: 'Organizer is not a participant.'
      meeting_medium: 'Invalid meeting medium.'
      meeting_attendance: 'Invalid meeting attendance.'
      field_value: 'Invalid value for given field.'
      company: 'Invalid company.'
      company_summary_response: 'Invalid company summary response.'
      user_setting_response: 'Invalid user setting response.'
      contact: 'Invalid contact.'
      contact_email: 'Invalid contact email.'
      contact_summary_response: 'Invalid contact summary response.'
      deal: 'Invalid deal.'
      deal_summary_response: 'Invalid deal summary response.'
      lead: 'Invalid lead.'
      lead_email: 'Invalid lead email.'
      lead_summary_response: 'Invalid lead summary response.'
      user: 'Invalid user.'
      team: 'Invalid team.'
      user_email: 'Invalid user email.'
      user_summary_response: 'Invalid user summary response.'
      calender_event: 'Invalid calender event.'
      provider_name: 'Invalid provider name.'
      provider_access_token_response: 'Invalid data for getting provider access token.'
      provider_meeting_id: 'Invalid provider meeting id.'
      picklist_field: 'Invalid picklist field.'
      picklist_value: 'Invalid picklist value.'
      email: "Invalid email - '%{email}' in %{field}."
      emails: "%{entity} with emails '%{emails}' not found."
      missing_organizer: "Organizer is missing."
      share_rule: 'Invalid share rule - %{error}'
      missing_parameters: 'Parameters are missing'
      checkout_without_checkin: 'Cannot checkout without checking in.'
      checkin_location: 'Checkin location is beyond set geofence'
    unauthorized: 'Unauthorized access.'
    internal_server_error: 'Internal Server Error'
    usage_limit_exceeded: 'Record limit has been reached for your plan. Please upgrade your plan.'
    internal_server:
      get_rsvp_token: 'Something went wrong while getting rsvp token.'
      validate_company: 'Something went wrong while validating company.'
      validate_contact: 'Something went wrong while validating contact.'
      validate_deal: 'Something went wrong while validating deal.'
      validate_lead: 'Something went wrong while validating lead.'
      validate_user: 'Something went wrong while validating user.'
      provider_access_token: 'Something went wrong while validating provider access token.'
      user_setting_response: 'Something went wrong while getting user setting.'
      tenant_usage: 'Something went wrong while checking tenant usage limits.'
    invalid_token: 'Invalid token.'
    forbidden:
      update_meeting: 'You do not have permission to update meeting.'
      delete_meeting: 'You do not have permission to delete meeting.'
      delete_note: 'You do not have permission to delete note.'
      meeting_details: 'You do not have access to this meeting.'
      checkin_checkout: 'You do not have access to check in or check out meeting.'
      checkout: 'you do not have access to checkout this meeting'
      create_custom_field: 'You do not have permission to create custom field.'
      update_custom_field: 'You do not have permission to update custom field.'
      delete_custom_field: 'You do not have permission to delete custom field.'
  share_rules:
    invalid:
      actions: 'Actions are invalid'
      unauthorized_target_user: 'Unauthorized target user'
      already_exists: 'This meeting is already shared with users/team with selected permissions'
      target_user_cannot_be_owner: 'You cannot share Entity to Owner of that Entity.'
      from_and_to_cannot_be_same: 'From and To users cannot be same.'
      invalid_source_user: 'Share rule record owner is not meeting owner.'
