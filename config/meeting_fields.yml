id:
  internal_name: 'id'
  display_name: 'ID'
  field_type: 'NUMBER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: false
  active: true
status:
  internal_name: 'status'
  display_name: 'Status'
  field_type: 'ENTITY_PICKLIST'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
  picklist:
    internal_name: 'statusPicklist'
    display_name: 'Status Picklist'
    picklist_values:
      status_1:
        internal_name: 'scheduled'
        display_name: 'Scheduled'
      status_2:
        internal_name: 'missed'
        display_name: 'Missed'
      status_3:
        internal_name: 'conducted'
        display_name: 'Conducted'
      status_4:
        internal_name: 'cancelled'
        display_name: 'Cancelled'
title:
  internal_name: 'title'
  display_name: 'Title'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
allDay:
  internal_name: 'allDay'
  display_name: 'All Day'
  field_type: 'TOGGLE'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: false
  active: true
from:
  internal_name: 'from'
  display_name: 'From'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: true
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
to:
  internal_name: 'to'
  display_name: 'To'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
timezone:
  internal_name: 'timezone'
  display_name: 'Timezone'
  field_type: 'PICK_LIST'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: false
  active: true
  picklist:
    internal_name: 'timezone'
    display_name: 'Timezone'
    picklist_values:
      tz_0:
        internal_name: 'Etc/GMT+12'
        display_name: '(GMT-12:00) International Date Line West'
      tz_1:
        internal_name: 'Pacific/Midway'
        display_name: '(GMT-11:00) Midway Island, Samoa'
      tz_2:
        internal_name: 'Pacific/Honolulu'
        display_name: '(GMT-10:00) Hawaii'
      tz_3:
        internal_name: 'US/Alaska'
        display_name: '(GMT-09:00) Alaska'
      tz_4:
        internal_name: 'America/Los_Angeles'
        display_name: '(GMT-08:00) Pacific Time (US & Canada)'
      tz_5:
        internal_name: 'America/Tijuana'
        display_name: '(GMT-08:00) Tijuana, Baja California'
      tz_6:
        internal_name: 'US/Arizona'
        display_name: '(GMT-07:00) Arizona'
      tz_7:
        internal_name: 'America/Chihuahua'
        display_name: '(GMT-07:00) Chihuahua, La Paz, Mazatlan'
      tz_8:
        internal_name: 'US/Mountain'
        display_name: '(GMT-07:00) Mountain Time (US & Canada)'
      tz_9:
        internal_name: 'America/Managua'
        display_name: '(GMT-06:00) Central America'
      tz_10:
        internal_name: 'US/Central'
        display_name: '(GMT-06:00) Central Time (US & Canada)'
      tz_11:
        internal_name: 'America/Mexico_City'
        display_name: '(GMT-06:00) Guadalajara, Mexico City, Monterrey'
      tz_12:
        internal_name: 'Canada/Saskatchewan'
        display_name: '(GMT-06:00) Saskatchewan'
      tz_13:
        internal_name: 'America/Bogota'
        display_name: '(GMT-05:00) Bogota, Lima, Quito, Rio Branco'
      tz_14:
        internal_name: 'US/Eastern'
        display_name: '(GMT-05:00) Eastern Time (US & Canada)'
      tz_15:
        internal_name: 'US/East-Indiana'
        display_name: '(GMT-05:00) Indiana (East)'
      tz_16:
        internal_name: 'Canada/Atlantic'
        display_name: '(GMT-04:00) Atlantic Time (Canada)'
      tz_17:
        internal_name: 'America/Caracas'
        display_name: '(GMT-04:00) Caracas, La Paz'
      tz_18:
        internal_name: 'America/Manaus'
        display_name: '(GMT-04:00) Manaus'
      tz_19:
        internal_name: 'America/Santiago'
        display_name: '(GMT-04:00) Santiago'
      tz_20:
        internal_name: 'Canada/Newfoundland'
        display_name: '(GMT-03:30) Newfoundland'
      tz_21:
        internal_name: 'America/Sao_Paulo'
        display_name: '(GMT-03:00) Brasilia'
      tz_22:
        internal_name: 'America/Argentina/Buenos_Aires'
        display_name: '(GMT-03:00) Buenos Aires, Georgetown'
      tz_23:
        internal_name: 'America/Godthab'
        display_name: '(GMT-03:00) Greenland'
      tz_24:
        internal_name: 'America/Montevideo'
        display_name: '(GMT-03:00) Montevideo'
      tz_25:
        internal_name: 'America/Noronha'
        display_name: '(GMT-02:00) Mid-Atlantic'
      tz_26:
        internal_name: 'Atlantic/Cape_Verde'
        display_name: '(GMT-01:00) Cape Verde Is.'
      tz_27:
        internal_name: 'Atlantic/Azores'
        display_name: '(GMT-01:00) Azores'
      tz_28:
        internal_name: 'Africa/Casablanca'
        display_name: '(GMT+00:00) Casablanca, Monrovia, Reykjavik'
      tz_29:
        internal_name: 'Etc/Greenwich'
        display_name: '(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London'
      tz_30:
        internal_name: 'Europe/Amsterdam'
        display_name: '(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna'
      tz_31:
        internal_name: 'Europe/Belgrade'
        display_name: '(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague'
      tz_32:
        internal_name: 'Europe/Brussels'
        display_name: '(GMT+01:00) Brussels, Copenhagen, Madrid, Paris'
      tz_33:
        internal_name: 'Europe/Sarajevo'
        display_name: '(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb'
      tz_34:
        internal_name: 'Africa/Lagos'
        display_name: '(GMT+01:00) West Central Africa'
      tz_35:
        internal_name: 'Asia/Amman'
        display_name: '(GMT+02:00) Amman'
      tz_36:
        internal_name: 'Europe/Athens'
        display_name: '(GMT+02:00) Athens, Bucharest, Istanbul'
      tz_37:
        internal_name: 'Asia/Beirut'
        display_name: '(GMT+02:00) Beirut'
      tz_38:
        internal_name: 'Africa/Cairo'
        display_name: '(GMT+02:00) Cairo'
      tz_39:
        internal_name: 'Africa/Harare'
        display_name: '(GMT+02:00) Harare, Pretoria'
      tz_40:
        internal_name: 'Europe/Helsinki'
        display_name: '(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius'
      tz_41:
        internal_name: 'Asia/Jerusalem'
        display_name: '(GMT+02:00) Jerusalem'
      tz_42:
        internal_name: 'Europe/Minsk'
        display_name: '(GMT+02:00) Minsk'
      tz_43:
        internal_name: 'Africa/Windhoek'
        display_name: '(GMT+02:00) Windhoek'
      tz_44:
        internal_name: 'Asia/Kuwait'
        display_name: '(GMT+03:00) Kuwait, Riyadh, Baghdad'
      tz_45:
        internal_name: 'Europe/Moscow'
        display_name: '(GMT+03:00) Moscow, St. Petersburg, Volgograd'
      tz_46:
        internal_name: 'Africa/Nairobi'
        display_name: '(GMT+03:00) Nairobi'
      tz_47:
        internal_name: 'Asia/Tbilisi'
        display_name: '(GMT+03:00) Tbilisi'
      tz_48:
        internal_name: 'Asia/Tehran'
        display_name: '(GMT+03:30) Tehran'
      tz_49:
        internal_name: 'Asia/Muscat'
        display_name: '(GMT+04:00) Abu Dhabi, Muscat'
      tz_50:
        internal_name: 'Asia/Baku'
        display_name: '(GMT+04:00) Baku'
      tz_51:
        internal_name: 'Asia/Yerevan'
        display_name: '(GMT+04:00) Yerevan'
      tz_52:
        internal_name: 'Asia/Kabul'
        display_name: '(GMT+04:30) Kabul'
      tz_53:
        internal_name: 'Asia/Yekaterinburg'
        display_name: '(GMT+05:00) Yekaterinburg'
      tz_54:
        internal_name: 'Asia/Karachi'
        display_name: '(GMT+05:00) Islamabad, Karachi, Tashkent'
      tz_55:
        internal_name: 'Asia/Calcutta'
        display_name: '(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura'
      tz_56:
        internal_name: 'Asia/Katmandu'
        display_name: '(GMT+05:45) Kathmandu'
      tz_57:
        internal_name: 'Asia/Almaty'
        display_name: '(GMT+06:00) Almaty, Novosibirsk'
      tz_58:
        internal_name: 'Asia/Dhaka'
        display_name: '(GMT+06:00) Astana, Dhaka'
      tz_59:
        internal_name: 'Asia/Rangoon'
        display_name: '(GMT+06:30) Yangon (Rangoon)'
      tz_60:
        internal_name: 'Asia/Bangkok'
        display_name: '(GMT+07:00) Bangkok, Hanoi, Jakarta'
      tz_61:
        internal_name: 'Asia/Krasnoyarsk'
        display_name: '(GMT+07:00) Krasnoyarsk'
      tz_62:
        internal_name: 'Asia/Hong_Kong'
        display_name: '(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi'
      tz_63:
        internal_name: 'Asia/Kuala_Lumpur'
        display_name: '(GMT+08:00) Kuala Lumpur, Singapore'
      tz_64:
        internal_name: 'Asia/Irkutsk'
        display_name: '(GMT+08:00) Irkutsk, Ulaan Bataar'
      tz_65:
        internal_name: 'Australia/Perth'
        display_name: '(GMT+08:00) Perth'
      tz_66:
        internal_name: 'Asia/Taipei'
        display_name: '(GMT+08:00) Taipei'
      tz_67:
        internal_name: 'Asia/Tokyo'
        display_name: '(GMT+09:00) Osaka, Sapporo, Tokyo'
      tz_68:
        internal_name: 'Asia/Seoul'
        display_name: '(GMT+09:00) Seoul'
      tz_69:
        internal_name: 'Asia/Yakutsk'
        display_name: '(GMT+09:00) Yakutsk'
      tz_70:
        internal_name: 'Australia/Adelaide'
        display_name: '(GMT+09:30) Adelaide'
      tz_71:
        internal_name: 'Australia/Darwin'
        display_name: '(GMT+09:30) Darwin'
      tz_72:
        internal_name: 'Australia/Brisbane'
        display_name: '(GMT+10:00) Brisbane'
      tz_73:
        internal_name: 'Australia/Canberra'
        display_name: '(GMT+10:00) Canberra, Melbourne, Sydney'
      tz_74:
        internal_name: 'Australia/Hobart'
        display_name: '(GMT+10:00) Hobart'
      tz_75:
        internal_name: 'Pacific/Guam'
        display_name: '(GMT+10:00) Guam, Port Moresby'
      tz_76:
        internal_name: 'Asia/Vladivostok'
        display_name: '(GMT+10:00) Vladivostok'
      tz_77:
        internal_name: 'Asia/Magadan'
        display_name: '(GMT+11:00) Magadan, Solomon Is., New Caledonia'
      tz_78:
        internal_name: 'Pacific/Auckland'
        display_name: '(GMT+12:00) Auckland, Wellington'
      tz_79:
        internal_name: 'Pacific/Fiji'
        display_name: '(GMT+12:00) Fiji, Kamchatka, Marshall Is.'
      tz_80:
        internal_name: 'Pacific/Tongatapu'
        display_name: "(GMT+13:00) Nuku'alofa"
      tz_81:
        internal_name: 'Europe/Andorra'
        display_name: '(GMT+01:00) Europe/Andorra'
      tz_82:
        internal_name: 'Asia/Dubai'
        display_name: '(GMT+04:00) Asia/Dubai'
      tz_83:
        internal_name: 'America/Antigua'
        display_name: '(GMT-04:00) America/Antigua'
      tz_84:
        internal_name: 'America/Anguilla'
        display_name: '(GMT-04:00) America/Anguilla'
      tz_85:
        internal_name: 'Europe/Tirane'
        display_name: '(GMT+01:00) Europe/Tirane'
      tz_86:
        internal_name: 'Africa/Luanda'
        display_name: '(GMT+01:00) Africa/Luanda'
      tz_87:
        internal_name: 'Antarctica/McMurdo'
        display_name: '(GMT+13:00) New Zealand time - McMurdo, South Pole'
      tz_88:
        internal_name: 'Antarctica/Casey'
        display_name: '(GMT+11:00) Casey'
      tz_89:
        internal_name: 'Antarctica/Davis'
        display_name: '(GMT+07:00) Davis'
      tz_90:
        internal_name: 'Antarctica/DumontDUrville'
        display_name: '(GMT+10:00) Dumont-d Urville'
      tz_91:
        internal_name: 'Antarctica/Mawson'
        display_name: '(GMT+05:00) Mawson'
      tz_92:
        internal_name: 'Antarctica/Palmer'
        display_name: '(GMT-03:00) Palmer'
      tz_93:
        internal_name: 'Antarctica/Rothera'
        display_name: '(GMT-03:00) Rothera'
      tz_94:
        internal_name: 'Antarctica/Syowa'
        display_name: '(GMT+03:00) Syowa'
      tz_95:
        internal_name: 'Antarctica/Troll'
        display_name: '(GMT+00:00) Troll'
      tz_96:
        internal_name: 'Antarctica/Vostok'
        display_name: '(GMT+06:00) Antarctica/Vostok'
      tz_97:
        internal_name: 'America/Argentina/Cordoba'
        display_name: '(GMT-03:00) Argentina (most areas: CB, CC, CN, ER, FM, MN, SE, SF)'
      tz_98:
        internal_name: 'America/Argentina/Salta'
        display_name: '(GMT-03:00) Salta (SA, LP, NQ, RN)'
      tz_99:
        internal_name: 'America/Argentina/Jujy'
        display_name: '(GMT-03:00) Jujuy (JY)'
      tz_100:
        internal_name: 'America/Argentina/Tucumn'
        display_name: '(GMT-03:00) Tucuman (TM)'
      tz_101:
        internal_name: 'America/Argentina/Catamarca'
        display_name: '(GMT-03:00) Catamarca (CT), Chubut (CH)'
      tz_102:
        internal_name: 'America/Argentina/La_Rioa'
        display_name: '(GMT-03:00) La Rioja (LR)'
      tz_103:
        internal_name: 'America/Argentina/San_Jun'
        display_name: '(GMT-03:00) San Juan (SJ)'
      tz_104:
        internal_name: 'America/Argentina/Mendoa'
        display_name: '(GMT-03:00) Mendoza (MZ)'
      tz_105:
        internal_name: 'America/Argentina/San_Lus'
        display_name: '(GMT-03:00) San Luis (SL)'
      tz_106:
        internal_name: 'America/Argentina/Rio_Gallgos'
        display_name: '(GMT-03:00) Santa Cruz (SC)'
      tz_107:
        internal_name: 'America/Argentina/Ushuaia'
        display_name: '(GMT-03:00) Tierra del Fuego (TF)'
      tz_108:
        internal_name: 'Pacific/Pago_Pago'
        display_name: '(GMT-11:00) Pacific/Pago_Pago'
      tz_109:
        internal_name: 'Europe/Vienna'
        display_name: '(GMT+01:00) Europe/Vienna'
      tz_110:
        internal_name: 'Australia/Lord_Howe'
        display_name: '(GMT+11:00) Lord Howe Island'
      tz_111:
        internal_name: 'Antarctica/Macquarie'
        display_name: '(GMT+11:00) Macquarie Island'
      tz_112:
        internal_name: 'Australia/Melbourne'
        display_name: '(GMT+11:00) Victoria'
      tz_113:
        internal_name: 'Australia/Sydney'
        display_name: '(GMT+11:00) New South Wales (most areas)'
      tz_114:
        internal_name: 'Australia/Broken_Hill'
        display_name: '(GMT+10:30) New South Wales (Yancowinna)'
      tz_115:
        internal_name: 'Australia/Lindeman'
        display_name: '(GMT+10:00) Queensland (Whitsunday Islands)'
      tz_116:
        internal_name: 'Australia/Eucla'
        display_name: '(GMT+08:45) Western Australia (Eucla)'
      tz_117:
        internal_name: 'America/Aruba'
        display_name: '(GMT-04:00) America/Aruba'
      tz_118:
        internal_name: 'Europe/Mariehamn'
        display_name: '(GMT+02:00) Europe/Mariehamn'
      tz_119:
        internal_name: 'America/Barbados'
        display_name: '(GMT-04:00) America/Barbados'
      tz_120:
        internal_name: 'Africa/Ouagadougou'
        display_name: '(GMT+00:00) Africa/Ouagadougou'
      tz_121:
        internal_name: 'Europe/Sofia'
        display_name: '(GMT+02:00) Europe/Sofia'
      tz_122:
        internal_name: 'Asia/Bahrain'
        display_name: '(GMT+03:00) Asia/Bahrain'
      tz_123:
        internal_name: 'Africa/Bujumbura'
        display_name: '(GMT+02:00) Africa/Bujumbura'
      tz_124:
        internal_name: 'Africa/Porto-Novo'
        display_name: '(GMT+01:00) Africa/Porto-Novo'
      tz_125:
        internal_name: 'America/St_Barthelemy'
        display_name: '(GMT-04:00) America/St_Barthelemy'
      tz_126:
        internal_name: 'Atlantic/Bermuda'
        display_name: '(GMT-04:00) Atlantic/Bermuda'
      tz_127:
        internal_name: 'Asia/Brunei'
        display_name: '(GMT+08:00) Asia/Brunei'
      tz_128:
        internal_name: 'America/La_Paz'
        display_name: '(GMT-04:00) America/La_Paz'
      tz_129:
        internal_name: 'America/Kralendijk'
        display_name: '(GMT+08:45) America/Kralendijk'
      tz_130:
        internal_name: 'America/Belem'
        display_name: '(GMT-03:00) Para (east), Amapa'
      tz_131:
        internal_name: 'America/Fortaleza'
        display_name: '(GMT-03:00) Brazil (northeast: MA, PI, CE, RN, PB)'
      tz_132:
        internal_name: 'America/Recife'
        display_name: '(GMT-03:00) Pernambuco'
      tz_133:
        internal_name: 'America/Araguaina'
        display_name: '(GMT-03:00) Tocantins'
      tz_134:
        internal_name: 'America/Maceio'
        display_name: '(GMT-03:00) Alagoas, Sergipe'
      tz_135:
        internal_name: 'America/Bahia'
        display_name: '(GMT-03:00) Bahia'
      tz_136:
        internal_name: 'America/Campo_Grande'
        display_name: '(GMT-04:00) Mato Grosso do Sul'
      tz_137:
        internal_name: 'America/Cuiaba'
        display_name: '(GMT-04:00) Mato Grosso'
      tz_138:
        internal_name: 'America/Santarem'
        display_name: '(GMT-03:00) Para (west)'
      tz_139:
        internal_name: 'America/Porto_Velho'
        display_name: '(GMT-04:00) Rondonia'
      tz_140:
        internal_name: 'America/Boa_Vista'
        display_name: '(GMT-04:00) Roraima'
      tz_141:
        internal_name: 'America/Eirunepe'
        display_name: '(GMT-05:00) Amazonas (west)'
      tz_142:
        internal_name: 'America/Rio_Braco'
        display_name: '(GMT-05:00) Acre'
      tz_143:
        internal_name: 'America/Nassau'
        display_name: '(GMT-05:00) America/Nassau'
      tz_144:
        internal_name: 'Asia/Thimphu'
        display_name: '(GMT+06:00) Asia/Thimphu'
      tz_145:
        internal_name: 'Africa/Gaborone'
        display_name: '(GMT+02:00) Africa/Gaborone'
      tz_146:
        internal_name: 'America/Belize'
        display_name: '(GMT-06:00) America/Belize'
      tz_147:
        internal_name: 'America/St_Johns'
        display_name: '(GMT-03:30) Newfoundland, Labrador (southeast)'
      tz_148:
        internal_name: 'America/Halifax'
        display_name: '(GMT-04:00) Atlantic - NS (most areas), PE'
      tz_149:
        internal_name: 'America/Glace_Bay'
        display_name: '(GMT-04:00) Atlantic - NS (Cape Breton)'
      tz_150:
        internal_name: 'America/Moncton'
        display_name: '(GMT-04:00) Atlantic - New Brunswick'
      tz_151:
        internal_name: 'America/Goose_Bay'
        display_name: '(GMT-04:00) Atlantic - Labrador (most areas)'
      tz_152:
        internal_name: 'America/Blanc-Sablon'
        display_name: '(GMT-04:00) AST - QC (Lower North Shore)'
      tz_153:
        internal_name: 'America/Toronto'
        display_name: '(GMT-05:00) Eastern - ON, QC (most areas)'
      tz_154:
        internal_name: 'America/Iqaluit'
        display_name: '(GMT-05:00) Eastern - NU (most areas)'
      tz_155:
        internal_name: 'America/Atikokan'
        display_name: '(GMT-05:00) EST - ON (Atikokan), NU (Coral H)'
      tz_156:
        internal_name: 'America/Winnipeg'
        display_name: '(GMT-06:00) Central - ON (west), Manitoba'
      tz_157:
        internal_name: 'America/Resolute'
        display_name: '(GMT-06:00) Central - NU (Resolute)'
      tz_158:
        internal_name: 'America/Rankin_Inlet'
        display_name: '(GMT-06:00) Central - NU (central)'
      tz_159:
        internal_name: 'America/Regina'
        display_name: '(GMT-06:00) CST - SK (most areas)'
      tz_160:
        internal_name: 'America/Swift_Current'
        display_name: '(GMT-06:00) CST - SK (midwest)'
      tz_161:
        internal_name: 'America/Edmonton'
        display_name: '(GMT-07:00) Mountain - AB, BC (E), SK (W)'
      tz_162:
        internal_name: 'America/Cambridge_Bay'
        display_name: '(GMT-07:00) Mountain - NU (west)'
      tz_163:
        internal_name: 'America/Yellowknife'
        display_name: '(GMT-07:00) Mountain - NT (central)'
      tz_164:
        internal_name: 'America/Inuvik'
        display_name: '(GMT-07:00) Mountain - NT (west)'
      tz_165:
        internal_name: 'America/Creston'
        display_name: '(GMT-07:00) MST - BC (Creston)'
      tz_166:
        internal_name: 'America/Dawson_Creek'
        display_name: '(GMT-07:00) MST - BC (Dawson Cr, Ft St John)'
      tz_167:
        internal_name: 'America/Fort_Nelson'
        display_name: '(GMT-07:00) MST - BC (Ft Nelson)'
      tz_168:
        internal_name: 'America/Whitehorse'
        display_name: '(GMT-07:00) MST - Yukon (east)'
      tz_169:
        internal_name: 'America/Dawson'
        display_name: '(GMT-07:00) MST - Yukon (west)'
      tz_170:
        internal_name: 'America/Vancouver'
        display_name: '(GMT-08:00) Pacific - BC (most areas)'
      tz_171:
        internal_name: 'Indian/Cocos'
        display_name: '(GMT+06:30) Indian/Cocos'
      tz_172:
        internal_name: 'Africa/Kinshasa'
        display_name: '(GMT+01:00) Dem. Rep. of Congo (west)'
      tz_173:
        internal_name: 'Africa/Lubumbashi'
        display_name: '(GMT+02:00) Dem. Rep. of Congo (east)'
      tz_174:
        internal_name: 'Africa/Bangui'
        display_name: '(GMT+01:00) Africa/Bangui'
      tz_175:
        internal_name: 'Africa/Brazzaville'
        display_name: '(GMT+01:00) Africa/Brazzaville'
      tz_176:
        internal_name: 'Europe/Zurich'
        display_name: '(GMT+01:00) Europe/Zurich'
      tz_177:
        internal_name: 'Africa/Abidjan'
        display_name: '(GMT+00:00) Africa/Abidjan'
      tz_178:
        internal_name: 'Pacific/Rarotonga'
        display_name: '(GMT-10:00) Pacific/Rarotonga'
      tz_179:
        internal_name: 'America/Punta_Arenas'
        display_name: '(GMT-03:00) Region of Magallanes'
      tz_180:
        internal_name: 'Pacific/Easter'
        display_name: '(GMT-05:00) Easter Island'
      tz_181:
        internal_name: 'Africa/Douala'
        display_name: '(GMT-06:00) Africa/Douala'
      tz_182:
        internal_name: 'Asia/Shanghai'
        display_name: '(GMT+08:00) Beijing Time'
      tz_183:
        internal_name: 'Asia/Urumqi'
        display_name: '(GMT+06:00) Xinjiang Time'
      tz_184:
        internal_name: 'America/Costa_Rica'
        display_name: '(GMT-06:00) America/Costa_Rica'
      tz_185:
        internal_name: 'America/Havana'
        display_name: '(GMT-05:00) America/Havana'
      tz_186:
        internal_name: 'America/Curacao'
        display_name: '(GMT-04:00) America/Curacao'
      tz_187:
        internal_name: 'Indian/Christmas'
        display_name: '(GMT+07:00) Indian/Christmas'
      tz_188:
        internal_name: 'Asia/Nicosia'
        display_name: '(GMT+02:00) Cyprus (most areas)'
      tz_189:
        internal_name: 'Asia/Famagusta'
        display_name: '(GMT+02:00) Northern Cyprus'
      tz_190:
        internal_name: 'Europe/Prague'
        display_name: '(GMT+01:00) Europe/Prague'
      tz_191:
        internal_name: 'Europe/Berlin'
        display_name: '(GMT+01:00) Germany (most areas)'
      tz_192:
        internal_name: 'Europe/Busingen'
        display_name: '(GMT+01:00) Busingen'
      tz_193:
        internal_name: 'Africa/Djibouti'
        display_name: '(GMT+03:00) Africa/Djibouti'
      tz_194:
        internal_name: 'Europe/Copenhagen'
        display_name: '(GMT+01:00) Europe/Copenhagen'
      tz_195:
        internal_name: 'America/Dominica'
        display_name: '(GMT-04:00) America/Dominica'
      tz_196:
        internal_name: 'America/Santo_Domingo'
        display_name: '(GMT-04:00) America/Santo_Domingo'
      tz_197:
        internal_name: 'Africa/Algiers'
        display_name: '(GMT+01:00) Africa/Algiers'
      tz_198:
        internal_name: 'America/Guayaquil'
        display_name: '(GMT-05:00) Ecuador (mainland)'
      tz_199:
        internal_name: 'Pacific/Galapagos'
        display_name: '(GMT-06:00) Galapagos Islands'
      tz_200:
        internal_name: 'Europe/Tallinn'
        display_name: '(GMT+02:00) Europe/Tallinn'
      tz_201:
        internal_name: 'Africa/El_Aaiun'
        display_name: '(GMT-00:00) Africa/El_Aaiun'
      tz_202:
        internal_name: 'Africa/Asmara'
        display_name: '(GMT+03:00) Africa/Asmara'
      tz_203:
        internal_name: 'Europe/Madrid'
        display_name: '(GMT+01:00) Spain (mainland)'
      tz_204:
        internal_name: 'Africa/Ceuta'
        display_name: '(GMT+01:00) Ceuta, Melilla'
      tz_205:
        internal_name: 'Atlantic/Canary'
        display_name: '(GMT+00:00) Canary Islands'
      tz_206:
        internal_name: 'Africa/Addis_Ababa'
        display_name: '(GMT+03:00) Africa/Addis_Ababa'
      tz_207:
        internal_name: 'Atlantic/Stanley'
        display_name: '(GMT-03:00) Atlantic/Stanley'
      tz_208:
        internal_name: 'Pacific/Chuuk'
        display_name: '(GMT+10:00) Chuuk/Truk, Yap'
      tz_209:
        internal_name: 'Pacific/Pohnpei'
        display_name: '(GMT+11:00) Pohnpei/Ponape'
      tz_210:
        internal_name: 'Pacific/Kosrae'
        display_name: '(GMT+11:00) Kosrae'
      tz_211:
        internal_name: 'Atlantic/Faroe'
        display_name: '(GMT+00:00) Atlantic/Faroe'
      tz_212:
        internal_name: 'Europe/Paris'
        display_name: '(GMT+01:00) Europe/Paris'
      tz_213:
        internal_name: 'Africa/Libreville'
        display_name: '(GMT+01:00) Africa/Libreville'
      tz_214:
        internal_name: 'Europe/London'
        display_name: '(GMT+00:00) Europe/London'
      tz_215:
        internal_name: 'America/Grenada'
        display_name: '(GMT-04:00) America/Grenada'
      tz_216:
        internal_name: 'America/Cayenne'
        display_name: '(GMT-04:00) America/Cayenne'
      tz_217:
        internal_name: 'Europe/Guernsey'
        display_name: '(GMT+00:00) Europe/Guernsey'
      tz_218:
        internal_name: 'Africa/Accra'
        display_name: '(GMT+00:00) Africa/Accra'
      tz_219:
        internal_name: 'Europe/Gibraltar'
        display_name: '(GMT+01:00) Europe/Gibraltar'
      tz_220:
        internal_name: 'America/Nuuk'
        display_name: '(GMT-03:00) Greenland (most areas)'
      tz_221:
        internal_name: 'America/Danmarkshavn'
        display_name: '(GMT+00:00) National Park (east coast)'
      tz_222:
        internal_name: 'America/Scoresbysund'
        display_name: '(GMT-01:00) Scoresbysund/Ittoqqortoormiit'
      tz_223:
        internal_name: 'America/Thule'
        display_name: '(GMT-01:00) Thule/Pituffik'
      tz_224:
        internal_name: 'Africa/Banjul'
        display_name: '(GMT+00:00) Africa/Banjul'
      tz_225:
        internal_name: 'Africa/Conakry'
        display_name: '(GMT+00:00) Africa/Conakry'
      tz_226:
        internal_name: 'America/Guadeloupe'
        display_name: '(GMT-04:00) America/Guadeloupe'
      tz_227:
        internal_name: 'Africa/Malabo'
        display_name: '(GMT+01:00) Africa/Malabo'
      tz_228:
        internal_name: 'Atlantic/South_Georgia'
        display_name: '(GMT-02:00) Atlantic/South_Georgia'
      tz_229:
        internal_name: 'America/Guatemala'
        display_name: '(GMT-06:00) America/Guatemala'
      tz_230:
        internal_name: 'Africa/Bissau'
        display_name: '(GMT+00:00) Africa/Bissau'
      tz_231:
        internal_name: 'America/Guyana'
        display_name: '(GMT-04:00) America/Guyana'
      tz_232:
        internal_name: 'America/Tegucigalpa'
        display_name: '(GMT-06:00) America/Tegucigalpa'
      tz_233:
        internal_name: 'Europe/Zagreb'
        display_name: '(GMT+01:00) Europe/Zagreb'
      tz_234:
        internal_name: 'America/Port-au-Prince'
        display_name: '(GMT-05:00) America/Port-au-Prince'
      tz_235:
        internal_name: 'Europe/Budapest'
        display_name: '(GMT+01:00) Europe/Budapest'
      tz_236:
        internal_name: 'Asia/Jakarta'
        display_name: '(GMT+07:00) Java, Sumatra'
      tz_237:
        internal_name: 'Asia/Pontianak'
        display_name: '(GMT+07:00) Borneo (west, central)'
      tz_238:
        internal_name: 'Asia/Makassar'
        display_name: '(GMT+08:00) Borneo (east, south), Sulawesi/Celebes, Bali, Nusa Tengarra, Tim (west)'
      tz_239:
        internal_name: 'Asia/Jayapura'
        display_name: '(GMT+09:00) New Guinea (West Papua / Irian Jaya), Malukus/Moluccas'
      tz_240:
        internal_name: 'Europe/Dublin'
        display_name: '(GMT+00:00) Europe/Dublin'
      tz_241:
        internal_name: 'Europe/Isle_of_Man'
        display_name: '(GMT+00:00) Europe/Isle_of_Man'
      tz_242:
        internal_name: 'Asia/Kolkata'
        display_name: '(GMT+5:30) Asia/Kolkata'
      tz_243:
        internal_name: 'Indian/Chagos'
        display_name: '(GMT+06:00) Indian/Chagos'
      tz_244:
        internal_name: 'Asia/Baghdad'
        display_name: '(GMT+03:00) Asia/Baghdad'
      tz_245:
        internal_name: 'Atlantic/Reykjavik'
        display_name: '(GMT+00:00) Atlantic/Reykjavik'
      tz_246:
        internal_name: 'Europe/Rome'
        display_name: '(GMT+01:00) Europe/Rome'
      tz_247:
        internal_name: 'Europe/Jersey'
        display_name: '(GMT+00:00) Europe/Jersey'
      tz_248:
        internal_name: 'America/Jamaica'
        display_name: '(GMT-05:00) America/Jamaica'
      tz_249:
        internal_name: 'Asia/Bishkek'
        display_name: '(GMT+06:00) Asia/Bishkek'
      tz_250:
        internal_name: 'Asia/Phnom_Penh'
        display_name: '(GMT+07:00) Asia/Phnom_Penh'
      tz_251:
        internal_name: 'Pacific/Tarawa'
        display_name: '(GMT+12:00) Gilbert Islands'
      tz_252:
        internal_name: 'Pacific/Kanton'
        display_name: '(GMT+013:00) Phoenix Islands'
      tz_253:
        internal_name: 'Pacific/Kiritimati'
        display_name: '(GMT+14:00) Line Islands'
      tz_254:
        internal_name: 'Indian/Comoro'
        display_name: '(GMT+03:00) Indian/Comoro'
      tz_255:
        internal_name: 'America/St_Kitts'
        display_name: '(GMT-04:00) America/St_Kitts'
      tz_256:
        internal_name: 'Asia/Pyongyang'
        display_name: '(GMT+09:00) Asia/Pyongyang'
      tz_257:
        internal_name: 'America/Cayman'
        display_name: '(GMT-05:00) America/Cayman'
      tz_258:
        internal_name: 'Asia/Qyzylorda'
        display_name: '(GMT+05:00) Qyzylorda/Kyzylorda/Kzyl-Orda'
      tz_259:
        internal_name: 'Asia/Qostanay'
        display_name: '(GMT+06:00) Qostanay/Kostanay/Kustanay'
      tz_260:
        internal_name: 'Asia/Aqtobe'
        display_name: '(GMT+05:00) Aqtobe/Aktobe'
      tz_261:
        internal_name: 'Asia/Aqtau'
        display_name: '(GMT+05:00) Mangghystau/Mankistau'
      tz_262:
        internal_name: 'Asia/Atyrau'
        display_name: '(GMT+05:00) Atyrau/Atirau/Gur yev'
      tz_263:
        internal_name: 'Asia/Oral'
        display_name: '(GMT+05:00) West Kazakhstan'
      tz_264:
        internal_name: 'Asia/Vientiane'
        display_name: '(GMT+07:00) Asia/Vientiane'
      tz_265:
        internal_name: 'America/St_Lucia'
        display_name: '(GMT-04:00) America/St_Lucia'
      tz_266:
        internal_name: 'Europe/Vaduz'
        display_name: '(GMT+1:00) Europe/Vaduz'
      tz_267:
        internal_name: 'Asia/Colombo'
        display_name: '(GMT+05:30) Asia/Colombo'
      tz_268:
        internal_name: 'Africa/Monrovia'
        display_name: '(GMT+00:00) Africa/Monrovia'
      tz_269:
        internal_name: 'Africa/Maseru'
        display_name: '(GMT+02:00) Africa/Maseru'
      tz_270:
        internal_name: 'Europe/Vilnius'
        display_name: '(GMT+02:00) Europe/Vilnius'
      tz_271:
        internal_name: 'Europe/Luxembourg'
        display_name: '(GMT+01:00) Europe/Luxembourg'
      tz_272:
        internal_name: 'Europe/Riga'
        display_name: '(GMT+02:00) Europe/Riga'
      tz_273:
        internal_name: 'Africa/Tripoli'
        display_name: '(GMT+02:00) Africa/Tripoli'
      tz_274:
        internal_name: 'Europe/Monaco'
        display_name: '(GMT+01:00) Europe/Monaco'
      tz_275:
        internal_name: 'Europe/Chisinau'
        display_name: '(GMT+02:00) Europe/Chisinau'
      tz_276:
        internal_name: 'Europe/Podgorica'
        display_name: '(GMT+01:00) Europe/Podgorica'
      tz_277:
        internal_name: 'America/Marigot'
        display_name: '(GMT-04:00) America/Marigot'
      tz_278:
        internal_name: 'Indian/Antananarivo'
        display_name: '(GMT-04:00) Indian/Antananarivo'
      tz_279:
        internal_name: 'Pacific/Majuro'
        display_name: '(GMT+12:00) Marshall Islands (most areas)'
      tz_280:
        internal_name: 'Pacific/Kwajalein'
        display_name: '(GMT+12:00) Kwajalein'
      tz_281:
        internal_name: 'Europe/Skopje'
        display_name: '(GMT+01:00) Europe/Skopje'
      tz_282:
        internal_name: 'Africa/Bamako'
        display_name: '(GMT+00:00) Africa/Bamako'
      tz_283:
        internal_name: 'Asia/Yangon'
        display_name: '(GMT++6:30) Asia/Yangon'
      tz_284:
        internal_name: 'Asia/Ulaanbaatar'
        display_name: '(GMT+08:00) Mongolia (most areas)'
      tz_285:
        internal_name: 'Asia/Hovd'
        display_name: '(GMT+07:00) Bayan-Olgiy, Govi-Altai, Hovd, Uvs, Zavkhan'
      tz_286:
        internal_name: 'Asia/Choibalsan'
        display_name: '(GMT+08:00) Dornod, Sukhbaatar'
      tz_287:
        internal_name: 'Asia/Macau'
        display_name: '(GMT+08:00) Asia/Macau'
      tz_288:
        internal_name: 'Pacific/Saipan'
        display_name: '(GMT+10:00) Pacific/Saipan'
      tz_289:
        internal_name: 'America/Martinique'
        display_name: '(GMT-04:00) America/Martinique'
      tz_290:
        internal_name: 'Africa/Nouakchott'
        display_name: '(GMT+00:00) Africa/Nouakchott'
      tz_291:
        internal_name: 'America/Montserrat'
        display_name: '(GMT-04:00) America/Montserrat'
      tz_292:
        internal_name: 'Europe/Malta'
        display_name: '(GMT+01:00) Europe/Malta'
      tz_293:
        internal_name: 'Indian/Mauritius'
        display_name: '(GMT+04:00) Indian/Mauritius'
      tz_294:
        internal_name: 'Indian/Maldives'
        display_name: '(GMT+05:00) Indian/Maldives'
      tz_295:
        internal_name: 'Africa/Blantyre'
        display_name: '(GMT+02:00) Africa/Blantyre'
      tz_296:
        internal_name: 'America/Cancun'
        display_name: '(GMT-05:00) Quintana Roo'
      tz_297:
        internal_name: 'America/Merida'
        display_name: '(GMT-06:00) Campeche, Yucatan'
      tz_298:
        internal_name: 'America/Monterrey'
        display_name: '(GMT-06:00) Durango Coahuila, Nuevo Leon, Tamaulipas (most areas)'
      tz_299:
        internal_name: 'America/Matamoros'
        display_name: '(GMT-06:00) Coahuila, Nuevo Leon, Tamaulipas (US border)'
      tz_300:
        internal_name: 'America/Ciudad_Juarez'
        display_name: '(GMT-07:00) Chihuahua (US border - west)'
      tz_301:
        internal_name: 'America/Ojinaga'
        display_name: '(GMT-07:00) Chihuahua (US border - east)'
      tz_302:
        internal_name: 'America/Mazatlan'
        display_name: '(GMT-07:00) Baja California Sur, Nayarit (most areas), Sinaloa'
      tz_303:
        internal_name: 'America/Bahia_Banderas'
        display_name: '(GMT-06:00) Bahia de Banderas'
      tz_304:
        internal_name: 'America/Hermosill'
        display_name: '(GMT-07:00) Sonora'
      tz_305:
        internal_name: 'Asia/Kuching'
        display_name: '(GMT+08:00) Sabah, Sarawak'
      tz_306:
        internal_name: 'Africa/Maputo'
        display_name: '(GMT+02:00) Africa/Maputo'
      tz_307:
        internal_name: 'Pacific/Noumea'
        display_name: '(GMT+11:00) Pacific/Noumea'
      tz_308:
        internal_name: 'Africa/Niamey'
        display_name: '(GMT+01:00) Africa/Niamey'
      tz_309:
        internal_name: 'Pacific/Norfolk'
        display_name: '(GMT+11:00) Pacific/Norfolk'
      tz_310:
        internal_name: 'Europe/Oslo'
        display_name: '(GMT+01:00) Europe/Oslo'
      tz_311:
        internal_name: 'Asia/Kathmandu'
        display_name: '(GMT+05:45) Asia/Kathmandu'
      tz_312:
        internal_name: 'Pacific/Nauru'
        display_name: '(GMT+12:00) Pacific/Nauru'
      tz_313:
        internal_name: 'Pacific/Niue'
        display_name: '(GMT-11:00) Pacific/Niue'
      tz_314:
        internal_name: 'Pacific/Chatham'
        display_name: '(GMT+13:45) Chatham Islands'
      tz_315:
        internal_name: 'America/Panama'
        display_name: '(GMT-05:00) America/Panama'
      tz_316:
        internal_name: 'America/Lima'
        display_name: '(GMT-05:00) America/Lima'
      tz_317:
        internal_name: 'Pacific/Tahiti'
        display_name: '(GMT-10:00) Society Islands'
      tz_318:
        internal_name: 'Pacific/Marquesas'
        display_name: '(GMT-09:30) Marquesas Islands'
      tz_319:
        internal_name: 'Pacific/Gambier'
        display_name: '(GMT-09:00) Gambier Islands'
      tz_320:
        internal_name: 'Pacific/Port_Moresby'
        display_name: '(GMT+10:00) Papua New Guinea (most areas)'
      tz_321:
        internal_name: 'Pacific/Bougainville'
        display_name: '(GMT+11:00) Bougainville'
      tz_322:
        internal_name: 'Asia/Manila'
        display_name: '(GMT+08:00) Asia/Manila'
      tz_323:
        internal_name: 'Europe/Warsaw'
        display_name: '(GMT+01:00) Europe/Warsaw'
      tz_324:
        internal_name: 'America/Miquelon'
        display_name: '(GMT-03:00) America/Miquelon'
      tz_325:
        internal_name: 'Pacific/Pitcairn'
        display_name: '(GMT-08:00) Pacific/Pitcairn'
      tz_326:
        internal_name: 'America/Puerto_Rico'
        display_name: '(GMT-04:00) America/Puerto_Rico'
      tz_327:
        internal_name: 'Asia/Gaza'
        display_name: '(GMT+02:00) Gaza Strip'
      tz_328:
        internal_name: 'Asia/Hebron'
        display_name: '(GMT+02:00) West Bank'
      tz_329:
        internal_name: 'Europe/Lisbon'
        display_name: '(GMT+00:00) Portugal (mainland)'
      tz_330:
        internal_name: 'Atlantic/Madeira'
        display_name: '(GMT+00:00) Madeira Islands'
      tz_331:
        internal_name: 'Pacific/Palau'
        display_name: '(GMT+09:00) Pacific/Palau'
      tz_332:
        internal_name: 'America/Asuncion'
        display_name: '(GMT-04:00) America/Asuncion'
      tz_333:
        internal_name: 'Asia/Qatar'
        display_name: '(GMT+03:00) Asia/Qatar'
      tz_334:
        internal_name: 'Indian/Reunion'
        display_name: '(GMT+04:00) Indian/Reunion'
      tz_335:
        internal_name: 'Europe/Bucharest'
        display_name: '(GMT+02:00) Europe/Bucharest'
      tz_336:
        internal_name: 'Europe/Kaliningrad'
        display_name: '(GMT+02:00) MSK-01 - Kaliningrad'
      tz_337:
        internal_name: 'Europe/Simferopol'
        display_name: '(GMT+03:00) Crimea'
      tz_338:
        internal_name: 'Europe/Kirov'
        display_name: '(GMT+03:00) MSK+00 - Kirov'
      tz_339:
        internal_name: 'Europe/Volgograd'
        display_name: '(GMT+03:00) MSK+00 - Volgograd'
      tz_340:
        internal_name: 'Europe/Astrakhan'
        display_name: '(GMT+04:00) MSK+01 - Astrakhan'
      tz_341:
        internal_name: 'Europe/Saratov'
        display_name: '(GMT+04:00) MSK+01 - Saratov'
      tz_342:
        internal_name: 'Europe/Ulyanovsk'
        display_name: '(GMT+04:00) MSK+01 - Ulyanovsk'
      tz_343:
        internal_name: 'Europe/Samara'
        display_name: '(GMT+04:00) MSK+01 - Samara, Udmurtia'
      tz_344:
        internal_name: 'Asia/Omsk'
        display_name: '(GMT+06:00) MSK+03 - Omsk'
      tz_345:
        internal_name: 'Asia/Novosibirsk'
        display_name: '(GMT+07:00) MSK+04 - Novosibirsk'
      tz_346:
        internal_name: 'Asia/Barnaul'
        display_name: '(GMT+07:00) MSK+04 - Altai'
      tz_347:
        internal_name: 'Asia/Tomsk'
        display_name: '(GMT+07:00) MSK+04 - Tomsk'
      tz_348:
        internal_name: 'Asia/Novokuznetsk'
        display_name: '(GMT+07:00) MSK+04 - Kemerovo'
      tz_349:
        internal_name: 'Asia/Chita'
        display_name: '(GMT+09:00) MSK+06 - Zabaykalsky'
      tz_350:
        internal_name: 'Asia/Khandyga'
        display_name: '(GMT+09:00) MSK+06 - Tomponsky, Ust-Maysky'
      tz_351:
        internal_name: 'Asia/Ust-Nera'
        display_name: '(GMT+10:00) MSK+07 - Oymyakonsky'
      tz_352:
        internal_name: 'Asia/Sakhalin'
        display_name: '(GMT+11:00) MSK+08 - Sakhalin Island'
      tz_353:
        internal_name: 'Asia/Srednekolymsk'
        display_name: '(GMT+11:00) MSK+08 - Sakha (E) North Kuril Is'
      tz_354:
        internal_name: 'Asia/Kamchatka'
        display_name: '(GMT+12:00) MSK+09 - Kamchatka'
      tz_355:
        internal_name: 'Asia/Anadyr'
        display_name: '(GMT+12:00) MSK+09 - Bering Sea'
      tz_356:
        internal_name: 'Africa/Kigali'
        display_name: '(GMT+02:00) Africa/Kigali'
      tz_357:
        internal_name: 'Asia/Riyadh'
        display_name: '(GMT+03:00) Asia/Riyadh'
      tz_358:
        internal_name: 'Pacific/Guadalcanal'
        display_name: '(GMT+11:00) Pacific/Guadalcanal'
      tz_359:
        internal_name: 'Indian/Mahe'
        display_name: '(GMT+04:00) Indian/Mahe'
      tz_360:
        internal_name: 'Africa/Khartoum'
        display_name: '(GMT+02:00) Africa/Khartoum'
      tz_361:
        internal_name: 'Europe/Stockholm'
        display_name: '(GMT+01:00) Europe/Stockholm'
      tz_362:
        internal_name: 'Asia/Singapore'
        display_name: '(GMT+01:00) Asia/Singapore'
      tz_363:
        internal_name: 'Atlantic/St_Helena'
        display_name: '(GMT+08:00) Atlantic/St_Helena'
      tz_364:
        internal_name: 'Europe/Ljubljana'
        display_name: '(GMT+00:00) Europe/Ljubljana'
      tz_365:
        internal_name: 'Arctic/Longyearbyen'
        display_name: '(GMT+01:00) Arctic/Longyearbyen'
      tz_366:
        internal_name: 'Europe/Bratislava'
        display_name: '(GMT+01:00) Europe/Bratislava'
      tz_367:
        internal_name: 'Africa/Freetown'
        display_name: '(GMT+00:00) Africa/Freetown'
      tz_368:
        internal_name: 'Europe/San_Marino'
        display_name: '(GMT+01:00) Europe/San_Marino'
      tz_369:
        internal_name: 'Africa/Dakar'
        display_name: '(GMT+00:00) Africa/Dakar'
      tz_370:
        internal_name: 'Africa/Mogadishu'
        display_name: '(GMT+03:00) Africa/Mogadishu'
      tz_371:
        internal_name: 'America/Paramaribo'
        display_name: '(GMT-03:00) America/Paramaribo'
      tz_372:
        internal_name: 'Africa/Juba'
        display_name: '(GMT+02:00) Africa/Juba'
      tz_373:
        internal_name: 'Africa/Sao_Tome'
        display_name: '(GMT+00:00) Africa/Sao_Tome'
      tz_374:
        internal_name: 'America/El_Salvador'
        display_name: '(GMT-06:00) America/El_Salvador'
      tz_375:
        internal_name: 'America/Lower_Princes'
        display_name: '(GMT-04:00) America/Lower_Princes'
      tz_376:
        internal_name: 'Asia/Damascus'
        display_name: '(GMT+02:00) Asia/Damascus'
      tz_377:
        internal_name: 'Africa/Mbabane'
        display_name: '(GMT+02:00) Africa/Mbabane'
      tz_378:
        internal_name: 'America/Grand_Turk'
        display_name: '(GMT-05:00) America/Grand_Turk'
      tz_379:
        internal_name: 'Africa/Ndjamena'
        display_name: '(GMT+01:00) Africa/Ndjamena'
      tz_380:
        internal_name: 'Indian/Kerguelen'
        display_name: '(GMT+05:00) Indian/Kerguelen'
      tz_381:
        internal_name: 'Africa/Lome'
        display_name: '(GMT+00:00) Africa/Lome'
      tz_382:
        internal_name: 'Asia/Dushanbe'
        display_name: '(GMT+05:00) Asia/Dushanbe'
      tz_383:
        internal_name: 'Pacific/Fakaofo'
        display_name: '(GMT+13:00) Pacific/Fakaofo'
      tz_384:
        internal_name: 'Asia/Dili'
        display_name: '(GMT+09:00) Asia/Dili'
      tz_385:
        internal_name: 'Asia/Ashgabat'
        display_name: '(GMT+05:00) Asia/Ashgabat'
      tz_386:
        internal_name: 'Africa/Tunis'
        display_name: '(GMT+01:00) Africa/Tunis'
      tz_387:
        internal_name: 'Europe/Istanbul'
        display_name: '(GMT+01:00) Europe/Istanbul'
      tz_388:
        internal_name: 'America/Port_of_Spain'
        display_name: '(GMT-04:00) America/Port_of_Spain'
      tz_389:
        internal_name: 'Pacific/Funafuti'
        display_name: '(GMT+12:00) Pacific/Funafuti'
      tz_390:
        internal_name: 'Africa/Dar_es_Salaam'
        display_name: '(GMT+03:00) Africa/Dar_es_Salaam'
      tz_391:
        internal_name: 'Europe/Kyiv'
        display_name: '(GMT+02:00) Ukraine (most areas)'
      tz_392:
        internal_name: 'Africa/Kampala'
        display_name: '(GMT+03:00) Africa/Kampala'
      tz_393:
        internal_name: 'Pacific/Wake'
        display_name: '(GMT+12:00) Wake Island'
      tz_394:
        internal_name: 'America/New_York'
        display_name: '(GMT-05:00) Eastern (most areas)'
      tz_395:
        internal_name: 'America/Detroit'
        display_name: '(GMT-05:00) Eastern - MI (most areas)'
      tz_396:
        internal_name: 'America/Kentucky/Louisville'
        display_name: '(GMT-05:00) Eastern - KY (Louisville area)'
      tz_397:
        internal_name: 'America/Kentucky/Monticello'
        display_name: '(GMT-05:00) Eastern - KY (Wayne)'
      tz_398:
        internal_name: 'America/Indiana/Indianapolis'
        display_name: '(GMT-05:00) Eastern - IN (most areas)'
      tz_399:
        internal_name: 'America/Indiana/Vincennes'
        display_name: '(GMT-05:00) Eastern - IN (Da, Du, K, Mn)'
      tz_400:
        internal_name: 'America/Indiana/Winamac'
        display_name: '(GMT-05:00) Eastern - IN (Pulaski)'
      tz_401:
        internal_name: 'America/Indiana/Marengo'
        display_name: '(GMT-05:00) Eastern - IN (Crawford)'
      tz_402:
        internal_name: 'America/Indiana/Petersburg'
        display_name: '(GMT-05:00) Eastern - IN (Pike)'
      tz_403:
        internal_name: 'America/Indiana/Vevay'
        display_name: '(GMT-05:00) Eastern - IN (Switzerland:)'
      tz_404:
        internal_name: 'America/Chicag'
        display_name: '(GMT-06:00) Central (most areas)'
      tz_405:
        internal_name: 'America/Indiana/Tell_City'
        display_name: '(GMT-06:00) Central - IN (Perry)'
      tz_406:
        internal_name: 'America/Indiana/Knox'
        display_name: '(GMT-06:00) Central - IN (Starke)'
      tz_407:
        internal_name: 'America/Menominee'
        display_name: '(GMT-06:00) Central - MI (Wisconsin border)'
      tz_408:
        internal_name: 'America/North_Dakota/Center'
        display_name: '(GMT-06:00) Central - ND (Oliver)'
      tz_409:
        internal_name: 'America/North_Dakota/New_Salem'
        display_name: '(GMT-06:00) Central - ND (Morton rural)'
      tz_410:
        internal_name: 'America/North_Dakota/Beulah'
        display_name: '(GMT-06:00) Central - ND (Mercer)'
      tz_411:
        internal_name: 'America/Denver'
        display_name: '(GMT-07:00) Mountain (most areas)'
      tz_412:
        internal_name: 'America/Boise'
        display_name: '(GMT-07:00) Mountain - ID (south) OR (east)'
      tz_413:
        internal_name: 'America/Phoenix'
        display_name: '(GMT-09:00) MST - Arizona (except Navajo)'
      tz_414:
        internal_name: 'America/Anchorage'
        display_name: '(GMT-09:00) Alaska (most areas)'
      tz_415:
        internal_name: 'America/Juneau'
        display_name: '(GMT-09:00) Alaska - Juneau area'
      tz_416:
        internal_name: 'America/Sitka'
        display_name: '(GMT-09:00) Alaska - Sitka area'
      tz_417:
        internal_name: 'America/Metlakatla'
        display_name: '(GMT-09:00) Alaska - Annette Island'
      tz_418:
        internal_name: 'America/Yakutat'
        display_name: '(GMT-09:00) Alaska - Yakutat'
      tz_419:
        internal_name: 'America/Nome'
        display_name: '(GMT-09:00) Alaska (west)'
      tz_420:
        internal_name: 'America/Adak'
        display_name: '(GMT-10:00) Aleutian Islands'
      tz_421:
        internal_name: 'Asia/Samarkand'
        display_name: '(GMT+05:00) Uzbekistan (west)'
      tz_422:
        internal_name: 'Asia/Tashkent'
        display_name: '(GMT+05:00) Uzbekistan (east)'
      tz_423:
        internal_name: 'Europe/Vatican'
        display_name: '(GMT+02:00) Europe/Vatican'
      tz_424:
        internal_name: 'America/St_Vincent'
        display_name: '(GMT-04:00) America/St_Vincent'
      tz_425:
        internal_name: 'America/Tortola'
        display_name: '(GMT-04:00) America/Tortola'
      tz_426:
        internal_name: 'America/St_Thomas'
        display_name: '(GMT-04:00) America/St_Thomas'
      tz_427:
        internal_name: 'Asia/Ho_Chi_Minh'
        display_name: '(GMT+07:00) Asia/Ho_Chi_Minh'
      tz_428:
        internal_name: 'Pacific/Efate'
        display_name: '(GMT+11:00) Pacific/Efate'
      tz_429:
        internal_name: 'Pacific/Wallis'
        display_name: '(GMT+12:00) Pacific/Wallis'
      tz_430:
        internal_name: 'Pacific/Apia'
        display_name: '(GMT+14:00) Pacific/Apia'
      tz_431:
        internal_name: 'Asia/Aden'
        display_name: '(GMT+03:00) Asia/Aden'
      tz_432:
        internal_name: 'Indian/Mayotte'
        display_name: '(GMT+03:00) Indian/Mayotte'
      tz_433:
        internal_name: 'Africa/Johannesburg'
        display_name: '(GMT+02:00) Africa/Johannesburg'
      tz_434:
        internal_name: 'Africa/Lusaka'
        display_name: '(GMT+02:00) Africa/Lusaka'

relatedTo:
  internal_name: 'relatedTo'
  display_name: 'Related To'
  field_type: 'ENTITY_LOOKUP'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: false
  active: true
  picklist:
    internal_name: 'relatedToLookUp'
    display_name: 'Related To Look Up'
    picklist_values:
      lookup_1:
        internal_name: 'LEAD'
        display_name: 'LEAD'
      lookup_2:
        internal_name: 'DEAL'
        display_name: 'DEAL'
      lookup_3:
        internal_name: 'CONTACT'
        display_name: 'CONTACT'
      lookup_4:
        internal_name: 'COMPANY'
        display_name: 'COMPANY'
participants:
  internal_name: 'participants'
  display_name: 'Invitees'
  field_type: 'MEETING_INVITEES'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
organizer:
  internal_name: 'organizer'
  display_name: 'Organizer'
  field_type: 'MEETING_ORGANIZER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: false
  active: true
medium:
  internal_name: 'medium'
  display_name: 'Medium'
  field_type: 'ENTITY_PICKLIST'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: true
  active: true
  picklist:
    internal_name: 'medium_picklist'
    display_name: 'Medium Picklist'
    picklist_values:
      picklist_1:
        internal_name: 'OFFLINE'
        display_name: 'Offline'
      picklist_2:
        internal_name: 'GOOGLE'
        display_name: 'Google Meet'
      picklist_3:
        internal_name: 'MICROSOFT'
        display_name: 'Outlook Calendar'
providerLink:
  internal_name: 'providerLink'
  display_name: 'Provider Link'
  field_type: 'URL'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: false
  active: true
location:
  internal_name: 'location'
  display_name: 'Location'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: false
  active: true
description:
  internal_name: 'description'
  display_name: 'Description'
  field_type: 'RICH_TEXT'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: false
  active: true
createdBy:
  internal_name: 'createdBy'
  display_name: 'Created By'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
createdAt:
  internal_name: 'createdAt'
  display_name: 'Created At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: true
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
updatedBy:
  internal_name: 'updatedBy'
  display_name: 'Updated By'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
importedBy:
  internal_name: 'importedBy'
  display_name: 'Imported By'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
updatedAt:
  internal_name: 'updatedAt'
  display_name: 'Updated At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
conductedBy:
  internal_name: 'conductedBy'
  display_name: 'Conducted By'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
conductedAt:
  internal_name: 'conductedAt'
  display_name: 'Conducted At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
cancelledBy:
  internal_name: 'cancelledBy'
  display_name: 'Cancelled By'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
cancelledAt:
  internal_name: 'cancelledAt'
  display_name: 'Cancelled At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: true
  is_required: false
  active: true
owner:
  internal_name: 'owner'
  display_name: 'Owner'
  field_type: 'LOOK_UP'
  is_standard: true
  is_sortable: false
  is_filterable: true
  is_internal: false
  is_required: true
  active: true
checkedInAt:
  internal_name: 'checkedInAt'
  display_name: 'Checked In At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
checkedInLatitude:
  internal_name: 'checkedInLatitude'
  display_name: 'Checked In Latitude'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
checkedInLongitude:
  internal_name: 'checkedInLongitude'
  display_name: 'Checked In Longitude'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
checkedOutAt:
  internal_name: 'checkedOutAt'
  display_name: 'Checked Out At'
  field_type: 'DATETIME_PICKER'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
checkedOutLatitude:
  internal_name: 'checkedOutLatitude'
  display_name: 'Checked Out Latitude'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
checkedOutLongitude:
  internal_name: 'checkedOutLongitude'
  display_name: 'Checked Out Longitude'
  field_type: 'TEXT_FIELD'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: false
  active: true
tenant_id:
  internal_name: 'tenant_id'
  display_name: 'Tenant ID'
  field_type: 'NUMBER'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: true
  is_required: true
  active: true
locationCoordinate:
  internal_name: 'locationCoordinate'
  display_name: 'Location Coordinate'
  field_type: 'GPS_COORDINATES'
  is_standard: true
  is_sortable: false
  is_filterable: false
  is_internal: false
  is_required: false
  active: true
