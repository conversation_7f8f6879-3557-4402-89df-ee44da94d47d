{"layoutItems": [{"id": 1, "type": "SECTION", "row": 1, "column": 1, "width": 12, "item": {"heading": "Basic Info", "collapsible": false}, "layoutItems": [{"type": "FIELD", "item": {"type": "TEXT_FIELD", "displayName": "Title", "internalName": "title", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": true, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 1, "column": 1, "width": 12}, {"type": "FIELD", "item": {"type": "TOGGLE", "displayName": "All Day", "internalName": "allDay", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 2, "column": 1, "width": 6}, {"type": "FIELD", "layoutItems": [], "item": {"type": "DATETIME_PICKER", "displayName": "From", "internalName": "from", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": false, "unique": false, "internal": false, "readOnly": false}, "row": 3, "column": 1, "width": 6}, {"type": "FIELD", "layoutItems": [], "item": {"type": "DATETIME_PICKER", "displayName": "To", "internalName": "to", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": false, "unique": false, "internal": false, "readOnly": false}, "row": 3, "column": 2, "width": 6}, {"type": "FIELD", "item": {"type": "PICK_LIST", "displayName": "Timezone", "internalName": "timezone", "pickLists": [{"id": 317, "displayName": "(GMT-12:00) International Date Line West", "name": "Etc/GMT+12", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 318, "displayName": "(GMT-11:00) Midway Island, Samoa", "name": "Pacific/Midway", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 319, "displayName": "(GMT-10:00) Hawaii", "name": "Pacific/Honolulu", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 320, "displayName": "(GMT-09:00) Alaska", "name": "US/Alaska", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 321, "displayName": "(GMT-08:00) Pacific Time (US & Canada)", "name": "America/Los_Angeles", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 322, "displayName": "(GMT-08:00) Tijuana, Baja California", "name": "America/Tijuana", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 323, "displayName": "(GMT-07:00) Arizona", "name": "US/Arizona", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 324, "displayName": "(GMT-07:00) Chihuahua, La Paz, Mazatlan", "name": "America/Chihuahua", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 325, "displayName": "(GMT-07:00) Mountain Time (US & Canada)", "name": "US/Mountain", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 326, "displayName": "(GMT-06:00) Central America", "name": "America/Managua", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 327, "displayName": "(GMT-06:00) Central Time (US & Canada)", "name": "US/Central", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 328, "displayName": "(GMT-06:00) Guadalajara, Mexico City, Monterrey", "name": "America/Mexico_City", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 329, "displayName": "(GMT-06:00) Saskatchewan", "name": "Canada/Saskatchewan", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 330, "displayName": "(GMT-05:00) Bogota, Lima, Quito, Rio Branco", "name": "America/Bogota", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 331, "displayName": "(GMT-05:00) Eastern Time (US & Canada)", "name": "US/Eastern", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 332, "displayName": "(GMT-05:00) Indiana (East)", "name": "US/East-Indiana", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 333, "displayName": "(GMT-04:00) Atlantic Time (Canada)", "name": "Canada/Atlantic", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 334, "displayName": "(GMT-04:00) Caracas, La Paz", "name": "America/Caracas", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 335, "displayName": "(GMT-04:00) Manaus", "name": "America/Manaus", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 336, "displayName": "(GMT-04:00) Santiago", "name": "America/Santiago", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 337, "displayName": "(GMT-03:30) Newfoundland", "name": "Canada/Newfoundland", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 338, "displayName": "(GMT-03:00) Brasilia", "name": "America/Sao_Paulo", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 339, "displayName": "(GMT-03:00) Buenos Aires, Georgetown", "name": "America/Argentina/Buenos_Aires", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 340, "displayName": "(GMT-03:00) Greenland", "name": "America/Godthab", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 341, "displayName": "(GMT-03:00) Montevideo", "name": "America/Montevideo", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 342, "displayName": "(GMT-02:00) Mid-Atlantic", "name": "America/Noronha", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 343, "displayName": "(GMT-01:00) Cape Verde Is.", "name": "Atlantic/Cape_Verde", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 344, "displayName": "(GMT-01:00) Azores", "name": "Atlantic/Azores", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 345, "displayName": "(GMT+00:00) Casablanca, Monrovia, Reykjavik", "name": "Africa/Casablanca", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 346, "displayName": "(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London", "name": "Etc/Greenwich", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 347, "displayName": "(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna", "name": "Europe/Amsterdam", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 348, "displayName": "(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague", "name": "Europe/Belgrade", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 349, "displayName": "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris", "name": "Europe/Brussels", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 350, "displayName": "(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb", "name": "Europe/Sarajevo", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 351, "displayName": "(GMT+01:00) West Central Africa", "name": "Africa/Lagos", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 352, "displayName": "(GMT+02:00) Amman", "name": "Asia/Amman", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 353, "displayName": "(GMT+02:00) Athens, Bucharest, Istanbul", "name": "Europe/Athens", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 354, "displayName": "(GMT+02:00) Beirut", "name": "Asia/Beirut", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 355, "displayName": "(GMT+02:00) Cairo", "name": "Africa/Cairo", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 356, "displayName": "(GMT+02:00) Harare, Pretoria", "name": "Africa/Harare", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 357, "displayName": "(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius", "name": "Europe/Helsinki", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 358, "displayName": "(GMT+02:00) Jerusalem", "name": "Asia/Jerusalem", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 359, "displayName": "(GMT+02:00) Minsk", "name": "Europe/Minsk", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 360, "displayName": "(GMT+02:00) Windhoek", "name": "Africa/Windhoek", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 361, "displayName": "(GMT+03:00) Kuwait, Riyadh, Baghdad", "name": "Asia/Kuwait", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 362, "displayName": "(GMT+03:00) Moscow, St. Petersburg, Volgograd", "name": "Europe/Moscow", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 363, "displayName": "(GMT+03:00) Nairobi", "name": "Africa/Nairobi", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 364, "displayName": "(GMT+03:00) Tbilisi", "name": "Asia/Tbilisi", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 365, "displayName": "(GMT+03:30) Tehran", "name": "Asia/Tehran", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 366, "displayName": "(GMT+04:00) Abu Dhabi, Muscat", "name": "Asia/Muscat", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 367, "displayName": "(GMT+04:00) Baku", "name": "Asia/Baku", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 368, "displayName": "(GMT+04:00) Yerevan", "name": "Asia/Yerevan", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 369, "displayName": "(GMT+04:30) Kabul", "name": "Asia/Kabul", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 370, "displayName": "(GMT+05:00) Yekaterinburg", "name": "Asia/Yekaterinburg", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 371, "displayName": "(GMT+05:00) Islamabad, Karachi, Tashkent", "name": "Asia/Karachi", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 372, "displayName": "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura", "name": "Asia/Calcutta", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 373, "displayName": "(GMT+05:45) Kathmandu", "name": "Asia/Katmandu", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 374, "displayName": "(GMT+06:00) Almaty, Novosibirsk", "name": "Asia/Almaty", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 375, "displayName": "(GMT+06:00) Astana, Dhaka", "name": "Asia/Dhaka", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 376, "displayName": "(GMT+06:30) Yangon (Rangoon)", "name": "Asia/Rangoon", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 377, "displayName": "(GMT+07:00) Bangkok, Hanoi, Jakarta", "name": "Asia/Bangkok", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 378, "displayName": "(GMT+07:00) Krasnoyarsk", "name": "Asia/Krasnoyarsk", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 379, "displayName": "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi", "name": "Asia/Hong_Kong", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 380, "displayName": "(GMT+08:00) Kuala Lumpur, Singapore", "name": "Asia/Kuala_Lumpur", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 381, "displayName": "(GMT+08:00) Irkutsk, Ulaan <PERSON>aar", "name": "Asia/Irkutsk", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 382, "displayName": "(GMT+08:00) Perth", "name": "Australia/Perth", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 383, "displayName": "(GMT+08:00) Taipei", "name": "Asia/Taipei", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 384, "displayName": "(GMT+09:00) Osaka, Sapporo, Tokyo", "name": "Asia/Tokyo", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 385, "displayName": "(GMT+09:00) Seoul", "name": "Asia/Seoul", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 386, "displayName": "(GMT+09:00) Yakutsk", "name": "Asia/Yakutsk", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 387, "displayName": "(GMT+09:30) Adelaide", "name": "Australia/Adelaide", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 388, "displayName": "(GMT+09:30) Darwin", "name": "Australia/Darwin", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 389, "displayName": "(GMT+10:00) Brisbane", "name": "Australia/Brisbane", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 390, "displayName": "(GMT+10:00) Canberra, Melbourne, Sydney", "name": "Australia/Canberra", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 391, "displayName": "(GMT+10:00) Hobart", "name": "Australia/Hobart", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 392, "displayName": "(GMT+10:00) Guam, Port Moresby", "name": "Pacific/Guam", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 393, "displayName": "(GMT+10:00) Vladivostok", "name": "Asia/Vladivostok", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 394, "displayName": "(GMT+11:00) <PERSON><PERSON><PERSON>, Solomon Is., New Caledonia", "name": "Asia/Magadan", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 395, "displayName": "(GMT+12:00) Auckland, Wellington", "name": "Pacific/Auckland", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 396, "displayName": "(GMT+12:00) Fiji, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>.", "name": "Pacific/Fiji", "lookupUrl": null, "systemDefault": true, "disabled": false}, {"id": 397, "displayName": "(GMT+13:00) <PERSON><PERSON><PERSON>alofa", "name": "Pacific/Tongatapu", "lookupUrl": null, "systemDefault": true, "disabled": false}], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 4, "column": 1, "width": 6}, {"type": "FIELD", "item": {"type": "TEXT_FIELD", "displayName": "Status", "internalName": "status", "pickLists": [], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": true, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": false, "filterable": true, "readOnly": false}, "row": 4, "column": 2, "width": 6}, {"column": 1, "row": 5, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Owner", "filterable": true, "greaterThan": null, "internal": false, "internalName": "owner", "length": null, "lessThan": null, "lookupUrl": "/users/meeting-invitee/lookup?q=key:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}, {"type": "FIELD", "item": {"type": "MEETING_ORGANIZER", "displayName": "Organizer", "internalName": "organizer", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": "/search/meeting-invitee/lookup?q=key:", "regex": null, "required": true, "multiValue": false, "unique": true, "internal": false, "standard": true, "sortable": false, "filterable": true, "readOnly": false}, "row": 5, "column": 2, "width": 6}, {"type": "FIELD", "item": {"type": "MEETING_INVITEES", "displayName": "Invitees", "internalName": "participants", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": "/search/meeting-invitee/lookup?q=key:", "regex": null, "required": true, "multiValue": true, "unique": true, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 6, "column": 1, "width": 6}, {"type": "FIELD", "item": {"type": "ENTITY_LOOKUP", "entity": null, "displayName": "Related To", "internalName": "relatedTo", "pickLists": [{"id": 0, "displayName": "LEAD", "name": "LEAD", "lookupUrl": "/search/lead/lookup?q=firstName:", "systemDefault": false, "disabled": false}, {"id": 1, "displayName": "DEAL", "name": "DEAL", "lookupUrl": "/search/deal/lookup?q=name:", "systemDefault": false, "disabled": false}, {"id": 2, "displayName": "CONTACT", "name": "CONTACT", "lookupUrl": "/search/contact/lookup?q=firstName:", "systemDefault": false, "disabled": false}, {"id": 3, "displayName": "COMPANY", "name": "COMPANY", "lookupUrl": "/companies/lookup?view=meeting&q=comp:", "systemDefault": false, "disabled": false}], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "showDefaultOptions": true, "multiValue": false, "active": true, "required": false, "filterable": true, "internal": false, "important": true, "standard": true, "sortable": true, "unique": false, "readOnly": false}, "row": 7, "column": 1, "width": 6}, {"type": "FIELD", "item": {"type": "ENTITY_PICKLIST", "displayName": "Medium", "internalName": "medium", "pickLists": [{"id": 0, "displayName": "Offline", "name": "OFFLINE", "lookupUrl": null, "systemDefault": false, "disabled": false}, {"id": 1, "displayName": "Google Meet", "name": "GOOGLE", "lookupUrl": null, "systemDefault": false, "disabled": false}, {"id": 2, "displayName": "MS Teams", "name": "MICROSOFT", "lookupUrl": null, "systemDefault": false, "disabled": false}], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": true, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": false, "filterable": false, "readOnly": false}, "row": 8, "column": 1, "width": 4}, {"type": "FIELD", "item": {"type": "URL", "displayName": "Provider Link", "internalName": "providerLink", "pickLists": [], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": false, "filterable": false, "readOnly": true}, "row": 9, "column": 1, "width": 6}, {"type": "FIELD", "item": {"type": "TEXT_FIELD", "displayName": "Location", "internalName": "location", "pickLists": [], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 8, "column": 2, "width": 4}, {"type": "FIELD", "item": {"type": "RICH_TEXT", "displayName": "Description", "internalName": "description", "pickLists": [], "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": true, "filterable": true, "readOnly": false}, "row": 10, "column": 1, "width": 12}, {"type": "FIELD", "item": {"type": "GPS_COORDINATES", "displayName": "Location Coordinate", "internalName": "locationCoordinate", "pickLists": null, "description": null, "length": null, "greaterThan": null, "lessThan": null, "lookupUrl": null, "regex": null, "required": false, "multiValue": false, "unique": false, "internal": false, "standard": true, "sortable": false, "filterable": false, "readOnly": false}, "row": 11, "column": 1, "width": 6}]}, {"id": 2, "column": 1, "item": {"collapsible": false, "heading": "Other Details"}, "row": 2, "type": "SECTION", "width": 12, "layoutItems": []}, {"id": 3, "column": 1, "item": {"collapsible": false, "heading": "Internals"}, "row": 11, "type": "SECTION", "width": 12, "layoutItems": [{"column": 1, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Created By", "filterable": true, "greaterThan": null, "internal": true, "internalName": "created<PERSON>y", "length": null, "lessThan": null, "lookupUrl": "/users/lookup?q=firstName:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}, {"column": 2, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Created At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "createdAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": true, "standard": true, "type": "DATETIME_PICKER", "unique": true}, "layoutItems": []}, {"column": 3, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Updated By", "filterable": true, "greaterThan": null, "internal": true, "internalName": "updatedBy", "length": null, "lessThan": null, "lookupUrl": "/users/lookup?q=firstName:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}, {"column": 1, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Updated At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "updatedAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "DATETIME_PICKER", "unique": true}, "layoutItems": []}, {"column": 2, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Conducted By", "filterable": true, "greaterThan": null, "internal": true, "internalName": "conductedBy", "length": null, "lessThan": null, "lookupUrl": "/users/lookup?q=firstName:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}, {"column": 3, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Conducted At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "conductedAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "DATETIME_PICKER", "unique": true}, "layoutItems": []}, {"column": 1, "row": 3, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Cancelled By", "filterable": true, "greaterThan": null, "internal": true, "internalName": "cancelledBy", "length": null, "lessThan": null, "lookupUrl": "/users/lookup?q=firstName:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}, {"column": 2, "row": 3, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Cancelled At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "cancelledAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "DATETIME_PICKER", "unique": true}, "layoutItems": []}, {"column": 1, "row": 4, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Imported By", "filterable": true, "greaterThan": null, "internal": true, "internalName": "importedBy", "length": null, "lessThan": null, "lookupUrl": "/users/lookup?q=firstName:", "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": true, "sortable": false, "standard": true, "type": "LOOK_UP", "unique": false}, "layoutItems": []}]}, {"id": 4, "column": 1, "item": {"collapsible": false, "heading": "Check In & Check Out Internals"}, "row": 13, "type": "SECTION", "width": 12, "layoutItems": [{"column": 1, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked In At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "checkedInAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "DATETIME_PICKER", "unique": false}, "layoutItems": []}, {"column": 2, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked In Latitude", "filterable": false, "greaterThan": null, "internal": true, "internalName": "checkedInLatitude", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "TEXT_FIELD", "unique": true}, "layoutItems": []}, {"column": 3, "row": 1, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked In Longitude", "filterable": false, "greaterThan": null, "internal": true, "internalName": "checkedInLongitude", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "TEXT_FIELD", "unique": false}, "layoutItems": []}, {"column": 1, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked Out At", "filterable": true, "greaterThan": null, "internal": true, "internalName": "checkedOutAt", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "DATETIME_PICKER", "unique": false}, "layoutItems": []}, {"column": 2, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked Out Latitude", "filterable": false, "greaterThan": null, "internal": true, "internalName": "checkedOutLatitude", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "TEXT_FIELD", "unique": true}, "layoutItems": []}, {"column": 3, "row": 2, "type": "FIELD", "width": 4, "item": {"description": null, "displayName": "Checked Out Longitude", "filterable": false, "greaterThan": null, "internal": true, "internalName": "checkedOutLongitude", "length": null, "lessThan": null, "lookupUrl": null, "multiValue": false, "pickLists": null, "readOnly": false, "regex": null, "required": false, "sortable": false, "standard": true, "type": "TEXT_FIELD", "unique": false}, "layoutItems": []}]}]}