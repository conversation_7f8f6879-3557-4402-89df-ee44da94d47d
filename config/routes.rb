require 'sidekiq/web'
Sidekiq::Web.use ActionDispatch::Cookies
Sidekiq::Web.use ActionDispatch::Session::CookieStore, key: "_interslice_session"

Rails.application.routes.draw do
  mount Rswag::Api::Engine => '/v2'
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html

  mount Sidekiq::Web, at: "/sidekiq"

  get "/v48348807127D18DF/meetings/health", to: "health#status"

  get 'health', to: "meetings#health"
  scope :v1 do
    resources :meetings, except: [:index] do
      collection do
        get 'external-lookup', to: 'external_lookup'
        get :lookup
        resources :webhooks, only: [] do
          collection do
            post :google
            post :microsoft
          end
        end
        resources :fields, only: [:create, :index, :show] do
          member do
            put :update
            put :activate
            put :deactivate
          end
        end
        post 'fields/create_fields', to: 'fields#create_fields'
        post 'fields/update_fields', to: 'fields#update_fields'
        resources :layout, only: [:index] do
          collection do
            get :list, to: 'list'
          end
        end
        post :search
        delete 'delete' => 'meetings#bulk_delete'
        post "notes/search", to: "notes#search"
        post 'import', to: 'meetings#import'

        resources :picklist, only: [] do
          resources :picklist_value, path: 'picklist-value', only: [:destroy] do
            member do
              put :update
              put :enable
              put :disable
            end
          end
        end

        post :share, to: 'share_rules#share_all'
        put 'share/:share_rule_id', to: 'share_rules#update', defaults: { id: nil }
        resources :share_rules, only: [:show, :destroy], path: 'share-rules' do
          collection do
            post :search
          end
        end
        post '/bulk-checkout', to: 'meetings#bulk_checkout'

        post :access
        get :access, to: 'meetings#access_related_to_entity'
      end

      member do
        post :cancel
        post :conduct
        post :rsvp
        post :p_rsvp
        post :checkin
        post :checkout
        post :share, to: 'share_rules#share'
        put 'share/:share_rule_id', to: 'share_rules#update'
      end

      resources :notes, only: [:create, :index, :destroy]
    end
  end

  namespace :v2 do
    resources :meetings, only: [] do
      collection do
        resources :notes, only: [] do
          post :search, on: :collection
        end
      end
    end
  end
end
