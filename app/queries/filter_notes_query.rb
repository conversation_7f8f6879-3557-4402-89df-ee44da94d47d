# frozen_string_literal: true

class FilterNotesQuery
  def initialize(tenant_id, user_id, filter_params, note_read_all)
    @tenant_id = tenant_id
    @user_id = user_id
    @filter_params = filter_params
    @note_read_all = note_read_all
  end

  def call
    scoped = default_scope_for_notes
    scoped = filter_note_rules(scoped, @filter_params[:jsonRule])
    scoped = sort(scoped, @filter_params[:sort])
    scoped = paginate(scoped, @filter_params[:page], @filter_params[:size])
    scoped
  end

  private

  def default_scope_for_notes
    if @note_read_all
      Note.where(tenant_id: @tenant_id)
    else
      Note.where(tenant_id: @tenant_id, created_by_id: @user_id)
    end
  end

  def filter_note_rules(note_rules, filter_rules)
    if filter_rules.present? && filter_rules[:rules].present?
      rules = filter_rules[:rules].map { |rule| JsonRule.new(rule.to_h) }
      rules = validate_and_modify_rules(rules)
      note_rules = BuildQueryForNotes.new(note_rules, rules).call
    end

    note_rules
  end

  def validate_and_modify_rules(rules)
    validate_rules(rules)
    rules
  end

  def validate_rules(rules)
    rules.each do |rule|
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rule')}") unless rule.valid?
    end
  end

  def sort(note_rules, sort_params)
    field, order = sort_params.to_s.split(',')

    field ||= 'createdAt'
    order ||= 'desc'

    if %w[createdAt updatedAt].exclude?(field) || %w[asc desc].exclude?(order)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.sortable_field_or_order')}")
    end

    note_rules.order("#{field.underscore} #{order}")
  end

  def paginate(note_rules, page, size)
    page ||= 1
    size ||= 10
    note_rules.page(page.to_i).per_page(size.to_i)
  end
end
