class FilterMeetingsQuery
  prepend SimpleCommand

  def initialize(tenant_id, user_id, filter_params, filter_options = {})
    @tenant_id = tenant_id
    @user_id = user_id
    @filter_params = filter_params
    @filter_options = filter_options.with_indifferent_access
    @filter_options[:view] = @filter_options[:view] || DEFAULT_VIEW
    @join_index_count = 1
  end

  def call
    load_fields
    scoped = meetings_for_tenant(@tenant_id)
    scoped = selected_fields_in_query(scoped)
    scoped = filter_meetings(scoped, @filter_params[:jsonRule])
    scoped = scoped.distinct
    scoped = sort(scoped, @filter_params[:sort]) unless @filter_options[:skip_sorting]
    scoped = paginate(scoped, @filter_params[:page], @filter_params[:size]) unless @filter_options[:skip_pagination]
    scoped
  end

  private

  def load_fields
    fields = Field.where(tenant_id: @tenant_id, active: true)
    @sortable_fields = fields.select { |field| field.is_sortable? }.map(&:internal_name)
    @filterable_fields_and_type = fields.select { |field| field.is_filterable? }
                                        .inject({}) { |hash, field| hash.merge(field.internal_name => field.field_type) }
                                        .with_indifferent_access
    @custom_fields = fields.reject { |field| field.is_standard? }.map(&:internal_name)
  end

  def meetings_for_tenant(tenant_id)
    Meeting.where(tenant_id: tenant_id)
  end

  def only_id_and_title_fields_selected?
    @filter_params[:fields] == %w[id title]
  end

  def get_selected_fields
    case @filter_params[:fields]
    when %w[id title]
      return [:id, :title]
    when %w[id]
      return [:id]
    end
  end

  def selected_fields_in_query(scoped)
    # TODO Add support for other fields when required
    selected_fields = get_selected_fields
    scoped = scoped.select(selected_fields) if selected_fields.present?

    scoped
  end

  #
  # Default View
  # 1. When user has read all, load all meetings
  # 2. When user has read, load owned by, created by, organizer, participant, meetings shared, parent entity shared meetings
  #
  # Share View
  # 1. When user has read all and (update all or reshare), load all meetings
  # 2. When user has read all but not update all or reshare, load only owned by, created by and organizer meetings
  # 3. When user has read and reshare load same as Default View (2)
  # 4. When user has read and not reshare load same as Share View (2)
  #
  def filter_meetings(tenant_scoped, json_rule)
    rules = []
    rules = json_rule[:rules] if json_rule.present? && json_rule[:rules].present?

    if @filter_options.dig(:permissions, :read_all)
      if @filter_options[:view] == SHARE_VIEW && !@filter_options.dig(:permissions, :update_all) && !@filter_options.dig(:permissions, :reshare)
        owner_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'long', value: @user_id, field: 'owner' })]), @tenant_id, @user_id, false, @join_index_count).result

        created_by_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'long', value: @user_id, field: 'createdBy' })]), @tenant_id, @user_id, false, @join_index_count).result

        organizer_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'organizer_lookup', value: { entity: 'user', id: @user_id }, field: 'organizer' })]), @tenant_id, @user_id, false, @join_index_count).result

        scope_after_default_filters = organizer_scope.or(owner_scope).or(created_by_scope)
      else
        scope_after_default_filters = tenant_scoped
      end
    else
      if @filter_options[:view] == SHARE_VIEW && !@filter_options.dig(:permissions, :reshare)
        owner_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'long', value: @user_id, field: 'owner' })]), @tenant_id, @user_id, false, @join_index_count).result

        created_by_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'long', value: @user_id, field: 'createdBy' })]), @tenant_id, @user_id, false, @join_index_count).result

        organizer_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'organizer_lookup', value: { entity: 'user', id: @user_id }, field: 'organizer' })]), @tenant_id, @user_id, false, @join_index_count).result

        scope_after_default_filters = organizer_scope.or(owner_scope).or(created_by_scope)
      else
        shared_meeting_ids, shared_from_users = fetch_shared_meeting_ids_and_user_ids

        if shared_meeting_ids.present?
          share_rule_scope_applied = true
          share_rule_scoped, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'in', type: 'long', value: shared_meeting_ids, field: 'id' })]), @tenant_id, @user_id, false, @join_index_count).result
        end

        if shared_from_users.present?
          collective_user_ids = (shared_from_users + [@user_id]).uniq
          owner_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'in', type: 'long', value: collective_user_ids, field: 'owner' })]), @tenant_id, @user_id, false, @join_index_count).result

          participant_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'in', type: 'associated_lookup', value: collective_user_ids.map { |uid| { entity: 'user', id: uid } }, field: 'associated_to' })]), @tenant_id, @user_id, true, @join_index_count).result
        else
          owner_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'long', value: @user_id, field: 'owner' })]), @tenant_id, @user_id, false, @join_index_count).result

          participant_scope, @join_index_count = BuildQueryFromJsonRule.call(tenant_scoped, validate_and_modify_rules([JsonRule.new({ operator: 'equal', type: 'associated_lookup', value: { entity: 'user', id: @user_id }, field: 'associated_to' })]), @tenant_id, @user_id, true, @join_index_count).result
        end

        @join_index_count -= 1
        entities_shared_data = parent_entities_shared_data
        associated_entity_values =
          [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].inject([]) do |arr, entity_type|
            if entities_shared_data.dig(entity_type, :shared_entity_ids).present?
              arr += entities_shared_data[entity_type][:shared_entity_ids].map { |entity_id| { 'entity' => entity_type, 'id' => entity_id } }
            end

            arr
          end
        associated_entity_owners_conditions =
          [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].map do |entity_type|
            if entities_shared_data.dig(entity_type, :shared_entity_owners).present?
              where_condition = "(lu#{@join_index_count}.entity LIKE '#{entity_type}%' AND lu#{@join_index_count}.owner_id IN (#{entities_shared_data[entity_type][:shared_entity_owners].join(',')}))"
            end
            where_condition
          end.compact
        associated_entity_owners_conditions << "(lu#{@join_index_count}.owner_id = #{@user_id})"

        if associated_entity_values.present?
          participant_scope = participant_scope.or(tenant_scoped.where("lu#{@join_index_count}.entity IN ('#{associated_entity_values.map{ |value| entity_lookup(value) }.join("','")}')"))
        end

        participant_scope = participant_scope.or(tenant_scoped.where(associated_entity_owners_conditions.join(" OR ")))
        @join_index_count += 1

        scope_after_default_filters = share_rule_scope_applied ? participant_scope.or(owner_scope).or(share_rule_scoped) : participant_scope.or(owner_scope)
      end
    end

    if @filter_options[:view] == CHECKIN_VIEW
      scope_after_default_filters = scope_after_default_filters.left_joins(:meeting_attendances)
      scope_for_meetings_not_checked_in_by_user = scope_after_default_filters
        .where(MeetingAttendance.arel_table[:user_id].not_eq(@user_id))
        .or(scope_after_default_filters.where(MeetingAttendance.arel_table[:user_id].eq(nil)))

      scope_for_meetings_previously_checked_out_by_user = scope_after_default_filters
        .where(MeetingAttendance.arel_table[:user_id].eq(@user_id))
        .where(MeetingAttendance.arel_table[:checked_out_at].not_eq(nil))

      scope_after_default_filters = scope_for_meetings_not_checked_in_by_user.or(scope_for_meetings_previously_checked_out_by_user)
    end

    rules = rules.map { |rule| JsonRule.new(rule.to_h) }
    rules = validate_and_modify_rules(rules)
    command = BuildQueryFromJsonRule.call(scope_after_default_filters, rules, @tenant_id, @user_id, false, @join_index_count)
    command.result.first
  end

  def validate_and_modify_rules(rules)
    rules.each do |rule|
      case rule.field
      when 'associated_leads', 'associated_contacts', 'associated_deals', 'associated_companies'
        entity_type = rule.field.gsub('associated_', '').singularize.downcase
        rule.field = 'associated_to'
        rule.type = 'associated_lookup'

        case rule.operator
        when 'equal', 'not_equal'
          rule.value = { 'entity' => entity_type, 'id' => rule.value }
        when'in', 'not_in'
          rule.value = rule.value.map { |value| { 'entity' => entity_type, 'id' => value } }
        when 'is_not_null', 'is_null'
          rule.value = entity_type
        end

      when 'created_by_fields', 'updated_by_fields', 'conducted_by_fields', 'cancelled_by_fields', 'organizer_fields', 'participant_fields', 'owner_fields', 'checked_in_out_by_fields'
        user_rule_filter_ids = FetchUserIdsByProperty.call(rule).result
        rule.operator = 'in'
        rule.value = user_rule_filter_ids.join(',')

        case rule.field
        when 'created_by_fields'
          rule.field = 'created_by'

        when 'updated_by_fields'
          rule.field = 'updated_by'

        when 'conducted_by_fields'
          rule.field = 'conducted_by'

        when 'cancelled_by_fields'
          rule.field = 'cancelled_by'

        when 'owner_fields'
          rule.field = 'owner'

        when 'organizer_fields'
          entity_type = LOOKUP_USER
          rule.field = 'organizer'
          rule.type = 'organizer_lookup'
          if user_rule_filter_ids.any?
            rule.value = user_rule_filter_ids.map { |value| { 'entity' => entity_type, 'id' => value } }
          else
            rule.operator = 'in'
            rule.value = [{ 'entity' => LOOKUP_USER, 'id' => 0 }]
          end

        when 'participant_fields'
          entity_type = LOOKUP_USER
          rule.field = 'participants'
          rule.type = 'participants_lookup'
          if user_rule_filter_ids.any?
            rule.value = user_rule_filter_ids.map { |value| { 'entity' => entity_type, 'id' => value } }
          else
            rule.operator = 'in'
            rule.value = [{ 'entity' => LOOKUP_USER, 'id' => 0 }]
          end

        when 'checked_in_out_by_fields'
          entity_type = LOOKUP_USER
          rule.field = 'user_id'
          rule.type = 'meeting_attendance'
          if user_rule_filter_ids.any?
            rule.value = user_rule_filter_ids
          else
            rule.operator = 'in'
            rule.value = [0]
          end
        end

      when 'checked_in_at', 'checked_out_at', 'checked_in_out_by'
        rule.type = 'meeting_attendance'
        rule.field = 'user_id' if rule.field == 'checked_in_out_by'
      end

      if rule.is_custom_field
        if @filterable_fields_and_type[rule.field] == 'CHECKBOX'
          rule.type = 'boolean'
        elsif @filterable_fields_and_type[rule.field] == 'PICK_LIST'
          rule.type = 'picklist'
        end
      end
    end
    validate_rules(rules)

    rules
  end

  def fetch_shared_meeting_ids_and_user_ids
    share_rule_scope = ShareRule.for_user(@tenant_id, @user_id)

    [
      share_rule_scope.reject { |rule| rule.share_all_records }.pluck(:meeting_id),
      share_rule_scope.select { |rule| rule.share_all_records }.pluck(:from_id)
    ]
  end

  def sort(meetings, sort)
    if sort
      col, order = sort&.split(',')
      underscored_col = col&.underscore if @custom_fields.exclude?(col)
    else
      underscored_col = col = 'from'
      order = 'asc'
    end

    unless @sortable_fields.include?(col) && ['asc', 'desc'].include?(order)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.sortable_field_or_order')}")
    end

    if @custom_fields.include?(col)
      meetings = meetings.select("#{only_id_and_title_fields_selected? ? 'meetings.id, meetings.title' : 'meetings.*'}, (meetings.custom_field_values -> '#{col}')").order(Arel.sql("meetings.custom_field_values -> '#{col}' #{order}"))
    elsif ['checkedInAt', 'checkedOutAt'].include?(col)
      meetings = meetings
        .left_joins(:meeting_attendances)
        .select((only_id_and_title_fields_selected? ? "meetings.id, meetings.title, meeting_attendances.#{underscored_col}" : "meetings.*, meeting_attendances.#{underscored_col}"))
        .order("meeting_attendances.#{underscored_col} #{order}")
    else
      meetings = meetings.select((only_id_and_title_fields_selected? ? "meetings.#{underscored_col}" : 'meetings.*')).order("meetings.#{underscored_col} #{order}")
    end

    meetings
  end

  def paginate(meetings, page, size)
    page ||= 1
    size ||= 10
    meetings.page(page.to_i).per_page(size.to_i)
  end

  def validate_rules(rules)
    filterable_custom_fields = @filterable_fields_and_type.keys & @custom_fields

    unless rules.select(&:is_custom_field).map(&:field).all? { |field| filterable_custom_fields.include?(field) }
      Rails.logger.error "FilterMeetingsQuery Unfilterable custom field is applied on meeting filters"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.non_filterable_field')}")
    end
    rules.each do |rule|
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rule')}") unless rule.valid?
    end
  end

  def parent_entities_shared_data
    [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].inject({}.with_indifferent_access) do |hash, entity_type|
      shared_response = ShareRules::ParentEntityAccess.new(entity_type).fetch
      hash[entity_type] = {
        shared_entity_owners: shared_response['accessByOwners'].keys.map(&:to_i),
        shared_entity_ids: shared_response['accessByRecords'].keys.map(&:to_i)
      }

      hash
    end
  end

  def entity_lookup(value)
    "#{value['entity']}_#{value['id']}"
  end
end
