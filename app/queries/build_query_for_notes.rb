# frozen_string_literal: true

class BuildQueryForNotes

  def initialize(scope, rules)
    @scope = scope
    @rules = rules
  end

  def call
    @rules.each { |rule| build_query(rule) }
    @scope
  end

  private

  def build_query(rule)
    send("has_#{rule.type}_#{rule.operator}", rule.field, rule.value)
  end

  def has_date_greater(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.gt(value))
  end

  def has_date_less(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.lt(value))
  end

  def has_date_less_or_equal(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.lteq(value))
  end

  def has_date_greater_or_equal(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value))
  end

  def has_date_between(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.between(value.first..value.last))
  end

  def has_date_not_between(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.not_between(value.first..value.last))
  end

  def has_date_is_null(field, value)
    field = Note.arel_table[field.to_sym]
    @scope = @scope.where(field.eq(nil))
  end

  def has_date_is_not_null(field, value)
    @scope = @scope.where.not({field => nil})
  end

  def has_long_equal(field, value)
    @scope = @scope.where({field => value})
  end

  def has_long_not_equal(field, value)
    @scope = @scope.where.not({field => value})
  end

  def has_long_is_null(field, value)
    @scope = @scope.where({field => nil})
  end

  def has_long_is_not_null(field, value)
    @scope = @scope.where.not({field => nil})
  end

  def has_long_in(field, value)
    values = value.split(',').flatten
    @scope = @scope.where({field => values})
  end

  def has_long_not_in(field, value)
    values = value.split(',').flatten
    @scope = @scope.where.not({field => values})
  end
end
