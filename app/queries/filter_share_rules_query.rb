# frozen_string_literal: true

class FilterShareRulesQuery
  def initialize(tenant_id, user_id, filter_params, share_rule_read_all)
    @tenant_id = tenant_id
    @user_id = user_id
    @filter_params = filter_params
    @share_rule_read_all = share_rule_read_all
  end

  def call
    scoped = default_share_rules_for_user
    scoped = filter_share_rules(scoped, @filter_params[:jsonRule])
    scoped = sort(scoped, @filter_params[:sort])
    scoped = paginate(scoped, @filter_params[:page], @filter_params[:size])
    scoped
  end

  private

  def default_share_rules_for_user
    if @share_rule_read_all
      ShareRule.where(tenant_id: @tenant_id)
    else
      ShareRule.where(tenant_id: @tenant_id, created_by_id: @user_id)
    end
  end

  def filter_share_rules(share_rules, filter_rules)
    if filter_rules.present? && filter_rules[:rules].present?
      rules = filter_rules[:rules].map { |rule| JsonRule.new(rule.to_h.merge(meeting_or_share_rule_model: 'ShareRule')) }
      rules = validate_and_modify_rules(rules)
      share_rules = BuildQueryForShareRules.new(share_rules, rules).call
    end

    share_rules
  end

  def validate_and_modify_rules(rules)
    rules.each do |rule|
      case rule.field
      when 'owner', 'created_by'
        rule.field = 'created_by_id'
      when 'updated_by'
        rule.field = 'updated_by_id'
      when 'entity'
        rule.field = 'meeting_id'
      when 'from'
        rule.field = 'from_id'
      when 'to'
        rule.field = 'to_id'
      end
    end

    validate_rules(rules)
    rules
  end

  def validate_rules(rules)
    rules.each do |rule|
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rule')}") unless rule.valid?
    end
  end

  def sort(share_rules, sort_params)
    field, order = sort_params.to_s.split(',')

    field ||= 'updatedAt'
    order ||= 'desc'

    if %w[createdAt updatedAt name systemDefault].exclude?(field) || %w[asc desc].exclude?(order)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.sortable_field_or_order')}")
    end

    share_rules.order("#{field.underscore} #{order}")
  end

  def paginate(share_rules, page, size)
    page ||= 1
    size ||= 10
    share_rules.page(page.to_i).per_page(size.to_i)
  end
end
