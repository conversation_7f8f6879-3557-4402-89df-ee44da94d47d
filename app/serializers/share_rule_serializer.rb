# frozen_string_literal: true

class ShareRuleSerializer
  def initialize(share_rule, payload_for_event: false)
    @share_rule = share_rule
    @payload_for_event = payload_for_event
  end

  def call
    return nil unless @share_rule

    unless @payload_for_event
      share_rule_permissions = Thread.current[:auth].permissions.find { |permission| permission.name == 'shareRule' }.action
      current_user_id = Thread.current[:auth].user_id
      share_rule_permissions.read = share_rule_permissions.read_all || (@share_rule.created_by_id == current_user_id && share_rule_permissions.read )
      share_rule_permissions.update = share_rule_permissions.update_all || (@share_rule.created_by_id == current_user_id && share_rule_permissions.update)
      share_rule_permissions.delete = share_rule_permissions.delete_all || (@share_rule.created_by_id == current_user_id && share_rule_permissions.delete)
    end

    json = Jbuilder.new
    json.body do
      json.createdAt @share_rule.created_at.iso8601(6)
      json.updatedAt @share_rule.updated_at.iso8601(6)
      json.createdBy UserSerializer.call(@share_rule.created_by).result
      json.updatedBy UserSerializer.call(@share_rule.updated_by).result
      json.owner UserSerializer.call(@share_rule.created_by).result
      json.recordActions share_rule_permissions.as_json unless @payload_for_event
      json.id @share_rule.id
      json.name @share_rule.name
      json.description @share_rule.description
      json.from do
        json.type @share_rule.from_type
        json.id @share_rule.from_id
        json.name share_rule_participants(@share_rule.from_id, @share_rule.from_type)&.name
      end
      json.to do
        json.type @share_rule.to_type
        json.id @share_rule.to_id
        json.name share_rule_participants(@share_rule.to_id, @share_rule.to_type)&.name
      end
      json.shareAllRecords @share_rule.share_all_records
      if @payload_for_event
        json.entityType 'MEETING'
        json.entityId @share_rule.meeting_id
      else
        if @share_rule.meeting_id.present?
          json.entity do
            json.id @share_rule.meeting_id
            json.name Meeting.find_by(id: @share_rule.meeting_id)&.title
          end
        else
          json.entity nil
        end
      end
      json.childEntities nil
      json.entityShareRuleId nil
      json.systemDefault @share_rule.system_default

      json.actions do
        json.read @share_rule.actions['read']
        json.update false
        json.delete false
        json.email false
        json.call false
        json.sms false
        json.task false
        json.note false
        json.meeting false
        json.document false
        json.deleteAll false
        json.quotation false
        json.reshare false
        json.reassign false
      end
    end
  end

  private

  def share_rule_participants(id, type)
    case type
    when USER
      User.select(:name).find_by(id: id, tenant_id: @share_rule.tenant_id)
    when TEAM
      Team.select(:name).find_by(id: id, tenant_id: @share_rule.tenant_id)
    end
  end
end
