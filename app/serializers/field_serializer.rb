class FieldSerializer
  prepend SimpleCommand

  def initialize(field)
    @field = field
  end

  def call
    return nil unless @field

    JSON(
      Jbuilder.new do |f|
        f.field do |field|
          field.id @field.id
          field.name @field.internal_name
          field.displayName @field.display_name
          field.type @field.field_type
          field.description @field.description
          field.standard @field.is_standard
          field.sortable @field.is_sortable
          field.filterable @field.is_filterable
          field.internal @field.is_internal
          field.required @field.is_required
          field.active @field.active
          field.picklist PicklistSerializer.call(@field.picklist, @field.system_default?).result
          field.createdAt @field.created_at
          field.updatedAt @field.updated_at
        end
        if @field.is_standard? && @field.is_required?
          f.fieldConfig do |field|
            field.name @field.internal_name
            field.standard @field.is_standard
            field.required @field.is_required
          end
        else
          f.fieldConfig nil
        end
      end
      .target!
    )
  end
end
