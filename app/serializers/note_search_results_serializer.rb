class NoteSearchResultsSerializer
  prepend SimpleCommand

  def initialize(notes, with_relations = false)
    @notes = notes
    @with_relations = with_relations
  end

  def call
    return nil unless @notes
    json = Jbuilder.new
    json.body do
      json.content (
        @notes.map do |n|
          result = NoteSerializer.call(n).result
          result = result.merge(meetingId: n.meeting_id)

          if @with_relations
            result = result.merge(relations: [{ entityId: n.meeting_id, entityType: MEETING }])
          end

          result
        end
      )
      json.page do
        json.no @notes.current_page.to_i
        json.size @notes.per_page
      end
      json.totalElements @notes.total_entries
      json.totalPages @notes.total_pages
      json.first @notes.previous_page.nil?
      json.last @notes.next_page.nil?
    end
  end
end