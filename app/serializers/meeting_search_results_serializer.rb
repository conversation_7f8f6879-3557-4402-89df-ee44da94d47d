class MeetingSearchResultsSerializer
  prepend SimpleCommand

  def initialize(meetings, selected_fields: [])
    @meetings = meetings
    @selected_fields = selected_fields
  end

  def call
    return nil unless @meetings

    json = Jbuilder.new
    json.body do
      json.content (
        @meetings.map do |m|
          if only_id_and_title_fields_selected?
            JSON(
              Jbuilder.new do |meeting|
                meeting.id m.id
                meeting.title m.title
              end
              .target!
            )
          else
            MeetingSerializer.call(m).result
          end
        end
      )

      json.page do
        json.no @meetings.current_page.to_i
        json.size @meetings.per_page
      end
      json.sort {}
      json.totalElements @meetings.total_entries
      json.totalPages @meetings.total_pages
      json.first @meetings.previous_page.nil?
      json.last @meetings.next_page.nil?
    end
  end

  private

  def only_id_and_title_fields_selected?
    @selected_fields == %w[id title]
  end
end
