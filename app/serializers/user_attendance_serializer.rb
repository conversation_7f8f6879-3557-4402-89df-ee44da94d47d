# frozen_string_literal: true

class UserAttendanceSerializer
  prepend <PERSON><PERSON><PERSON>mand

  def initialize(user_attendance)
    @user_attendance = user_attendance
  end

  def call
    JSON(
      Jbuilder.new do |attendance|
        attendance.user UserSerializer.call(@user_attendance.user).result
        attendance.checkedInAt @user_attendance.checked_in_at
        attendance.isCheckedInOutsideGeofence @user_attendance.is_checked_in_outside_geofence
        attendance.checkedInLatitude @user_attendance.checked_in_latitude.present? ? @user_attendance.checked_in_latitude.to_f : nil
        attendance.checkedInLongitude @user_attendance.checked_in_longitude.present? ? @user_attendance.checked_in_longitude.to_f : nil
        attendance.checkedOutAt @user_attendance.checked_out_at
        attendance.isCheckedOutOutsideGeofence @user_attendance.is_checked_out_outside_geofence
        attendance.checkedOutLatitude @user_attendance.checked_out_latitude.present? ? @user_attendance.checked_out_latitude.to_f : nil
        attendance.checkedOutLongitude @user_attendance.checked_out_longitude.present? ? @user_attendance.checked_out_longitude.to_f : nil
      end.target!
    )
  end
end
