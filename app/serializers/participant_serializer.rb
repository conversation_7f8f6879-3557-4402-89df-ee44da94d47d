class ParticipantSerializer
  prepend SimpleCommand

  def initialize(participant, meeting, look_up_owner = false)
    @participant = participant
    @meeting = meeting
    @look_up_owner = look_up_owner
  end

  def call
    return nil unless @participant && @meeting
    meeting_look_up = @participant.meeting_look_ups.where(meeting: @meeting).first
    JSON(
      Jbuilder.new do |participant|
        participant.id @participant.entity_id
        participant.entity @participant.entity_type
        participant.name @participant.name&.strip
        participant.ownerId @participant.owner_id if @look_up_owner
        if [LOOKUP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_EXTERNAL].include?(@participant.entity_type)
          participant.email @participant.email
          participant.rsvpResponse meeting_look_up.rsvp_response
          participant.rsvpMessage meeting_look_up.rsvp_message
        end
      end
      .target!
      )
  end
end
