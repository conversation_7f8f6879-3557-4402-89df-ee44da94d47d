# frozen_string_literal: true

module V2
  class FieldSerializer
    prepend SimpleCommand

    def initialize(field)
      @field = field
    end

    def call
      return nil unless @field

      JSON(
        Jbuilder.new do |json|
          json.id @field.id
          json.name @field.internal_name
          json.displayName @field.display_name
          json.fieldType @field.field_type
          json.description @field.description
          json.standard @field.is_standard
          json.sortable @field.is_sortable
          json.filterable @field.is_filterable
          json.internal @field.is_internal
          json.required @field.is_required
          json.active @field.active
          json.tenantId @field.tenant_id
          json.picklistValues @field.picklist.present? ? V2::PicklistValuesSerializer.call(@field.picklist.picklist_values).result : nil
          json.createdAt @field.created_at.iso8601(6)
          json.updatedAt @field.updated_at.iso8601(6)
          json.createdBy UserSerializer.call(@field.created_by, false).result
          json.updatedBy UserSerializer.call(@field.updated_by, false).result
        end.target!
      )
    end
  end
end
