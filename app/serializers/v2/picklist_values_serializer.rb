# frozen_string_literal: true

module V2
  class PicklistValuesSerializer
    prepend SimpleCommand

    def initialize(values)
      @values = values
    end

    def call
      return nil if @values.blank?

      json = Jbuilder.new
      json.array! @values do |value|
        json.id value.id
        json.name value.internal_name
        json.displayName value.display_name
        json.disabled value.disabled
        json.createdAt value.created_at.iso8601(6)
        json.updatedAt value.updated_at.iso8601(6)
      end
    end
  end
end
