# frozen_string_literal: true

class ShareRulesSerializer
  prepend SimpleCommand

  def initialize(share_rules)
    @share_rules = share_rules
  end

  def call
    json = Jbuilder.new
    json.body do
      json.content (
        @share_rules.map do |share_rule|
          ShareRuleSerializer.new(share_rule).call
        end
      )

      json.number @share_rules.current_page.to_i
      json.size @share_rules.per_page
      json.sort {}
      json.totalElements @share_rules.total_entries
      json.totalPages @share_rules.total_pages
      json.first @share_rules.previous_page.nil?
      json.last @share_rules.next_page.nil?
    end
  end
end
