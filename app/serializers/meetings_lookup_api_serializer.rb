# frozen_string_literal: true

class MeetingsLookupApiSerializer
  prepend <PERSON><PERSON><PERSON>mand

  def initialize(meetings, view = nil)
    @meetings = meetings
    @view = view
    @user_id = GetSecurityContext.call.result.user_id
  end

  def call
    return nil unless @meetings

    geofence_config = nil

    if @view == CHECKIN_VIEW && @user_id
      geofence_config = User.find_by(id: @user_id)&.get_geofence_config
    end

    json = Jbuilder.new
    json.body do
      json.content (
        @meetings.map do |m|
          JSON(
            Jbuilder.new do |meeting_json|
              meeting_json.id m.id
              meeting_json.name m.title
              meeting_json.geofenceConfig geofence_config if @view == CHECKIN_VIEW
              meeting_json.locationCoordinate do
                meeting_json.lat m.location_latitude.to_f
                meeting_json.lon m.location_longitude.to_f
              end if @view == CHECKIN_VIEW && m.location_latitude.present? && m.location_longitude.present? 
            end
            .target!
          )
        end
      )

      json.page do
        json.no @meetings.current_page.to_i
        json.size @meetings.per_page
      end
      json.sort {}
      json.totalElements @meetings.total_entries
      json.totalPages @meetings.total_pages
      json.first @meetings.previous_page.nil?
      json.last @meetings.next_page.nil?
    end
  end
end
