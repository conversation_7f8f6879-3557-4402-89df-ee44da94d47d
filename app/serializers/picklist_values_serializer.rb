class PicklistValuesSerializer
  prepend SimpleCommand

  def initialize(values, system_default = false)
    @values = if values.is_a?(ActiveRecord::Relation)
      values.order(created_at: :asc)
    elsif values.respond_to?(:sort_by) && values.first.respond_to?(:created_at)
      values.sort_by(&:created_at)
    else
      values
    end
    @system_default = system_default
  end

  def call
    return nil if @values.blank?
    json = Jbuilder.new
    json.array! @values do |value|
      json.id value.id
      json.name value.internal_name
      json.displayName value.display_name
      json.systemDefault @system_default
      json.disabled value.disabled
    end
  end
end
