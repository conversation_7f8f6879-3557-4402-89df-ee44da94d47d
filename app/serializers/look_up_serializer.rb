class LookUpSerializer
  prepend SimpleCommand

  def initialize(look_up, look_up_owner = false)
    @look_up = look_up
    @look_up_owner = look_up_owner
  end

  def call
    return nil unless @look_up

    JSON(
      Jbuilder.new do |look_up|
      look_up.id @look_up.entity_id
      look_up.entity @look_up.entity_type
      look_up.name @look_up.name&.strip
      look_up.email @look_up.email if [LOOKUP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_EXTERNAL].include?(@look_up.entity_type)
      look_up.ownerId @look_up.owner_id if @look_up_owner
    end
    .target!
    )
  end
end
