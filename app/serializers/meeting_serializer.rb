class MeetingSerializer
  prepend SimpleCommand

  def initialize(meeting, external_participant = nil, record_actions = true, deleted_by = nil, look_up_owner = false, include_geofence_config = false)
    @meeting = meeting
    @external_participant = external_participant
    @record_actions = record_actions
    @deleted_by = deleted_by
    @look_up_owner = look_up_owner
    @include_geofence_config = include_geofence_config
    @user_id = GetSecurityContext.call.result&.user_id
  end

  def call
    return nil unless @meeting
    JSON(
      Jbuilder.new do |meeting|
        meeting.id @meeting.id unless @external_participant
        meeting.tenantId @meeting.tenant_id unless @external_participant

        meeting.(@meeting, :title, :description, :from, :to)
        meeting.allDay @meeting.all_day
        meeting.location @meeting.location
        meeting.medium @meeting.medium
        meeting.providerLink @meeting.provider_link
        meeting.createdAt @meeting.created_at unless @external_participant
        meeting.updatedAt @meeting.updated_at unless @external_participant
        meeting.conductedAt @meeting.conducted_at unless @external_participant
        meeting.cancelledAt @meeting.cancelled_at unless @external_participant

        meeting.timezone LookUpSerializer.call(@meeting.time_zone).result
        meeting.owner UserSerializer.call(@meeting.owner, @external_participant.present?).result
        meeting.createdBy UserSerializer.call(@meeting.created_by).result unless @external_participant
        meeting.updatedBy UserSerializer.call(@meeting.updated_by).result unless @external_participant
        meeting.importedBy UserSerializer.call(@meeting.imported_by).result
        meeting.conductedBy UserSerializer.call(@meeting.conducted_by).result unless @external_participant
        meeting.cancelledBy UserSerializer.call(@meeting.cancelled_by).result unless @external_participant
        meeting.participants @meeting.participants.collect{|participant| ParticipantSerializer.call(participant, @meeting, @look_up_owner).result} unless @external_participant
        meeting.organizer LookUpSerializer.call(@meeting.organizer, @look_up_owner).result
        meeting.relatedTo @meeting.related_to.collect{ |participant| LookUpSerializer.call(participant, @look_up_owner).result } unless @external_participant
        meeting.recordActions @meeting.get_record_actions unless @external_participant || !@record_actions
        meeting.status @meeting.status unless @external_participant
        meeting.rsvp @external_participant.rsvp_response if @external_participant
        meeting.customFieldValues @meeting.custom_field_values
        meeting.geofenceConfig User.find_by(id: @user_id)&.get_geofence_config if @include_geofence_config && @user_id

        meeting.locationCoordinate do
          meeting.lat @meeting.location_latitude.to_f
          meeting.lon @meeting.location_longitude.to_f
        end if @meeting.location_latitude.present? && @meeting.location_longitude.present?

        owner_attendance = @meeting.meeting_attendances.find_by(user_id: @meeting.owner_id)
        meeting.checkedInDetails MeetingAttendanceSerializer.call(owner_attendance, 'in').result unless @external_participant
        meeting.checkedOutDetails MeetingAttendanceSerializer.call(owner_attendance, 'out').result unless @external_participant

        unless @external_participant
          meeting.userAttendances @meeting.meeting_attendances.includes(:user).map { |attendance| UserAttendanceSerializer.call(attendance).result }
        end

        if @deleted_by.present?
          meeting.deletedBy UserSerializer.call(@deleted_by).result
          meeting.deletedAt DateTime.now.utc
        end
      end
      .target!
    )
  end
end
