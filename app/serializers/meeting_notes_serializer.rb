class MeetingNotesSerializer
  prepend SimpleCommand

  def initialize(notes)
    @notes = notes
  end

  def call
    return nil unless @notes

    json = Jbuilder.new
    json.body do
      json.content (@notes.map do |m|
        NoteSerializer.call(m).result
      end)

      json.page do
        json.no @notes.current_page.to_i
        json.size @notes.per_page
      end
      json.totalElements @notes.total_entries
      json.totalPages @notes.total_pages
      json.first @notes.previous_page.nil?
      json.last @notes.next_page.nil?
    end
  end
end