class FieldsSerializer
  prepend SimpleCommand

  def initialize(fields, picklist_values_over_picklist = false)
    @fields = fields
    @picklist_values_over_picklist = picklist_values_over_picklist
  end

  def call
    return nil unless @fields

    json = Jbuilder.new
    json.array! @fields do |f|
      json.id f.id
      json.name f.internal_name
      json.displayName f.display_name
      json.type f.field_type
      json.description f.description
      json.standard f.is_standard
      json.sortable f.is_sortable
      json.filterable f.is_filterable
      json.internal f.is_internal
      json.required f.is_required
      json.active f.active
      json.tenantId f.tenant_id
      if @picklist_values_over_picklist
        json.pickLists f.picklist.present? ? PicklistValuesSerializer.call(f.picklist.picklist_values, f.system_default?).result : nil
      else
        json.picklist PicklistSerializer.call(f.picklist, f.system_default?).result
      end
      json.createdAt f.created_at
      json.updatedAt f.updated_at
    end
  end
end
