class PicklistSerializer
  prepend SimpleCommand

  def initialize(picklist, system_default = false)
    @picklist = picklist
    @system_default = system_default
  end

  def call
    return nil unless @picklist
    JSON(
      Jbuilder.new do |picklist|
        picklist.id @picklist.id
        picklist.displayName @picklist.display_name
        picklist.picklistValues PicklistValuesSerializer.call(@picklist.picklist_values,  @system_default).result
      end
      .target!
    )
  end
end
