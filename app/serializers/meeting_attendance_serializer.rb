class MeetingAttendanceSerializer
  prepend SimpleCommand

  def initialize(meeting_attendance, in_or_out = nil)
    @meeting_attendance = meeting_attendance || MeetingAttendance.new
    @in_or_out = in_or_out
  end

  def call
    return nil unless @meeting_attendance.present? && %w(in out).include?(@in_or_out)

    JSON(
      Jbuilder.new do |attendance|
        attendance.at @meeting_attendance.send("checked_#{@in_or_out}_at")
        attendance.latitude @meeting_attendance.send("checked_#{@in_or_out}_latitude")
        attendance.longitude @meeting_attendance.send("checked_#{@in_or_out}_longitude")
      end.target!
    )
  end
end
