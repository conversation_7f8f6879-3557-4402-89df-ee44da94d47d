# frozen_string_literal: true

class DeleteAssociatedShareRulesJob < ApplicationJob
  queue_as :default
  sidekiq_options retry: 3

  def perform(meeting_id, user_id, tenant_id)
    return unless meeting_id.present?

    ShareRule.where(tenant_id: tenant_id, meeting_id: meeting_id).each do |share_rule|
      old_serialized_share_rule = ShareRuleSerializer.new(share_rule, payload_for_event: true).call
      if share_rule.destroy
        publish_events('delete', 'share_rule', { serialized_share_rule: old_serialized_share_rule, user_id: user_id, tenant_id: tenant_id })
      end
    end
  end
end
