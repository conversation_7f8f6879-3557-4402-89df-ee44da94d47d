# frozen_string_literal: true

class MeetingScheduledPublisherJob < ApplicationJob

  queue_as :reminder_queue
  sidekiq_options retry: 0

  def perform(meeting_ids)
    start_time = Time.now.beginning_of_minute
    end_time = start_time.end_of_minute + 15.minutes

    meetings = Meeting.where(id: meeting_ids, status: SCHEDULED).where(from: start_time..end_time)
    MeetingsScheduledInMinutesPublisher.call(meetings) if meetings.any?
  end
end
