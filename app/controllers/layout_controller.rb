class LayoutController < ApplicationController
  before_action :verify_view_name, only: [:index]

  def index
    command = GetLayoutView.call(layout_params)
    json_response(command.result, :ok) if command.success?
  end

  def list
    command = GetLayoutList.call
    json_response(command.result, :ok) if command.success?
  end

  private

  def layout_params
    params.permit(
      :view
    )
  end

  def verify_view_name
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.view')}") if ['create', 'edit'].exclude?(params['view'])
  end
end
