# frozen_string_literal: true

class ApplicationController < ActionController::API
  include JsonResponse
  include ExceptionHandler

  # called before every action on controllers
  before_action :authenticate
  attr_reader :current_user

  private

  # Check for valid request token and return user
  def authenticate
    @current_user = AuthorizeApiRequest.call(request.headers, params.permit!).result
  end
end
