# frozen_string_literal: true

module EventsPublisher
  def publish_events(action, entity, options = {})
    return if options.blank?
    options = options.with_indifferent_access
    if options[:meeting].present? && options[:meeting].online?
      send("publish_online_#{entity}_#{action}_events", options)
    else
      send("publish_#{entity}_#{action}_events", options)
    end
  end

  private

  def publish_meeting_create_events(options)
    meeting = options[:meeting]
    case meeting.status
    when SCHEDULED
      ParticipantAddedEventPublisher.call(meeting)
      MeetingScheduledEventPublisher.call(meeting, meeting.participants)
      MeetingScheduledRelatedToEntityPublisher.call(meeting) if meeting_related_to_entity?(meeting.related_to)
    when CANCELLED
      ParticipantRemovedEventPublisher.call(meeting)
      MeetingCancelledEventPublisher.call(meeting)
    end

    meeting.publish_entity_metadata
    PublishEvent.call(Event::MeetingCreated.new(meeting, options[:current_user_id]))
    Rails.logger.info "Event::MeetingCreated event for #{meeting.inspect}"
  end

  def publish_online_meeting_create_events(options)
    meeting = options[:meeting]
    meeting.publish_entity_metadata

    PublishEvent.call(Event::MeetingCreated.new(meeting, options[:current_user_id]))
    Rails.logger.info "Event::MeetingCreated event for #{meeting.inspect}"
  end

  def publish_meeting_update_events(options)
    meeting = options[:meeting]
    case meeting.status
    when CANCELLED
      ParticipantRemovedEventPublisher.call(meeting)
      MeetingCancelledEventPublisher.call(meeting)
      meeting.publish_entity_metadata
      if options[:removed_participants].present?
        Rails.logger.info "Update service succeded: publishing metadata"
        options[:removed_participants].each do |look_up|
          if [LOOKUP_DEAL, LOOKUP_LEAD].include? look_up.entity_type
            EntityMetadataPublisher.call(look_up, meeting.owner_id)
          end
        end
      end
    when MISSED, CONDUCTED
      meeting.publish_entity_metadata
    when SCHEDULED
      ParticipantAddedEventPublisher.call(meeting, options[:added_participants]) if options[:added_participants]
      Rails.logger.info "Update service succeded: added_participants: #{options[:added_participants]}"
      MeetingScheduledEventPublisher.call(meeting, options[:new_participants]) if options[:new_participants].present?
      Rails.logger.info "Update service succeded: new_participants: #{options[:new_participants]}"
      if meeting_time_changed?(options[:meeting_changed])
        MeetingReScheduledEventPublisher.call(meeting, options[:old_participants])
        meeting.publish_entity_metadata
      elsif meeting_status_changed?(options[:meeting_changed]) || options[:new_participants].present? || options[:added_participants].present?
        meeting.publish_entity_metadata
      end
      Rails.logger.info "Update service succeded: old_participants: #{options[:old_participants]}"

      Rails.logger.info "Update service succeded: removed_participants: #{options[:removed_participants]}"
      if options[:removed_participants].present?
        ParticipantRemovedEventPublisher.call(meeting, options[:removed_participants], options[:new_participants])

        Rails.logger.info "Update service succeded: publishing metadata"
        options[:removed_participants].each do |look_up|
          if [LOOKUP_DEAL, LOOKUP_LEAD].include? look_up.entity_type
            EntityMetadataPublisher.call(look_up, meeting.owner_id)
          end
        end
      end
      MeetingScheduledRelatedToEntityPublisher.call(meeting) if meeting_related_to_entity?(options[:new_participants])
    end

    PublishEvent.call(Event::MeetingUpdated.new(meeting, options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{meeting.inspect}"
  end

  def publish_online_meeting_update_events(options)
    meeting = options[:meeting]
    case meeting.status
    when CANCELLED, MISSED, CONDUCTED
      meeting.publish_entity_metadata
    when SCHEDULED
      if (meeting_time_changed?(options[:meeting_changed]) || meeting_status_changed?(options[:meeting_changed]) || options[:new_participants].present? || options[:added_participants].present?)
        meeting.publish_entity_metadata
      end
      Rails.logger.info 'Update service succeded: publishing metadata'
      options[:removed_participants].each do |look_up|
        if [LOOKUP_DEAL, LOOKUP_LEAD].include?(look_up.entity_type)
          EntityMetadataPublisher.call(look_up, meeting.owner_id)
        end
      end
    end
    PublishEvent.call(Event::MeetingUpdated.new(meeting, options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{meeting.inspect}"
  end

  def publish_meeting_checkin_events(options)
    PublishEvent.call(Event::MeetingCheckedIn.new(options[:tenant_id], options[:meeting_ids], options[:current_user_id], options[:meeting_attendance]))
    Rails.logger.info "Event::MeetingCheckedIn event. MeetingIds - #{options[:meeting_ids]}"
  end

  def publish_meeting_checkout_events(options)
    PublishEvent.call(Event::MeetingCheckedOut.new(options[:tenant_id], options[:meeting_ids], options[:current_user_id], options[:meeting_attendance]))
    Rails.logger.info "Event::MeetingCheckedOut event. MeetingIds - #{options[:meeting_ids]}"
  end

  def publish_meeting_rsvp_events(options)
    if options[:notify_organiser]
      MeetingParticipantRsvpUpdatedEventPublisher.call(options[:meeting_participant].meeting, options[:meeting_participant].look_up)
    end

    PublishEvent.call(Event::MeetingUpdated.new(options[:meeting_participant].meeting, options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{options[:meeting_participant].meeting.inspect}"
  end

  def publish_online_meeting_rsvp_events(options); end

  def publish_meeting_p_rsvp_events(options)
    MeetingParticipantRsvpUpdatedEventPublisher.call(options[:meeting_participant].meeting, options[:meeting_participant].look_up)
    PublishEvent.call(Event::MeetingUpdated.new(options[:meeting_participant].meeting, options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{options[:meeting_participant].meeting.inspect}"
  end

  def publish_online_meeting_p_rsvp_events(options); end

  def publish_meeting_conduct_events(options)
    options[:meeting].publish_entity_metadata
    PublishEvent.call(Event::MeetingUpdated.new(options[:meeting], options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{options[:meeting].inspect}"
  end

  def publish_meeting_cancel_events(options)
    ParticipantRemovedEventPublisher.call(options[:meeting])
    MeetingCancelledEventPublisher.call(options[:meeting])
    options[:meeting].publish_entity_metadata
    PublishEvent.call(Event::MeetingUpdated.new(options[:meeting], options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{options[:meeting].inspect}"
  end

  def publish_online_meeting_cancel_events(options)
    options[:meeting].publish_entity_metadata
    PublishEvent.call(Event::MeetingUpdated.new(options[:meeting], options[:old_meeting], options[:current_user_id], options[:metadata]))
    Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{options[:meeting].inspect}"
  end

  def publish_meeting_destroy_events(options)
    meeting = options[:meeting]
    (meeting.participants + meeting.related_to).uniq { |l| l['entity'] }.each do |look_up|
      EntityMetadataPublisher.call(look_up, meeting.owner_id)
    end
    ParticipantRemovedEventPublisher.call(meeting)
    MeetingCancelledEventPublisher.call(meeting, options[:deleted_by_id]) if((meeting.upcoming? && meeting.status != 'cancelled') && !options[:cascade_delete])
    PublishEvent.call(Event::MeetingDeleted.new(options[:old_meeting], options[:deleted_by_id]))
    Rails.logger.info "Event::MeetingDeleted event. Deleted Meeting #{options[:old_meeting].inspect}"
  end

  def publish_online_meeting_destroy_events(options)
    meeting = options[:meeting]
    (meeting.participants + meeting.related_to).uniq { |l| l['entity'] }.each do |look_up|
      EntityMetadataPublisher.call(look_up, meeting.owner_id)
    end
    PublishEvent.call(Event::MeetingDeleted.new(options[:old_meeting], options[:deleted_by_id]))
    Rails.logger.info "Event::MeetingDeleted event. Deleted Meeting #{options[:old_meeting].inspect}"
  end

  def meeting_related_to_entity?(participants)
    return participants.find{ |p| [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_COMPANY].include?(p.entity_type) } if participants.kind_of?(Array)

    participants.where("entity LIKE ? OR entity LIKE ? OR entity LIKE ? OR entity LIKE ?", 'lead_%', 'contact_%', 'deal_%', 'company_%').exists?
  end

  def meeting_time_changed?(meeting_changed)
    (meeting_changed.keys & %w[from to]).any?
  end

  def meeting_status_changed?(meeting_changed)
    meeting_changed.keys.include?('status')
  end

  def publish_field_create_events(options)
    PublishEvent.call(Event::MeetingFieldCreated.new(options[:field]))
    PublishEvent.call(Event::MeetingFieldCreatedV2.new(options[:field]))
    Rails.logger.info "Event::MeetingFieldCreated V1 & V2 Created Field ID #{options[:field].id}"
    PublishEvent.call(Event::MeetingLayoutUpdated.new(options[:field].tenant_id))
    Rails.logger.info "Event::MeetingLayoutUpdated for Tenant ID #{options[:field].tenant_id.inspect}"
    TenantUsagePublisher.call(options[:tenant_id])
  end

  def publish_field_update_events(options)
    PublishEvent.call(Event::MeetingFieldUpdated.new(options[:field]))
    PublishEvent.call(Event::MeetingFieldUpdatedV2.new(options[:field], options[:old_serialized_field_data]))
    Rails.logger.info "Event::MeetingFieldUpdated V1 & V2 Updated Field ID #{options[:field].id}"
    PublishEvent.call(Event::MeetingLayoutUpdated.new(options[:field].tenant_id))
    Rails.logger.info "Event::MeetingLayoutUpdated for Tenant ID #{options[:field].tenant_id.inspect}"
  end

  def publish_field_activate_events(options)
    PublishEvent.call(Event::MeetingFieldUpdated.new(options[:field]))
    PublishEvent.call(Event::MeetingFieldUpdatedV2.new(options[:field], options[:old_serialized_field_data]))
    Rails.logger.info "Event::MeetingFieldUpdated V1 & V2 Updated Field ID #{options[:field].id}"
    PublishEvent.call(Event::MeetingLayoutUpdated.new(options[:field].tenant_id))
    Rails.logger.info "Event::MeetingLayoutUpdated for Tenant ID #{options[:field].tenant_id.inspect}"
    TenantUsagePublisher.call(options[:field].tenant_id)
  end

  def publish_picklist_value_update_events(options)
    PublishEvent.call(Event::PicklistValueUpdated.new(options[:picklist_value_hash], options[:old_picklist_value_hash], options[:user]))
    Rails.logger.info "Event::PicklistValueUpdated Updated Picklist Value #{options[:picklist_value_hash].inspect}"
    PublishEvent.call(Event::MeetingFieldUpdatedV2.new(options[:field], options[:old_serialized_field_data]))
    Rails.logger.info "Event::MeetingFieldUpdated V2 Updated Field ID #{options[:field].id}"

    PublishEvent.call(Event::MeetingLayoutUpdated.new(options[:user].tenant_id))
    Rails.logger.info "Event::MeetingLayoutUpdated for Tenant ID #{options[:user].tenant_id.inspect}"
  end

  def publish_share_rule_create_events(options)
    Rails.logger.info "Event::ShareRuleCreated V2 #{options[:share_rule].id}"
    PublishEvent.call(Event::ShareRuleCreatedV2.new(options[:share_rule]))
  end

  def publish_share_rule_update_events(options)
    Rails.logger.info "Event::ShareRuleUpdated V2 #{options[:share_rule].id}"
    PublishEvent.call(Event::ShareRuleUpdatedV2.new(options[:share_rule], options[:old_share_rule]))
  end

  def publish_share_rule_delete_events(options)
    Rails.logger.info "Event::ShareRuleDeleted V2 #{options[:serialized_share_rule]['id']}"
    PublishEvent.call(Event::ShareRuleDeletedV2.new(options[:serialized_share_rule], options[:user_id], options[:tenant_id]))
  end

  alias publish_field_deactivate_events publish_field_activate_events
  alias publish_online_meeting_checkin_events publish_meeting_checkin_events
  alias publish_online_meeting_checkout_events publish_meeting_checkout_events
  alias publish_online_meeting_conduct_events publish_meeting_conduct_events
end
