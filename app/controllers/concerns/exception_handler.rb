module ExceptionHandler
  extend ActiveSupport::Concern

  SERVICE_CODE = '015'
  MODULE_SECURITY = '01'
  MODULE_API = '02'
  MODULE_DOMAIN = '03'

  # Define custom error subclasses - rescue catches `StandardErrors`
  class AuthenticationError < StandardError; end
  class InternalServerError < StandardError; end
  class InvalidDataError < StandardError; end
  class MissingToken < StandardError; end
  class InvalidToken < StandardError; end
  class Forbidden < StandardError; end
  class NotFound < StandardError; end
  class ParticipantNotFound < StandardError; end
  class MeetingExpired < StandardError; end
  class MeetingCancelled < StandardError; end
  class NoteCreateNotAllowed < StandardError; end
  class NoteAccessNotAllowed < StandardError; end
  class ConnectedAccountNotFound < StandardError; end
  class EntityCannotBeDestroyed < StandardError; end
  class ProviderInternalServerError < StandardError; end
  class ProviderForbidden < StandardError; end
  class ProviderUnauthorized < StandardError; end
  class ProviderInvalidDataError < StandardError; end
  class ProviderNotFound < StandardError; end

  included do
    # Define custom handlers
    rescue_from ExceptionHandler::AuthenticationError do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.invalid_credentials),
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InternalServerError do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.internal_error),
        :internal_server_error
      )
    end

    rescue_from ExceptionHandler::MissingToken do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.missing_token),
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InvalidToken do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.invalid_token),
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::Forbidden do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.unauthorized),
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InvalidDataError do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.invalid),
        :unprocessable_entity
      )
    end

    rescue_from ActiveRecord::RecordNotFound do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.not_found),
        :not_found
      )
    end

    rescue_from ExceptionHandler::NotFound do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.not_found),
        :not_found
      )
    end

    rescue_from ExceptionHandler::ParticipantNotFound do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.participant_not_found),
        :not_found
      )
    end

    rescue_from ExceptionHandler::MeetingExpired do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.meeting_expired),
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::MeetingCancelled do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.meeting_cancelled),
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::NoteCreateNotAllowed do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.note_create_not_allowed),
        :forbidden
      )
    end

    rescue_from ExceptionHandler::NoteAccessNotAllowed do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.note_access_not_allowed),
        :forbidden
      )
    end

    rescue_from ExceptionHandler::ConnectedAccountNotFound do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.connected_account_not_found),
        :not_found
      )
    end

    rescue_from ExceptionHandler::EntityCannotBeDestroyed do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.picklist_value_cannot_be_deleted),
        :bad_request
      )
    end

    rescue_from ExceptionHandler::ProviderInternalServerError do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.provider_internal_server),
        :internal_server_error
      )
    end

    rescue_from ExceptionHandler::ProviderForbidden do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.provider_forbidden),
        :forbidden
      )
    end

    rescue_from ExceptionHandler::ProviderUnauthorized do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.provider_unauthorized),
        :forbidden
      )
    end

    rescue_from ExceptionHandler::ProviderInvalidDataError do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.provider_invalid),
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::ProviderNotFound do |e|
      json_response(
        error_hash(e, default_code: ErrorCode.provider_not_found),
        :not_found
      )
    end

    def error_hash(e, default_code:)
      errorCode, message, id = e.message&.split('||')
      errors = {
        errorCode: errorCode || default_code,
        message: message,
        timestamp: Time.now.utc
      }

      if id.present?
        errors['id'] = id
      end

      errors
    end
  end
end
