class NotesController < ApplicationController
  def index
    command = GetNotes.call(params[:meeting_id], page_params)
    if command.success?
      json_response(MeetingNotesSerializer.call(command.result).result, :ok)
    end
  end

  def create
    command = CreateNote.call(note_params, params[:meeting_id])
    if command.success?
      json_response(NoteSerializer.call(command.result).result, :ok)
    end
  end

  def destroy
    command = DeleteNote.call(params[:id], params[:meeting_id], params[:publishUsage])

    json_response({}, :ok) if command.success?
  end

  def search
    command = SearchNotes.call(params)

    if command.success?
      json_response(NoteSearchResultsSerializer.call(command.result).result, :ok)
    end
  end

  private
    def note_params
      params.permit(:description)
    end

    def page_params
      params.permit(:page, :size)
    end
end
