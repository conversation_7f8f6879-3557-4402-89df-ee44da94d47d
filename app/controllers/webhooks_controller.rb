class WebhooksController < ApplicationController
  skip_before_action :authenticate

  def google
    GoogleWebhookProcessorJob.perform_later({ 'x-goog-resource-id' => request.headers['x-goog-channel-id']})
    render json: {}
  end

  def microsoft
    if !params[:validationToken].present? && params[:value].present?
      MicrosoftWebhookProcessorJob.perform_later(microsoft_params[:value].first)
    end

    render plain: params[:validationToken], status: :ok, content_type: 'text/plain'
  end

  private

  def microsoft_params
    params.permit(
      value: [
        :id,
        :subscriptionId,
        :subscriptionExpirationDateTime,
        :clientState,
        :changeType,
        :resource,
        :tenantId,
        resourceData: [
          'id'
        ]
      ]
    )
  end
end
