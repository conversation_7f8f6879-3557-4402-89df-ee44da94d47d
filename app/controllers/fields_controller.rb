class FieldsController < ApplicationController

  def create
    command = Field::CreateField.call(create_params)
    if command.success?
      json_response(FieldsSerializer.call([command.result], true).result.first, :created)
    end
  end

  def activate
    command = Field::UpdateFieldStatus.call(params[:id], ACTIVATE)
    if command.success?
      json_response({}, :ok)
    end
  end

  def deactivate
    command = Field::UpdateFieldStatus.call(params[:id], DEACTIVATE)
    if command.success?
      json_response({}, :ok)
    end
  end

  def index
    command = GetFields.call(params)
    json_response(FieldsSerializer.call(command.result).result, :ok) if command.success?
  end

  #INFO: Migration API to create meeting fields
  def create_fields
    params = field_creation_params
    command = CreateMeetingFieldsForTenant.call(params['tenantId'], params['userId'])
    json_response({'message': command.result}, :ok) if command.success?
  end

  # INFO: Migration API to update meeting fields
  def update_fields
    command = UpdateMeetingFieldsForTenant.call(params['fromTenantId'], params['toTenantId'])
    json_response({}, :ok) if command.success?
  end

  def show
    command = GetField.call(params['id'])
    json_response(FieldSerializer.call(command.result).result, :ok) if command.success?
  end

  def update
    command = UpdateField.call(params['id'], update_params)
    if command.success?
      json_response(FieldsSerializer.call([command.result]).result.first, :ok)
    end
  end

  private

  def create_params
    params.permit(
      :displayName,
      :description,
      :filterable,
      :sortable,
      :required,
      :type,
      {
        pickLists: [
          :id,
          :name,
          :displayName
        ]
      }
    )
  end

  def update_params
    params.permit(
      :displayName,
      :description,
      :required,
      :sortable,
      :filterable,
      {
        pickLists: [
          :id,
          :name,
          :displayName
        ]
      }
    )
  end

  # ?tenantId=123&userId=44&name=Tony
  def field_creation_params
    params.permit(
      :tenantId,
      :userId
    )
  end
end
