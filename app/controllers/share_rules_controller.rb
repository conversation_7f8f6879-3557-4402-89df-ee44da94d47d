# frozen_string_literal: true

class ShareRulesController < ApplicationController

  def show
    json_response(ShareRuleSerializer.new(ShareRules::Read.new(params[:id]).call).call, :ok)
  end

  def share
    result = ShareRules::Create.new(share_params).call
    json_response(ShareRuleSerializer.new(result).call, :ok)
  end

  def share_all
    result = ShareRules::Create.new(share_params).call
    json_response(ShareRuleSerializer.new(result).call, :ok)
  end

  def search
    json_response(ShareRulesSerializer.call(ShareRules::Search.new(params.permit!).call).result, :ok)
  end

  def update
    result = ShareRules::Update.new(share_params).call
    json_response(ShareRuleSerializer.new(result).call, :ok)
  end

  def destroy
    ShareRules::Delete.new(params[:id]).call
    json_response(:ok)
  end

  private

  def share_params
    params.permit(
      :id,
      :share_rule_id,
      :name,
      :description,
      from: %i[
        id
        type
      ],
      to: %i[
        id
        type
      ],
      actions: %i[
        read
        update
        delete
        email
        call
        note
        sms
        task
      ]
    )
  end
end
