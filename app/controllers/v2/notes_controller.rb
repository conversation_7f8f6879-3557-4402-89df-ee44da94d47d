# frozen_string_literal: true

module V2
  class NotesController < ApplicationController

    def search
      command = V2::SearchNotes.call(permit_params)

      if command.success?
        json_response(NoteSearchResultsSerializer.call(command.result, true).result, :ok)
      end
    end

    def permit_params
      case action_name
      when 'search'
        params.permit(:page, :size, :sort, jsonRule: {})
      end
    end
  end
end
