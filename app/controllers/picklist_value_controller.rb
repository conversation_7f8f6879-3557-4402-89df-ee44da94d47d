class PicklistValueController < ApplicationController

  def update
    command = PicklistValue::Update.call(permitted_params)
    if command.success?
      json_response(command.result, :ok)
    end
  end

  def enable
    command = PicklistValue::UpdateStatus.call(permitted_params, :enable)
    json_response({}, :ok) if command.success?
  end

  def disable
    command = PicklistValue::UpdateStatus.call(permitted_params, :disable)
    json_response({}, :ok) if command.success?
  end

  def destroy
    command = PicklistValue::Delete.call(permitted_params)
    json_response({}, :ok) if command.success?
  end

  private

  def permitted_params
    params.permit(
      :displayName,
      :picklist_id,
      :id
    )
  end
end
