class MeetingsController < ApplicationController
  skip_before_action :authenticate, only: :health

  def health
    return head :ok
  end

  def create
    command = CreateMeeting.call(meeting_params)
    if command.success?
      json_response(Response::Created.new(command.result).output, :created)
    end
  end

  def show
    command = GetMeetingDetails.call(params[:id])
    if command.success?
      json_response(MeetingSerializer.call(command.result, nil, true, nil, false, true).result, :ok)
    end
  end

  def update
    command = UpdateMeeting.call(meeting_params)
    if command.success?
      json_response(MeetingSerializer.call(command.result).result, :ok)
    end
  end

  def checkin
    command = MarkMeetingAttendance.call(params[:id], :in, checkin_checkout_params)
    if command.success?
      json_response({}, :ok)
    end
  end

  def checkout
    command = MarkMeetingAttendance.call(params[:id], :out, checkin_checkout_params)
    if command.success?
      json_response({}, :ok)
    end
  end

  def bulk_checkout
    result = BulkCheckout.new(bulk_checkout_params).call
    json_response(result, :ok)
  end

  def rsvp
    command = CaptureInternalUserRsvp.call(params[:id], rsvp_params)
    if command.success?
      json_response({}, :created)
    end
  end

  def p_rsvp
    command = CaptureExternalUserRsvp.call(params[:id],public_rsvp_params)
    if command.success?
      meeting_participant = command.result
      json_response(MeetingSerializer.call(meeting_participant.meeting, meeting_participant).result, :created)
    end
  end

  def search
    command = GetMeetings.call(filter_params)
    if command.success?
      json_response(MeetingSearchResultsSerializer.call(command.result, selected_fields: filter_params[:fields]).result, :ok)
    end
  end

  def conduct
    command = ConductMeeting.call(params[:id])
    if command.success?
      json_response(MeetingSerializer.call(command.result).result, :ok)
    end
  end

  def cancel
    command = CancelMeeting.call(params[:id])
    if command.success?
      json_response(MeetingSerializer.call(command.result).result, :ok)
    end
  end

  def destroy
    command = DeleteMeeting.call(params[:id], params[:publishUsage])
    if command.success?
      json_response({}, :ok)
    end
  end

  def bulk_delete
    command = DeleteBulkMeetings.call(params[:ids])
    json_response(command.result, :ok) if command.success?
  end

  def external_lookup
    command = GetExternalLookups.call(params[:q])
    json_response(ExternalLookupResultsSerializer.call(command.result).result, :ok) if command.success?
  end

  def lookup
    command = Meetings::LookupApi.call(lookup_params)
    if command.success?
      json_response(MeetingsLookupApiSerializer.call(command.result, lookup_params[:view]).result, :ok)
    end
  end

  def import
    command = ImportMeeting.call(import_params)
    json_response(ImportMeetingSerializer.call(command.result.id).result) if command.success?
  end

  def access
    result = CheckMeetingsAccess.new({ meeting_ids: access_params[:_json] }).check_meetings_access
    json_response({ entityIds: result }, :ok)
  end

  def access_related_to_entity
    result = CheckMeetingsAccess.new(access_related_to_entity_params).get_meetings_related_to_entity
    json_response({ entityIds: result }, :ok)
  end

  private

  def meeting_params
    params.permit(
      :id,
      :title,
      :description,
      :from,
      :to,
      :allDay,
      :medium,
      :providerLink,
      :location,
      :status,
      {
        owner: [:id, :name]
      },
      {
        customFieldValues: {},
      },
      {
        timezone: [
          :id,
          :name
        ]
      },
      {
        participants: [
          :entity,
          :id,
          :name,
          :email
        ]
      },
      {
        relatedTo: [
          :entity,
          :id,
          :name,
          :email
        ]
      },
      {
        checkedInDetails: [
          :latitude,
          :longitude
        ]
      },
      {
        checkedOutDetails: [
          :latitude,
          :longitude
        ]
      },
      {
        locationCoordinate: [:lat, :lon]
      }
    )
  end

  def rsvp_params
    params.permit(
      :notifyOrganiser,
      :rsvpMessage,
      :rsvpResponse
    )
  end

  def public_rsvp_params
    params.permit(
      :pid,
      :rsvpMessage,
      :rsvpResponse
    )
  end

  def filter_params
    params.permit(
      :page,
      :size,
      :sort,
      {
        jsonRule: [
          :condition,
          rules: [
            :operator,
            :type,
            :field,
            :value,
            :id,
            :primaryField,
            :property,
            {
              value: [
                :id,
                :entity
              ]
            },
            { value: [] }
          ]
        ],
        fields: []
      }
    )
  end

  def checkin_checkout_params
    params.permit(
      :location,
      {
        checkedInDetails: [
          :latitude,
          :longitude
        ]
      },
      {
        checkedOutDetails: [
          :latitude,
          :longitude
        ]
      }
    )
  end

  def import_params
    params.permit(
      :jobId,
      meeting: [
        :title,
        :description,
        :allDay,
        :organizer,
        {
          timezone: [
            :id,
            :name
          ]
        },
        :location,
        :medium,
        {
          customFieldValues: {}
        },
        :status,
        {
          relatedToLeads: []
        },
        {
          relatedToContacts: []
        },
        {
          relatedToCompanies: []
        },
        {
          participants: []
        },
        :from,
        :to,
        :owner
      ]
    )
  end

  def lookup_params
    params.permit(:view, :q, :timezone)
  end

  def bulk_checkout_params
    params.permit(:latitude, :longitude, meetingIds: [])
  end

  def access_params
    params.permit(_json: [])
  end

  def access_related_to_entity_params
    params.permit(:relatedToEntityId, :relatedToEntityType)
  end
end
