# frozen_string_literal: true

class ListenForCheckoutMeetings
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(FIELD_SALES_EXCHANGE, FIELD_SALES_CHECKOUT_MEETINGS_EVENT, FIELD_SALES_CHECKOUT_MEETINGS_QUEUE) do |payload|
      Rails.logger.info "Received message for userID - #{payload['performedBy']} for #{FIELD_SALES_CHECKOUT_MEETINGS_QUEUE}"
      payload = JSON(payload)

      CheckoutMeetings.new(payload).call
    end
  end
end
