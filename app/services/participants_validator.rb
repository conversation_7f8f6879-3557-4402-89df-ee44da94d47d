class ParticipantsValidator
  prepend SimpleCommand

  def initialize params
    @params = params
    @user = params[:user]
    @organizer_exists = params[:organizer_exists]
  end

  def call
    participants = build_participants
    validated_participants = []
    validated_participants += validate_users(participants.select{|x| x.is_a_user?})
    validated_participants += validate_leads(participants.select{|x| x.is_a_lead?})
    validated_participants += validate_contacts(participants.select{|x| x.is_a_contact?})
    validated_participants += validate_deals(participants.select{|x| x.is_a_deal?})
    validated_participants += validate_companies(participants.select{|x| x.is_a_company?})
    validated_participants
  end

  private

  def build_participants
    participants_data = @params[:participants] || []
    if @params[:action] == 'create' && !@organizer_exists
      participants_data << {
        tenant_id: @user.tenant_id, entity: LOOKUP_USER, id: @user.id, name: @user.name
      }
    end
    participants = participants_data.collect do |pdata|
      pdata[:tenant_id] = @user.tenant_id
      look_up = GetLookUp.call(pdata).result
      look_up.name = pdata[:name]&.strip if pdata[:name]
      look_up.email = pdata[:email] if pdata[:email]
      look_up.save unless look_up.new_record?
      look_up
    end
    remove_duplicates(participants)
  end

  def remove_duplicates(participants)
    participants.uniq { |participant| participant.entity }
  end

  def validate_users(user_participants = [])
    return [] if user_participants.empty?
    ValidateUsers.call(user_participants).result
  end

  def validate_leads(lead_participants = [])
    return [] if lead_participants.empty?
    ValidateLeads.call(lead_participants).result
  end

  def validate_contacts(contact_participants = [])
    return [] if contact_participants.empty?
    ValidateContacts.call(contact_participants).result
  end

  def validate_deals(deal_participants = [])
    return [] if deal_participants.empty?
    ValidateDeals.call(deal_participants).result
  end

  def validate_companies(company_participants = [])
    return [] if company_participants.empty?
    ValidateCompanies.call(company_participants).result
  end
end
