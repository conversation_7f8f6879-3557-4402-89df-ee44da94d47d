# frozen_string_literal: true

class MeetingCheckedinBeyondGeofenceEventPublisher
  prepend SimpleCommand

  def initialize(meeting, user, tenant_id)
    @meeting = meeting
    @user = user
    @tenant_id = tenant_id
  end

  def call
    event = Event::MeetingCheckedinBeyondGeofence.new(@meeting, @user, @tenant_id)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingCheckinBeyondGeofence event data: #{event.to_json}"
  end
end
