class GetField
  prepend SimpleCommand

  def initialize(field_id)
    @field_id = field_id
  end

  def call
    command = GetSecurityContext.call
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    auth_data = command.result
    tenant_id = auth_data.tenant_id
    field = Field.find_by(tenant_id: tenant_id, id: @field_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.field')}") if field.blank? || field.internal_name == 'tenant_id'

    field
  end
end
