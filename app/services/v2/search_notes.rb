# frozen_string_literal: true

class V2::SearchNotes
  prepend SimpleCommand

  def initialize(params)
    @params = params
    @auth_data = nil
    @current_user = nil
  end

  def call
    command = GetSecurityContext.call

    if command.success?
      @auth_data = command.result
      @current_user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      unless @auth_data.can_access?('note', 'read')
        Rails.logger.error "User doesn't have permission to read notes"
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end
  
      FilterNotesQuery.new(@auth_data.tenant_id, @current_user.id, @params, @auth_data.can_access?('note', 'read_all')).call
    else
      Rails.logger.error "Unauthorised: User context missing in SearchNotes V2"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
