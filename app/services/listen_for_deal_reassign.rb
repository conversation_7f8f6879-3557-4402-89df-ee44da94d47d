# frozen_string_literal: true

class ListenForDealReassign
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_REASSIGNED_EVENT, DEAL_REASSIGNED_QUEUE) do |payload|
      Rails.logger.info "Received message #{DEAL_EXCHANGE} for #{DEAL_REASSIGNED_EVENT}"
      payload = JSON(payload)
      UpdateLookUp.call(payload['deal']['tenantId'], payload['id'], LOOKUP_DEAL, { owner_id: payload['deal']['ownerId'] })
    end
  end
end
