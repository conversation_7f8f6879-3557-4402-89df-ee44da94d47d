class ParticipantRemovedEventPublisher

  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting, participants_to_unrelate = [], new_participants = [])
    @meeting = meeting
    @participants_to_unrelate = participants_to_unrelate
    @new_participants = new_participants
  end

  def call
    if(@participants_to_unrelate.present?)
      user_removed = @participants_to_unrelate.select{ |participant| participant.is_a_user? }
      other_entities_removed = @participants_to_unrelate.select{ |participant| !participant.is_a_user? }
      if user_removed.present?
        @participants_to_unrelate = remove_duplicates(
                                      @participants_to_unrelate.union(
                                        @meeting.participants.select{ |participant| !participant.is_a_user? }.union(
                                          @meeting.related_to
                                        )
                                      )
                                    )
        unrelate
      end

      if other_entities_removed.present?
        @participants_to_unrelate = @meeting.participants.select{ |participant| participant.is_a_user? } - @new_participants
        @participants_to_unrelate = remove_duplicates @participants_to_unrelate.union(other_entities_removed)
        unrelate
      end

    else
      @participants_to_unrelate = (@meeting.participants + @meeting.related_to).uniq{ |l| l.entity }
      unrelate
    end

  end

  private

  def unrelate
    @participants_to_unrelate
      .select{ |x| x.entity_type.include?(LOOKUP_USER) && x.entity_id != @meeting.owner_id }
      .each do |participant|
        @participants_to_unrelate
          .uniq { |entity| entity['entity'] }
          .reject{|x| [LOOKUP_USER, LOOKUP_EXTERNAL].include?(x.entity_type) }.each do |unrelated_to|
            send("publish_meeting_unrelated_to_#{unrelated_to.entity_type.downcase}", participant, unrelated_to)
          end
      end
  end

  def remove_duplicates participants
    participants.uniq { |participant| participant.entity }
  end

  def publish_meeting_unrelated_to_lead participant, unrelated_to
    event = Event::MeetingUnrelatedToLead.new(@meeting, participant, unrelated_to)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingUnrelatedToLead Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{unrelated_to.entity}"
  end

  def publish_meeting_unrelated_to_deal participant, unrelated_to
    event = Event::MeetingUnrelatedToDeal.new(@meeting, participant, unrelated_to)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingUnrelatedToDeal Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{unrelated_to.entity}"
  end

  def publish_meeting_unrelated_to_contact participant, unrelated_to
    event = Event::MeetingUnrelatedToContact.new(@meeting, participant, unrelated_to)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingUnrelatedToContact Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{unrelated_to.entity}"
  end

  def publish_meeting_unrelated_to_company participant, unrelated_to
    event = Event::MeetingUnrelatedToCompany.new(@meeting, participant, unrelated_to)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingUnrelatedToCompany Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{unrelated_to.entity}"
  end
end
