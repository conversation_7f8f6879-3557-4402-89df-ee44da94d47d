# frozen_string_literal: true

class ListenForDeactivateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, DEACTIVATE_CONNECTED_ACCOUNT_EVENT, DEACTIVATE_CONNECTED_ACCOUNT_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{DEACTIVATE_CONNECTED_ACCOUNT_EVENT}"
      payload = JSON(payload)
      user_id = payload['userId']
      tenant_id = payload['tenantId']
      provider = payload['providerName']
      disconnected_by = payload['disconnectedBy']
      user_email = payload['userEmail']
      tenant_email = payload['tenantEmail']
      DeactivateConnectedAccount.call(user_id, tenant_id, provider, disconnected_by, user_email, tenant_email)
    end
  end
end
