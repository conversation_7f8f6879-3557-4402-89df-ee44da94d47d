require 'rest-client'

class TenantUsagePublisher
  prepend SimpleCommand

  def initialize(tenant_id = nil)
    @tenant_id = tenant_id
  end

  def call
    Rails.logger.info 'Tenant Usage publisher called'
    data = Meeting.usage_per_tenant(@tenant_id) + Field.usage_per_tenant(@tenant_id)
    event = Event::TenantUsage.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::TenantUsagePublisher -> Tenant ID: #{@tenant_id} -> Event Name: #{event.routing_key}"
  end
end
