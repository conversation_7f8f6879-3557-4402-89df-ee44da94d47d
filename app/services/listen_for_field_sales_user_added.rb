# frozen_string_literal: true

class ListenForFieldSalesUserAdded
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(FIELD_SALES_EXCHANGE, FIELD_SALES_USER_ADDED_EVENT, FIELD_SALES_USER_ADDED_QUEUE) do |payload|
      payload = JSON(payload)

      Rails.logger.info "Received field sales user added event for users - #{payload['userIds']}"

      users = User.where(id: payload['userIds'], tenant_id: payload['tenantId'])
      
      users.each do |user|
        geofence_config = user.geofence_config || {}
        geofence_config[:fieldSalesEnabled] = true
        user.update(geofence_config: geofence_config)
      end
    end
  end
end
