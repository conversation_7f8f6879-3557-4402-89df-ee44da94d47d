class BuildRsvpUrl
  prepend SimpleCommand

  def initialize meeting, participant, rsvp_token, rsvp_status="yes"
    @meeting = meeting
    @participant = participant
    @rsvp_token = rsvp_token
    @rsvp_status = rsvp_status
  end

  def call
    base_url = APP_KYLAS_HOST + "/meetings/rsvp"
    params = []
    params << ["mid", @meeting.public_id]
    params << ["pid", @participant.public_id]
    params << ["token", @rsvp_token]
    params << ["rsvp", @rsvp_status]
    params_data = URI.encode_www_form(params)
    encoded_data = Base64.encode64(params_data)
    complete_url = [base_url , URI.encode_www_form([["data",encoded_data]])].join("?")
    return complete_url
  end
end