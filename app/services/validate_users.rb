require 'rest-client'
class ValidateUsers
  prepend SimpleCommand

  def initialize(users = [])
    @users = users
  end

  def call
    return [] if @users.nil? || @users.empty?
    command = GetSecurityContext.call
    
    if command.success?
      auth_data = command.result
      verified_users = get_user_summary
      verified_users.each do |v_user|
        user = @users.select{|x| x.entity_id == v_user["id"]}.first
        if user.nil?
          Rails.logger.error "ValidateUser: couldnt find matching users in response from IAM"
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user')}")
        else
          user.name = v_user["name"]
          email = v_user["email"]["value"]
          if user.email && user.email != email
            Rails.logger.error "ValidateUser: Email doesn't match"
            raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_email')}")
          else
            user.email = email
          end
        end
      end
      return @users
    else
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_user_summary
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    user_ids_params = @users.collect{|user| user.entity_id }.join(",")
    begin
      response = RestClient.get(SERVICE_IAM + "/v1/users/summary?id=" + user_ids_params,{'Authorization': "Bearer #{token}"}
      )
      return JSON(response.body) unless response.nil?

      Rails.logger.error "ValidateUser.get_user_summary invalid response"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_summary_response')}")
    rescue RestClient::Unauthorized
      Rails.logger.error "ValidateUser.get_user_summary iam 401"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    rescue RestClient::NotFound
      Rails.logger.error "ValidateUser.get_user_summary iam 404"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_summary_response')}")
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateUser.get_user_summary iam 500"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.user')}")
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateUser.get_user_summary iam 400"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_summary_response')}")
    end
  end
end
