# frozen_string_literal: true

class DeactivateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(user_id, tenant_id, provider, disconnected_by, user_email, tenant_email)
    @user_id = user_id
    @tenant_id = tenant_id
    @provider = provider
    @disconnected_by = disconnected_by
    @user_email = user_email
    @tenant_email = tenant_email
  end

  def call
    Rails.logger.info 'Deactivate connected account called'
    @user = GetUserDetails.call(@user_id, @tenant_id, false, nil).result
    connected_account = @user.connected_accounts.find_by(provider_name: @provider, active: true)
    if connected_account.present?
      if connected_account.provider_name == MICROSOFT_TEAMS_PROVIDER
        Calendar::Microsoft::UnsubscribeWebhook.call(connected_account)
      end
      account_updated = connected_account.update(active: false, provider_subscription_resource_id: nil)
      if account_updated && @disconnected_by == AUTO_DISCONNECTED
        CalenderDisconnectedPublisher.call(@tenant_id, @user_id, @disconnected_by, @user_email, @tenant_email)
      end
      if account_updated
        Rails.logger.info "Account successfully deactivated"
        true
      else
        Rails.logger.info "Account deactivation failed - #{connected_account.errors.messages.inspect}"
        false
      end
    else
      raise(ExceptionHandler::ConnectedAccountNotFound, "#{ErrorCode.connected_account_not_found}||#{I18n.t('error.connected_account_not_found')}")
    end
  end
end
