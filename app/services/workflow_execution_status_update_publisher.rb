# frozen_string_literal: true

class WorkflowExecutionStatusUpdatePublisher
  prepend SimpleCommand

  def initialize(data, publishing_details)
    @data = data
    @publishing_details = publishing_details
  end

  def call
    Rails.logger.info "Meeting Service: Publishers::WorkflowExecutionStatusUpdatePublisher called"

    event = Event::WorkflowExecutionStatusUpdate.new(@data, @publishing_details[:reply_to_event])
    PublishEvent.call(event, @publishing_details[:reply_to_exchange], 'workflow')
    Rails.logger.info "Event::WorkflowExecutionStatusUpdate data #{@data[:eventId]}"
  end
end
