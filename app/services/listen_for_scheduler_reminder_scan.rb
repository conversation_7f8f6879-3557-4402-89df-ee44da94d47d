# frozen_string_literal: true

# DEPRICATED
class ListenForSchedulerReminderScan
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, SCHEDULER_REMINDER_SCAN_EVENT, SCHEDULER_REMINDER_SCAN_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{SCHEDULER_REMINDER_SCAN_EVENT}"
      payload = JSON(payload)
      start_time = DateTime.parse(payload['triggeredAt']).end_of_minute + 15.minutes
      end_time = start_time + 15.minutes
      meetings = Meeting.where(:from => start_time..end_time.end_of_minute)
      meetings_by_minute = {}
      meetings.each do |meeting|
        curr_minute = ((meeting['from'] - start_time) / 60).ceil
        if meetings_by_minute[curr_minute].present?
          meetings_by_minute[curr_minute] << meeting.id
        else
          meetings_by_minute[curr_minute] = [meeting.id]
        end
      end

      meetings_by_minute.each do |curr_minute, meeting_ids|
        MeetingScheduledPublisherJob.set(wait: curr_minute.minutes).perform_later(meeting_ids)
      end
    end
  end
end
