class UpdateLookUp
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(tenant_id, entity_id, entity_type, lookup_params = {})
    @tenant_id = tenant_id
    @entity_id = entity_id
    @entity_type = entity_type
    @lookup_params = lookup_params
  end

  def call
    if params = @lookup_params.keep_if{ |key| [:name, :email, :owner_id].include?(key) }
      LookUp.where(tenant_id: @tenant_id, entity:"#{@entity_type}_#{@entity_id}").update_all(params)
    end
  end
end
