# frozen_string_literal: true

require 'rest-client'

# UNUSED
class SearchUsersByIds
  prepend SimpleCommand

  def initialize(user_ids, token = nil)
    @user_ids = user_ids
    @token = token
    @page = -1
  end

  def call
    return {} if @user_ids.blank?
    search_users
  end

  private

  def search_users
    if @token.blank?
      command = GetSecurityContext.call("token")
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
      @token = command.result
    end

    payload = { fields: %w[id firstName lastName] }
    payload[:jsonRule] = {
      rules: [
        {
          id: 'id',
          field: 'id',
          type: 'double',
          operator: 'in',
          value: @user_ids.join(',')
        }
      ],
      condition: 'AND',
      valid: true
    }

    user_id_name_hash = {}
    first_page_response = search_request(payload)
    user_id_name_hash.merge!(parse_response(first_page_response))
    number_of_pages = first_page_response['totalPages']
    if number_of_pages > 1
      (number_of_pages - 1).times do
        next_response = search_request(payload)
        user_id_name_hash.merge!(parse_response(next_response))
      end
    end

    user_id_name_hash
  end

  def search_request(payload)
    begin
      response = RestClient.post(
        "#{SERVICE_IAM}/v1/users/search?sort=updatedAt,desc&page=#{page_number}&size=100",
        payload.to_json,
        {
          Authorization: "Bearer #{@token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchUsersByIds invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchUsersByIds 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchUsersByIds 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchUsersByIds 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  def parse_response(parsed_response)
    parsed_response['content'].map do |user_hash|
      [user_hash['id'], "#{user_hash['firstName']} #{user_hash['lastName']}".strip]
    end.to_h
  end

  def page_number
    @page += 1
  end
end
