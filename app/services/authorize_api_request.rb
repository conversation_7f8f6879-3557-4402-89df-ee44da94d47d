class AuthorizeApiRequest
  prepend SimpleCommand

  def initialize(headers = {}, params = {})
    @headers = headers
    @params = params
  end

  def call
    auth_data = decode_auth_token
    thread = Thread.current
    thread[:auth] = auth_data
    thread[:token] = http_auth_header
    auth_data
  end

  private

  attr_reader :headers

  def decode_auth_token
    @decoded_auth_token ||= ParseToken.call(http_auth_header).result
  end

  def http_auth_header
    if headers['Authorization'].present?
      return headers['Authorization'].split(' ').last
    end
    raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
  end
end
