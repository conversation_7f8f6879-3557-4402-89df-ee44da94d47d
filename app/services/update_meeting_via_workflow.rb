# frozen_string_literal: true

class UpdateMeetingViaWorkflow
  prepend SimpleCommand

  def initialize(payload)
    @payload = payload
    @tenant_id = @payload.dig('metadata', 'tenantId')
    @user_id = @payload.dig('metadata', 'userId')
  end

  def call
    add_token_in_current_thread
    meeting = Meeting.find_by(tenant_id: @tenant_id, id: @payload.dig('metadata', 'entityId'))

    return unless meeting.present?

    updated_meeting = meeting.as_json
    updated_meeting.transform_keys! { |key| key.camelize(:lower) }
    meeting_fields = updated_meeting.keys
    @payload['entity'].each do |key, val|
      if meeting_fields.include?(key)
        updated_meeting[key] = val
      end
    end
    if @payload.dig('entity').keys.include?('allDay')
      updated_meeting['allDay'] = @payload.dig('entity', 'allDay', 'allDay')
      updated_meeting['from'] = @payload.dig('entity', 'allDay', 'from')
      updated_meeting['to'] = @payload.dig('entity', 'allDay', 'to')
    elsif @payload.dig('entity').keys.include?('from')
      updated_meeting['allDay'] = @payload.dig('entity', 'from', 'allDay')
      updated_meeting['from'] = @payload.dig('entity', 'from', 'from')
      updated_meeting['to'] = @payload.dig('entity', 'from', 'to')
    end

    existing_participants = meeting.participants.map do |participant|
      {
        id: participant.entity_id,
        name: participant.name,
        email: participant.email,
        entity: participant.entity_type
      }.with_indifferent_access
    end
    # TODO: use participants validator here
    updated_meeting['participants'] = existing_participants
    if @payload.dig('entity', 'participants').present?
      participants = @payload.dig('entity', 'participants').map(&:with_indifferent_access)
      participants.each do |participant|
        participant['entity'] = participant['entity'].downcase
      end

      user = GetUserDetails.call(@user_id, @tenant_id).result
      validated_entities = ParticipantsValidator.call({ user: user, participants: participants }).result
      updated_meeting['participants'] += validated_entities.map do |entity|
        {
          id: entity.entity_id,
          name: entity.name,
          email: entity.email,
          entity: entity.entity_type
        }.with_indifferent_access
      end
    end

    updated_meeting['related_to'] =
      meeting.related_to.map do |lookup|
        { 'id' => lookup.entity_id, 'entity' => lookup.entity_type, 'name' => lookup.name }
      end
    
    if @payload.dig('entity', 'timezone').present? || meeting.time_zone.present?
      updated_meeting['timezone'] = @payload.dig('entity', 'timezone') || { id: meeting.time_zone.entity_id, name: meeting.time_zone.name }
    end

    updated_meeting.delete('timezone_id')
    updated_meeting = updated_meeting.with_indifferent_access

    field_names = @payload.dig('entity', 'customFieldValues')&.keys
    if field_names.present?
      picklist_custom_fields = Field.where(tenant_id: @tenant_id, is_standard: false, field_type: 'PICK_LIST', internal_name: field_names).to_a
      picklist_field_internal_names = picklist_custom_fields.map(&:internal_name)
      picklist_field_values = @payload.dig('entity', 'customFieldValues').slice(*picklist_field_internal_names)
      pl_value_ids = picklist_field_values.values
      picklist_values = PicklistValue.where(tenant_id: @tenant_id, id: pl_value_ids).to_a

      picklist_payload = picklist_field_internal_names.inject({}) do |hash, field_name|
        picklist_value = picklist_values.find { |p_v| p_v.id == picklist_field_values[field_name] }
        hash.merge!(field_name => { id: picklist_value.id, name: picklist_value.display_name }) if picklist_value.present?
        hash
      end
      updated_meeting['customFieldValues'] = meeting.custom_field_values.merge(updated_meeting['customFieldValues']).merge(picklist_payload)
    else
      updated_meeting['customFieldValues'] = meeting.custom_field_values
    end

    Rails.logger.info "UPDATING MEETING WITH PAYLOAD - #{updated_meeting}"
    UpdateMeeting.call(updated_meeting, @payload['metadata'], false)
  end

  private

  def add_token_in_current_thread
    thread = Thread.current
    Rails.logger.info "USER_ID = #{@user_id} || TENANT_ID = #{@tenant_id}"
    token = GenerateToken.call(@user_id, @tenant_id).result
    thread[:token] = token
    thread[:auth] = ParseToken.call(token).result
  end
end
