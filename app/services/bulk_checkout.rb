# frozen_string_literal: true

class BulkCheckout
  include EventsPublisher

  def initialize(params)
    @meeting_ids = params[:meetingIds]
    @latitude = params[:latitude]
    @longitude = params[:longitude]
  end

  def call
    command = GetSecurityContext.call
    unless command.success?
      Rails.logger.error 'Unauthorised: User context missing in BulkCheckout'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless @meeting_ids.present? && @latitude && @longitude
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.missing_parameters')}")
    end

    @auth_data = command.result
    @tenant_id = @auth_data.tenant_id
    @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

    meetings = Meeting.where(id: @meeting_ids, tenant_id: @tenant_id).includes(:meeting_attendances)

    invalid_meetings = []
    successful_meetings = []
    (@meeting_ids - meetings.collect(&:id)).each do |id|
      invalid_meetings << {
        id: id,
        name: nil,
        message: I18n.t('error.not_found.meeting')
      }
    end

    meetings.each do |meeting|
      unless user_can_checkout?(meeting)
        invalid_meetings << {
          id: meeting.id,
          name: meeting.title,
          message: I18n.t('error.forbidden.checkout')
        }

        next
      end

      @meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
      unless @meeting_attendance
        invalid_meetings << {
          id: meeting.id,
          name: meeting.title,
          message: I18n.t('error.invalid.checkout_without_checkin')
        }

        next
      end

      if @meeting_attendance.checked_out?
        invalid_meetings << {
          id: meeting.id,
          name: meeting.title,
          message: I18n.t('error.invalid.meeting_action')
        }

        next
      end

      serialized_meeting = MeetingSerializer.call(meeting, nil, false).result

      @meeting_attendance.assign_attributes(
        checked_out_at: DateTime.now.utc,
        checked_out_latitude: @latitude,
        checked_out_longitude: @longitude
      )

      @meeting_attendance.set_is_checked_out_outside_geofence({ latitude: @latitude, longitude: @longitude }, @user, meeting)

      meeting.updated_by = @user
      if @meeting_attendance.save
        meeting.save
        PublishEvent.call(Event::MeetingUpdated.new(meeting, serialized_meeting, @user.id))

        successful_meetings << {
          id: meeting.id,
          name: meeting.title
        }

        @successful_meeting_attendence = @meeting_attendance
      else
        invalid_meetings << {
          id: meeting.id,
          name: meeting.title,
          message: I18n.t('error.invalid.meeting', error: @meeting_attendance.errors.full_messages.join(', '))
        }
      end
    end


    if successful_meetings.any?
      publish_events(
        'checkout',
        'meeting',
        {
          meeting_ids: successful_meetings.map { |meeting| meeting[:id] },
          tenant_id: @tenant_id,
          current_user_id: @user.id,
          meeting_attendance: @successful_meeting_attendence
        }
      )
    end

    { successful_meetings: successful_meetings, invalid_meetings: invalid_meetings }
  end

  private

  def user_can_checkout?(meeting)
    ([meeting.owner_id] + meeting.participants.select(&:is_a_user?).map(&:entity_id)).include?(@user.id)
  end
end
