require 'rest-client'
class ValidateContacts
  prepend SimpleCommand

  def initialize(contacts = [])
    @contacts = contacts
  end

  def call
    return [] if @contacts.blank?

    command = GetSecurityContext.call
    if command.success?
      verified_contacts = get_contact_summary
      verified_contacts.each do |v_contact|
        contact = @contacts.select{|x| x.entity_id == v_contact["id"]}.first
        if contact.nil?
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.contact')}")
        else
          contact.name = v_contact["name"]
          contact.owner_id = v_contact['ownerId']
          if contact.email
            email = v_contact["emails"].select{|x| x["primary"] }.first.try(:[],"value")
            if email && contact.email != email
              raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.contact_email')}")
            end
          end
        end
      end
      return @contacts
    else
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_contact_summary
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    contact_ids_params = @contacts.collect{|contact| contact.entity_id }.join(",")
    begin
      response = RestClient.get(
        SERVICE_SEARCH + "/v1/summaries/contacts?view=meeting&id=" + contact_ids_params,
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "ValidateContact.get_contact_summary invalid response"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.contact_summary_response')}")
    rescue RestClient::NotFound
      Rails.logger.error "ValidateContact.get_contact_summary sales 404"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.contact_summary_response')}")
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateContact.get_contact_summary sales 500"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.validate_contact')}")
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateContact.get_contact_summary sales 400"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.contact_summary_response')}")
    end
  end
end