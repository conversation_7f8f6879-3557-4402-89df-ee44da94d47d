class CancelMeeting
  include MeetingAccessHelper
  include EventsPublisher
  prepend SimpleCommand
  attr_accessor(:user, :tenant_id, :auth_data, :meeting_id)

  def initialize meeting_id
    @meeting_id = meeting_id
    @meeting_cancelled_at = DateTime.now.utc
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @tenant_id)
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.update_meeting')}") unless user_can_update_meeting?(@meeting)

      old_serialized_meeting = MeetingSerializer.call(@meeting, nil, false, nil, true).result
      begin
        updated_fields = {
          status: CANCELLED,
          updated_by: @user,
          cancelled_at: @meeting_cancelled_at,
          cancelled_by: @user
        }
        updated_fields.merge!(conducted_at: nil, conducted_by: nil) if @meeting.status == CONDUCTED
        if should_cancel_online_meeting?
          cancel_online_meeting_response = Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_CANCEL_EVENT, @user).result
          updated_fields[:provider_link] = cancel_online_meeting_response[:provider_link]
          updated_fields[:provider_meeting_id] = cancel_online_meeting_response[:provider_meeting_id]
        end
        @meeting.update!(updated_fields)
        publish_events('cancel', 'meeting', {meeting: @meeting, old_meeting: old_serialized_meeting, current_user_id: @user.id})
        @meeting
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "Error while cancelling the meeting : #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.cancel_meeting')}")
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in CancelMeeting'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def should_cancel_online_meeting?
    @meeting.online? && @meeting.valid? && @meeting.status != CANCELLED
  end
end
