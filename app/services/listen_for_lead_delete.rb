class ListenForLeadDelete
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(LEAD_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{LEAD_DELETED_EVENT}"
      payload = JSON(payload)
      id = payload["id"]
      tenant_id = payload["tenantId"]
      user_id  = payload["userId"]
      publish_usage = payload['publishUsage']

      UpdateMeetingForEntityDeleteEvent.call(id, tenant_id, user_id, LOOKUP_LEAD, publish_usage)
    end
  end

end
