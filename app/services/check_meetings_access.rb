# frozen_string_literal: true

class CheckMeetingsAccess < ApplicationService
  def initialize(params)
    @params = params
  end

  def check_meetings_access
    filter_params ={
      jsonRule: {
        condition: "AND",
        rules: [
          {
            operator: "in",
            id: "id",
            field: "id",
            type: "long",
            value: @params[:meeting_ids].join(',')
          }
        ],
        valid: true
      },
      fields: ['id']
    }

    get_meeting_ids(filter_params)
  end

  def get_meetings_related_to_entity
    filter_params ={
      jsonRule: {
        condition: "AND",
        rules: [
          {
            operator: 'equal',
            id: "associated#{@params[:relatedToEntityType].capitalize}s",
            field: "associated#{@params[:relatedToEntityType].capitalize}s",
            type: 'long',
            value: @params[:relatedToEntityId]
          }
        ],
        valid: true
      },
      fields: ['id']
    }
    begin
      get_meeting_ids(filter_params)
    rescue StandardError => e
      Rails.logger.error "TenantId: #{Thread.current[:tenant_id]} Error in get_meetings_related_to_entity: #{e.message}"
      []
    end
  end

  private

  def get_meeting_ids(filter_params)
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      user_id = auth_data.user_id
      tenant_id = auth_data.tenant_id

      command = FilterMeetingsQuery.call(
        tenant_id,
        user_id,
        filter_params,
        {
          skip_sorting: true,
          skip_pagination: true,
          permissions: { read_all: auth_data.can_access?('meeting', 'read_all') }
        }
      )
      command.result.map(&:id)
    else
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
