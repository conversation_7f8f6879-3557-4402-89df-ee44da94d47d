class DeleteNote
  prepend SimpleCommand
  attr_accessor(:user, :tenant_id, :auth_data, :meeting_id)

  def initialize(note_id, meeting_id, publish_usage)
    @meeting_id = meeting_id
    @note_id    = note_id
    @publish_usage= [false, 'false', nil].include?(publish_usage) ? false : true
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @user      = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @auth_data.tenant_id)
      @note    = @meeting.notes.find(@note_id) if @meeting.present?

      raise(ActiveRecord::RecordNotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      raise(ActiveRecord::RecordNotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.note')}") if @note.blank?

      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.delete_note')}") unless user_can_delete_note?

      begin
        @note.destroy!
        TenantUsagePublisher.call(@auth_data.tenant_id) if @publish_usage
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "Error while deleting the note : #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.note')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in deleting note"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def user_can_delete_note?
    (
      @auth_data.can_access?('note', 'delete_all') ||
      (
        @auth_data.can_access?('note', 'delete') &&
        (
          @note.created_by_id.eql?(@user.id) ||
          @meeting.owner_id.eql?(@user.id)
        )
      )
    )
  end
end
