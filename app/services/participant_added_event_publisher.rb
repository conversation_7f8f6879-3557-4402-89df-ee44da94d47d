class ParticipantAddedEventPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting, participants_to_relate = [])
    @meeting = meeting
    @participants_to_relate = participants_to_relate
  end

  def call
    if @participants_to_relate.present?  # When meeting is updated
      new_users = @participants_to_relate.select{|participant| participant.is_a_user?}
      new_entities = @participants_to_relate.select{|participant| !participant.is_a_user?}
      existing_entities = (@meeting.participants + @meeting.related_to)
                          .select{|participant| !participant.is_a_user? &&
                            new_entities.map(&:entity).exclude?(participant.entity) }
      existing_users = @meeting.participants.select{|participant| participant.is_a_user? && new_users.map(&:entity).exclude?(participant.entity) }
      if new_users.present?
        relate new_users, existing_entities + new_entities
      end
      if new_entities
        relate existing_users, new_entities
      end
    else         # when meeting is created
      new_users = @meeting.participants.select{|participant| participant.is_a_user?}
      new_entities = @meeting.participants.select{|participant| !participant.is_a_user?}
      new_entities += @meeting.related_to
      relate new_users, new_entities.uniq{|en| en.entity }
    end
  end

  private

    def relate users, entities
      users.reject{|x| x.entity_id == @meeting.owner_id}.each do |user|
        entities.uniq { |entity| entity['entity'] }.each do |entity|
          next if entity.entity_type.eql? LOOKUP_EXTERNAL
          send("publish_meeting_related_to_#{entity.entity_type.downcase}", user, entity)
        end
      end
    end

    def publish_meeting_related_to_lead participant, related_to
      event = Event::MeetingRelatedToLead.new(@meeting, participant, related_to)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingRelatedToLead Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{related_to.entity}"
    end

    def publish_meeting_related_to_deal participant, related_to
      event = Event::MeetingRelatedToDeal.new(@meeting, participant, related_to)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingRelatedToDeal Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{related_to.entity}"
    end

    def publish_meeting_related_to_contact participant, related_to
      event = Event::MeetingRelatedToContact.new(@meeting, participant, related_to)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingRelatedToContact Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{related_to.entity}"
    end

    def publish_meeting_related_to_company participant, related_to
      event = Event::MeetingRelatedToCompany.new(@meeting, participant, related_to)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingRelatedToCompany Meeting ID #{@meeting.id}, User #{participant.id} participant and entity #{related_to.entity}"
    end
end
