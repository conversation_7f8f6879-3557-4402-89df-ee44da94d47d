# frozen_string_literal: true

class ImportMeeting
  prepend SimpleCommand

  def initialize(params)
    @params = params
    @tenant_id = nil
  end

  def call
    command = GetSecurityContext.call
    unless command.success?
      Rails.logger.error 'Unauthorized: User context missing in ImportMeeting'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_medium')}") unless @params.dig(:meeting, :medium) == OFFLINE

    @tenant_id = command.result.tenant_id
    @current_user_id = command.result.user_id
    validate_all_emails
    validate_and_update_related_to
    update_participants
    if @params[:meeting][:organizer].present?
      organizer_look_up = find_organizer
      entity = organizer_look_up[:entity].split('_').first
      id = entity.eql?(LOOKUP_EXTERNAL) ? nil : organizer_look_up[:entity].split('_').last
      @params[:meeting][:organizer] = {
        entity: entity,
        id: id,
        name: organizer_look_up[:name],
        email: organizer_look_up[:email],
        owner_id: organizer_look_up[:owner_id]
      }

    end
    validate_and_update_owner
    @params[:meeting][:imported_by_id] = @current_user_id
    CreateMeeting.call(@params[:meeting].except(*[:relatedToLeads, :relatedToCompanies, :relatedToContacts]), imported_meeting = true).result
  end

  def validate_all_emails
    %w[relatedToLeads relatedToContacts relatedToCompanies participants].each do |field|
      @params.dig(:meeting, field)&.each do |email|
        unless email.match(EMAIL_REGEXP)
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.email', email: email, field: field)}")
        end
      end
    end

    %w[owner organizer].each do |field|
      email = @params.dig(:meeting, field)
      next if email.blank?
      unless email.match(EMAIL_REGEXP)
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.email', email: email, field: field)}")
      end
    end
  end

  def validate_and_update_related_to
    search_leads = SearchLeads.call(@params.dig(:meeting, :relatedToLeads), @tenant_id).result
    if search_leads.present? && search_leads[:unmatched].present?
      emails = search_leads[:unmatched].map { |lead| lead[:email] }
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.emails', entity: LOOKUP_LEAD, emails: emails.join(', '))}")
    end

    search_contacts = SearchContacts.call(@params.dig(:meeting, :relatedToContacts), @tenant_id).result
    if search_contacts.present? && search_contacts[:unmatched].present?
      emails = search_contacts[:unmatched].map { |contact| contact[:email] }
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.emails', entity: LOOKUP_CONTACT, emails: emails.join(', '))}")
    end

    search_companies = SearchCompanies.call(@params.dig(:meeting, :relatedToCompanies), @tenant_id).result
    if search_companies.present? && search_companies[:unmatched].present?
      emails = search_companies[:unmatched].map { |company| company[:email] }
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.emails', entity: LOOKUP_COMPANY, emails: emails.join(', '))}")
    end

    related_to = []
    search_leads.present? && search_leads[:matched].each do |lead|
      related_to << {
        entity: lead[:entity].split('_').first,
        id: lead[:entity].split('_').last,
        name: lead[:name],
        owner_id: lead[:owner_id]
      }
    end

    search_contacts.present? && search_contacts[:matched].each do |contact|
      related_to << {
        entity: contact[:entity].split('_').first,
        id: contact[:entity].split('_').last,
        name: contact[:name],
        owner_id: contact[:owner_id]
      }
    end

    search_companies.present? && search_companies[:matched].each do |company|
      related_to << {
        entity: company[:entity].split('_').first,
        id: company[:entity].split('_').last,
        name: company[:name],
        owner_id: company[:owner_id]
      }
    end

    @params[:meeting][:related_to] = related_to
  end

  def update_participants
    participants = []
    search_users = SearchUsersByEmails.call(@params.dig(:meeting, :participants), @tenant_id).result
    search_users[:matched]&.each do |user|
      participants << {
        entity: LOOKUP_USER,
        id: user[:entity].split('_').last,
        name: user[:name],
        email: user[:email]
      }
    end

    if search_users[:unmatched].present?
      unmatched_user_emails = search_users[:unmatched].map { |user| user[:email] }
      search_contacts = SearchContacts.call(unmatched_user_emails, @tenant_id).result
      search_contacts.present? && search_contacts[:matched].each do |contact|
        participants << {
          entity: LOOKUP_CONTACT,
          id: contact[:entity].split('_').last,
          name: contact[:name],
          email: contact[:email],
          owner_id: contact[:owner_id]
        }
      end

      if search_contacts[:unmatched].present?
        unmatched_contact_emails = search_contacts[:unmatched].map { |contact| contact[:email] }
        search_leads = SearchLeads.call(unmatched_contact_emails, @tenant_id).result
        search_leads.present? && search_leads[:matched].each do |lead|
          participants << {
            entity: LOOKUP_LEAD,
            id: lead[:entity].split('_').last,
            name: lead[:name],
            email: lead[:email],
            owner_id: lead[:owner_id]
          }
        end

        search_leads[:unmatched].each do |entity|
          participants << {
            entity: LOOKUP_EXTERNAL,
            name: entity[:email],
            email: entity[:email]
          }
        end
      end
    end

    @params[:meeting][:participants] = participants
  end

  def validate_and_update_owner
    validated_user =
      if @params.dig(:meeting, :owner).present?
        search_users = SearchUsersByEmails.call([@params.dig(:meeting, :owner)], @tenant_id).result

        if search_users[:unmatched].present?
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.emails', entity: 'Owner user', emails: @params.dig(:meeting, :owner))}")
        end

        user = search_users[:matched].first
        GetUserDetails.call(user[:entity].split('_').last, @tenant_id, authorization_needed = false, name = user[:name]).result
      else
        GetUserDetails.call(@current_user_id, @tenant_id).result
      end

    @params[:meeting][:owner] = validated_user
  end

  def find_organizer
    email = @params[:meeting][:organizer]
    existing_user = LookUp.where(email: email, tenant_id: @tenant_id).where("entity like '#{LOOKUP_USER}'").first
    return { entity: existing_user.entity, email: existing_user.email, name: existing_user.name, tenant_id: existing_user.look_up.tenant_id } if existing_user.present?

    search_user = SearchUsersByEmails.call([email], @tenant_id).result
    return search_user[:matched][0] if search_user[:matched].present?

    search_contact = SearchContacts.call([email], @tenant_id).result
    return search_contact[:matched][0] if search_contact[:matched].present?

    search_lead = SearchLeads.call([email], @tenant_id).result
    return search_lead[:matched][0] if search_lead[:matched].present?

    search_external = SearchExternal.call([email], @tenant_id).result
    return search_external[:matched][0] || search_external[:unmatched][0]
  end
end
