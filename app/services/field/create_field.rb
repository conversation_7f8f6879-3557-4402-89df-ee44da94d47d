class Field::<PERSON><PERSON><PERSON>ield
  prepend SimpleCommand
  include EventsPublisher
  include <PERSON>Helper

  def initialize(params)
    @params = UnderscorizeHashKeys.call(params.to_h).result.with_indifferent_access
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.create_custom_field')}") unless auth_data.can_access?('customField', 'write')

      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result
      field = Field.new(@params.slice(:display_name, :description))
      field.internal_name = generate_internal_name('cf', @params[:display_name])
      field.is_standard = false
      field.created_by = field.updated_by = user
      field.is_filterable = @params[:filterable]
      field.is_sortable = @params[:sortable]
      field.is_required = @params[:required]
      field.field_type = @params[:type]
      field.tenant_id = user.tenant_id

      if @params[:pick_lists].present?
        picklist = Picklist.new(
          display_name: "#{field.display_name} Picklist",
          internal_name: generate_internal_name('cp', "#{field.display_name} Picklist"),
          field: field,
          tenant_id: field.tenant_id
        )

        picklist_values = @params[:pick_lists].inject([]) do |arr, picklist_value|
          if picklist_value[:display_name].present?
            arr << { display_name: picklist_value[:display_name] }.to_h.merge(
              picklist_id: picklist.id,
              tenant_id: picklist.tenant_id,
              disabled: false,
              internal_name: generate_internal_name('cpv', picklist_value[:display_name])
            )
          end
          arr
        end
      end

      begin
        ActiveRecord::Base.transaction do
          field.save!
          if @params[:pick_lists].present?
            picklist.save!
            picklist_values.map! do |picklist_value|
              picklist_value.merge!(picklist_id: picklist.id)
              picklist_value
            end
            picklist_values.each { |picklist_value| PicklistValue.create!(picklist_value) }
          end
        end
        publish_events('create', 'field', { field: field.reload, tenant_id: field.tenant_id })
        field
      rescue StandardError => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.cannot_create_entity}||#{I18n.t('error.invalid.field')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in Create Field"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
