class Field::UpdateFieldStatus
  prepend SimpleCommand
  include <PERSON><PERSON>ublisher

  def initialize(id, action)
    @id = id
    @action = action
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.update_custom_field')}") unless auth_data.can_access?('customField', 'update')

      field = Field.find_by(id: @id, tenant_id: auth_data.tenant_id)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.field')}") if field.nil? || field.is_standard?
      old_serialized_field_data = V2::FieldSerializer.call(field).result

      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result

      begin
        ActiveRecord::Base.transaction do
          update_hash = { active: (@action == ACTIVATE), updated_by: user }
          if @action == DEACTIVATE
            update_hash.merge!(is_filterable: false, is_sortable: false, is_required: false)
          end
          field.update!(update_hash)
        end
        publish_events("#{@action}", 'field', { field: field.reload, tenant_id: field.tenant_id, old_serialized_field_data: old_serialized_field_data })
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.field')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in meeting field #{@action}"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
