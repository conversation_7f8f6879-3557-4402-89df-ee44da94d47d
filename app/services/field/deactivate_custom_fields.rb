class Field::DeactivateCustomFields
  prepend SimpleCommand
  include EventsPublisher

  def initialize(tenant_id)
    @tenant_id = tenant_id
  end

  def call
    custom_fields = Field.where(tenant_id: @tenant_id, is_standard: false, active: true)
    custom_fields_old_serialized_hash = custom_fields.map { |field| [field.id, V2::FieldSerializer.call(field).result] }.to_h
    custom_field_ids = custom_fields.map(&:id)
    custom_fields.update_all(
      active: false,
      is_filterable: false,
      is_sortable: false,
      is_required: false
    )

    Field.where(tenant_id: @tenant_id, id: custom_field_ids).each do |custom_field|
      publish_events(
        DEACTIVATE,
        'field',
        {
          field: custom_field,
          tenant_id: custom_field.tenant_id,
          old_serialized_field_data: custom_fields_old_serialized_hash[custom_field.id]
        }
      )
    end
  end
end
