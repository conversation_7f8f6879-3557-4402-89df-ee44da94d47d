# frozen_string_literal: true

require 'rest-client'

class SearchCompanies
  prepend SimpleCommand

  def initialize(email_ids, tenant_id)
    @email_ids = email_ids
    @tenant_id = tenant_id
  end

  def call
    return [] if @email_ids.blank?
    EntitySearchResultParser.call(@email_ids, search_companies, LOOKUP_COMPANY, @tenant_id).result
  end

  private

  def search_companies
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?

    token = command.result
    payload = { fields: %w[id firstName lastName emails ownedBy] }
    rules = []
    @email_ids.each do |email|
      rules << {
        id: 'multi_field',
        field: 'multi_field',
        type: 'multi_field',
        input: 'multi_field',
        operator: 'multi_field',
        value: email
      }
    end

    payload[:jsonRule] = { rules: rules, condition: 'OR', valid: true }

    begin
      response = RestClient.post(
        "#{SERVICE_SEARCH}/v1/search/company?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body) unless response.nil?
      Rails.logger.error 'SearchCompanies invalid response'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error 'SearchCompanies 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'SearchCompanies 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'SearchCompanies 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
