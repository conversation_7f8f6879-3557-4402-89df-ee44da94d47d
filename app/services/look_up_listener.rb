class LookUpListener
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    ListenForUserUpdate.call
    ListenForLeadUpdate.call
    ListenForContactUpdate.call
    ListenForDealUpdate.call
    ListenForCompanyUpdate.call
    ListenForDailySchedulerEvent.call
    ListenForUsageLimitChanged.call
    ListenForWorkflowMeetingUpdate.call
    ListenForCompanyReassign.call
    ListenForDealReassign.call
    ListenForTeamUpdatedV2.call
    Listeners::CreateAllShareRulesForHierarchy.call
    Listeners::DeleteAllShareRulesForHierarchy.call
    ListenForCheckoutMeetings.call
  end
end
