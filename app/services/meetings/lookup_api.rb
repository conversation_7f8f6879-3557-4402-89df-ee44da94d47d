# frozen_string_literal: true

module Meetings
  class LookupApi
    prepend SimpleCommand

    def initialize(params)
      @params = params.to_h.with_indifferent_access
      @params[:view] = [DEFAULT_VIEW, SHARE_VIEW, CHECKIN_VIEW].include?(@params[:view]) ? @params[:view] : DEFAULT_VIEW
    end

    def call
      command = GetSecurityContext.call
      if command.success?
        @auth_data = command.result

        FilterMeetingsQuery.call(@auth_data.tenant_id, @auth_data.user_id, filter_params, { permissions: permissions, view: @params[:view] }).result
      else
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end
    end

    private

    def filter_params
      fields = %w[id title]

      if @params[:view] == CHECKIN_VIEW
        fields = %w[id title location_latitude location_longitude]
      end

      {
        fields: fields,
        page: 1,
        size: 5,
        jsonRule: json_rules
      }.with_indifferent_access
    end

    def json_rules
      rules = []
      if @params[:q].present?
        rules << {
          operator: 'contains',
          id: 'title',
          field: 'title',
          type: 'string',
          value: @params[:q]
        }
      end

      if @params[:view] == CHECKIN_VIEW
        time_zone =
          if @params[:timezone]
            TZInfo::Timezone.all_identifiers.include?(@params[:timezone]) ? @params[:timezone] : UserSettingService.new.fetch(@auth_data.user_id)[:time_zone]
          else
            UserSettingService.new.fetch(@auth_data.user_id)[:time_zone]
          end

        rules += [
          {
            operator: 'not_equal',
            id: 'status',
            field: 'status',
            type: 'string',
            value: CANCELLED
          },
          {
            operator: 'greater_or_equal',
            id: 'from',
            field: 'from',
            type: 'date',
            value: DateTime.now.in_time_zone(time_zone).beginning_of_day.utc
          },
          {
            operator: 'equal',
            field: 'participants',
            id: 'participants',
            value: {
              entity: 'user',
              id: @auth_data.user_id
            },
            type: 'participants_lookup'
          }
        ]
      end

      if rules.any?
        {
          condition: 'AND',
          valid: true,
          rules: rules
        }
      else
        nil
      end
    end

    def permissions
      {
        reshare: @auth_data.can_access?('meeting', 'reshare'),
        create_share_rule: @auth_data.can_access?('shareRule', 'write'),
        read_all: @auth_data.can_access?('meeting', 'read_all'),
        update_all:  @auth_data.can_access?('meeting', 'update_all')
      }
    end
  end
end
