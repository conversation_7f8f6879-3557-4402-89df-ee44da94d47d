class GetExternalLookups
  prepend SimpleCommand

  def initialize(query_string = nil)
    @query_string = query_string&.downcase
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      @tenant_id = auth_data.tenant_id
      filter_external_lookups
    else
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def filter_external_lookups
    look_ups = LookUp.where(tenant_id: @tenant_id).
      where("entity like ?", "#{LOOKUP_EXTERNAL}_%")

    look_ups = look_ups.where("(lower(email) like ? or lower(name) like ?)", "%#{@query_string}%", "%#{@query_string}%") if @query_string
    look_ups.order(updated_at: :desc).limit(5)
  end
end
