class MeetingScheduledRelatedToEntityPublisher
  prepend SimpleCommand
  attr_reader :meeting

  def initialize(meeting)
    @meeting = meeting
  end

  def call
    Rails.logger.info "Meeting Service: MeetingSchedulededRelatedToEntityPublisher called"
    data = MeetingSerializer.call(meeting).result.except("recordActions")
    event = Event::MeetingScheduledRelatedToEntity.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingScheduledRelatedToEntity data #{event.to_json}"
  end
end
