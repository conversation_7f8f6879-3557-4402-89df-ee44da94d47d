# frozen_string_literal: true

class ListenForTenantCreation
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, TENANT_CREATED_EVENT, TENANT_CREATED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{TENANT_CREATED_EVENT}"
      payload = JSON(payload)
      tenant_id = payload["tenantId"]
      user_id = payload["userId"]
      name = payload['firstName'].to_s + " " + payload['lastName'].to_s
      CreateMeetingFieldsForTenant.new(tenant_id, user_id, false, name).call
    end
  end
end
