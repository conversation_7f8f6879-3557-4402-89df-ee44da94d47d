class MeetingParticipantRsvpUpdatedEventPublisher
  prepend SimpleCommand

  def initialize(meeting, participant)
    @meeting = meeting
    @participant = participant
  end

  def call
    event = Event::MeetingParticipantRsvpUpdated.new(@meeting, @participant)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingParticipantRsvpUpdated event for: #{@meeting.inspect} and #{@participant.inspect} participant"
    Rails.logger.info "Event::MeetingParticipantRsvpUpdated event data: #{event.to_json}"
  end
end
