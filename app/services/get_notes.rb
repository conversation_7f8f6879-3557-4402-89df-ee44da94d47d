class GetNotes
  include Meeting<PERSON>ccessHelper
  prepend SimpleCommand

  def initialize(meeting_id, page_params)
    @page_params = page_params
    @meeting_id = meeting_id
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @user_id = @auth_data.user_id
      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @auth_data.tenant_id)
      raise(ActiveRecord::RecordNotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      if can_user_access_notes?
        notes = @meeting.notes.order(created_at: :desc)
        notes = paginate(notes)
      else
        raise(ExceptionHandler::NoteAccessNotAllowed, "#{ErrorCode.note_access_not_allowed}||#{I18n.t('error.note_access_not_allowed')}")
      end
    else
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private
    def can_user_access_notes?
      (user_can_read_meeting?(@meeting) && @auth_data.can_access?('note', 'read')) || @auth_data.can_access?('note', 'read_all')
    end

    def paginate(notes)
      page = @page_params[:page].present? ? @page_params[:page] : 1
      size = @page_params[:size].present? ? @page_params[:size] : 10

      notes.page(page.to_i).per_page(size.to_i)
    end
end
