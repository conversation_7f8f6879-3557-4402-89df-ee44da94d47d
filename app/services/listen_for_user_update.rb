class ListenForUserUpdate
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    subscribe_user_name_updated_event
    subscribe_user_email_updated_event
  end

  private
    def subscribe_user_name_updated_event
      RabbitmqConnection.subscribe(USER_EXCHANGE, USER_NAME_UPDATED_EVENT, USER_NAME_UPDATED_QUEUE) do |payload|
        Rails.logger.info "Received message #{payload} for #{USER_NAME_UPDATED_EVENT}"
        payload = JSON(payload)
        id = payload["userId"]
        tenant_id = payload["tenantId"]
        name = "#{payload['firstName']} #{payload['lastName']}".strip

        lookup_params = {}
        lookup_params[:name] = name
        UpdateLookUp.call(tenant_id, id, LOOKUP_USER, lookup_params)
        User.where(id: id, tenant_id:tenant_id).update_all(name: name)
      end
    end

    def subscribe_user_email_updated_event
      RabbitmqConnection.subscribe(USER_EXCHANGE, USER_EMAIL_UPDATED_EVENT, USER_EMAIL_UPDATED_QUEUE) do |payload|
        Rails.logger.info "Received message #{payload} for #{USER_EMAIL_UPDATED_EVENT}"
        payload = JSON(payload)
        id = payload["userId"]
        tenant_id = payload["tenantId"]

        lookup_params = {}
        lookup_params[:email] = payload["email"]
        UpdateLookUp.call(tenant_id, id, LOOKUP_USER, lookup_params)
      end
    end
end
