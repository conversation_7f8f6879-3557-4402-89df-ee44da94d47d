class ConductMeeting
  include MeetingAccessHelper
  include EventsPublisher
  prepend <PERSON><PERSON>ommand

  def initialize(meeting_id)
    @meeting_id = meeting_id
    @meeting_conducted_at = DateTime.now.utc
  end

  def call
    command = GetSecurityContext.call
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    @auth_data = command.result
    @tenant_id = @auth_data.tenant_id
    @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

    @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @tenant_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless user_can_update_meeting?(@meeting)

    old_serialized_meeting = MeetingSerializer.call(@meeting, nil, false, nil, true).result
    @meeting.status = CONDUCTED
    @meeting.conducted_at = @meeting_conducted_at
    @meeting.conducted_by = @user
    @meeting.updated_by = @user
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: @meeting.errors.full_messages.join(', '))}") unless @meeting.valid? && !meeting_cancelled?

    @meeting.save
    publish_events('conduct', 'meeting', {meeting: @meeting, old_meeting: old_serialized_meeting, current_user_id: @user.id})

    @meeting
  end

  private

  def meeting_cancelled?
    @meeting.status_was == CANCELLED
  end
end
