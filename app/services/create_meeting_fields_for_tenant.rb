# frozen_string_literal: true

class CreateMeetingFieldsForTenant
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(tenant_id, user_id, authorization_needed = true, name = nil)
    @tenant_id = tenant_id
    @user_id = user_id
    @authorization_needed = authorization_needed
    @name = name
  end

  def call
    user = GetUserDetails.call(@user_id, @tenant_id, @authorization_needed, @name).result

    fields = load_fields

    fields.each do |_, field_data|
      if PICKLIST_FIELD_TYPES.exclude?(field_data['field_type'])
        begin
          ActiveRecord::Base.transaction do
            Field.create!(field_data.merge('tenant_id' => @tenant_id, 'created_by' => user, 'updated_by' => user))
          end
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error "Error while creating field for tenant id #{@tenant_id} field #{field_data.inspect} Message: #{e.message}"
        end
      elsif PICKLIST_FIELD_TYPES.include?(field_data['field_type'])
        begin
          ActiveRecord::Base.transaction do
            field = Field.create!(
              field_data.reject { |attr, _| attr == 'picklist' }
                        .merge('tenant_id' => @tenant_id, 'created_by' => user, 'updated_by' => user)
            )

            picklist = Picklist.create!(
              field_data['picklist']
              .reject { |attr, _|  attr == 'picklist_values' }
              .merge('field' => field, 'tenant_id' => @tenant_id)
            )

            field_data['picklist']['picklist_values'].each do |_, value_data|
              PicklistValue.create!(value_data.merge('picklist' => picklist, 'tenant_id' => @tenant_id))
            end
          end
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error "Error while creating field for tenant id #{@tenant_id} field #{field_data.inspect} Message: #{e.message}"
        end
      end
    end

    fields.blank? ? "Fields Existing for Tenant" : "Fields Created for Tenant"
  end

  private

  def load_fields
    fields = YAML.load_file("#{Rails.root}/config/meeting_fields.yml")

    existing_fields = Field.where(tenant_id: @tenant_id).pluck(:internal_name)
    fields = fields.reject { |field, _| existing_fields.include?(field) }

    fields
  end
end
