# frozen_string_literal: true

class ListenForDailySchedulerEvent
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, MEETING_SCHEDULER_EVENT, MEETING_SCHEDULER_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{MEETING_SCHEDULER_QUEUE}"
      payload = JSON(payload)

      MarkMeetingAsMissed.call()
      Calendar::RenewAccounts.call()
      OutlookChangeLog.delete_all

      current_time = '03:00 AM'.to_time
      meetings = Meeting.where(:from => current_time..(current_time + 24.hours))
      meetings_by_minute = {}
      meetings.each do |meeting|
        curr_minute = ((meeting['from'] - current_time) / 60).ceil
        if meetings_by_minute[curr_minute].present?
          meetings_by_minute[curr_minute] << meeting.id
        else
          meetings_by_minute[curr_minute] = [meeting.id]
        end
      end

      meetings_by_minute.each do |curr_minute, meeting_ids|
        MeetingScheduledPublisherJob.set(wait: curr_minute.minutes).perform_later(meeting_ids)
      end
    end
  end
end
