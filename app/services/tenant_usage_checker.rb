# frozen_string_literal: true

require 'rest-client'

class TenantUsageChecker
  prepend SimpleCommand

  def initialize(tenant_id)
    @tenant_id = tenant_id
  end

  def call
    return true unless @tenant_id.present?

    command = GetSecurityContext.call('token')
    unless command.success?
      Rails.logger.error "Unauthorized: User context missing in TenantUsageChecker tenant_id: #{@tenant_id}"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    token = command.result
    usage_data = fetch_tenant_usage(token)
    
    if usage_limit_reached?(usage_data)
      Rails.logger.error "Usage limit reached for tenant #{@tenant_id}: #{usage_data}"
      raise(ExceptionHandler::UsageLimitExceeded, "#{ErrorCode.usage_limit_exceeded}||#{I18n.t('error.usage_limit_exceeded')}")
    end

    true
  end

  private

  def fetch_tenant_usage(token)
    begin
      response = RestClient.get(
        "#{SERVICE_IAM}/v1/tenants/usage",
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )

      unless response.nil?
        usage_data = JSON.parse(response.body)
        return usage_data
      end

      Rails.logger.error "TenantUsageChecker: Invalid response from usage API tenant_id: #{@tenant_id}"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.tenant_usage')}")

    rescue RestClient::ExceptionWithResponse => e
      Rails.logger.error "TenantUsageChecker: Error fetching usage data: tenant_id: #{@tenant_id}, #{e.message}"
      case e.response&.code
      when 401, 403
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      when 404
        raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.tenant_usage')}")
      else
        raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.tenant_usage')}")
      end
    rescue StandardError => e
      Rails.logger.error "TenantUsageChecker: Unexpected error  tenant_id: #{@tenant_id}: #{e.message}"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.tenant_usage')}")
    end 
  end

  def usage_limit_reached?(usage_data)
    return false unless usage_data.present? && usage_data['records'].present?

    records = usage_data['records']
    used = records['used'].to_i
    total = records['total'].to_i

    return false if total <= 0

    used >= total
  end
end
