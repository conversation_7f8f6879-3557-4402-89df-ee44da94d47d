# frozen_string_literal: true

class ProcessMicrosoftWebhook
  prepend SimpleCommand

  def initialize(params)
    @params = params
  end

  def call
    connected_account = load_connected_account
    return if connected_account.blank?

    return unless connected_account.active?

    if @params['changeType'] == 'created'
      sleep(1)
    end

    unless @params['changeType'] == 'created'
      return unless Meeting.exists?(provider_meeting_id: @params.dig('resourceData', 'id'))
    end

    # for cancelled meeting, we get webhook for deleted event
    if @params['changeType'] == 'deleted'
      ProcessEvent.call(
        connected_account,
        {
          id: @params.dig('resourceData', 'id'),
          status: 'cancelled'
        })

      return
    end

    event = Calendar::Microsoft::GetEvent.call(connected_account, @params.dig('resourceData', 'id')).result[:data]
    return if event.blank?

    change_log = OutlookChangeLog.find_by(resource_id: event['id'], change_key: event['changeKey'])
    return if change_log.present?

    return unless OutlookChangeLog.create(resource_id: event['id'], change_key: event['changeKey'])

    modifield_event = {}
    modifield_event['id'] = event.dig('id')
    modifield_event['attendees'] =
      event['attendees'].map do |attendee|
        {
          email: attendee['emailAddress']['address'],
          responseStatus: get_rsvp_response(attendee['status']['response'])
        }
      end

    modifield_event['organizer'] = { email: event.dig('organizer', 'emailAddress', 'address') }
    modifield_event['htmlLink'] = event.dig('onlineMeeting', 'joinUrl')
    modifield_event['start'] = event['start']
    modifield_event['end'] = event['end']
    modifield_event['summary'] = event['subject']
    modifield_event['description'] = event.dig('body', 'content')
    modifield_event['location'] = event.dig('location', 'displayName')
    modifield_event['medium'] = MICROSOFT_TEAMS_PROVIDER
    modifield_event['all_day'] = event['isAllDay']
    ProcessEvent.call(connected_account, modifield_event)
  end

  private

  def load_connected_account
    Rails.logger.info @params
    ConnectedAccount.find_by(provider_subscription_resource_id: @params.dig('subscriptionId'), active: true)
  end

  def get_rsvp_response(status)
    case status
    when 'none'
      return 'needsAction'
    when 'tentativelyAccepted'
      return 'tentative'
    when 'accepted'
      return 'accepted'
    when 'declined'
      return 'declined'
    end
  end
end
