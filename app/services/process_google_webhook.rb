class ProcessGoogleWebhook
  prepend SimpleCommand

  def initialize(params)
    @params = params
  end

  def call
    connected_account = load_connected_account
    return if connected_account.blank?
    return unless connected_account.active
    events = Calendar::Google::FetchEvents.call(connected_account).result
    process_events(events[:items], connected_account) if events[:items].present?
    connected_account.update(next_sync_token: events[:next_sync_token])
  end

  private
  def load_connected_account
    ConnectedAccount.find_by(provider_subscription_resource_id: @params['x-goog-resource-id'], active: true)
  end

  def process_events events, connected_account
    events.each do |event|
      begin
        event['medium'] = GOOGLE_PROVIDER
        ProcessEvent.call(connected_account, event)
      rescue Exception => e
        Rails.logger.info "Exception while syncing meeting for #{connected_account.id} | #{event} | #{e.to_s}"
      end
    end
  end
end
