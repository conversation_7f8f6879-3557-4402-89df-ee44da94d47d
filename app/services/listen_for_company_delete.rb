class ListenForCompanyDelete
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(COMPANY_EXCHANGE, COMPANY_DELETED_EVENT, COMPANY_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message - #{payload} from #{COMPANY_EXCHANGE} for #{COMPANY_DELETED_EVENT}"
      payload = JSON(payload)
      id = payload.dig('metadata', 'entityId')
      tenant_id = payload.dig('metadata', 'tenantId')
      user_id = payload.dig('metadata', 'userId')
      publish_usage = payload['publishUsage']

      UpdateMeetingForEntityDeleteEvent.call(id, tenant_id, user_id, LOOKUP_COMPANY, publish_usage)
    end
  end
end
