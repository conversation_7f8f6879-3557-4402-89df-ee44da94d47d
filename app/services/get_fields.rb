class GetFields
  prepend SimpleCommand

  def initialize(params = {})
    @params = params
  end

  def call
    command = GetSecurityContext.call
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    auth_data = command.result
    tenant_id = auth_data.tenant_id
    qry = Field.where(tenant_id: tenant_id)
    qry = qry.where.not(internal_name: 'tenant_id')
    qry = qry.where(is_standard: false) if @params['custom-only'] == 'true'
    result = qry.order(created_at: :asc)
  end
end
