# frozen_string_literal: true

class ListenForFieldSalesUserRemoved
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(FIELD_SALES_EXCHANGE, FIELD_SALES_USER_REMOVED_EVENT, FIELD_SALES_USER_REMOVED_QUEUE) do |payload|
      payload = JSON(payload)

      Rails.logger.info "Received field sales user removed event for users - #{payload['userIds']}"

      users = User.where(id: payload['userIds'], tenant_id: payload['tenantId'])
      
      users.each do |user|
        geofence_config = user.geofence_config || {}
        geofence_config[:fieldSalesEnabled] = false
        user.update(geofence_config: geofence_config)
      end
    end
  end
end
