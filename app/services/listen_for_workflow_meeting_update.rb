# frozen_string_literal: true

class ListenForWorkflowMeetingUpdate
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(WORKFLOW_EXCHANGE, WORKFLOW_MEETING_UPDATED_EVENT, WORK<PERSON>OW_MEETING_UPDATED_QUEUE) do |payload, metadata|
      Rails.logger.info "Received message #{payload.inspect} for #{WORKFLOW_MEETING_UPDATED_QUEUE}"
      payload = JSON(payload)
      metadata_headers = metadata.with_indifferent_access[:headers]

      response = {
        eventId: payload.dig('metadata', 'eventId'),
        status: SUCCESS,
        statusCode: 200
      }

      result = ApplicationService.new.handle_errors do        
        UpdateMeetingViaWorkflow.call(payload)
        {
          error_details: nil
        }
      end

      if(result[:error_details].present?)
        response[:status] = FAILED
        response[:statusCode] = Rack::Utils.status_code(result[:status])
        response[:errorCode] = result.dig(:error_details, :error_code)
        response[:errorMessage] = result.dig(:error_details, :message)
      end

      WorkflowExecutionStatusUpdatePublisher.call(response, { reply_to_exchange: metadata_headers[:replyToExchange], reply_to_event: metadata_headers[:replyToEvent] })
    end
  end
end
