class ListenForDealUpdate
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_NAME_UPDATED_EVENT, DEAL_NAME_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message - #{payload} from ex.iam for #{DEAL_NAME_UPDATED_EVENT}"
      payload = JSON(payload)
      id = payload["id"]
      tenant_id = payload["tenantId"]

      lookup_params = {}
      lookup_params[:name] = payload["name"]
      UpdateLookUp.call(tenant_id, id, LOOKUP_DEAL, lookup_params)
    end
  end
end
