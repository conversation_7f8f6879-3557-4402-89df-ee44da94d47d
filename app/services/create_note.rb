class CreateNote
  include MeetingAccessHelper
  prepend SimpleCommand

  def initialize(params, meeting_id)
    @params = params
    @meeting_id = meeting_id
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @auth_data.tenant_id)

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      if user_allowed_to_create_note
        begin
          ActiveRecord::Base.transaction do
            note = @meeting.notes.new(@params)
            note.created_by = @user
            note.tenant_id = @auth_data.tenant_id
            note.save!
            note
          end
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error e.message
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.note')}")
        end
      else
        raise(ExceptionHandler::NoteCreateNotAllowed, "#{ErrorCode.note_create_not_allowed}||#{I18n.t('error.note_create_not_allowed')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in CreateNote"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def user_allowed_to_create_note
    user_can_read_meeting?(@meeting) && @auth_data.can_access?('note', 'write')
  end
end
