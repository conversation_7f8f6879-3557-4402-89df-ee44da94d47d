# frozen_string_literal: true

module ShareRules
  class Delete
    include ShareRules::Helper
    include EventsPublisher

    def initialize(share_rule_id)
      @share_rule_id = share_rule_id
    end

    def call
      authorize_and_get_auth

      @user_id = @auth_data.user_id
      @tenant_id = @auth_data.tenant_id

      unless @auth_data.can_access?('shareRule', 'delete')
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      share_rule = ShareRule.find_by(id: @share_rule_id, tenant_id: @tenant_id)

      unless share_rule.present?
        raise(ExceptionHandler::NotFound, "#{ErrorCode.share_rule_not_found}||#{I18n.t('error.not_found.share_rule')}")
      end

      unless !share_rule.system_default && (share_rule.created_by_id == @user_id || @auth_data.can_access?('shareRule', 'delete_all'))
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      old_serialized_share_rule = ShareRuleSerializer.new(share_rule, payload_for_event: true).call

      begin
        ActiveRecord::Base.transaction do
          share_rule.destroy!
          publish_events('delete', 'share_rule', { serialized_share_rule: old_serialized_share_rule, user_id: @user_id, tenant_id: @tenant_id })
        end
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(
          ExceptionHandler::InvalidDataError,
          "#{ErrorCode.invalid_share_rule}||#{I18n.t('error.invalid.share_rule', error: e.message.gsub(VALIDATION_FAILED_ERROR, ''))}"
        )
      end
    end
  end
end
