# frozen_string_literal: true

module ShareRules
  module Helper
    include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    def authorize_and_get_auth
      command = GetSecurityContext.call
      unless command.success?
        Rails.logger.error 'Unauthorised: User context missing in ShareRules::Create'
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      @auth_data = command.result
    end

    def validate_params
      if @params[:id].present?
        @meeting = Meeting.find_by(id: @params[:id], tenant_id: @tenant_id)
        raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") unless @meeting.present?
        @params[:from] ||= {}
        @params[:from][:id] ||= @meeting.owner_id
        @params[:from][:type] ||= USER
      end

      if (@params.dig(:from, :type) == USER && @params.dig(:from, :id) == @params.dig(:to, :id))
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_from_and_to_users}||#{I18n.t('share_rules.invalid.from_and_to_cannot_be_same')}")
      end

      actions = @params[:actions].compact_blank
      if (actions.keys - ALLOWED_MEETING_SHARE_ACTIONS).any? || (ALLOWED_MEETING_SHARE_ACTIONS - actions.keys).any?
        Rails.logger.error 'Actions not allowed'
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_share_actions}||#{I18n.t('share_rules.invalid.actions')}")
      end
      @params[:actions] = actions
    end

    def validate_share_rule_participants
      GetUserDetails.call(@params.dig(:from, :id), @tenant_id).result
      if @params.dig(:to, :type) == USER
        GetUserDetails.call(@params.dig(:to, :id), @tenant_id).result
      else
        GetTeamDetails.new(@params.dig(:to, :id), @tenant_id).call
      end
    end

    def find_meeting_validate_access
      if @meeting.owner_id != @params.dig(:from, :id)
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_source_user}||#{I18n.t('share_rules.invalid.invalid_source_user')}")
      end

      if [@meeting.created_by_id, @meeting.owner_id, @meeting.organizer.entity_id].include?(@params.dig(:to, :id))
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_target_user}||#{I18n.t('share_rules.invalid.target_user_cannot_be_owner')}")
      end

      unless user_can_share_meeting?(@meeting)
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.meeting_details')}")
      end
    end

    def validate_can_share_all_meetings
      unless @params.dig(:from, :id) == @user_id || @auth_data.can_access?('meeting', 'update_all')
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.meeting_details')}")
      end
    end

    def validate_uniqueness_of_share_rule(share_all_records: false, action: 'create')
      existing_share_rule = ShareRule.select(:id).find_by(
        tenant_id: @tenant_id,
        from_id: @params.dig(:from, :id),
        to_type: @params.dig(:to, :type),
        to_id: @params.dig(:to, :id),
        meeting_id: @params[:id],
        share_all_records: share_all_records
      )

      if existing_share_rule.present? && action == 'update'
        existing_share_rule = existing_share_rule.id.to_i == @params[:share_rule_id].to_i ? nil : existing_share_rule
      end

      if existing_share_rule.present?
        raise(
          ExceptionHandler::InvalidDataError,
          "#{ErrorCode.duplicate_share_rule}||#{I18n.t('share_rules.invalid.already_exists')}||#{existing_share_rule.id}"
        )
      end
    end
  end
end
