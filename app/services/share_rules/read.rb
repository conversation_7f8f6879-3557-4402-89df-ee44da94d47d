# frozen_string_literal: true

module ShareRules
  class Read
    def initialize(share_rule_id)
      @share_rule_id = share_rule_id
    end

    def call
      command = GetSecurityContext.call
      unless command.success?
        Rails.logger.error 'Unauthorised: User context missing in ShareRules::Read'
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      @auth_data = command.result

      share_rule = ShareRule.find_by(tenant_id: @auth_data.tenant_id, id: @share_rule_id)

      unless share_rule.present?
        raise(ExceptionHandler::NotFound, "#{ErrorCode.share_rule_not_found}||#{I18n.t('error.not_found.share_rule')}")
      end

      unless user_can_read_share_rule?(share_rule)
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      share_rule
    end

    private

    def user_can_read_share_rule?(share_rule)
      (
        @auth_data.can_access?('shareRule', 'read_all') ||
        (
          @auth_data.can_access?('shareRule', 'read') &&
          share_rule.created_by_id == @auth_data.user_id
        )
      )
    end
  end
end
