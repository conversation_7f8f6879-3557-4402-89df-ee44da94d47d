# frozen_string_literal: true

module ShareRules
  class Update
    include ShareRules::Helper
    include EventsPublisher

    def initialize(params)
      @params = UnderscorizeHashKeys.call(params.to_h).result.with_indifferent_access
    end

    def call
      authorize_and_get_auth

      @user_id = @auth_data.user_id
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@user_id, @tenant_id).result

      unless @auth_data.can_access?('shareRule', 'update')
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      share_rule = ShareRule.find_by(id: @params[:share_rule_id], tenant_id: @tenant_id)

      unless share_rule.present?
        raise(ExceptionHandler::NotFound, "#{ErrorCode.share_rule_not_found}||#{I18n.t('error.not_found.share_rule')}")
      end

      unless !share_rule.system_default && (share_rule.created_by_id == @user_id || @auth_data.can_access?('shareRule', 'update_all'))
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      end

      validate_params

      validate_share_rule_participants

      if @params[:id].present?
        find_meeting_validate_access
        validate_uniqueness_of_share_rule(action: 'update')
        @params[:meeting_id] = @params[:id]
        @params[:share_all_records] = false
      else
        validate_can_share_all_meetings
        validate_uniqueness_of_share_rule(share_all_records: true, action: 'update')
        @params[:meeting_id] = nil
        @params[:share_all_records] = true
      end

      old_serialized_share_rule = ShareRuleSerializer.new(share_rule, payload_for_event: true).call
      share_rule.assign_attributes(
        @params.except(:entity_type, :id, :share_rule_id, :from, :to)
          .merge(from_id: @params[:from][:id], from_type: @params[:from][:type])
          .merge(to_id: @params[:to][:id], to_type: @params[:to][:type])
          .merge(updated_by: @user)
      )

      begin
        ActiveRecord::Base.transaction do
          share_rule.save!
          publish_events('update', 'share_rule', { share_rule: share_rule, old_share_rule: old_serialized_share_rule })
        end
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(
          ExceptionHandler::InvalidDataError,
          "#{ErrorCode.invalid_share_rule}||#{I18n.t('error.invalid.share_rule', error: e.message.gsub(VALIDATION_FAILED_ERROR, ''))}"
        )
      end

      share_rule
    end
  end
end
