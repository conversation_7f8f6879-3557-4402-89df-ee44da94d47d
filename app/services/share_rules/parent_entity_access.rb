# frozen_string_literal: true

require 'rest-client'

module ShareRules
  class ParentEntityAccess
    def initialize(entity)
      @entity = entity
    end

    def fetch
      command = GetSecurityContext.call("token")
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
      token = command.result

      return {} unless [LO<PERSON>UP_LEAD, LO<PERSON>UP_DEAL, LOOKUP_CONTACT, <PERSON>OOKUP_COMPANY].include?(@entity)

      begin
        response = RestClient.get(
          "#{SERVICE_CONFIG}/v1/internal/share/access/#{@entity.upcase}/MEETING",
          {
            Authorization: "Bearer #{token}"
          }
        )

        return JSON(response.body) unless response.nil?

        Rails.logger.error "ParentEntityAccess - invalid response"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      rescue RestClient::NotFound
        Rails.logger.error "ParentEntityAccess - 404"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      rescue RestClient::InternalServerError
        Rails.logger.error "ParentEntityAccess - 500"
        raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
      rescue RestClient::BadRequest
        Rails.logger.error "ParentEntityAccess - 400"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end
  end
end
