# frozen_string_literal: true

require 'rest-client'

class ValidateTeams
  def initialize(teams)
    @teams = teams
  end

  def call
    teams_response = fetch_teams
    @teams.each do |team|
      verified_team = teams_response.find { |resp| resp['id'] == team.id }
      if verified_team.present?
        team.name = verified_team['name']
      else
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.team')}")
      end
    end

    @teams
  end

  private

  def fetch_teams
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result

    response = RestClient.post(
      "#{SERVICE_IAM}/v1/teams/search",
      payload.to_json,
      {
        Authorization: "Bearer #{token}",
        content_type: :json
      }
    )

    return JSON.parse(response.body)['content'] unless response.nil?
    Rails.logger.error "Get teams -> invalid response"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue RestClient::InternalServerError
    Rails.logger.error "Get teams -> 500"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  rescue RestClient::BadRequest
    Rails.logger.error "Get teams -> 400"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue StandardError => e
    Rails.logger.error "Get teams -> 400 - StandardError -> #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  end

  def payload
    {
      fields: [
        'name',
        'id'
      ],
      jsonRule: {
        rules: [
          {
            operator: 'in',
            id: 'id',
            field: 'id',
            type: 'long',
            value: @teams.map(&:id).join(',')
          }
        ],
        condition: 'AND',
        valid: true
      }
    }
  end
end
