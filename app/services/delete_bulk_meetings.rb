# frozen_string_literal: true

class DeleteBulkMeetings
  include EventsPublisher
  prepend SimpleCommand
  attr_accessor(:user, :tenant_id, :auth_data, :meeting_id)

  def initialize meeting_ids
    @meeting_ids = meeting_ids
  end

  def call
    command = GetSecurityContext.call

    if command.success?
      @auth_data = command.result
      @meetings = Meeting.where(id: @meeting_ids, tenant_id: @auth_data.tenant_id)
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      raise(ActiveRecord::RecordNotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meetings.blank?

      result = []
      @meetings.each do |meeting|
        begin
          @meeting = meeting
          serialized_meeting = MeetingSerializer.call(@meeting, nil, false, @user, true).result
          meeting_data = dup_meeting_data
          if @meeting.online? && @meeting.valid? && @meeting.status != CANCELLED
            Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_DELETE_EVENT, @user)
          end
          unless user_has_valid_permissions? && @meeting.destroy
            result << build_response_hash('error')
            next
          end

          result << build_response_hash('success')
          publish_events(
            'destroy',
            'meeting',
            {
              meeting: meeting_data,
              old_meeting: serialized_meeting,
              deleted_by_id: @user.id
            }
          )
          Rails.logger.info "MEETING id #{@meeting.id}, title: #{@meeting.title}, is deleted by tenantId #{@auth_data.tenant_id}, userId #{@user.id}, in bulk delete action"
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error "Error while deleting the meeting #{meeting.id}: #{e.message}"
          result << build_response_hash('error')

          next
        rescue StandardError => e
          Rails.logger.error "Error while deleting the meeting #{meeting.id}: #{e.message}"
          result << build_response_hash('error')
          next
        end
      end

      PublishUsageJob.perform_later(@auth_data.tenant_id)

      { response: result, successCount: result.count{|obj| obj[:result].eql?('success')}, errorCount: result.count{|obj| obj[:result].eql?('error') } }
    else
      Rails.logger.error "Unauthorised: User context missing in deleting Meeting"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def user_has_valid_permissions?
    meeting_permission = @auth_data.permissions.find{ |p| p.name == 'meeting' }

    return meeting_permission&.action&.delete if @meeting.created_by_id.eql?(@user.id)
    return meeting_permission&.action&.delete_all unless @meeting.created_by_id.eql?(@user.id)
  end

  def dup_meeting_data
    result     = @meeting.dup
    result.id  = @meeting.id
    result.updated_at   = @meeting.updated_at
    result.related_to   = @meeting.related_to.dup
    result.participants = @meeting.participants.dup
    result.organizer = @meeting.organizer

    result
  end

  def build_response_hash(status)
    {
      entityId: @meeting.id,
      ownerId: @meeting.owner_id,
      tenantId: @auth_data.tenant_id,
      result: status
    }
  end
end
