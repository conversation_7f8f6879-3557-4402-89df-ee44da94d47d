class UpdateField
  prepend SimpleCommand
  include FieldHelper
  include EventsPublisher

  def initialize(field_id, params)
    @field_id = field_id
    @params = UnderscorizeHashKeys.call(params.to_h).result.with_indifferent_access
  end

  def call
    command = GetSecurityContext.call
    unless command.success?
      Rails.logger.error "UNAUTHORISED: User context missing in Updating Field"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    auth_data = command.result
    user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result
    field = Field.find_by(id: @field_id, tenant_id: auth_data.tenant_id)

    raise(ExceptionHandler::NotFound,  "#{ErrorCode.not_found}||#{I18n.t('error.not_found.field')}") if field.nil? || field.internal_name == 'tenant_id'
    old_serialized_field_data = V2::FieldSerializer.call(field).result

    if field.is_standard?
      field.assign_attributes(
        display_name: @params[:display_name],
        description: @params[:description],
        updated_by: user,
        updated_at: Time.now.utc
      )
    else
      field.assign_attributes(
        display_name: @params[:display_name],
        description: @params[:description],
        is_filterable: @params[:filterable],
        is_sortable: @params[:sortable],
        is_required: @params[:required],
        updated_by: user,
        updated_at: Time.now.utc
      )
    end

    begin
      ActiveRecord::Base.transaction do
        field.save!
        new_picklist_values = @params[:pick_lists].to_a.select { |picklist_value| picklist_value[:id].nil? }
        if new_picklist_values.present? && field.field_type == 'PICK_LIST' && !field.is_standard?
          picklist_id = field.picklist.id
          new_picklist_values.each do |picklist_value|
            if picklist_value[:display_name].present?
              PicklistValue.create!(
                picklist_id: picklist_id,
                tenant_id: auth_data.tenant_id,
                disabled: false,
                internal_name: generate_internal_name('cpv', picklist_value[:display_name]),
                display_name: picklist_value[:display_name]
              )
            end
          end
        end
      end

      publish_events('update', 'field', { field: field.reload, old_serialized_field_data: old_serialized_field_data })
      field
    rescue StandardError => e
      Rails.logger.error e.message
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.field')}")
    end
  end
end
