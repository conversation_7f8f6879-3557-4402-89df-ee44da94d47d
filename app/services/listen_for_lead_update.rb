class ListenForLeadUpdate
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(LEAD_EXCHANGE, LEAD_UPDATED_EVENT, LEAD_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{LEAD_UPDATED_EVENT}"
      payload = JSON(payload)
      id = payload["id"]
      tenant_id = payload["tenantId"]
      emails = payload["emails"].presence || []
      email_data = emails.find{ |e| e['primary'] == true }

      lookup_params = {}
      lookup_params[:name] = "#{payload['firstName']} #{payload['lastName']}".strip
      lookup_params[:email] = email_data ? email_data['value'] : nil
      lookup_params[:owner_id] = payload['ownerId']
      UpdateLookUp.call(tenant_id, id, LOOKUP_LEAD, lookup_params)
    end
  end
end
