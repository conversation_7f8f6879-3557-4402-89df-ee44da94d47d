class UpdateMeeting
  include MeetingEntitiesExtractor
  include EventsPublisher
  include MeetingAccessHelper
  prepend SimpleCommand
  attr_accessor(:user, :tenant_id, :auth_data)

  def initialize(params, metadata = {}, validate_entities = true)
    @metadata = metadata
    @params = UnderscorizeHashKeys.call(params.to_h.except('customFieldValues'))
                                  .result
                                  .merge(custom_field_values: params['customFieldValues'])
                                  .with_indifferent_access
    @validate_entities = validate_entities
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      @meeting = Meeting.find_by(id: @params[:id], tenant_id: @tenant_id)
      if(@params[:owner].present?)
        @owner = GetUserDetails.call(@params.dig(:owner, :id), @tenant_id).result
        @params[:owner] = @owner
      end

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      if @meeting.online?
        user_look_up = LookUp.find_by(tenant_id: @user.tenant_id, entity: "user_#{@user.id}")

        unless user_look_up.present?
          Rails.logger.error "Unauthorized: User Lookup missing for userid - #{@user.id}"
          raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
        end

        unless @meeting.organizer.email == user_look_up.email || @meeting.owner == @user
          connected_account = @user.connected_accounts.find_by(tenant_id: @user.tenant_id, email: @meeting.organizer.email)
          unless connected_account.present?
            @params.except('custom_field_values').each do |k, _|
              @params[k] = @meeting[k]
            end
            @params['timezone'] = { id: @meeting.time_zone.entity_id, name: @meeting.time_zone.name }
            @params['owner'] = @owner if @owner.present?
          end
        end
      end

      @existing_participant = @meeting.participants.to_a.deep_dup
      @existing_related_to = @meeting.related_to.to_a.deep_dup

      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.update_meeting')}") unless user_can_update_meeting?(@meeting)

      @params[:participants] ||= []
      @params[:related_to] ||= []

      organizer_participant =
        if @metadata['workflowId'].present? || @meeting.online?
          true
        else
          organizer_entity_id = @meeting.organizer.entity_id
          organizer_entity_type = @meeting.organizer.entity_type

          @params[:participants].find do |participant|
            participant['id'] == organizer_entity_id && participant['entity'] == organizer_entity_type
          end
        end

      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.organizer_is_not_a_participant')}") unless organizer_participant.present?

      if @owner.present?
        @params[:participants] << {
          tenant_id: @tenant_id,
          entity: LOOKUP_USER,
          id: @owner.id,
          name: @owner.name
        }.with_indifferent_access
      end

      old_serialized_meeting = MeetingSerializer.call(@meeting, nil, false, nil, true).result
      participants_and_related_entities = (@params[:participants] + @params[:related_to]).uniq{|p| "#{p['entity']}_#{p['id']}"}
      @validated_entities =
        if @validate_entities
          ParticipantsValidator.call({ user: @user, participants: participants_and_related_entities }).result
        else
          ParticipantsBuilder.call({ participants: participants_and_related_entities, owner: @meeting.owner }).result
        end

      @params[:time_zone] = get_time_zone
      @params[:updated_by] = @user
      begin
        @meeting.location_latitude = @params[:location_coordinate].present? ? @params[:location_coordinate][:lat] : nil 
        @meeting.location_longitude = @params[:location_coordinate].present? ? @params[:location_coordinate][:lon] : nil
        @meeting.participants = get_validated_participants + get_valid_external_entities
        @meeting.related_to = get_validated_related_to
        @meeting.assign_attributes(@params.slice!(:participants, :related_to, :checked_in_details, :checked_out_details, :custom_field_values, :location_coordinate))

        if @meeting.all_day? && @meeting.from.present?
          bod = @meeting.from.to_datetime.in_time_zone(@meeting.time_zone.name).beginning_of_day
          eod = bod.end_of_day
          @meeting.from = bod.utc
          @meeting.to = eod.utc
        end
        validate_meeting_medium

        update_meeting_attendance  if check_in_details_present? || check_out_details_present?
        @meeting.custom_field_values = validate_custom_field_values(@params[:custom_field_values])
        modify_dependent_fields if @meeting.status_changed? || @meeting.missed?
        meeting_changed = @meeting.changes
        if should_update_online_meeting?
          user_for_connected_account =
            if @meeting.owner_id == @meeting.organizer.entity_id && @meeting.organizer.is_a_user?
              @user
            else
              User.find_by(tenant_id: @tenant_id, id: @meeting.organizer.entity_id)
            end

          update_online_meeting_command = Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_UPDATE_EVENT, user_for_connected_account)
          if update_online_meeting_command.success?
            @meeting.provider_link = update_online_meeting_command.result[:provider_link]
            @meeting.provider_meeting_id = update_online_meeting_command.result[:provider_meeting_id]
          end
        end
        @meeting.save!
        participants = added_participants.union(added_related_to).uniq{|p| p.entity }
        old_participants = @meeting.participants - added_participants
        publish_events(
          'update',
          'meeting',
          {
            meeting: @meeting,
            meeting_changed: meeting_changed,
            added_participants: participants,
            new_participants: added_participants,
            removed_participants: removed_participants,
            old_participants: old_participants,
            old_meeting: old_serialized_meeting,
            current_user_id: @user.id,
            metadata: @metadata
          }
        )

        if @check_in_or_check_out_event.present?
          publish_events(
            @check_in_or_check_out_event,
            'meeting',
            {
              meeting_ids: [@meeting.id],
              tenant_id: @tenant_id,
              current_user_id: @user.id,
              meeting_attendance: @meeting.meeting_attendances.find_by(user_id: @user.id)
            }
          )
        end

        if meeting_changed.keys.include?('from')
          MeetingNotification.schedule(@meeting)
        end

        @meeting
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: e.message)}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in UpdatingMeeting"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  # We are not allowing to change the medium once set on meeting
  def validate_meeting_medium
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_medium')}") if @meeting.medium_changed?
  end

  def should_update_online_meeting?
    @meeting.online? && @meeting.valid? && @meeting.status_was != CANCELLED && [@meeting.owner.id, @meeting.organizer.entity_id].include?(@user.id) && @meeting.organizer.is_a_user?
  end

  def added_participants
    new_participants = @meeting.participants.map(&:entity) - @existing_participant.map(&:entity)
    @meeting.participants.select{|participant| new_participants.include?(participant.entity) }
  end

  def removed_participants
    old_participants = @existing_participant.map(&:entity) - @meeting.participants.map(&:entity)
    removed_participants = @existing_participant.select{|participant| old_participants.include?(participant.entity) }
    removed_participants.union(removed_related_to).uniq{|r| r.entity }
  end

  def added_related_to
    new_related_to = @meeting.related_to.map(&:entity) - @existing_related_to.map(&:entity)
    @meeting.related_to.select{|related_to| new_related_to.include?(related_to.entity) }
  end

  def removed_related_to
    old_related_to = @existing_related_to.map(&:entity) - @meeting.related_to.map(&:entity)
    @existing_related_to.select{|related_to| old_related_to.include?(related_to.entity) }
  end

  def get_time_zone
    time_zone_data = @params[:timezone]
    time_zone = nil
    if time_zone_data
      time_zone_data[:tenant_id] = @auth_data.tenant_id
      time_zone_data[:entity] = LOOKUP_TIMEZONE
      time_zone = GetLookUp.call(time_zone_data).result
    else
      user_settings = UserSettingService.new.fetch
      user_time_zone = user_settings[:timezone]
      time_zone_picklist_value = PicklistValue.find_by(tenant_id: @auth_data.tenant_id, internal_name: user_time_zone)
      if time_zone_picklist_value.present?
        time_zone = GetLookUp.call({
          id: time_zone_picklist_value.id,
          name: user_time_zone,
          tenant_id: @auth_data.tenant_id,
          entity: LOOKUP_TIMEZONE
        }).result
      end
    end
    @params.delete(:timezone)
    time_zone
  end

  def update_meeting_attendance
    meeting_attendance = @meeting.meeting_attendances.find_by(user_id: @user.id)

    if meeting_attendance.present? && meeting_attendance.checked_in?
      meeting_attendance.assign_attributes(
        checked_in_latitude: @params.dig(:checked_in_details, :latitude),
        checked_in_longitude: @params.dig(:checked_in_details, :longitude)
      )

      if meeting_attendance.changed? && !meeting_attendance.checked_out?
        meeting_attendance.checked_out_at = DateTime.now.utc
      elsif meeting_attendance.changed? && meeting_attendance.checked_out?
        meeting_attendance.assign_attributes(
          checked_in_at: DateTime.now.utc,
          checked_out_at: nil,
          checked_out_latitude: nil,
          checked_out_longitude: nil,
          is_checked_out_outside_geofence: false
        )
      elsif check_out_details_present?
        meeting_attendance.assign_attributes(
          checked_out_latitude: @params.dig(:checked_out_details, :latitude),
          checked_out_longitude: @params.dig(:checked_out_details, :longitude)
        )

        if meeting_attendance.changed? && meeting_attendance.checked_out?
          meeting_attendance.checked_out_at = nil
        else
          meeting_attendance.checked_out_at ||= DateTime.now.utc
        end
      end
    elsif meeting_attendance.nil?
      meeting_attendance = @meeting.meeting_attendances.new(
        checked_in_at: DateTime.now,
        checked_in_latitude: @params.dig(:checked_in_details, :latitude),
        checked_in_longitude: @params.dig(:checked_in_details, :longitude),
        checked_out_latitude: @params.dig(:checked_out_details, :latitude),
        checked_out_longitude: @params.dig(:checked_out_details, :longitude),
        is_checked_out_outside_geofence: false,
        user_id: @user.id
      )
    end

    unless meeting_attendance.valid? && meeting_attendance.checked_in?
      Rails.logger.error "Invalid: Could not checkin or checkout meeting. Errors #{meeting_attendance.errors.full_messages.inspect}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: meeting_attendance.errors.full_messages.join(', '))}")
    else
      @check_in_or_check_out_event = meeting_attendance.changes.any? ? (meeting_attendance.checked_in_at_changed? ? 'checkin' : 'checkout') : nil

      if @check_in_or_check_out_event == 'checkin'
        meeting_attendance.set_is_checked_in_outside_geofence(@params.dig(:checked_in_details), @user, @meeting)
      end

      if @check_in_or_check_out_event == 'checkout'
        meeting_attendance.set_is_checked_out_outside_geofence(@params.dig(:checked_out_details), @user, @meeting)
      end

      meeting_attendance.save
    end
  end

  def check_in_details_present?
    (
      @params.dig(:checked_in_details, :latitude).present? ||
      @params.dig(:checked_in_details, :longitude).present?
    )
  end

  def check_out_details_present?
    (
      @params.dig(:checked_out_details, :latitude).present? ||
      @params.dig(:checked_out_details, :longitude).present?
    )
  end

  def modify_dependent_fields
    case @meeting.status
    when CANCELLED
      @meeting.cancelled_at = DateTime.now.utc
      @meeting.cancelled_by = @user
      if @meeting.status_was == CONDUCTED
        @meeting.conducted_at = nil
        @meeting.conducted_by = nil
      end
    when CONDUCTED
      @meeting.conducted_at = DateTime.now.utc
      @meeting.conducted_by = @user
    when SCHEDULED, MISSED
      @meeting.status = SCHEDULED
      @meeting.status = MISSED if @meeting.missed?
      if @meeting.status_was == CONDUCTED
        @meeting.conducted_at = nil
        @meeting.conducted_by = nil
      end
    end
  end

  def validate_custom_field_values(custom_field_values)
    custom_field_values ||= {}
    custom_fields = Field.where(tenant_id: @auth_data.tenant_id, is_standard: false).to_a
    inactive_fields = custom_fields.filter { |field| !field['active'] }.map(&:internal_name)
    incoming_inactive_fields = custom_field_values.keys.filter { |key| inactive_fields.include?(key) }
    if incoming_inactive_fields.any?
      incoming_inactive_fields.each do |field|
        if @meeting.custom_field_values[field] != custom_field_values[field]
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.inactive_fields', fields: field)}")
        end
      end
    end

    non_existant_fields = custom_field_values.keys - custom_fields.map(&:internal_name)
    if non_existant_fields.any?
      Rails.logger.error "CreateMeeting - Invalid custom fields - #{non_existant_fields.join(', ')}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.non_existant_field', fields: non_existant_fields.join(', '))}")
    end

    custom_field_names = custom_fields.map(&:internal_name)
    custom_field_values.slice(*custom_field_names).each do |key, value|
      field = custom_fields.find { |f| f.internal_name == key }

      unless valid_value?(field, value)
        Rails.logger.error "UpdateMeeting Invalid value #{value.inspect} for field type #{field.field_type}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.field_value')}")
      end
    end
    @meeting.custom_field_values.except(*custom_field_names).merge(custom_field_values.slice(*custom_field_names))
  end
end
