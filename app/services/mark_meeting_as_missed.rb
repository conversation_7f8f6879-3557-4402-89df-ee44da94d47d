class MarkMeetingAsMissed
  include EventsPublisher
  prepend <PERSON><PERSON>om<PERSON>

  def call
    current_time = DateTime.now.utc
    twenty_four_hours_ago = (current_time - 24.hours)

    to_field = Meeting.arel_table[:to]
    status_field = Meeting.arel_table[:status]

    meetings_passed_yesterday = Meeting.where(
      to_field.between(twenty_four_hours_ago..current_time)
      .and(status_field.eq(SCHEDULED))
    )

    if meetings_passed_yesterday.present?
       meetings_passed_yesterday.find_in_batches do |meeting_group|
        meeting_group.each do |meeting|
          begin
            old_serialized_meeting = MeetingSerializer.call(meeting, nil, false, nil, true).result
            meeting.update!(status: MISSED)
            Rails.logger.info "MarkMeetingAsMissed Meeting #{meeting.id} status: #{meeting.status}"

            publish_events('update', 'meeting', { meeting: meeting, old_meeting:old_serialized_meeting  })
            Rails.logger.info "Event::MeetingUpdated event. Updated Meeting #{meeting.id} status: #{meeting.status}"
          rescue StandardError => e
            Rails.logger.error "MarkMeetingAsMissed Meeting #{meeting.id} not marked as missed. Message #{e.message}"
          end
        end
      end
    else
      Rails.logger.info "MarkMeetingAsMissed: No Scheduled Meetings to mark as missed"
    end
  end
end
