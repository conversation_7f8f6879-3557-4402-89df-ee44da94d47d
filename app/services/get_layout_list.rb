# frozen_string_literal: true

class GetLayoutList
  prepend SimpleCommand

  def call
    command = GetSecurityContext.call
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    fields = GetFields.call.result
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.field')}") if fields.blank?

    serialized_fields = get_serialized_fields(fields)

    list_layout = load_layout_list()
    list_layout['pageConfig']['tableConfig']['columns'] = (serialized_fields + associated_fields + user_fields(fields) + checked_in_out_by_field)
    list_layout
  end

  private

  def get_serialized_fields(fields)
    lookup_url_hash = { 'entity' => 'USER', 'lookupUrl' => USER_LOOKUP_URL }

    json = Jbuilder.new
    json.array! fields do |f|
      json.id f.internal_name
      json.header f.display_name
      json.fieldType field_type(f)
      json.isStandard f.is_standard
      json.isSortable f.is_sortable
      json.isFilterable f.is_filterable
      json.isInternal internal?(f)
      json.isRequired f.is_required
      json.active f.active
      json.showDefaultOptions (f.field_type == 'LOOK_UP') ? true : false
      json.lookup lookup_url_hash if (f.field_type == 'LOOK_UP')
      json.picklist PicklistSerializer.call(f.picklist, f.system_default?).result
      json.primaryField nil
    end
  end

  def field_type(f)
    f.field_type == 'ENTITY_PICKLIST' ? 'PICK_LIST' : f.field_type
  end

  def internal?(f)
    f.field_type == 'ENTITY_PICKLIST' ? true : f.is_internal
  end

  def load_layout_list
    YAML.load_file("#{Rails.root}/config/meeting-layout-list.json")
  end

  def associated_fields
    [
      {
        "id" => "associatedLeads",
        "header" => "Associated Leads",
        "fieldType" => "LOOK_UP",
        "isStandard" => true,
        "isSortable" => false,
        "isFilterable" => true,
        "isInternal" => false,
        "isRequired" => false,
        "active" => true,
        "showDefaultOptions" => true,
        "lookup" => {
          "entity" => "LEAD",
          "lookupUrl" => RELATED_LEAD_LOOKUP_URL
        },
        "picklist" => nil,
        "primaryField" => nil
      },
      {
        "id" => "associatedContacts",
        "header" => "Associated Contacts",
        "fieldType" => "LOOK_UP",
        "isStandard" => true,
        "isSortable" => false,
        "isFilterable" => true,
        "isInternal" => false,
        "isRequired" => false,
        "active" => true,
        "showDefaultOptions" => true,
        "lookup" => {
          "entity" => "CONTACT",
          "lookupUrl" => RELATED_CONTACT_LOOKUP_URL
        },
        "picklist" => nil,
        "primaryField" => nil
      },
      {
        "id" => "associatedDeals",
        "header" => "Associated Deals",
        "fieldType" => "LOOK_UP",
        "isStandard" => true,
        "isSortable" => false,
        "isFilterable" => true,
        "isInternal" => false,
        "isRequired" => false,
        "active" => true,
        "showDefaultOptions" => true,
        "lookup" => {
          "entity" => "DEAL",
          "lookupUrl" => RELATED_DEAL_LOOKUP_URL
        },
        "picklist" => nil,
        "primaryField" => nil
      },
      {
        "id" => "associatedCompanies",
        "header" => "Associated Companies",
        "fieldType" => "LOOK_UP",
        "isStandard" => true,
        "isSortable" => false,
        "isFilterable" => true,
        "isInternal" => false,
        "isRequired" => false,
        "active" => true,
        "showDefaultOptions" => true,
        "lookup" => {
          "entity" => "COMPANY",
          "lookupUrl" => RELATED_COMPANY_LOOKUP_URL
        },
        "picklist" => nil,
        "primaryField" => nil
      }
    ]
  end

  def checked_in_out_by_field
    [
      {
        "id" => "checkedInOutBy",
        "header" => "Checked In/Out By",
        "fieldType" => "LOOK_UP",
        "isStandard" => true,
        "isSortable" => false,
        "isFilterable" => true,
        "isInternal" => false,
        "isRequired" => false,
        "active" => true,
        "showDefaultOptions" => true,
        "lookup" => {
          "entity" => "USER",
          "lookupUrl" => USER_LOOKUP_URL
        },
        "picklist" => nil,
        "primaryField" => nil
      }
    ]
  end

  def user_fields(fields)
    fields_display_names = {}
    fields = fields.where(internal_name: %w[createdBy updatedBy conductedBy cancelledBy organizer participants owner]).pluck(:internal_name, :display_name).to_h

    [
      {
        'id' => 'createdByFields',
        'header' => "#{fields['createdBy']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'createdBy'
      },
      {
        'id' => 'updatedByFields',
        'header' => "#{fields['updatedBy']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'updatedBy'
      },
      {
        'id' => 'conductedByFields',
        'header' => "#{fields['conductedBy']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'conductedBy'
      },
      {
        'id' => 'cancelledByFields',
        'header' => "#{fields['cancelledBy']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'cancelledBy'
      },
      {
        'id' => 'organizerFields',
        'header' => "#{fields['organizer']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'organizer'
      },
      {
        'id' => 'ownerFields',
        'header' => "#{fields['owner']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'owner'
      },
      {
        'id' => 'participantFields',
        'header' => "#{fields['participants']} Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'participants'
      },
      {
        'id' => 'checkedInOutByFields',
        'header' => "Checked In/Out By Fields",
        'fieldType' => 'ENTITY_FIELDS',
        'isStandard' => true,
        'isSortable' => false,
        'isFilterable' => true,
        'isInternal' => false,
        'isRequired' => false,
        'active' => true,
        'showDefaultOptions' => true,
        'lookup' => {
          'entity' => 'TEAM',
          'lookupUrl' => ASSOCIATED_TEAM_LOOKUP_URL
        },
        'picklist' => nil,
        'primaryField' => 'checkedInOutBy'
      }
    ]
  end
end
