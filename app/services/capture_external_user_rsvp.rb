class CaptureExternalUserRsvp
  include EventsPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize meeting_public_id, rsvp_data
    @meeting_id = meeting_public_id
    @participant_id = rsvp_data[:pid]
    @rsvp_message =rsvp_data[:rsvpMessage]
    @rsvp_response =rsvp_data[:rsvpResponse]
  end

  def call
    raise(ExceptionHandler::ParticipantNotFound, "#{ErrorCode.participant_not_found}||#{I18n.t('error.not_found.participant')}") unless @participant_id.present?

    raise ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rsvp_response')}" unless @rsvp_response.present? && [RSVP_YES, RSVP_NO, RSVP_MAYBE].include?(@rsvp_response)

    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      tenant_id = @auth_data.tenant_id
      meeting = Meeting.where(tenant_id: tenant_id).where(public_id: @meeting_id).first

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") unless meeting.present?

      raise(ExceptionHandler::MeetingExpired, "#{ErrorCode.meeting_expired}||#{I18n.t('error.meeting_expired')}") unless meeting.from >= Time.current

      raise(ExceptionHandler::MeetingCancelled, "#{ErrorCode.meeting_cancelled}||#{I18n.t('error.meeting_cancelled')}") unless meeting.status == SCHEDULED

      serialized_meeting = MeetingSerializer.call(meeting, nil, false, nil, true).result
      @current_participant = meeting.participants.where(public_id: @participant_id).first
      raise(ExceptionHandler::ParticipantNotFound, "#{ErrorCode.participant_not_found}||#{I18n.t('error.not_found.participant')}") unless @current_participant.present?

      meeting_participant = @current_participant.meeting_look_ups.where(meeting: meeting).first
      meeting_participant.rsvp_response = @rsvp_response
      meeting_participant.rsvp_message = @rsvp_message
      begin
        meeting_participant.save!
        publish_events(
          'p_rsvp',
          'meeting',
          {
            meeting_participant: meeting_participant,
            old_meeting: serialized_meeting,
            current_user_id: @auth_data.user_id
          }
        )
        return meeting_participant
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_participant', error: e.message)}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in CaptureInternalUserRSVP"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

end
