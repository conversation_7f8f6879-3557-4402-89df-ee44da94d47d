# frozen_string_literal: true

class MarkMeetingAttendance
  include EventsPublisher
  prepend SimpleCommand

  def initialize(meeting_id, in_or_out, params)
    @meeting_id = meeting_id
    @in_or_out = in_or_out
    @params = UnderscorizeHashKeys.call(params.to_h).result.with_indifferent_access
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @tenant_id)
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      serialized_meeting = MeetingSerializer.call(@meeting, nil, false, nil, true).result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.checkin_checkout')}") unless can_user_checkin_or_checkout?

      @meeting_attendance = @meeting.meeting_attendances.find_or_initialize_by(user_id: @user.id)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_action')}") if invalid_action?

      @meeting_attendance.set_is_checked_in_outside_geofence(@params.dig("checked_in_details"), @user, @meeting) if check_in?
      @meeting_attendance.set_is_checked_out_outside_geofence(@params.dig("checked_out_details"), @user, @meeting) if check_out?

      @meeting_attendance.send("checked_#{@in_or_out}_at=", DateTime.now.utc)
      @meeting_attendance.send("checked_#{@in_or_out}_latitude=", @params.dig("checked_#{@in_or_out}_details", :latitude))
      @meeting_attendance.send("checked_#{@in_or_out}_longitude=", @params.dig("checked_#{@in_or_out}_details", :longitude))

      if check_in? && @meeting_attendance.checked_out?
        @meeting_attendance.send('checked_out_at=', nil)
        @meeting_attendance.send('checked_out_latitude=', nil)
        @meeting_attendance.send('checked_out_longitude=', nil)
        @meeting_attendance.send('is_checked_out_outside_geofence=', false)
      end

      if @meeting_attendance.valid?
        begin
          @meeting_attendance.save!
          publish_events(
            "check#{@in_or_out}",
            'meeting',
            {
              meeting_ids: [@meeting.id],
              tenant_id: @tenant_id,
              current_user_id: @user.id,
              meeting_attendance: @meeting_attendance
            }
          )

          PublishEvent.call(Event::MeetingUpdated.new(@meeting, serialized_meeting, @user.id))
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error e.message
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: e.message)}")
        end
      else
        Rails.logger.error "INVALID: Could not check in or check out. Errors: #{@meeting_attendance.errors.full_messages.inspect}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: @meeting_attendance.errors.full_messages.join(', '))}")
      end

      @meeting_attendance
    else
      Rails.logger.error 'Unauthorised: User context missing in MarkMeetingAttendance'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def invalid_action?
    (check_in? && @meeting_attendance.checked_in? && !@meeting_attendance.checked_out?) || (check_out? && @meeting_attendance.checked_out?)
  end

  def can_user_checkin_or_checkout?
    ([@meeting.owner_id] + @meeting.participants.select(&:is_a_user?).map(&:entity_id)).include?(@user.id)
  end

  def check_in?
    @in_or_out == :in
  end

  def check_out?
    @in_or_out == :out
  end
end
