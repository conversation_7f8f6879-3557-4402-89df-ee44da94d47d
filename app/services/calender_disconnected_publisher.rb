# frozen_string_literal: true

class CalenderDisconnectedPublisher
  prepend SimpleCommand
  def initialize(tenant_id, user_id, disconnected_by, user_email, tenant_email)
    @tenant_id = tenant_id
    @user_id = user_id
    @disconnected_by = disconnected_by
    @user_email = user_email
    @tenant_email = tenant_email
  end

  def call
    event = Event::CalenderDisconnected.new({
      tenantId: @tenant_id,
      userId: @user_id,
      disconnectedBy: @disconnected_by,
      userEmail: @user_email,
      tenantEmail: @tenant_email
    })
    PublishEvent.call(event)
    Rails.logger.info "Event::CalenderDisconnected event: #{event.to_json}"
  end
end
