require 'rest-client'
class ValidateDeals
  prepend SimpleCommand

  def initialize(deals = [])
    @deals = deals
  end

  def call
    return [] if @deals.nil? || @deals.empty?
    command = GetSecurityContext.call
    if command.success?
      verified_deals = get_deal_summary
      verified_deals.each do |v_deal|
        deal = @deals.select{|x| x.entity_id == v_deal["id"]}.first
        if deal.nil?
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.deal')}")
        else
          deal.name = v_deal["name"]
          deal.owner_id = v_deal['ownerId']
        end
      end
      return @deals
    else
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_deal_summary
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    deal_ids_params = @deals.collect{|deal| deal.entity_id }.join(",")
    begin
      response = RestClient.get(
        SERVICE_SEARCH + "/v1/summaries/deals?view=meeting&id=" + deal_ids_params,
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "ValidateDeal.get_deal_summary invalid response"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.deal_summary_response')}")
    rescue RestClient::NotFound
      Rails.logger.error "ValidateDeal.get_deal_summary deal 404"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.deal_summary_response')}")
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateDeal.get_deal_summary deal 500"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.deal')}")
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateDeal.get_deal_summary deal 400"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.deal_summary_response')}")
    end
  end
end
