class ListenForCompanyUpdate
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize
  end

  def call
    RabbitmqConnection.subscribe(COMPANY_EXCHANGE, COMPANY_NAME_UPDATED_EVENT, COMPANY_NAME_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message - #{payload} from #{COMPANY_EXCHANGE} for #{COMPANY_NAME_UPDATED_EVENT}"
      payload = JSON(payload)
      id = payload["companyId"]
      tenant_id = payload["tenantId"]
      name = payload["companyName"]
      lookup_params = {}
      lookup_params[:name] = name

      UpdateLookUp.call(tenant_id, id, LOOKUP_COMPANY, lookup_params)
    end
  end
end
