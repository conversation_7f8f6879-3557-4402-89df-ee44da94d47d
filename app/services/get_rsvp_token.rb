require 'rest-client'
class GetRsvpToken
  prepend SimpleCommand

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    begin
      response = RestClient.get(SERVICE_IAM + "/v1/api-keys/meeting-rsvp", {'Authorization': "Bearer #{token}"}
      )
      return JSON(response.body)["apiKey"] unless response.nil?
      Rails.logger.error "GetRsvpToken invalid response"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rsvp_token')}")
    rescue RestClient::Unauthorized
      Rails.logger.error "GetRsvpToken iam 401"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    rescue RestClient::NotFound
      Rails.logger.error "GetRsvpToken iam 404"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rsvp_token')}")
    rescue RestClient::InternalServerError
      Rails.logger.error "GetRsvpToken iam 500"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.get_rsvp_token')}")
    rescue RestClient::BadRequest
      Rails.logger.error "GetRsvpToken iam 400"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rsvp_token')}")
    end
  end
end