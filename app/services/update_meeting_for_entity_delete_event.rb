# frozen_string_literal: true

class UpdateMeetingForEntityDeleteEvent
  prepend SimpleCommand
  include ActiveModel::Validations
  include EventsPublisher

  def initialize(entity_id, tenant_id, user_id, entity_type, publish_usage_for_child = true)
    @tenant_id   = tenant_id
    @entity_id   = entity_id
    @entity_type = entity_type
    @user_id = user_id
    @publish_usage_for_child = false?(publish_usage_for_child) ? false : true
  end

  def call
    begin
      @lookup = LookUp.find_by(tenant_id: @tenant_id, entity: "#{@entity_type}_#{@entity_id}")

      return if @lookup.blank?

      look_up_entity = @lookup.entity
      look_up_email = @lookup.email

      publish_usage = false

      @lookup.meetings.uniq.each do |meeting|
        @meeting = meeting
        @user = GetUserDetails.call(@user_id, @tenant_id, false, @name).result
        # for deal, company just remove lookup relation as meeting will remain associated with contact
        look_up_deal_or_company_present = [LOOKUP_DEAL, LOOKUP_COMPANY].include?(@lookup.entity_type)
        if look_up_deal_or_company_present || is_related_to_other_entities?
          begin
            if @meeting.online? && @meeting.valid? && @meeting.status != CANCELLED && !look_up_deal_or_company_present
              begin
                update_online_meeting_command = Calendar::Base.call(nil, @meeting, ONLINE_MEETING_UPDATE_EVENT, @user)
                @meeting.provider_link = update_online_meeting_command.result[:provider_link]
                @meeting.provider_meeting_id = update_online_meeting_command.result[:provider_meeting_id]
                @meeting.save!
              rescue StandardError => e
                Rails.logger.error "Failed to update calendar event on #{@entity_type} delete | Error - #{e}"
              end
            end

            if @meeting.organizer.entity == look_up_entity
              data = {
                tenant_id: @tenant_id,
                email: look_up_email,
                name: look_up_email,
                entity: LOOKUP_EXTERNAL
              }

              external_entity_look_up = GetLookUp.call(data).result
              @meeting.organizer = external_entity_look_up
            end
          rescue StandardError => e
            Rails.logger.error "#{self.class}, Exception - #{e}"
          end
          @meeting.save!

          MeetingLookUp.where(meeting_id: @meeting.id, look_up_id: @lookup.id).destroy_all
          EntityMetadataPublisher.call(@lookup, @meeting.owner_id) if [LOOKUP_DEAL, LOOKUP_LEAD].include? @lookup.entity_type
          PublishEvent.call(Event::MeetingEntityDisassociated.new(@meeting, @lookup))
        else
          result = dup_meeting_data
          serialized_meeting = MeetingSerializer.call(@meeting, nil, false, @user, true).result
          begin
            if @meeting.online? && @meeting.valid? && @meeting.status != CANCELLED
              Calendar::Base.call(nil, @meeting, ONLINE_MEETING_DELETE_EVENT, @user)
            end
          rescue StandardError => e
            Rails.logger.error "#{self.class}, Exception - #{e}"
          end

          @meeting.destroy!
          DeleteAssociatedShareRulesJob.perform_later(@meeting_id, @user_id, @meeting.tenant_id)

          publish_events('destroy', 'meeting', { meeting: result, deleted_by_id:  @user_id, old_meeting: serialized_meeting, cascade_delete: true })

          Rails.logger.info "MEETING id #{@meeting.id}, title: #{@meeting.title}, is deleted in consume event, by tenantId #{@tenant_id}, userId #{@user_id}"
        end
      end

      PublishUsageJob.perform_later(@tenant_id) if @publish_usage_for_child

      @lookup.destroy
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Error while unrelating / deleting the meeting based on event : #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: e.message)}")
    end
  end

  private

  def is_related_to_other_entities?
    has_other_entity_participants = @meeting.participants.select{|participant| !participant.is_a_user? && participant.id != @lookup.id}.present?
    has_other_entity_related_to   = @meeting.related_to.select{|related_to| !related_to.is_a_user? && related_to.id != @lookup.id}.present?

    has_other_entity_participants || has_other_entity_related_to
  end

  def dup_meeting_data
    result     = @meeting.dup
    result.id  = @meeting.id
    result.updated_at   = @meeting.updated_at
    result.related_to   = @meeting.related_to.dup
    result.participants = @meeting.participants.dup
    result.organizer = @meeting.organizer

    result
  end

  def false?(value)
    value == 'false' || value == false
  end
end
