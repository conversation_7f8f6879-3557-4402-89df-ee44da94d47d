# frozen_string_literal: true

class UpdateMeetingFieldsForTenant
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(from_tenant_id, to_tenant_id)
    @from_tenant_id = from_tenant_id
    @to_tenant_id = to_tenant_id
  end

  def call
    command = GetSecurityContext.call
    unless command.success?
      Rails.logger.info('UpdateMeetingFieldsForTenant: Unauthorised User')
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.tenant')}") if invalid_tenants?

    begin
      updated_count = PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'MS Teams')
                                    .where(tenant_id: (@from_tenant_id..@to_tenant_id).to_a)
                                    .update_all(display_name: 'Outlook Calendar')
      Rails.logger.info "UpdateMeetingFieldsForTenant medium picklist value microsoft updated for Tenants #{@from_tenant_id} to #{@to_tenant_id} count #{updated_count}"
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "UpdateMeetingFieldsForTenant Error while updating microsoft medium picklist value for Tenant #{@from_tenant_id} to #{@to_tenant_id} Message #{e.message}"
    end
  end

  private

  def invalid_tenants?
    @from_tenant_id, @to_tenant_id = [@from_tenant_id,  @to_tenant_id].map(&:to_i)
    (
      @to_tenant_id < @from_tenant_id ||
      @from_tenant_id.negative? ||
      @to_tenant_id.negative?
    )
  end
end
