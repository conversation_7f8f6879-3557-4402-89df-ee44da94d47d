require 'rest-client'

class SearchContacts
  prepend SimpleCommand

  def initialize(email_ids, tenant_id, token = nil)
    @email_ids = email_ids
    @tenant_id = tenant_id
    @token = token
  end

  def call
    return [] if @email_ids.blank?
    EntitySearchResultParser.call(@email_ids, search_contacts, LOOKUP_CONTACT, @tenant_id).result
  end

  private

  def search_contacts
    if @token.blank?
      command = GetSecurityContext.call('token')
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
      @token = command.result
    end

    payload = { fields: %w[id firstName lastName emails ownerId] }
    rules = []
    @email_ids.each do |email|
      rules << {
        id: 'multi_field',
        field: 'multi_field',
        type: 'multi_field',
        input: 'multi_field',
        operator: 'multi_field',
        value: email
      }
    end

    payload[:jsonRule] = { rules: rules, condition: 'OR', valid: true }

    begin
      response = RestClient.post(
        "#{SERVICE_SEARCH}/v1/search/contact?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          Authorization: "Bearer #{@token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchContacts invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchContacts 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchContacts 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchContacts 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
