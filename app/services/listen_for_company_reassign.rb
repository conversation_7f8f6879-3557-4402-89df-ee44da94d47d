# frozen_string_literal: true

class ListenForCompanyReassign
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(COMPANY_EXCHANGE, COMPANY_REASSIGNED_EVENT, COMPANY_REASSIGNED_QUEUE) do |payload|
      Rails.logger.info "Received message #{COMPANY_EXCHANGE} for #{COMPANY_REASSIGNED_EVENT}"
      payload = JSON(payload)
      UpdateLookUp.call(payload['metadata']['tenantId'], payload['entity']['id'], LOOKUP_COMPANY, { owner_id: payload['entity']['ownedBy']['id'] })
    end
  end
end
