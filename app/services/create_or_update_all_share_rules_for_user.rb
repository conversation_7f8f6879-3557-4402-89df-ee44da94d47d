# frozen_string_literal: true

class CreateOrUpdateAllShareRulesForUser
  prepend SimpleCommand
  include EventsPublisher

  def initialize(params)
    @params = params
    @tenant_id = @params['tenantId']
  end

  def call
    return unless @params['userActions'].present?

    from_id = @params['ownerId']
    begin
      from_user = find_or_create_user(from_id, @params['name'])
      @params['userActions'].each do |user_action|
        begin
          find_or_create_user(user_action['id'], user_action['name'])
        rescue ExceptionHandler::AuthenticationError => e
          Rails.logger.error "Listeners::CreateAllShareRulesForHierarchy to user #{user_action['id']} missing #{e.message}"
          next
        end

        share_rule = ShareRule.find_or_initialize_by(
          tenant_id: @tenant_id,
          from_id: from_id,
          from_type: USER,
          to_id: user_action['id'],
          to_type: USER,
          share_all_records: true,
          system_default: true
        )
        unless share_rule.new_record?
          old_serialized_share_rule = ShareRuleSerializer.new(share_rule, payload_for_event: true).call
        end
        share_rule.name = 'Via_Manager'
        share_rule.actions = user_action['action'].with_indifferent_access.merge(read: true)
        share_rule.created_by = from_user if share_rule.new_record?
        share_rule.updated_by = from_user
        if share_rule.new_record? && share_rule.save
          publish_events('create', 'share_rule', { share_rule: share_rule })
        elsif share_rule.save
          publish_events('update', 'share_rule', { share_rule: share_rule, old_share_rule: old_serialized_share_rule })
        end
      end
    rescue ExceptionHandler::AuthenticationError => e
      Rails.logger.error "Listeners::CreateAllShareRulesForHierarchy from user missing #{e.message}"
    end
  end

  private

  def find_or_create_user(user_id, username)
    @existing_users ||= User.where(tenant_id: @tenant_id, id: extract_user_ids).to_a
    @existing_users.find { |user| user.id == user_id } || GetUserDetails.call(user_id, @tenant_id, false, username).result
  end

  def extract_user_ids
    @user_ids ||= [@params['ownerId']] + @params['userActions'].to_a.map { |user_action| user_action['id'] }
  end
end
