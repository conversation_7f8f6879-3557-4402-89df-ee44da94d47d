# frozen_string_literal: true

class CheckoutMeetings
  include EventsPublisher
  prepend SimpleCommand

  def initialize(payload)
    @payload = payload.with_indifferent_access
  end

  def call
    meeting_ids = @payload['meetingIds']
    meetings = Meeting.where(id: meeting_ids, tenant_id: @payload['tenantId']).includes(:meeting_attendances)

    return unless meetings.present?

    meetings.each do |meeting|
      meeting_attendance = meeting.meeting_attendances.find_by(user_id: @payload[:performedBy])
      if meeting_attendance.present?
        old_serialized_meeting = MeetingSerializer.call(meeting, nil, false).result
        meeting_attendance.update(
          checked_out_at: @payload['markedAt'],
          checked_out_latitude: @payload['latitude'],
          checked_out_longitude: @payload['longitude']
        )
        meeting.update(updated_by_id: @payload[:performedBy])

        PublishEvent.call(Event::MeetingUpdated.new(meeting, old_serialized_meeting, @payload[:performedBy]))
      end
    end
  end
end
