class ListenForUsageLimitChanged
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{USAGE_LIMIT_CHANGED_QUEUE}"
      payload = JSON(payload)

      tenant_id = payload['tenantId']
      plan_id = payload['planId']

      Field::DeactivateCustomFields.call(tenant_id) if DEACTIVATE_CUSTOM_FIELD_PLANS.include?(plan_id)
    end
  end
end
