# frozen_string_literal: true

class SubscribeToWebhook
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(connected_account)
    @connected_account = connected_account
  end

  def call
    return unless @connected_account.active
    return unless @connected_account.sync_type['calendar_to_kylas']
    subscribe_to_google_webhook if @connected_account.google_provider?
    subscribe_to_microsoft_webhook if @connected_account.microsoft_provider?
  end

  private

  def subscribe_to_google_webhook
    provider_subscription_id = "#{@connected_account.id}_" + "#{Time.now.to_i }"
    response = Calendar::Google::WatchEvent.call(@connected_account, provider_subscription_id, false).result
    if response[:resource_id]
      @connected_account.update(
        subscription_renewal_date: (Date.today + 7.days),
        provider_subscription_resource_id: response[:resource_id]
      )
    end
  end

  def subscribe_to_microsoft_webhook
    response = Calendar::Microsoft::SubscribeToWebhook.call(@connected_account).result
    if response[:resource_id].present?
      @connected_account.update(
        subscription_renewal_date: (Date.today + MS_RENEWAL_DURATION.days),
        provider_subscription_resource_id: response[:resource_id]
      )
    end
  end
end
