class GetMeetings
  prepend SimpleCommand

  def initialize(filter_params)
    @filter_params = filter_params
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      user_id = auth_data.user_id
      @tenant_id = auth_data.tenant_id
      validate_related_lookup

      command = FilterMeetingsQuery.call(@tenant_id, user_id, @filter_params, { permissions: { read_all: auth_data.can_access?('meeting', 'read_all') } })
      command.result
    else
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private
    def validate_related_lookup
      lookup = get_related_lookup
      if lookup
        if lookup.entity_type == LOOKUP_LEAD
          ValidateLeads.call([lookup]).result
        elsif lookup.entity_type == LOOKUP_DEAL
          ValidateDeals.call([lookup]).result
        elsif lookup.entity_type == LOOKUP_CONTACT
          ValidateContacts.call([lookup]).result
        elsif lookup.entity_type == LOOKUP_COMPANY
          ValidateCompanies.call([lookup]).result
        else
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.related_to')}")
        end
      end
    end

    def get_related_lookup
      if json_rule = @filter_params[:jsonRule]
        if json_rule[:rules].present?
          rules = json_rule[:rules]
          participant_rule = rules.find {|rule| "related_lookup" == rule[:type] }
        end
      end
      GetLookUp.call(participant_rule[:value]&.merge(tenant_id: @tenant_id)).result if participant_rule
    end
end
