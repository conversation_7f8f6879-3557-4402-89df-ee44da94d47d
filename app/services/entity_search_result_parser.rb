class EntitySearchResultParser
  prepend SimpleCommand

  def initialize(email_ids, data, entity_type, tenant_id)
    @email_ids = email_ids
    @data = data
    @entity_type = entity_type
    @tenant_id = tenant_id
  end

  def call
    matched = []
    unmatched = []
    emails_to_search = @email_ids.dup
    content = (@entity_type.eql? LOOKUP_USER) ? @data : @data['content']

    emails_to_search.each do |email|
      content.each do |entity|
        emails = entity['emails'].presence || []

        # In case of users search results it comes in string type 'email' field
         email_values = [entity['emailId']] if emails.empty?

        next if emails.blank? && email_values.blank?

        email_values = emails.map{|e| e['value']} unless email_values
        if email_values.include?(email)
          entity_hash = { entity: "#{@entity_type}_#{entity['id']}", email: email, name: "#{entity['firstName']} #{entity['lastName']}".strip, tenant_id: @tenant_id }
          entity_hash.merge!(owner_id: entity['ownerId']) if entity['ownerId'].present?
          matched << entity_hash
          @email_ids.delete(email)
        end
      end
    end

    # Prepare unmatched
    @email_ids.each{ |email| unmatched << { entity: LOOKUP_EXTERNAL, email: email, tenant_id: @tenant_id } }

    return { matched: matched, unmatched: unmatched }
  end
end
