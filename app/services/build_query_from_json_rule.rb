class BuildQueryFromJsonRule
  prepend SimpleCommand

  def initialize(scope, rules = [], tenant_id = nil, user_id = nil, system_rule = false, join_index_count = 1)
    @rules = rules
    @scope = scope
    @tenant_id = tenant_id
    @user_id = user_id
    @index = join_index_count
    @system_rule = system_rule
  end

  def call
    @rules.each {|rule| parse_rule(rule) }
    [@scope, @index]
  end

  private

  def parse_rule rule
    build_query rule
  end

  def build_query rule
    if rule.is_custom_field
      if %w[picklist long].include?(rule.type)
        send("has_custom_#{rule.type}_#{rule.operator}", rule.field, rule.value, rule.type)
      else
        send("has_custom_#{rule.type}_#{rule.operator}", rule.field, rule.value)
      end
    else
      send("has_#{rule.type}_#{rule.operator}", rule.field, rule.value)
    end
  end

  # STANDARD FIELDS
  def has_string_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.matches_regexp(value, false))
  end

  def has_string_not_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.does_not_match_regexp("#{value}", false))
  end

  def has_string_contains field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.matches("%#{value}%"))
  end

  def has_string_not_contains field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where.not(field.matches("%#{value}%"))
  end

  def has_string_begins_with field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.matches("#{value}%"))
  end

  def has_string_is_empty field, value
    @scope = @scope.where({ field => ["", nil] })
  end

  def has_string_is_not_empty field, value
    @scope = @scope.where.not({ field => ["", nil] })
  end

  def has_string_in field, value
    values = value.split(',').flatten
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.matches_any(values))
  end

  def has_string_not_in field, value
    values = value.split(',').flatten
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where.not(field.matches_any(values))
  end

  def has_meeting_attendance_equal(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.eq(value))
  end

  def has_meeting_attendance_not_equal(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope
              .left_joins(:meeting_attendances)
              .where(field.not_eq(value))
              .or(@scope.left_joins(:meeting_attendances).where(field.eq(nil)))
  end

  def has_meeting_attendance_greater(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.gt(value))
  end

  def has_meeting_attendance_greater_or_equal(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.gteq(value))
  end

  def has_meeting_attendance_less(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.lt(value))
  end

  def has_meeting_attendance_less_or_equal(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.lteq(value))
  end

  def has_meeting_attendance_between(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.between(value.first..value.last))
  end

  def has_meeting_attendance_not_between(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.not_between(value.first..value.last))
  end

  def has_meeting_attendance_in(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.in(value))
  end

  def has_meeting_attendance_not_in(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope
              .left_joins(:meeting_attendances)
              .where(field.not_in(value))
              .or(@scope.left_joins(:meeting_attendances).where(field.eq(nil)))
  end

  def has_meeting_attendance_is_null(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.eq(nil))
  end

  def has_meeting_attendance_is_not_null(field, value)
    field = MeetingAttendance.arel_table[field.to_sym]
    @scope = @scope.left_joins(:meeting_attendances).where(field.not_eq(nil))
  end

  def has_date_greater field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.gt(value))
  end

  def has_date_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.eq(value))
  end

  def has_date_less field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.lt(value))
  end

  def has_date_less_or_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.lteq(value))
  end

  def has_date_greater_or_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value))
  end

  def has_date_between field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.between(value.first..value.last))
  end

  def has_date_not_between field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.not_between(value.first..value.last))
  end

  def has_date_is_null field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.eq(nil))
  end

  def has_date_is_not_null field, value
    @scope = @scope.where.not({field => nil})
  end

  def has_boolean_equal field, value
    @scope = @scope.where({field => value})
  end

  def has_boolean_not_equal field, value
    @scope = @scope.where.not({field => value})
  end

  def has_long_equal field, value
    @scope = @scope.where({field => value})
  end

  def has_long_not_equal field, value
    @scope = @scope.where.not({field => value})
  end

  def has_long_is_null field, value
    @scope = @scope.where({field => nil})
  end

  def has_long_is_not_null field, value
    @scope = @scope.where.not({field => nil})
  end

  def has_long_greater field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.gt(value))
  end

  def has_long_greater_or_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value))
  end

  def has_long_less field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.lt(value))
  end

  def has_long_less_or_equal field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.lteq(value))
  end

  def has_long_in field, value
    values = value.split(',').flatten
    @scope = @scope.where({field => values})
  end

  def has_long_not_in field, value
    values = value.split(',').flatten
    @scope = @scope.where.not({field => values})
  end

  def has_long_between field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value.first).and(field.lteq(value.last)))
  end

  def has_long_not_between field, value
    field = Meeting.arel_table[field.to_sym]
    @scope = @scope.where.not(field.gteq(value.first).and(field.lteq(value.last)))
  end

  def has_organizer_lookup_equal field, value
    @scope = @scope.joins(organizer_join).where("lu#{@index}.entity": entity_lookup(value))
    @index = @index + 1
  end

  def has_organizer_lookup_not_equal field, value
    @scope = @scope.joins(organizer_join).where.not("lu#{@index}.entity": entity_lookup(value))
    @index = @index + 1
  end

  def has_organizer_lookup_in field, values
    @scope = @scope.joins(organizer_join).where("lu#{@index}.entity": values.map{ |value| entity_lookup(value) })
    @index = @index + 1
  end

  def has_organizer_lookup_not_in field, values
    @scope = @scope.joins(organizer_join).where.not("lu#{@index}.entity": values.map{ |value| entity_lookup(value) })
    @index = @index + 1
  end

  def has_participants_lookup_equal field, value
    # FIXME: Need to handle or here itself because rails does not allow or with joins.
    # Check issue here - https://github.com/rails/rails/issues/24055
    @scope = @scope.joins((@system_rule ? participant_or_organizer_join : participant_join)).where("lu#{@index}.entity": entity_lookup(value))
    @index = @index + 1
  end

  def has_participants_lookup_not_equal field, value
    @scope = @scope.where.not(id: Meeting.where(tenant_id: @tenant_id).joins(participant_join).where("lu#{@index}.entity = '#{entity_lookup(value)}'"))
    @index += 1
  end

  def has_participants_lookup_in field, values
    @scope = @scope.joins(participant_join).where("lu#{@index}.entity IN ('#{ values.map{ |value| entity_lookup(value) }.join("','")}')")
    @index += 1
  end

  def has_participants_lookup_not_in field, values
    @scope = @scope.where.not(id: Meeting.where(tenant_id: @tenant_id).joins(participant_join).where("lu#{@index}.entity IN ('#{values.map{ |value| entity_lookup(value) }.join("','")}')"))
    @index += 1
  end

  def participant_join(entity_condition = nil)
    str = "INNER JOIN meeting_look_ups mlu#{@index} ON mlu#{@index}.meeting_id = meetings.id AND mlu#{@index}.participant = TRUE INNER JOIN look_ups lu#{@index} ON mlu#{@index}.look_up_id = lu#{@index}.id"

    if entity_condition.present?
      str += " AND lu#{@index}.entity #{entity_condition}"
    end

    str
  end

  def organizer_join
    "INNER JOIN meeting_look_ups mlu#{@index} ON mlu#{@index}.meeting_id = meetings.id AND mlu#{@index}.organizer = TRUE INNER JOIN look_ups lu#{@index} ON mlu#{@index}.look_up_id = lu#{@index}.id"
  end

  def participant_or_organizer_join
    "INNER JOIN meeting_look_ups mlu#{@index} ON mlu#{@index}.meeting_id = meetings.id AND (mlu#{@index}.participant = TRUE OR mlu#{@index}.organizer = TRUE) INNER JOIN look_ups lu#{@index} ON mlu#{@index}.look_up_id = lu#{@index}.id"
  end

  def has_associated_lookup_equal field, value
    @scope = @scope.joins(associate_join).where("lu#{@index}.entity = '#{entity_lookup(value)}'")
    @index += 1
  end
  alias has_related_lookup_equal has_associated_lookup_equal

  def has_associated_lookup_not_equal field, value
    @scope = @scope.where.not(id: Meeting.where(tenant_id: @tenant_id).joins(associate_join).where("lu#{@index}.entity = '#{entity_lookup(value)}'"))
    @index += 1
  end

  def has_associated_lookup_in field, values
    @scope = @scope.joins(associate_join).where("lu#{@index}.entity IN ('#{values.map{ |value| entity_lookup(value) }.join("','")}')")
    @index += 1
  end

  def has_associated_lookup_not_in field, values
    @scope = @scope.where.not(id: Meeting.where(tenant_id: @tenant_id).joins(associate_join).where("lu#{@index}.entity IN ('#{values.map{ |value| entity_lookup(value) }.join("','")}')"))
    @index += 1
  end

  def has_associated_lookup_is_not_null field, value
    @scope = @scope.joins(associate_join).where("lu#{@index}.entity LIKE '#{value}%'")
    @index += 1
  end

  def has_associated_lookup_is_null field, value
    @scope = @scope.where.not(id: Meeting.where(tenant_id: @tenant_id).joins(associate_join).where("lu#{@index}.entity LIKE '#{value}%'"))
    @index += 1
  end

  def associate_join
    "INNER JOIN meeting_look_ups mlu#{@index} ON mlu#{@index}.meeting_id = meetings.id INNER JOIN look_ups lu#{@index} ON mlu#{@index}.look_up_id = lu#{@index}.id"
  end

  def current_user_lookup
    "user_#{@user_id}"
  end

  def entity_lookup(value)
    "#{value['entity']}_#{value['id']}"
  end

  # CUSTOM FIELDS
  def has_custom_string_equal(field, value)
    @scope = @scope.where("#{jsonb_column(field, :string)} ~* '#{value}'")
  end

  def has_custom_string_not_equal(field, value)
    @scope = @scope.where("NOT (#{jsonb_column(field, :string)} ~* '#{value}') OR (#{jsonb_column(field, :string)} IS NULL)")
  end

  def has_custom_string_contains(field, value)
    @scope = @scope.where("#{jsonb_column(field, :string)} ILIKE '%#{escaped(value)}%'")
  end

  def has_custom_string_not_contains(field, value)
    @scope = @scope.where("NOT (#{jsonb_column(field, :string)} ILIKE '%#{escaped(value)}%') OR (#{jsonb_column(field, :string)} IS NULL)")
  end

  def has_custom_string_begins_with(field, value)
    @scope = @scope.where("#{jsonb_column(field, :string)} ILIKE '#{escaped(value)}%'")
  end

  def has_custom_string_is_empty(field, value)
    @scope = @scope.where("#{jsonb_column(field, :string)} IS NULL")
  end

  def has_custom_string_is_not_empty(field, value)
    @scope = @scope.where("#{jsonb_column(field, :string)} IS NOT NULL")
  end

  def has_custom_string_in(field, value)
    values = value.to_s.split(',').flatten.map(&:strip)
    @scope = @scope.where(
      values.map do |val|
        "(#{jsonb_column(field, :string)} ILIKE '#{escaped(val)}')"
      end.join (' OR ')
    )
  end

  def has_custom_string_not_in(field, value)
    values = value.to_s.split(',').flatten.map(&:strip)
    @scope = @scope.where(
      "NOT (" + (values.map do |val|
        "(#{jsonb_column(field, :string)} ILIKE '#{escaped(val)}')"
      end.join (' OR ')) + ") OR (#{jsonb_column(field, :string)} IS NULL)"
    )
  end

  def escaped(value)
    value.gsub('_', '\\_').gsub('%', '\\%')
  end

  def has_custom_boolean_equal(field, value)
    condition = "#{jsonb_column(field, :boolean)} = #{value.to_s.upcase}"

    if value == false
      condition += " OR #{jsonb_column(field, :boolean)} IS NULL"
    end
    @scope = @scope.where(condition)
  end

  def has_custom_boolean_not_equal(field, value)
    condition = "NOT #{jsonb_column(field, :boolean)} = #{value.to_s.upcase}"

    if value == true
      condition += " OR #{jsonb_column(field, :boolean)} IS NULL"
    end
    @scope = @scope.where(condition)
  end

  def has_custom_date_greater(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} > '#{value}'")
  end

  def has_custom_date_equal(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} = '#{value}'")
  end

  def has_custom_date_less(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} < '#{value}'")
  end

  def has_custom_date_less_or_equal(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} <= '#{value}'")
  end

  def has_custom_date_greater_or_equal(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} >= '#{value}'")
  end

  def has_custom_date_between(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} BETWEEN '#{value.first}' AND '#{value.last}'")
  end

  def has_custom_date_not_between(field, value)
    @scope = @scope.where("(#{jsonb_column(field, :date)} < '#{value.first}' OR #{jsonb_column(field, :date)} > '#{value.last}')")
  end

  def has_custom_date_is_null(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} IS NULL")
  end

  def has_custom_date_is_not_null(field, value)
    @scope = @scope.where("#{jsonb_column(field, :date)} IS NOT NULL")
  end

  def has_custom_long_equal(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} = #{value}")
  end

  def has_custom_long_not_equal(field, value, type)
    @scope = @scope.where("NOT #{jsonb_column(field, type)} = #{value} OR #{jsonb_column(field, type)} IS NULL")
  end

  def has_custom_long_is_null(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} IS NULL")
  end

  def has_custom_long_is_not_null(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} IS NOT NULL")
  end

  def has_custom_long_greater(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} > #{value}")
  end

  def has_custom_long_greater_or_equal(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} >= #{value}")
  end

  def has_custom_long_less(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} < #{value}")
  end

  def has_custom_long_less_or_equal(field, value, type)
    @scope = @scope.where("#{jsonb_column(field, type)} <= #{value}")
  end

  def has_custom_long_in(field, value, type)
    values = value.split(',').flatten.join(',')
    @scope = @scope.where("#{jsonb_column(field, type)} IN (#{values})")
  end

  def has_custom_long_not_in(field, value, type)
    values = value.split(',').flatten.join(',')
    @scope = @scope.where("#{jsonb_column(field, type)} NOT IN (#{values}) OR #{jsonb_column(field, type)} IS NULL")
  end

  def has_custom_long_between(field, value, type)
    @scope = @scope.where("(#{jsonb_column(field, type)} >= #{value.first} AND #{jsonb_column(field, type)} <= #{value.last})")
  end

  def has_custom_long_not_between(field, value, type)
    @scope = @scope.where("(#{jsonb_column(field, type)} < #{value.first} OR #{jsonb_column(field, type)} > #{value.last})")
  end

  def jsonb_column(field, type)
    if type.to_s == 'picklist'
      "cast(custom_field_values -> '#{field}' ->> 'id' as #{converted_data[type]})"
    else
      "cast(custom_field_values ->> '#{field}' as #{converted_data[type]})"
    end
  end

  def converted_data
    {
      boolean: 'boolean',
      string: 'text',
      long: 'double precision',
      picklist: 'integer',
      date: 'timestamp',
    }.with_indifferent_access
  end

  alias has_custom_picklist_equal has_custom_long_equal
  alias has_custom_picklist_not_equal has_custom_long_not_equal
  alias has_custom_picklist_is_null has_custom_long_is_null
  alias has_custom_picklist_is_not_null has_custom_long_is_not_null
  alias has_custom_picklist_greater has_custom_long_greater
  alias has_custom_picklist_greater_or_equal has_custom_long_greater_or_equal
  alias has_custom_picklist_less has_custom_long_less
  alias has_custom_picklist_less_or_equal has_custom_long_less_or_equal
  alias has_custom_picklist_in has_custom_long_in
  alias has_custom_picklist_not_in has_custom_long_not_in
  alias has_custom_picklist_between has_custom_long_between
  alias has_custom_picklist_not_between has_custom_long_not_between
end
