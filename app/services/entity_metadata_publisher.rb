class EntityMetadataPublisher
  prepend SimpleCommand
  def initialize(look_up, meeting_owner_id)
    @look_up = look_up
    @meeting_owner_id = meeting_owner_id
  end

  def call
    if @look_up.is_a_deal?
      event = Event::DealMetadata.new(@look_up, @look_up.future_scheduled_meeting_time_from_now, @meeting_owner_id)
    elsif @look_up.is_a_lead?
      event = Event::LeadMetadata.new(@look_up, @look_up.future_scheduled_meeting_time_from_now, @meeting_owner_id)
    end
    if event.present?
      PublishEvent.call(event)
      Rails.logger.info "Event::EntityMetadata event: #{event.to_json}"
    end
  end
end
