# frozen_string_literal: true

require 'rest-client'

class SearchUsersByEmails
  prepend SimpleCommand

  def initialize(email_ids, tenant_id, token = nil)
    @email_ids = email_ids
    @tenant_id = tenant_id
    @token = token
  end

  def call
    return {} if @email_ids.blank?
    EntitySearchResultParser.call(@email_ids, search_users, LOOKUP_USER, @tenant_id).result
  end

  private

  def search_users
    if @token.blank?
      command = GetSecurityContext.call("token")
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
      @token = command.result
    end
    payload = { "emailIds": @email_ids }

    begin
      response = RestClient.post(
        SERVICE_IAM + "/v1/users/search-by-email",
        payload.to_json,
        {
          :Authorization => "Bearer #{@token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchUsersByEmails invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchUsersByEmails 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchUsersByEmails 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchUsersByEmails 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
