require 'rest-client'
class ValidateLeads
  prepend SimpleCommand

  def initialize(leads = [])
    @leads = leads
  end

  def call
    return [] if @leads.blank?

    command = GetSecurityContext.call
    if command.success?
      verified_leads = get_lead_summary
      verified_leads.each do |v_lead|
        lead = @leads.select{|x| x.entity_id == v_lead["id"]}.first
        if lead.nil?
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.lead')}")
        else
          lead.name = v_lead['name']
          lead.owner_id = v_lead['ownerId']
          if lead.email
            email = v_lead["emails"].select{|x| x["primary"] }.first.try(:[],"value")
            if email && lead.email != email
              raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.lead_email')}")
            end
          end
        end
      end
      return @leads
    else
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_lead_summary
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    lead_ids_params = @leads.collect{|lead| lead.entity_id }.join(",")
    begin
      url = "/v1/summaries/leads?view=meeting&includeConverted=true"
      response = RestClient.get(
        SERVICE_SEARCH + "#{url}&id=" + lead_ids_params,
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "ValidateLead.get_lead_summary invalid response"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.lead_summary_response')}")
    rescue RestClient::NotFound
      Rails.logger.error "ValidateLead.get_lead_summary sales 404"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.lead_summary_response')}")
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateLead.get_lead_summary sales 500"
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.lead')}")
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateLead.get_lead_summary sales 400"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.lead_summary_response')}")
    end
  end
end
