class SearchNotes
  prepend SimpleCommand

  def initialize(params)
    @params = params
    @user = nil
    @auth_data = nil
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      meeting_ids = get_meeting_ids

      notes = Note.joins(:meeting)
                    .where(meetings: { tenant_id: @user.tenant_id, id: meeting_ids})
                    .select(:id, :description, :created_by_id, :created_at, :meeting_id)
      notes = notes.order(id: :asc)
      notes = paginate(notes)
    else
      Rails.logger.error "Unauthorised: User context missing in SearchNotes"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_meeting_ids
    if json_rule = @params[:jsonRule]
      if json_rule[:rules].present?
        rule = json_rule[:rules].find {|r| r['field'] == 'entityId'}
      end
    end
    return [] unless rule
    return rule[:value].split(',')
  end

  def paginate(notes)
    page = @params[:page] || 1
    size = @params[:size] || 1000
    notes.page(page.to_i).per_page(size.to_i)
  end
end