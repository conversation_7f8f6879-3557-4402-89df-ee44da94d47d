class MeetingCancelledEventPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting, deleted_by_id = nil)
    @meeting = meeting
    @deleted_by_id = deleted_by_id
  end

  def call
    @meeting.participants.each do |participant|
      if @meeting.organizer == participant
        publish_meeting_cancelled_to_organizer
      elsif [<PERSON><PERSON><PERSON>UP_USER, LOOKUP_LEAD, LO<PERSON>UP_CONTACT, LOOKUP_EXTERNAL].include? participant.entity_type
        publish_meeting_cancelled_to_participant participant
      end
    end
  end

  private

    def publish_meeting_cancelled_to_organizer
      event = Event::MeetingCancelled.new(@meeting, @deleted_by_id)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingCancelled event for: #{event.to_json} and #{@meeting.owner.inspect} owner"
    end

    def publish_meeting_cancelled_to_participant participant
      event = Event::MeetingCancelledWithParticipant.new(@meeting, participant)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingCancelledWithParticipant event for: #{@meeting.inspect} and #{participant.inspect} participant"
      Rails.logger.info "Event::MeetingCancelledWithParticipant event data: #{event.to_json}"
    end
end
