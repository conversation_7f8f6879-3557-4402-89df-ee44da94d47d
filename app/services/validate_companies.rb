require 'rest-client'

class ValidateCompanies
  prepend SimpleCommand

  def initialize(companies = [])
    @companies = companies
  end

  def call
    return [] if @companies.blank?

    command = GetSecurityContext.call
    if command.success?
      verified_companies = get_company_summary
      verified_companies.each do |v_company|
        company = @companies.select{|x| x.entity_id == v_company['id']}.first
        if company.nil?
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.company')}")
        else
          company.name = v_company['name']
          company.owner_id = v_company['ownerId']
        end
      end
      return @companies
    else
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def get_company_summary
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result
    company_ids_params = @companies.collect{|company| company.entity_id }.join(',')
    begin
      response = RestClient.get(
        SERVICE_SEARCH + '/v1/summaries/companies?view=meeting&id=' + company_ids_params,
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

      Rails.logger.error 'ValidateCompanies.get_company_summary invalid response'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.company_summary_response')}")

    rescue RestClient::NotFound
      Rails.logger.error 'ValidateCompanies.get_company_summary company 404'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.company_summary_response')}")

    rescue RestClient::InternalServerError
      Rails.logger.error 'ValidateCompanies.get_company_summary company 500'
      raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.validate_compay')}")

    rescue RestClient::BadRequest
      Rails.logger.error 'ValidateCompanies.get_company_summary company 400'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.company_summary_response')}")
    end
  end
end
