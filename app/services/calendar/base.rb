# frozen_string_literal: true

module Calendar
  # Base class for creating meetings at provider side
  class Base
    prepend SimpleCommand

    def initialize(auth_data, meeting, event, user, payload = {})
      @auth_data = auth_data
      @meeting = meeting
      @event = event
      @user = user
      @payload = payload
    end

    def call
      provider_and_event = "Calendar::#{@meeting.medium.capitalize}::#{@event}Event"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.calender_event')}") unless Object.const_defined?(provider_and_event)

      connected_account = @user.connected_accounts
                               .where(tenant_id: @auth_data.try(:tenant_id) || @user.tenant_id,
                                      provider_name: @meeting.medium,
                                      active: true)
                               .where('sync_type @> ?', '{ "kylas_to_calendar": true }').first


      if connected_account.nil?
        raise(ExceptionHandler::ConnectedAccountNotFound,
          "#{ErrorCode.connected_account_not_found}||#{I18n.t('error.connected_account_not_found')}")
      end

      Object.const_get(provider_and_event).call(@auth_data, @meeting, connected_account, @payload)&.result
    end
  end
end
