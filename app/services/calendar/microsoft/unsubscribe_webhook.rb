# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Microsoft
    class UnsubscribeWebhook
      prepend SimpleCommand

      def initialize(connected_account)
        @connected_account = connected_account
      end

      def call
        send_request
      end

      private

      def send_request
        access_token = @connected_account.access_token

        url = URI("https://graph.microsoft.com/v1.0/subscriptions/#{@connected_account.provider_subscription_resource_id}")

        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Delete.new(url)
        request['Authorization'] = "Bearer #{access_token}"

        response = https.request(request)
        Rails.logger.info "Unsubscribed MS webhook #{@connected_account.id} - #{response.code}"
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED, Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Calendar::Microsoft::UnsubscribeWebhook exception for #{@connected_account.id} | #{e}"
        return false
      end
    end
  end
end
