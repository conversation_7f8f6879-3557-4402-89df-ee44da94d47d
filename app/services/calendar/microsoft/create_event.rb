# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Microsoft
    class CreateEvent
      prepend SimpleCommand

      def initialize(auth_data, meeting, connected_account, payload = {})
        @auth_data = auth_data
        @meeting = meeting
        @connected_account = connected_account
        @payload = payload
      end

      def call
        response = send_request
        { provider_link: response.dig('onlineMeeting', 'joinUrl'), provider_meeting_id: response['id'] }
      end

      private

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI("https://graph.microsoft.com/v1.0/me/calendars/#{CGI::escape(@connected_account.calendar_id)}/events")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Post.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        request.body = payload
        Rails.logger.info "#{self.class} - Payload for Microsoft - #{request.body}"
        response = https.request(request)

        case response.code
        when '201'
          JSON.parse(response.body)
        when '400'
          Rails.logger.error "Error in Calendar::Microsoft::CreateEvent - #{response.code} - #{response.body}"
          raise(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||#{I18n.t('error.provider_invalid')}")
        when '401'
          Rails.logger.error "Error in Calendar::Microsoft::CreateEvent - #{response.code} - #{response.body}"
          raise(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||#{I18n.t('error.provider_unauthorized')}")
        when '403'
          Rails.logger.error "Error in Calendar::Microsoft::CreateEvent - #{response.code} - #{response.body}"
          raise(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||#{I18n.t('error.provider_forbidden')}")
        else
          Rails.logger.error "Error in Calendar::Microsoft::CreateEvent - #{response.code} - #{response.body}"
          raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
        end
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
             Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Exception in Calendar::Microsoft::CreateEvent, Exception - #{e}"
        raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
      end

      def payload
        time_zone = @meeting.time_zone.name
        attendees = @payload[:participants].map do |participant|
          {
            emailAddress: {
              address: participant.email,
              name: participant.name
            },
            type: 'required'
          }
        end
        attendees.reject! { |participant| participant.dig(:emailAddress, :address).blank? }
        start_time = { timeZone: time_zone, dateTime: @meeting.from.in_time_zone(time_zone).strftime('%FT%T') }
        end_time = { timeZone: time_zone, dateTime: @meeting.to.in_time_zone(time_zone).strftime('%FT%T') }
        JSON.dump(
          {
            subject: @meeting.title,
            body: {
              contentType: 'HTML',
              content: @meeting.description
            },
            start: start_time,
            end: end_time,
            location: {
              displayName: @meeting.location
            },
            attendees: attendees,
            allowNewTimeProposals: true,
            transactionId: SecureRandom.uuid,
            isOnlineMeeting: true,
            onlineMeetingProvider: 'teamsForBusiness',
            isOrganizer: true,
            isAllDay: false,
            isReminderOn: true
          }
        )
      end
    end
  end
end
