# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Microsoft
    class RenewSubscription
      prepend SimpleCommand

      def initialize(connected_account)
        @connected_account = connected_account
      end

      def call
        response = send_request

        if response
          Rails.logger.info "Renewed subscription for connected account #{@connected_account.id} | Subscription Id - #{@connected_account.provider_subscription_resource_id}"
          true
        else
          Rails.logger.info "Subscription renewal failed for connected account #{@connected_account.id} | Subscription Id - #{@connected_account.provider_subscription_resource_id}"
          false
        end
      end

      private

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI("https://graph.microsoft.com/v1.0/subscriptions/#{@connected_account.provider_subscription_resource_id}")

        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Patch.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Content-Type'] = 'application/json'
        request.body = JSON.dump({
          'expirationDateTime': (DateTime.now + MS_RENEWAL_DURATION.days).strftime('%Y-%m-%dT%H:%MZ'),
        })

        response = https.request(request)

        return JSON.parse(response.body) if response.code == '200'

        Rails.logger.error "Calendar::Microsoft::RenewSubscription exception for #{@connected_account.id} | #{@connected_account.provider_subscription_resource_id} | #{response.code}"
        return false

      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED, Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Calendar::Microsoft::RenewSubscription exception for #{@connected_account.id} | #{@connected_account.provider_subscription_resource_id} | #{e}"
        return false
      end
    end
  end
end
