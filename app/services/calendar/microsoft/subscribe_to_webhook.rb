# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Microsoft
    class SubscribeToWebhook
      prepend SimpleCommand

      def initialize(connected_account)
        @connected_account = connected_account
      end

      def call
        response = send_request

        return {} unless response

        { resource_id: response['id'] }
      end

      private

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI('https://graph.microsoft.com/v1.0/subscriptions')

        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Post.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Content-Type'] = 'application/json'
        request.body = JSON.dump({
          'changeType': 'created,updated,deleted',
          'notificationUrl': "#{API_KYLAS_HOST}/v1/meetings/webhooks/microsoft",
          'resource': '/me/events',
          'expirationDateTime': (DateTime.now + MS_RENEWAL_DURATION.days),
          'clientState': @connected_account.get_client_secret_value
        })

        response = https.request(request)

        return JSON.parse(response.body) if response.code == '201'

        Rails.logger.error "Calendar::Microsoft::SubscribeToWebhook exception for #{@connected_account.id} | #{response.code}"

        return false
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED, Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Calendar::Microsoft::SubscribeToWebhook exception for #{@connected_account.id} | #{e}"
        return false
      end
    end
  end
end
