# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Microsoft
    class GetEvent
      prepend SimpleCommand

      def initialize(connected_account, event_id)
        @connected_account = connected_account
        @event_id = event_id
      end

      def call
        response = send_request
        return {} unless response

        { data: response }
      end

      private

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI("https://graph.microsoft.com/v1.0/me/events/#{CGI::escape(@event_id)}")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Get.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        response = https.request(request)

        case response.code
        when '200'
          return JSON.parse(response.body)
        when '400'
          Rails.logger.info "Calendar::Microsoft::GetEvent exception for #{@connected_account.id} | 400"
        when '401'
          Rails.logger.info "Calendar::Microsoft::GetEvent exception for #{@connected_account.id} | 401"
        when '403'
          Rails.logger.info "Calendar::Microsoft::GetEvent exception for #{@connected_account.id} | 403"
        else
          Rails.logger.error "Calendar::Microsoft::GetEvent - #{response.code}"
        end
        return false

      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
             Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Exception in Calendar::Microsoft::GetEvent, Exception - #{e}"
        return false
      end
    end
  end
end
