# frozen_string_literal: true

module Calendar
  class RenewAccounts
    prepend <PERSON>Command

    def call
      ms_accounts_to_renew = ConnectedAccount.
        where(active: true, provider_name: MICROSOFT_TEAMS_PROVIDER).
        where("sync_type->>'calendar_to_kylas' = ?", 'true').
        where("subscription_renewal_date <= ?", Date.today)

      ms_accounts_to_renew.each do |account|
        renewed = Calendar::Microsoft::RenewSubscription.call(account)
        if renewed
          account.update(subscription_renewal_date: (Date.today + MS_RENEWAL_DURATION.days))
        end
      end

      google_accounts_to_renew = ConnectedAccount.
        where(active: true, provider_name: GOOGLE_PROVIDER).
        where("sync_type->>'calendar_to_kylas' = ?", 'true').
        where("subscription_renewal_date <= ?", Date.today)

      google_accounts_to_renew.each do |account|
        renewed = Calendar::Google::RenewSubscription.call(account)
        if renewed
          account.update(subscription_renewal_date: (Date.today + 7.days))
        end
      end
    end
  end
end
