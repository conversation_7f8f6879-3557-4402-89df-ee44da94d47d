# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  # Class for getting access token of provided vendor
  class GetProviderAccessToken
    prepend SimpleCommand

    def initialize(provider_name, organizer_token = nil)
      @provider_name = provider_name
      @organizer_token = organizer_token
    end

    def call
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.provider_name')}") and return if @provider_name.blank?

      token = @organizer_token

      unless token.present?
        command = GetSecurityContext.call('token')
        raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

        token = command.result
      end


      url = URI("#{SERVICE_IAM}/v1/calendar-oauth/#{@provider_name.downcase}/access-token")

      http = Net::HTTP.new(url.host, url.port)

      request = Net::HTTP::Get.new(url)
      request['Authorization'] = "Bearer #{token}"
      request['Accept'] = 'application/json'

      response = http.request(request)
      case response.code
      when '200'
        JSON.parse(response.body)
      when '400'
        Rails.logger.error "Calendar::GetProviderAccessToken - #{response.code} | Response - #{response.body} | token used - #{token}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.provider_access_token_response')}")
      when '403'
        Rails.logger.error "Calendar::GetProviderAccessToken - #{response.code} | Response - #{response.body} | token used - #{token}"
        raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
      else
        Rails.logger.error "Calendar::GetProviderAccessToken - #{response.code} | Response - #{response.body} | token used - #{token}"
        raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.provider_access_token')}")
      end
    end
  end
end
