# frozen_string_literal: true

module Calendar
  module Google
    class RenewSubscription
      prepend SimpleCommand

      def initialize(connected_account)
        @connected_account = connected_account
      end

      def call
        provider_subscription_id = "#{@connected_account.id}_" + "#{Time.now.to_i }"
        response = Calendar::Google::WatchEvent.call(@connected_account, provider_subscription_id).result
        if response[:resource_id]
          @connected_account.update(
            subscription_renewal_date: (Date.today + 7.days),
            provider_subscription_resource_id: response[:resource_id]
          )
        end
      end
    end
  end
end
