# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Google
    class FetchEvents
      prepend SimpleCommand

      def initialize(connected_account)
        @connected_account = connected_account
      end

      def call
        response = send_request
        return {} unless response
        {
          next_sync_token: response['nextSyncToken'],
          items: response['items']
        }
      end

      private

      def payload
        payload ="timeMax=#{(DateTime.now + 3.months).utc.iso8601}&timeMin=#{(DateTime.now-3.months).utc.iso8601}&maxResults=2500&showDeleted=true"
        payload = "&syncToken=#{@connected_account.next_sync_token}" if @connected_account.next_sync_token
        payload
      end

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI("https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events?#{payload}")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Get.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        Rails.logger.info "#{self.class}, #{url}"
        response = https.request(request)

        case response.code
        when '200'
          Rails.logger.info JSON.parse(response.body)
          return JSON.parse(response.body)
        when '400'
          Rails.logger.info "Calendar::Google::FetchEvents exception for #{@connected_account.id} | 400"
        when '401'
          Rails.logger.info "Calendar::Google::FetchEvents exception for #{@connected_account.id} | 401"
        when '403'
          Rails.logger.info "Calendar::Google::FetchEvents exception for #{@connected_account.id} | 403"
        else
          Rails.logger.error "Calendar::Google::FetchEvents exception for #{@connected_account.id} | #{response.code}"
        end
        return false
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
        Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Calendar::Google::FetchEvents exception for #{@connected_account.id} | #{e.to_s}"
        return false
      end
    end
  end
end
