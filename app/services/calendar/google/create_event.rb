# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Google
    class CreateEvent
      prepend SimpleCommand

      def initialize(auth_data, meeting, connected_account, payload = {})
        @auth_data = auth_data
        @meeting = meeting
        @connected_account = connected_account
        @payload = payload
      end

      def call
        response = send_request
        { provider_link: response['hangoutLink'], provider_meeting_id: response['id'] }
      end

      private

      def google_meeting_status
        case @meeting.status
        when CANCELLED
          'cancelled'
        else
          'confirmed'
        end
      end

      def send_request
        access_token = @connected_account.fetch_access_token

        url = URI("https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events?conferenceDataVersion=1&sendUpdates=all")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Post.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        request.body = payload
        Rails.logger.info "#{self.class}, Request Body - #{request.body}"
        response = https.request(request)

        case response.code
        when '200'
          JSON.parse(response.body)
        when '400'
          Rails.logger.error "Error in Calendar::Google::CreateEvent - #{response.code} - #{JSON.parse(response.body)}"
          raise(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||#{I18n.t('error.provider_invalid')}")
        when '401'
          Rails.logger.error "Error in Calendar::Google::CreateEvent - #{response.code} - #{JSON.parse(response.body)}"
          raise(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||#{I18n.t('error.provider_unauthorized')}")
        when '403'
          Rails.logger.error "Error in Calendar::Google::CreateEvent - #{response.code} - #{JSON.parse(response.body)}"
          raise(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||#{I18n.t('error.provider_forbidden')}")
        else
          Rails.logger.error "Calendar::Google::CreateEvent - #{response.code} - #{JSON.parse(response.body)}"
          raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
        end
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
             Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Exception in Calendar::Google::CreateEvent, Exception - #{e}"
        raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
      end

      def payload
        time_zone = @meeting.time_zone.name
        attendees = @payload[:participants].map do |participant|
          { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
        end
        attendees.reject! { |participant| participant[:email].blank? }
        start_time = { dateTime: @meeting.from.in_time_zone(time_zone).strftime('%FT%T%:z') }
        end_time = { dateTime: @meeting.to.in_time_zone(time_zone).strftime('%FT%T%:z') }
        JSON.dump(
          {
            kind: 'calendar#event',
            summary: @meeting.title,
            location: @meeting.location,
            status: google_meeting_status,
            description: @meeting.description,
            start: start_time,
            end: end_time,
            source: {
              url: APP_KYLAS_HOST,
              title: KYLAS
            },
            attendees: attendees || [],
            conferenceData: {
              createRequest: {
                conferenceSolutionKey: {
                  type: 'hangoutsMeet'
                },
                requestId: SecureRandom.uuid,
                status: {
                  statusCode: 'success'
                }
              }
            },
            reminders: {
              useDefault: true
            }
          }
        )
      end
    end
  end
end
