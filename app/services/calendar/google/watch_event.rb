# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Google
    class WatchEvent
      prepend SimpleCommand
      attr_reader :fetch_token

      def initialize(connected_account, provider_subcription_id, fetch_token=true)
        @connected_account = connected_account
        @provider_subcription_id = provider_subcription_id
        @fetch_token = fetch_token
      end

      def call
        response = send_request
        return {} unless response
        {
          resource_id: response['id']
        }
      end

      private

      def payload
        JSON.dump(
          {
            "id": @provider_subcription_id,
            "type": "webhook",
            "address": "#{API_KYLAS_HOST}/v1/meetings/webhooks/google"
          }
        )
      end

      def send_request
        access_token =  fetch_token ? @connected_account.fetch_access_token : @connected_account.send(:decrypt_token)

        url = URI("https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events/watch")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Post.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        request.body = payload
        Rails.logger.info "#{self.class}, Request Body - #{request.body}"
        response = https.request(request)

        case response.code
        when '200'
          return JSON.parse(response.body)
        when '400'
          Rails.logger.info "Calendar::Google::WatchEvent exception for #{@connected_account.id} | 400"
        when '401'
          Rails.logger.info "Calendar::Google::WatchEvent exception for #{@connected_account.id} | 401"
        when '403'
          Rails.logger.info "Calendar::Google::WatchEvent exception for #{@connected_account.id} | 403"
        else
          Rails.logger.error "Calendar::Google::WatchEvent exception for #{@connected_account.id} | #{response.code}"
        end
        return false
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
        Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Calendar::Google::WatchEvent exception for #{@connected_account.id} | #{e.to_s}"
        return false
      end
    end
  end
end
