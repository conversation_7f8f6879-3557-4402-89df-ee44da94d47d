# frozen_string_literal: true

require 'uri'
require 'json'
require 'net/http'

module Calendar
  module Google
    class CancelEvent
      prepend SimpleCommand

      def initialize(auth_data, meeting, connected_account, payload = {})
        @auth_data = auth_data
        @meeting = meeting
        @connected_account = connected_account
      end

      def call
        provider_meeting_id_presence

        response = send_request
        { provider_link: response['hangoutLink'], provider_meeting_id: response['id'] }
      end

      private

      def provider_meeting_id_presence
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.provider_meeting_id')}") if @meeting.provider_meeting_id.nil?
      end

      def send_request
        if @meeting.organizer.is_a_user? && @meeting.organizer.entity_id != @auth_data.user_id
          organizer_token = GenerateToken.call(@meeting.organizer.entity_id, @meeting.tenant_id)
        end

        access_token = @connected_account.fetch_access_token(organizer_token)

        url = URI("https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@meeting.provider_meeting_id)}?conferenceDataVersion=1&sendUpdates=all")
        https = Net::HTTP.new(url.host, url.port)
        https.use_ssl = true

        request = Net::HTTP::Patch.new(url)
        request['Authorization'] = "Bearer #{access_token}"
        request['Accept'] = 'application/json'
        request['Content-Type'] = 'application/json'
        request.body = payload
        response = https.request(request)

        case response.code
        when '200'
          JSON.parse(response.body)
        when '400'
          raise(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||#{I18n.t('error.provider_invalid')}")
        when '401'
          raise(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||#{I18n.t('error.provider_unauthorized')}")
        when '403'
          raise(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||#{I18n.t('error.provider_forbidden')}")
        when '404'
          raise(ExceptionHandler::ProviderNotFound, "#{ErrorCode.provider_not_found}||#{I18n.t('error.provider_not_found')}")
        else
          Rails.logger.error "Calendar::Google::CancelEvent - #{response.code}"
          raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
        end
      rescue SocketError, Timeout::Error, Errno::EINVAL, Errno::ECONNRESET, EOFError, Errno::ECONNREFUSED,
             Net::HTTPBadResponse, Net::HTTPHeaderSyntaxError, Net::ProtocolError => e
        Rails.logger.error "Exception in Calendar::Google::CancelEvent, Exception - #{e}"
        raise(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||#{I18n.t('error.provider_internal_server')}")
      end

      def payload
        JSON.dump({ status: 'cancelled' })
      end
    end
  end
end
