# Note: Accept event as generic hash which will support outlook + google

class ProcessEvent
  include EventsPublisher
  prepend SimpleCommand

  attr_accessor :event
  def initialize(connected_account, event)
    @connected_account = connected_account
    @event = event.with_indifferent_access
  end

  def call
    @meeting = find_or_build_event
    # TODO: handle meeting updates
  end

  private

  def find_or_build_event
    old_serialized_meeting = nil
    token = GenerateToken.call(@connected_account.user.id, @connected_account.user.tenant_id).result
    meeting = Meeting.find_or_initialize_by(provider_meeting_id: event[:id], tenant_id: @connected_account.tenant_id)
    if(meeting.persisted? && event[:status].eql?('cancelled'))
      old_serialized_meeting = MeetingSerializer.call(meeting, nil, false, nil, true).result
      meeting.status = CANCELLED
      meeting.cancelled_at = DateTime.now
      meeting.cancelled_by = meeting.owner
      meeting.save!
      publish_events('cancel', 'meeting', { meeting: meeting, old_meeting: old_serialized_meeting, current_user_id: @connected_account.user_id })
      return
    end
    
    old_serialized_meeting = MeetingSerializer.call(meeting, nil, false, nil, true).result if meeting.persisted? && old_serialized_meeting.blank?

    existing_participant = meeting.participants.to_a.deep_dup
    existing_related_to = meeting.related_to.to_a.deep_dup
    look_ups = search_look_ups event[:attendees], token

    organizer_lookup = search_organizer(event[:organizer][:email], token)

    # incase of all_day meeting we only get date instead of datetime in payload
    meeting.assign_attributes(
      created_by_id: @connected_account.user_id,
      from: event.dig(:start, :dateTime) || event.dig(:start, :date),
      to: event.dig(:end, :dateTime) || event.dig(:end, :date).to_datetime.end_of_day,
      provider_link: event[:htmlLink],
      title: event[:summary] || '(No title)',
      description: event[:description],
      location: event[:location],
      updated_by: @connected_account.user,
      tenant_id: @connected_account.tenant_id,
      medium: event[:medium]
    )

    meeting.owner = @connected_account.user unless meeting.owner.present?

    if event[:all_day].present? || event.dig(:start, :date).present?
      meeting.all_day = event[:all_day] || true
    end

    if meeting.organizer.blank?
      organizer_look_up = LookUp.find_or_create_by(organizer_lookup)
      meeting.organizer = organizer_look_up
    end

    event_to_publish = meeting.persisted? ? 'update' : 'create'
    meeting_changes = meeting.changes
    if meeting.save!
      save_attendees(event[:attendees], look_ups, meeting)
      meeting.participant_look_ups.joins(:look_up).
        where("look_ups.email": organizer_lookup[:email]).
        where( "look_ups.entity like ?", "#{organizer_lookup[:entity_type]}_%").update(organizer: true)

      if event_to_publish == 'update'
        participants = added_participants(meeting.reload, existing_participant).union(added_related_to(meeting, existing_related_to)).uniq { |p| p.entity }
        old_participants = meeting.participants - added_participants(meeting, existing_participant)
        payload = {
          meeting: meeting,
          meeting_changed: meeting_changes,
          added_participants: participants,
          new_participants: added_participants(meeting, existing_participant),
          removed_participants: removed_participants(meeting, existing_participant, existing_related_to),
          old_participants: old_participants,
          old_meeting: old_serialized_meeting,
          current_user_id: @connected_account.user_id
        }
      else
        payload = { meeting: meeting, current_user_id: @connected_account.user_id }
      end
      publish_events(event_to_publish, 'meeting', payload)
    end
    meeting
  end

  def added_participants(meeting, existing_participant)
    new_participants = meeting.participants.map(&:entity) - existing_participant.map(&:entity)
    meeting.participants.select { |participant| new_participants.include?(participant.entity) }
  end

  def added_related_to(meeting, existing_related_to)
    new_related_to = meeting.related_to.map(&:entity) - existing_related_to.map(&:entity)
    meeting.related_to.select { |related_to| new_related_to.include?(related_to.entity) }
  end

  def removed_participants(meeting, existing_participant, existing_related_to)
    old_participants = existing_participant.map(&:entity) - meeting.participants.map(&:entity)
    removed_participants = existing_participant.select { |participant| old_participants.include?(participant.entity) }
    removed_participants.union(removed_related_to(meeting, existing_related_to)).uniq { |r| r.entity }
  end

  def removed_related_to(meeting, existing_related_to)
    old_related_to = existing_related_to.map(&:entity) - meeting.related_to.map(&:entity)
    existing_related_to.select{ |related_to| old_related_to.include?(related_to.entity) }
  end

  def save_attendees(attendees, look_ups, meeting)
    emails = event[:attendees]&.collect{ |a| a[:email] }

    return if emails.blank?

    look_ups = look_ups[:unmatched] + look_ups[:matched]

    meeting.organizer_look_up.update(participant: true)
    emails = emails - [meeting.organizer.email]
    emails.each do |email|
      look_ups.each do |look_up|
        attendee = attendees.find{|data| data[:email] == email}
        if look_up[:email] == email
          # To handle multiple lookups, we need to make sure external lookups exist
          if look_up[:entity_type] == LOOKUP_EXTERNAL
            searched_look_up = SearchExternal.call([email], @connected_account.tenant_id)
            look_up = searched_look_up[:matched].first || searched_look_up[:unmatched].first
          end
          look_up = LookUp.find_or_create_by(look_up)
          meeting_look_up = meeting.reload.participant_look_ups.find_or_initialize_by(look_up_id: look_up.id)
          meeting.reload.related_to_look_ups.find_or_create_by(look_up_id: look_up.id) if(look_up.entity.starts_with?('lead') || look_up.entity.starts_with?('contact'))
          meeting_look_up.rsvp_response = get_rsvp_response(attendee) if attendee.present?
          meeting_look_up.save
        end
      end
    end
    emails = emails + [meeting.organizer.email]
    participants_to_remove = meeting.participants.pluck(:email) - emails
    meeting.related_to_look_ups.where(look_up_id: meeting.related_to.where(email: participants_to_remove).collect(&:id)).delete_all
    meeting.participant_look_ups.where(look_up_id: meeting.participants.where(email: participants_to_remove).collect(&:id)).delete_all
  end

  def get_rsvp_response attendee
    response = attendee[:responseStatus]
    case response
    when 'needsAction'
      return nil
    when 'accepted'
      return RSVP_YES
    when 'declined'
      return RSVP_NO
    when 'tentative'
      return RSVP_MAYBE
    end
  end

  def search_look_ups attendees, token
    return {} if attendees.blank?
    emails = attendees&.collect{ |a| a[:email] }
    result = { matched: [], unmatched: [] }
    search_leads = { matched: [], unmatched: [] }
    search_contact = { matched: [], unmatched: [] }
    search_user = { matched: [], unmatched: []}
    search_external = { matched: [], unmatched: []}

    existing_look_ups = LookUp.where(email: emails, tenant_id: @connected_account.tenant_id).where.not("entity like '#{LOOKUP_EXTERNAL}_%'")
    existing_emails = existing_look_ups.map(&:email)
    if existing_look_ups.present?
      result[:matched] = existing_look_ups.map { |look_up| { entity: look_up.entity, email: look_up.email, name: look_up.name, tenant_id: look_up.tenant_id } }
    end
    email_ids_to_search = emails.reject{ |email_id| existing_emails.include?(email_id) }
    if email_ids_to_search.present?
      search_contact = SearchContacts.call(email_ids_to_search, @connected_account.tenant_id, token).result
      if search_contact[:unmatched].present?
        search_leads = SearchLeads.call(search_contact[:unmatched].map{|c| c[:email]}, @connected_account.tenant_id, token).result
        search_contact[:unmatched] = []
        if search_leads[:unmatched].present?
          search_user = SearchUsersByEmails.call(search_leads[:unmatched].map{|c| c[:email]}, @connected_account.tenant_id, token).result
          search_leads[:unmatched] = []
          if search_user[:unmatched].present?
            search_external = SearchExternal.call(search_user[:unmatched].map{|c| c[:email]}, @connected_account.tenant_id).result
            search_user[:unmatched] = []
          end
        end
      end
      search_contact.merge!(search_leads){|result,val1,val2|  (val1 + val2).uniq }
      search_contact.merge!(search_user){|result,val1,val2|  (val1 + val2).uniq }
      search_contact.merge!(search_external){|result,val1,val2|  (val1 + val2).uniq }
      result.merge!(search_contact){|result,val1,val2|  (val1 + val2).uniq }
    end

    result
  end

  def search_organizer email, token

    existing_user = LookUp.where(email: email, tenant_id: @connected_account.tenant_id).where("entity like '#{LOOKUP_USER}'").first
    return { entity: existing_user.entity, email: existing_user.email, name: existing_user.name, tenant_id: existing_user.look_up.tenant_id } if existing_user.present?

    search_user = SearchUsersByEmails.call([email], @connected_account.tenant_id, token).result
    return search_user[:matched][0] if search_user[:matched].present?

    search_contact = SearchContacts.call([email], @connected_account.tenant_id, token).result
    return search_contact[:matched][0] if search_contact[:matched].present?

    search_lead = SearchLeads.call([email], @connected_account.tenant_id, token).result
    return search_lead[:matched][0] if search_lead[:matched].present?

    search_external = SearchExternal.call([email], @connected_account.tenant_id).result
    return search_external[:matched][0] || search_external[:unmatched][0]
  end
end
