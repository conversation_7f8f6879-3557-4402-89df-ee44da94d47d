# frozen_string_literal: true
require 'rest-client'

class FetchGeofenceConfigurationForUser
  prepend SimpleCommand

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?
    
    token = command.result
    auth_data = GetSecurityContext.call.result

    url = "/v1/field-sales/executives/#{auth_data.user_id}/geofence"

    begin
      response = RestClient.get(
        SERVICE_FIELD_SALES + url,
        {
          Authorization: "Bearer #{token}",
          content_type: :json
        }
      )

      return JSON(response.body) unless response.nil?

      Rails.logger.error "Fetch Geofence Configuration For User - invalid response - User ID: #{auth_data.user_id}, Tenant ID: #{auth_data.tenant_id}"
    rescue RestClient::InternalServerError
      Rails.logger.error "Fetch Geofence Configuration For User - 500 - User ID: #{auth_data.user_id}, Tenant ID: #{auth_data.tenant_id}"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Fetch Geofence Configuration For User - 400 - User ID: #{auth_data.user_id}, Tenant ID: #{auth_data.tenant_id}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      return nil
    rescue StandardError => e
      Rails.logger.error "Fetch Geofence Configuration For User StandardError -> #{e.message} - User ID: #{auth_data.user_id}, Tenant ID: #{auth_data.tenant_id}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
