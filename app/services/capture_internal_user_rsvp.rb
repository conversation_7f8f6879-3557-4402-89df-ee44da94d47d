class CaptureInternalUserRsvp
  include EventsPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting_id, rsvp_data)
    @meeting = Meeting.find meeting_id
    @rsvp_data = rsvp_data
  end

  def call
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.rsvp_response_blank')}") unless @rsvp_data[:rsvpResponse].present?

    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") unless @meeting.tenant_id == @tenant_id.to_i

      serialized_meeting = MeetingSerializer.call(@meeting, nil, false, nil, true).result

      current_participant = @meeting.participant_for_user(@user)
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.participant')}") unless current_participant.present?

      meeting_look_up = current_participant.meeting_look_ups.where(meeting: @meeting).first
      meeting_look_up.rsvp_response = @rsvp_data[:rsvpResponse]
      meeting_look_up.rsvp_message = @rsvp_data[:rsvpMessage]
      begin
        meeting_look_up.save!
        publish_events(
          'rsvp',
          'meeting',
          {
            meeting_participant: meeting_look_up,
            notify_organiser:  @rsvp_data[:notifyOrganiser],
            old_meeting: serialized_meeting,
            current_user_id: @user.id
          }
        )
        return meeting_look_up
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.participant')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in CaptureInternalUserRSVP"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

end
