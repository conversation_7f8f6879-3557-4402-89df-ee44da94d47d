class MeetingsScheduledInMinutesPublisher
  prepend SimpleCommand
  attr_reader :meetings

  def initialize(meetings)
    @meetings = meetings
  end

  def call
    Rails.logger.info "Meeting Service: MeetingScheduledInMinutesPublisher called"
    event = Event::MeetingScheduledInMinutes.new(@meetings)
    PublishEvent.call(event)
    Rails.logger.info "Event::MeetingScheduledInMinutes data #{event.to_json}"
  end
end
