class UnderscorizeHashKeys
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize data
    raise 'input should be hash' unless data.is_a?(Hash)
    @data = data.transform_keys! &:to_s
  end
  
  def call
    @data.transform_keys! &:underscore
    @data.each do |x,y| 
        if y.is_a? Hash
          UnderscorizeHashKeys.call(y)
        elsif y.is_a? Array
          y.each{|z| UnderscorizeHashKeys.call(z) if z.is_a?(Hash)}
        end
      end
  end

end