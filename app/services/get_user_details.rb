class GetUserDetails
  prepend SimpleCommand

  def initialize user_id, tenant_id, authorization_needed = true, name = nil
    @user_id = user_id
    @tenant_id = tenant_id
    @name = name
    @authorization_needed = authorization_needed
  end

  def call
    user = User.
      find_or_initialize_by(
        #TODO make the IAM call and return data
        id: @user_id,
        tenant_id: @tenant_id
      )
    if user.new_record?
      if @authorization_needed
        user_lookup = ValidateUsers.call([LookUp.new(entity:"#{LOOKUP_USER}_#{@user_id}", tenant_id: @tenant_id)]).result.first
        user.name = user_lookup.name
      else
        user.name = @name
      end
    end
    if user.save
      user
    else
      Rails.logger.error "GetUserDetails user errors #{user.errors.full_messages}"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.not_found("user: #{@user_id}")}||#{I18n.t('error.unauthorized')}")
    end
  end
end
