class PicklistValue::Update
  prepend SimpleCommand
  include EventsPublisher

  def initialize(params)
    @params = params
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.update_custom_field')}") unless auth_data.can_access?('customField', 'update')

      picklist_value =
        PicklistValue.where(
                        tenant_id: auth_data.tenant_id,
                        picklist_id: @params[:picklist_id],
                        id: @params[:id]
                      )
                      .includes(picklist: [:field])
                      .first
      field = picklist_value&.picklist&.field

      if picklist_value.nil? || field.is_standard?
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_value')}")
      end

      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result
      old_picklist_value_hash = PicklistValuesSerializer.call([picklist_value], field.system_default?).result.first
      old_serialized_field_data = V2::FieldSerializer.call(field).result

      begin
        ActiveRecord::Base.transaction do
          picklist_value.update!(display_name: @params[:displayName])
          field.update!(updated_by: user, updated_at: Time.now.utc)
          meetings_updated =
            Meeting.where(tenant_id: auth_data.tenant_id)
                    .where("cast (custom_field_values ->> '#{field.internal_name}' as jsonb) = '#{Arel.sql(escape_quotes({ id: picklist_value.id, name: old_picklist_value_hash['displayName'] }.to_json))}'")
                    .update_all(
                      "custom_field_values = (custom_field_values::jsonb || '#{Arel.sql(escape_quotes({ field.internal_name => { id: picklist_value.id, name: picklist_value.display_name } }.to_json))}')"
                    )
          Rails.logger.info "PicklistValue::Update Meetings updated #{meetings_updated}"
        end

        picklist_value_hash = PicklistValuesSerializer.call([picklist_value], field.system_default?).result.first
        publish_events(
          'update',
          'picklist_value',
          {
            old_picklist_value_hash: old_picklist_value_hash,
            picklist_value_hash: picklist_value_hash ,
            user: user,
            field: field.reload,
            old_serialized_field_data: old_serialized_field_data
          }
        )

        picklist_value_hash
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "PicklistValue::Update Error #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_value')}")
      rescue StandardError => e
        Rails.logger.error "PicklistValue::Update Standard Error #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_value')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in picklist value update"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def escape_quotes(value)
    value.split("'").join("''")
  end
end
