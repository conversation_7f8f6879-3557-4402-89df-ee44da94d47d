class PicklistValue::Delete
  prepend SimpleCommand
  include EventsPublisher

  def initialize(params)
    @params = params
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.delete_custom_field')}") unless auth_data.can_access?('customField', 'delete')

      picklist_value = PicklistValue.where(tenant_id: auth_data.tenant_id, picklist_id: @params[:picklist_id], id: @params[:id])
                                    .includes(picklist: [:field])
                                    .first
      field = picklist_value&.picklist&.field
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_field')}") if picklist_value.nil? || field.is_standard?
      old_serialized_field_data = V2::FieldSerializer.call(field).result

      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result

      meetings = Meeting.where(tenant_id: auth_data.tenant_id)
                        .where("custom_field_values @> ?", { field.internal_name => { id: picklist_value.id, name: picklist_value.display_name } }.to_json)

      if meetings.any?
        Rails.logger.error "Meetings with picklist value #{picklist_value.inspect} present."
        raise(ExceptionHandler::EntityCannotBeDestroyed, ErrorCode.picklist_value_cannot_be_deleted)
      else
        begin
          ActiveRecord::Base.transaction do
            picklist_value.destroy!
            field.update!(updated_by: user, updated_at: Time.now.utc)
          end
          publish_events('update', 'field', { field: field.reload, old_serialized_field_data: old_serialized_field_data })
        rescue ActiveRecord::RecordInvalid => e
          Rails.logger.error e.message
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_value')}")
        end
      end
    else
      Rails.logger.error "Unauthorised: User context missing in picklist value delete"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
