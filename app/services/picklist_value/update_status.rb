class PicklistValue::UpdateStatus
  prepend SimpleCommand
  include EventsPublisher

  def initialize(params, action)
    @params = params
    @action = action
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.update_custom_field')}") unless auth_data.can_access?('customField', 'update')

      picklist_value = PicklistValue.where(tenant_id: auth_data.tenant_id, picklist_id: @params[:picklist_id], id: @params[:id])
                                    .includes(picklist: [:field])
                                    .first
      field = picklist_value&.picklist&.field
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_field')}") if picklist_value.nil? || field.is_standard?
      old_serialized_field_data = V2::FieldSerializer.call(field).result

      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result

      begin
        ActiveRecord::Base.transaction do
          picklist_value.update!(disabled: (@action == :disable))
          field.update!(updated_by: user, updated_at: Time.now.utc)
        end
        publish_events('update', 'field', { field: field.reload, old_serialized_field_data: old_serialized_field_data })
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.picklist_field')}")
      end
    else
      Rails.logger.error "Unauthorised: User context missing in picklist value update #{@action}"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end
end
