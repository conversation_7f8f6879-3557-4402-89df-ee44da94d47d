# frozen_string_literal: true

class GetTeamDetails
  def initialize(team_id, tenant_id)
    @team_id = team_id
    @tenant_id = tenant_id
  end

  def call
    team = Team.find_or_initialize_by(
        id: @team_id,
        tenant_id: @tenant_id
      )

    if team.new_record?
      team = ValidateTeams.new([team]).call.first
    end

    if team.save
      team
    else
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.not_found("team: #{@team_id}")}||#{I18n.t('error.unauthorized')}")
    end
  end
end
