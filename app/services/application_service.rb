# frozen_string_literal: true

class ApplicationService
  def handle_errors
    yield

  rescue ExceptionHandler::AuthenticationError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_credentials,
        message: message
      },
      status: :unauthorized
    }

  rescue ExceptionHandler::InternalServerError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.internal_error,
        message: message
      },
      status: :internal_server_error
    }

  rescue ExceptionHandler::MissingToken => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.missing_token,
        message: message
      },
      status: :unauthorized
    }

  rescue ExceptionHandler::InvalidToken => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_token,
        message: message
      },
      status: :unauthorized
    }

  rescue ExceptionHandler::Forbidden => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.unauthorized,
        message: message
      },
      status: :unauthorized
    }

  rescue ExceptionHandler::InvalidDataError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ActiveRecord::RecordNotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::NotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::ParticipantNotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::MeetingExpired => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.meeting_expired,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::MeetingCancelled => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.meeting_cancelled,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::NoteCreateNotAllowed => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.note_create_not_allowed,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::NoteAccessNotAllowed => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.note_access_not_allowed,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::ConnectedAccountNotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.connected_account_not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::EntityCannotBeDestroyed => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.picklist_value_cannot_be_deleted,
        message: message
      },
      status: :bad_request
    }

  rescue ExceptionHandler::ProviderInternalServerError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.provider_internal_server,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::ProviderForbidden => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.provider_forbidden,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::ProviderUnauthorized => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.provider_unauthorized,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::ProviderInvalidDataError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.provider_invalid,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue StandardError => e    
    {
      error_details: {
        error_code: nil,
        message: e.message
      },
      status: :internal_server_error
    }
  end
end
