class ListenForContactDelete
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(CONTACT_EXCHANGE, CONTACT_DELETED_EVENT, CONTACT_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{CONTACT_DELETED_QUEUE}"
      payload = JSON(payload)
      id = payload["id"]
      tenant_id = payload["tenantId"]
      user_id  = payload["userId"]
      publish_usage = payload['publishUsage']

      UpdateMeetingForEntityDeleteEvent.call(id, tenant_id, user_id, LOOKUP_CONTACT, publish_usage)
    end
  end

end
