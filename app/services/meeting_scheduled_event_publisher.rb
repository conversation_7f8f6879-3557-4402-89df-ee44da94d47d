class MeetingScheduledEventPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting, participants)
    @meeting = meeting
    @participants = participants
  end

  def call
    @participants.each do |participant|
      if @meeting.organizer == participant
        publish_meeting_scheduled_to_organizer
      elsif [LO<PERSON>UP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_EXTERNAL].include? participant.entity_type
        publish_meeting_scheduled_to_participant participant
      end
    end
  end

  private

    def publish_meeting_scheduled_to_organizer
      event = Event::MeetingScheduled.new(@meeting)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingScheduled event for: #{@meeting.inspect} and #{@meeting.organizer.inspect} organizer"
    end

    def publish_meeting_scheduled_to_participant participant
      event = Event::MeetingScheduledWithParticipant.new(@meeting, participant)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingScheduledWithParticipant event for: #{@meeting.inspect} and #{participant.inspect} participant"
      Rails.logger.info "Event::MeetingScheduledWithParticipant event data: #{event.to_json}"
    end
end
