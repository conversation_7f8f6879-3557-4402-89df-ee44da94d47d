class GetLookUp
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize look_up_data
    @data = look_up_data
  end

  def call
    if @data[:id].blank? && (!@data[:entity].eql? LOOKUP_EXTERNAL)
      raise ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_participant')}"
    elsif @data[:tenant_id].blank?
      raise ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.tenant')}"
    elsif @data[:entity].blank?
      raise ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting_participant')}"
    else
      look_up = if @data[:entity].eql? LOOKUP_EXTERNAL
                  entity = @data[:id] ? "#{@data[:entity]}_#{@data[:id]}" : @data[:entity]

                  existing_look_up = LookUp.where(
                    tenant_id: @data[:tenant_id],
                    email: @data[:email],
                  ).where("entity LIKE '#{LOOKUP_EXTERNAL}%'")
                  if existing_look_up.exists?
                    existing_look_up.first
                  else
                    LookUp.new(
                      tenant_id: @data[:tenant_id],
                      entity: entity,
                      email: @data[:email]
                    )
                  end
                else
                  LookUp.find_or_initialize_by(
                    tenant_id: @data[:tenant_id],
                    entity: "#{@data[:entity]}_#{@data[:id]}"
                  )
                end
      if look_up.new_record?
        look_up.name = @data[:name]&.strip
        look_up.email = @data[:email]
      end
      look_up
    end
  end
end
