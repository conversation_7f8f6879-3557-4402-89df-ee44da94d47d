class GetMeetingDetails
  include MeetingAccessHelper
  prepend SimpleCommand

  def initialize id
    @id = id
  end

  def call
    @auth_data = GetSecurityContext.call.result
    if @auth_data
      @meeting = Meeting.find_by(id: @id, tenant_id: @auth_data.tenant_id)

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.meeting_details')}") unless user_can_read_meeting?(@meeting)

      @meeting
    end
  end
end
