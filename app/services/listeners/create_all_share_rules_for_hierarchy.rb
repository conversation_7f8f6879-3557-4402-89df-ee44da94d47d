# frozen_string_literal: true

module Listeners
  class CreateAllShareRulesForHierarchy
    prepend SimpleCommand
    include ActiveModel::Validations

    def call
      RabbitmqConnection.subscribe(USER_EXCHANGE, CREATE_ALL_MEETINGS_SHARE_RULES_EVENT, CREATE_ALL_MEETINGS_SHARE_RULES_QUEUE) do |payload|
        payload = JSON(payload)
        Rails.logger.info "Received message for #{CREATE_ALL_MEETINGS_SHARE_RULES_EVENT} for tenant id #{payload['tenantId']} for user id #{payload['ownerId']}"

        CreateOrUpdateAllShareRulesForUser.call(payload) if payload['userActions'].present?
      end
    end
  end
end
