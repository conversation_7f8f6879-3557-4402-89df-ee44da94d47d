# frozen_string_literal: true

module Listeners
  class DeleteAllShareRulesForHierarchy
    prepend SimpleCommand
    include ActiveModel::Validations

    def call
      RabbitmqConnection.subscribe(USER_EXCHANGE, DELETE_ALL_MEETINGS_SHARE_RULES_EVENT, DELETE_ALL_MEETINGS_SHARE_RULES_QUEUE) do |payload|
        payload = JSON(payload)
        Rails.logger.info "Received message for #{DELETE_ALL_MEETINGS_SHARE_RULES_EVENT} for tenant id #{payload['tenantId']} for user id #{payload['ownerId']}"

        DeleteAllShareRulesForUser.call(payload) if payload['userIds'].present?
      end
    end
  end
end
