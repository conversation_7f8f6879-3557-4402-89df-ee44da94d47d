# frozen_string_literal: true

# Class to listen event for account connect
class ListenForConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, CONNECTED_ACCOUNT_SYNC_EVENT, CONNECTED_ACCOUNT_SYNC_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{CONNECTED_ACCOUNT_SYNC_EVENT}"
      payload = JSON(payload)
      @user_id = payload['userId']
      @tenant_id = payload['tenantId']
      @name = "#{payload['firstName']} #{payload['lastName']}".strip
      @data = payload
      CreateConnectedAccount.call(@user_id, @tenant_id, @name, @data)
    end
  end
end
