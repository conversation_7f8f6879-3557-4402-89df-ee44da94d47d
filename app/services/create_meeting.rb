# frozen_string_literal: true

# TODO: Add possible refactors like avoiding modifying data before validation.
class CreateMeeting
  include MeetingEntitiesExtractor
  include EventsPublisher
  prepend SimpleCommand

  def initialize(params, imported_meeting = false)
    @params = UnderscorizeHashKeys.call(params.to_h.except(:customFieldValues))
      .result
      .merge(custom_field_values: params[:customFieldValues])
      .with_indifferent_access
    @user = nil
    @tenant_id = nil
    @auth_data = nil
    @owner = @params[:owner]
    @imported_meeting = imported_meeting
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id

      TenantUsageChecker.call(@tenant_id)

      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      if(@owner.present? && !@owner.is_a?(User))
        @owner = GetUserDetails.call(@owner[:id], @tenant_id).result
        @params[:owner] = @owner
      end

      @params[:participants] ||= []
      @params[:related_to] ||= []
      @params[:participants] << @params[:organizer] if @params[:organizer].present?
      if @owner.present?
        @params[:participants] << {
          tenant_id: @tenant_id,
          entity: LOOKUP_USER,
          id: @params[:owner].id,
          name: @params[:owner].name
        }.with_indifferent_access
      end

      participants_and_related_entities = (@params[:participants] + @params[:related_to]).uniq{|p| "#{p['entity']}_#{p['id']}"}

      @validated_entities =
        unless @imported_meeting
          ParticipantsValidator.call(
            {
              user: @user,
              participants: participants_and_related_entities,
              action: 'create',
              organizer_exists: @params[:organizer].present?
            }
          ).result
        else
          ParticipantsBuilder.call(
            {
              participants: participants_and_related_entities,
              owner: @params[:owner],
              organizer_exists: @params[:organizer].present?
            }
          ).result
        end

      validated_participants = get_validated_participants + get_valid_external_entities
      validated_related_to = get_validated_related_to
      time_zone = get_time_zone
      location_latitude = @params[:location_coordinate].present? ? @params[:location_coordinate][:lat] : nil
      location_longitude = @params[:location_coordinate].present? ? @params[:location_coordinate][:lon] : nil
      meeting = Meeting.new(@params.slice!(:participants, :related_to, :checked_in_details, :checked_out_details, :custom_field_values, :organizer, :location_coordinate))
      meeting.time_zone = time_zone
      if meeting.all_day? && meeting.from.present?
        bod = meeting.from.to_datetime.in_time_zone(meeting.time_zone.name).beginning_of_day
        eod = bod.end_of_day
        meeting.from = bod.utc
        meeting.to = eod.utc
      end
      meeting.owner = @owner || @user
      meeting.created_by = meeting.updated_by = @user
      meeting.tenant_id = @tenant_id
      meeting.location_latitude = location_latitude
      meeting.location_longitude = location_longitude
      meeting = modify_fields_according_to_status(meeting)
      meeting = get_meeting_attendace(meeting)
      meeting.custom_field_values = validate_custom_field_values(@params[:custom_field_values]) if @params[:custom_field_values].present?
      if should_create_online_meeting?(meeting)
        online_meeting_command = Calendar::Base.call(@auth_data, meeting, ONLINE_MEETING_CREATE_EVENT, @user,
                                                     { participants: validated_participants })
        if online_meeting_command.success?
          meeting.provider_link = online_meeting_command.result[:provider_link]
          meeting.provider_meeting_id = online_meeting_command.result[:provider_meeting_id]
        end
      end
      begin
        ActiveRecord::Base.transaction do
          meeting.save!
          validated_participants.each{ |participant| meeting.participants << participant }
          owner_id = @owner&.id || @user.id

          if @params[:organizer].present?
            organizer = @params[:organizer]
            meeting.participant_look_ups.joins(:look_up).
              where("look_ups.email": organizer[:email]).
              where( "look_ups.entity like ?", "#{organizer[:entity]}_%").update(organizer: true)
          else
            organizer_id = @imported_meeting ? owner_id : @user.id
            user_lookup = meeting.participant_look_ups.joins(:look_up).where("look_ups.entity": "#{LOOKUP_USER}_#{organizer_id}").first
            if user_lookup.present?
              user_lookup.update(organizer: true)
            else
              Rails.logger.error "Organizer is missing."
              raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.missing_organizer')}")
            end
          end
          validated_related_to.each{ |related_to| meeting.related_to << related_to }
          publish_events('create', 'meeting', {meeting: meeting, current_user_id: @user.id})
          if meeting.meeting_attendances.present?
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
            publish_events(
              'checkin',
              'meeting',
              {
                meeting_ids: [meeting.id],
                tenant_id: @tenant_id,
                current_user_id: @user.id,
                meeting_attendance: meeting_attendance
              }
            )
            MeetingCheckedinBeyondGeofenceEventPublisher.call(meeting, @user, meeting.tenant_id) if meeting_attendance.is_checked_in_outside_geofence
          end

          MeetingNotification.schedule(meeting)
          meeting
        end
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: e.message.gsub(VALIDATION_FAILED_ERROR, ''))}")
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in CreateMeeting'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def should_create_online_meeting?(meeting)
    meeting.online? && meeting.valid?
  end

  def get_time_zone
    time_zone_data = @params[:timezone]
    time_zone = nil
    if time_zone_data
      time_zone_data[:tenant_id] = @auth_data.tenant_id
      time_zone_data[:entity] = LOOKUP_TIMEZONE
      time_zone = GetLookUp.call(time_zone_data).result
    else
      user_settings = UserSettingService.new.fetch
      user_time_zone = user_settings[:timezone]
      time_zone_picklist_value = PicklistValue.find_by(tenant_id: @auth_data.tenant_id, internal_name: user_time_zone)
      if time_zone_picklist_value.present?
        time_zone = GetLookUp.call({
          id: time_zone_picklist_value.id,
          name: user_time_zone,
          tenant_id: @auth_data.tenant_id,
          entity: LOOKUP_TIMEZONE
        }).result
      end
    end
    @params.delete(:timezone)
    time_zone
  end

  def get_meeting_attendace(meeting)
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.checked_out_details')}") if @params[:checked_out_details].present?

    if @params[:checked_in_details].present?
      meeting_attendance = meeting.meeting_attendances.new(
        checked_in_at: DateTime.now,
        checked_in_latitude: @params.dig(:checked_in_details, :latitude),
        checked_in_longitude: @params.dig(:checked_in_details, :longitude),
        user_id: @user.id
      )

      unless meeting_attendance.valid?
        Rails.logger.error "Invalid: Could not check in meeting. Errors: #{meeting_attendance.errors.full_messages.inspect}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: meeting_attendance.errors.full_messages.join(', '))}")
      end

      meeting_attendance.set_is_checked_in_outside_geofence(@params.dig(:checked_in_details), @user, meeting)
    end

    meeting
  end

  def modify_fields_according_to_status(meeting)
    case meeting.status
    when SCHEDULED, MISSED
      meeting.status = SCHEDULED
      meeting.status = MISSED if meeting.missed?
    when CONDUCTED
      meeting.conducted_at = DateTime.now.utc
      meeting.conducted_by = @user
    when CANCELLED
      meeting.cancelled_at = DateTime.now.utc
      meeting.cancelled_by = @user
    end
    meeting
  end

  def validate_custom_field_values(custom_field_values)
    custom_fields = Field.where(tenant_id: @auth_data.tenant_id, is_standard: false).to_a
    inactive_fields = custom_fields.filter { |field| !field['active'] }.map(&:internal_name)
    incoming_inactive_fields = custom_field_values.keys.filter { |key| inactive_fields.include?(key) }
    if incoming_inactive_fields.any?
      Rails.logger.error "CreateMeeting - Inactive fields - #{incoming_inactive_fields.join(', ')}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.inactive_fields', fields: incoming_inactive_fields.join(', '))}")
    end

    non_existant_fields = custom_field_values.keys - custom_fields.map(&:internal_name)
    if non_existant_fields.any?
      Rails.logger.error "CreateMeeting - Invalid custom fields - #{non_existant_fields.join(', ')}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.non_existant_field', fields: non_existant_fields.join(', '))}")
    end

    custom_field_values = custom_field_values.slice(*custom_fields.map(&:internal_name))
    custom_field_values.each do |key, value|
      field = custom_fields.find { |f| f.internal_name == key }

      unless valid_value?(field, value)
        Rails.logger.error "CreateMeeting Invalid value #{value.inspect} for field type #{field.field_type}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.custom_field', field: field.field_type, value: value)}")
      end
    end

    custom_field_values
  end
end
