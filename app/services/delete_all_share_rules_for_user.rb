# frozen_string_literal: true

class DeleteAllShareRulesForUser
  prepend SimpleCommand
  include EventsPublisher

  def initialize(params)
    @params = params
  end

  def call
    return if @params['userIds'].blank?

    ShareRule.where(
      tenant_id: @params['tenantId'],
      from_id: @params['ownerId'],
      from_type: USER,
      to_id: @params['userIds'],
      to_type: USER,
      share_all_records: true,
      system_default: true
    ).to_a.each do |share_rule_to_delete|
      serialized_share_rule = ShareRuleSerializer.new(share_rule_to_delete, payload_for_event: true).call
      if share_rule_to_delete.destroy
        publish_events('delete', 'share_rule', { serialized_share_rule: serialized_share_rule, user_id: @params['ownerId'], tenant_id: @params['tenantId'] })
      end
    end
  end
end
