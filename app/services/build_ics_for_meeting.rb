require 'icalendar'

class BuildIcsForMeeting
  prepend SimpleCommand

  def initialize meeting
    @meeting = meeting
  end

  def call
    cal = Icalendar::Calendar.new
    cal.event do |e|
      e.uid = @meeting.public_id
      if @meeting.all_day
        e.dtstart     = Icalendar::Values::Date.new(@meeting.from.strftime("%Y%m%d"))
        e.dtend     = Icalendar::Values::Date.new ((@meeting.from.to_date + 1.day).strftime("%Y%m%d"))
      else
        e.dtstart     = @meeting.from.utc.strftime("%Y%m%dT%H%M%SZ")
        e.dtend       = @meeting.to.utc.strftime("%Y%m%dT%H%M%SZ")
      end
      e.summary     = @meeting.title
      e.location = @meeting.location
      e.description = @meeting.description
      if @meeting.status == SCHEDULED
        e.status = "CONFIRMED"
        cal.ip_method="REQUEST"
      elsif @meeting.status == CANCELLED
        e.status = "CANCELLED"
        cal.ip_method="CANCEL"
      end
      e.last_modified = @meeting.updated_at.utc.strftime("%Y%m%dT%H%M%SZ")
      e.organizer = Icalendar::Values::CalAddress.new("mailto:#{@meeting.organizer.email}", cn: @meeting.organizer.name)
      @meeting.participants.each do |participant|
        attendee = Icalendar::Values::CalAddress.new(
          "mailto:#{participant.email}",
          'cutype' => 'INDIVIDUAL',
          'role' => 'REQ-PARTICIPANT',
          'partstat' => 'NEEDS-ACTION',
          'cn' => participant.name,
          'x-num-guests' => 0
        )
        e.append_attendee attendee
      end
    end
    if @meeting.time_zone
      cal.timezone do |t|
        t.tzid = @meeting.time_zone.name
      end
    end
    cal.to_ical
  end
  
end
