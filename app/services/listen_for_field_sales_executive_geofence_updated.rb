# frozen_string_literal: true

class ListenForFieldSalesExecutiveGeofenceUpdated
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(FIELD_SALES_EXCHANGE, FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_EVENT, FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)

      Rails.logger.info "Received geofence update for executive ID - #{payload.dig('executive','id')}"

      user = User.find_by(id: payload.dig('executive', 'id'), tenant_id: payload.dig('tenantId'))
      return unless user.present?

      geofence_config = user.geofence_config || {}
      geofence_config[:meetingCheckInCheckOut] = payload.dig('entity', 'meetingCheckInCheckOut')
      geofence_config[:fieldSalesEnabled] = true
      user.update!(geofence_config: geofence_config)
    end
  end
end
