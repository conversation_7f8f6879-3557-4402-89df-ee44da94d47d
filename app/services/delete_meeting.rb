class DeleteMeeting
  include EventsPublisher
  prepend SimpleCommand
  attr_accessor(:user, :tenant_id, :auth_data, :meeting_id)

  def initialize meeting_id, publish_usage = true
    @meeting_id = meeting_id
    @publish_usage = false?(publish_usage) ? false : true
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @meeting = Meeting.find_by(id: @meeting_id, tenant_id: @auth_data.tenant_id)
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.meeting')}") if @meeting.blank?

      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||#{I18n.t('error.forbidden.delete_meeting')}") unless user_has_valid_permissions?

      old_serialized_meeting = MeetingSerializer.call(@meeting, nil, false, @user, true).result
      begin
        result = dup_meeting_data
       
        #We dont want to delete meeting from calendar as it sends cancel notification to all lead/contact
        #Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_DELETE_EVENT, @user) if should_delete_online_meeting?

        @meeting.destroy!

        DeleteAssociatedShareRulesJob.perform_later(@meeting_id, @user.id, @meeting.tenant_id)

        PublishUsageJob.perform_later(@auth_data.tenant_id) if @publish_usage

        publish_events('destroy', 'meeting', {meeting: result, deleted_by_id: @user.id, old_meeting: old_serialized_meeting})
        Rails.logger.info "MEETING id #{@meeting.id}, title: #{@meeting.title}, is deleted by tenantId #{@auth_data.tenant_id}, userId #{@user.id}"
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "Error while deleting the meeting : #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.meeting', error: e.message)}")
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in deleting Meeting'
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end
  end

  private

  def should_delete_online_meeting?
    @meeting.online? && @meeting.valid? && @meeting.status != CANCELLED && @meeting.created_by_id == @user.id
  end

  def user_has_valid_permissions?
    meeting_permission = @auth_data.permissions.find{ |p| p.name == 'meeting' }

    return meeting_permission&.action&.delete if @meeting.created_by_id.eql?(@user.id)
    return meeting_permission&.action&.delete_all unless @meeting.created_by_id.eql?(@user.id)
  end

  def dup_meeting_data
    result     = @meeting.dup
    result.id  = @meeting.id
    result.updated_at   = @meeting.updated_at
    result.related_to   = @meeting.related_to.dup
    result.participants = @meeting.participants.dup
    result.organizer = @meeting.organizer
    result
  end

  def false?(value)
    value == 'false' || value == false
  end
end
