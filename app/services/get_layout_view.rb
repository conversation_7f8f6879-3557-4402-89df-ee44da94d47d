# frozen_string_literal: true

class GetLayoutView
  prepend SimpleCommand

  def initialize(params)
    @params = params
  end

  def call
    command = GetSecurityContext.call
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    auth_data = command.result
    tenant_id = auth_data.tenant_id

    prepare_layout(tenant_id)
  end

  private

  def prepare_layout(tenant_id)
    fields = get_fields(tenant_id)
    layout = load_layout

    related_to_hash = {
      'LEAD' => RELATED_LEAD_LOOKUP_URL,
      'DEAL' => RELATED_DEAL_LOOKUP_URL,
      'CONTACT' => RELATED_CONTACT_LOOKUP_URL,
      'COMPANY' => RELATED_COMPANY_LOOKUP_URL
    }.with_indifferent_access

    layout.each do |_, layout_v|
      layout_v.map! do |section|
        if section.dig('item', 'heading') == 'Other Details'
          internal_names = @custom_fields.map(&:internal_name)
          fields.select { |f| internal_names.include?(f['internalName']) }.each do |f|
            section['layoutItems'] << generate_custom_layout_item(f)
          end
          section
        else
          section.each do |section_k, section_v|
            if section_k == 'layoutItems'
              section_v.map! do |layout_item|
                layout_item.each do |item_k, item_v|
                  if item_k == 'item'
                    field = fields.find { |f| f['internalName'] == item_v['internalName'] }
                    if field['internalName'] == 'relatedTo'
                      field['pickLists'].each { |picklist_v| picklist_v.merge!('lookupUrl' => related_to_hash[picklist_v['name']]) }
                    end
                    item_v.merge!(field)
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  def get_fields(tenant_id)
    fields = Field.where(tenant_id: tenant_id)
    raise( ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found.field')}") if fields.blank?

    fields = fields.where(is_internal: false) if @params[:view] == 'create'
    fields = fields.reject { |field| EXCLUDED_MEETING_LAYOUT_FIELDS.include?(field['internal_name']) }
    @custom_fields = fields.select { |field| !field.is_standard? }
    @custom_field_position = 0 if @custom_fields.present?

    get_serialized_fields(fields)
  end

  def get_serialized_fields(fields)
    json = Jbuilder.new
    json.array! fields do |f|
      json.type field_type(f)
      json.displayName f.display_name
      json.internalName f.internal_name
      json.internal internal?(f)
      json.sortable f.is_sortable
      json.filterable f.is_filterable
      json.required f.is_required
      json.standard f.is_standard
      json.readOnly read_only_field?(f)
      json.active f.active
      json.lookupUrl f.field_type == 'LOOK_UP' ? ( f.internal_name == 'owner' ? USER_OWNER_URL : USER_LOOKUP_URL) : (f.field_type == 'MEETING_INVITEES' ? MEETING_INVITEE_LOOKUP_URL : nil)
      json.pickLists f.picklist.present? ? PicklistValuesSerializer.call(f.picklist.picklist_values, f.system_default?).result : nil
    end
  end

  def read_only_field?(field)
    !field.active || (field.internal_name == 'medium' && @params[:view] == 'edit') || %w[providerLink organizer].include?(field.internal_name)
  end

  def field_type(f)
    f.field_type == 'ENTITY_PICKLIST' ? 'PICK_LIST' : f.field_type
  end

  def internal?(f)
    f.field_type == 'ENTITY_PICKLIST' ? true : f.is_internal
  end

  def load_layout
    layout = YAML.load_file("#{Rails.root}/config/meeting-layout-view.json")

    if @params[:view] == 'create'
      layout["layoutItems"] = layout["layoutItems"].reject do |section|
        ['Internals', 'Check In & Check Out Internals'].include?(section['item']['heading'])
      end
    end

    unless @custom_fields.present?
      layout["layoutItems"] = layout["layoutItems"].reject do |section|
        section['item']['heading'] == 'Other Details'
      end
    end

    layout
  end

  def generate_custom_layout_item(field)
    @custom_field_position += 1
    {
      column: @custom_field_position.even? ? 2 : 1,
      row: (@custom_field_position.to_f / 2).ceil,
      type: 'FIELD',
      width: 6,
      item: {
          description: nil,
          greaterThan: nil,
          length: nil,
          lessThan: nil,
          multiValue: false,
          regex: nil,
          unique: false
      }.merge(field),
      layoutItems: [
      ]
    }.with_indifferent_access
  end
end
