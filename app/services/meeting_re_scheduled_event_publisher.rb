class MeetingReScheduledEventPublisher
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(meeting, participants)
    @meeting = meeting
    @participants = participants
  end

  def call
    @participants.each do |participant|
      if @meeting.organizer == participant
        publish_meeting_re_scheduled_to_organizer
      elsif [LOOKUP_USER, LO<PERSON>UP_LEAD, LOOKUP_CONTACT, LOOKUP_EXTERNAL].include? participant.entity_type
        publish_meeting_re_scheduled_to_participant participant
      end
    end
  end

  private

    def publish_meeting_re_scheduled_to_organizer
      event = Event::MeetingReScheduled.new(@meeting)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingReScheduled event data: #{event.to_json}"
    end

    def publish_meeting_re_scheduled_to_participant participant
      event = Event::MeetingReScheduledWithParticipant.new(@meeting, participant)
      PublishEvent.call(event)
      Rails.logger.info "Event::MeetingReScheduledWithParticipant event data: #{event.to_json}"
    end
end
