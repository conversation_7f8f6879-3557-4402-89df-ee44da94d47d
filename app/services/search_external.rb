require 'rest-client'
class SearchExternal
  prepend <PERSON>Command

  def initialize(email_ids, tenant_id)
    @email_ids = email_ids
    @tenant_id = tenant_id
  end

  def call
    return [] if @email_ids.blank?
    matched = []
    unmatched = []
    @email_ids.each do |email|
      look_up = LookUp.where(tenant_id: @tenant_id, email: email).first
      if look_up.present?
        matched << { entity: look_up.entity, email: email, name: look_up.name, tenant_id: @tenant_id }
      else
        unmatched << { entity: LOOKUP_EXTERNAL, email: email, tenant_id: @tenant_id, name: email }
      end
    end

    d =  { matched: matched, unmatched: unmatched }

    Rails.logger.info "SEARCH EXTERNAL: #{@email_ids} | #{d}"
    return { matched: matched, unmatched: unmatched }
  end
end

