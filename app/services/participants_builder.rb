class ParticipantsBuilder
  prepend SimpleCommand

  def initialize params
    @params = params
    @tenant_id = params[:owner].tenant_id
    @organizer_exists = params[:organizer_exists]
  end

  def call
    build_participants
  end

  private

  def build_participants
    participants_data = @params[:participants] || []
    unless @organizer_exists
      participants_data << {
        tenant_id: @tenant_id,
        entity: LOOKUP_USER,
        id: @params[:owner].id,
        name: @params[:owner].name
      }
    end
    participants = participants_data.collect do |participant_data|
      participant_data[:tenant_id] = @tenant_id
      look_up = GetLookUp.call(participant_data).result
      look_up.name = participant_data[:name]&.strip if participant_data[:name]
      look_up.email = participant_data[:email] if participant_data[:email]
      look_up.owner_id = participant_data[:owner_id] if participant_data[:owner_id].present?
      look_up.save unless look_up.new_record?
      look_up
    end

    remove_duplicates(participants)
  end

  def remove_duplicates(participants)
    participants.uniq { |participant| participant.entity }
  end
end
