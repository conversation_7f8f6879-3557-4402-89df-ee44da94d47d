# frozen_string_literal: true

class CreateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize(user_id, tenant_id, name, params)
    @user_id = user_id
    @tenant_id = tenant_id
    @data = params
    @name = name
  end

  def call
    @user = GetUserDetails.call(@user_id, @tenant_id, false, @name).result
    create_connected_account(@user)
  end

  private

  def create_connected_account(user)
    acct = ConnectedAccount.find_or_initialize_by(provider_name: @data['providerName'], tenant_id: @tenant_id,
                                                  email: @data['email'], user_id: user.id)
    sync_type =
      case @data['syncType']
      when 'KYLAS_TO_CALENDAR'
        { kylas_to_calendar: true, calendar_to_kylas: false }
      when 'CALENDAR_TO_KYLAS'
        { kylas_to_calendar: false, calendar_to_kylas: true }
      when 'TWO_WAY'
        { kylas_to_calendar: true, calendar_to_kylas: true }
      else
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.sync_type')}")
      end

    expiry = (DateTime.now + 50.minutes).to_i
    acct.assign_attributes(access_token: @data['accessToken'],
                           expires_at: expiry,
                           active: true,
                           calendar_id: @data['calendarId'],
                           sync_type: sync_type)
    SubscribeToWebhook.call(acct.reload) if acct.save!
  end
end
