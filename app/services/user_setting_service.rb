# frozen_string_literal: true

require 'rest-client'

class UserSettingService
  def fetch(user_id = nil)
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    @user_id = user_id
    @token = command.result
    fetch_user_settings
  end

  private

  def fetch_user_settings
    response = RestClient.get(
      "#{SERVICE_IAM}/v1/users/#{ @user_id || 'me' }",
      {
        :Authorization => "Bearer #{@token}",
        content_type: :json,
        accept: :json
      }
    )
    body = JSON(response.body)

    {
      timezone: body['timezone'],
      date_format: body['dateFormat'],
      permissions: body['permissions']
    }
  rescue RestClient::NotFound => e
    Rails.logger.error "Error while fetching User setting: 404 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_setting_response')}")
  rescue RestClient::InternalServerError => e
    Rails.logger.error "Error while fetching User Setting: 500 - #{e.message}"
    raise(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||#{I18n.t('error.internal_server.user_setting_response')}")
  rescue RestClient::BadRequest => e
    Rails.logger.error "Error while fetching User setting: 400 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_setting_response')}")
  rescue StandardError => e
    Rails.logger.error "Error while fetching User setting: 400 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid.user_setting_response')}")
  end
end
