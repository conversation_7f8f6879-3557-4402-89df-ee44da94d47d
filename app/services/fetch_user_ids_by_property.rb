# frozen_string_literal: true
require 'rest-client'

class FetchUserIdsByProperty
  prepend SimpleCommand

  def initialize(rule)
    @rule = rule
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}") unless command.success?

    token = command.result

    url = '/v1/users/search-for-id'

    begin
      response = RestClient.post(
        SERVICE_IAM + url,
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json
        }
      )

      return JSON(response.body) unless response.nil?

      Rails.logger.error 'Get User Ids by Team Ids - invalid response'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get User Ids by Team Ids - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get User Ids by Team Ids - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get User Ids by Team Ids - 400 - StandardError -> #{e.message}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  private

  def payload
    {
      "fields": [],
      "jsonRule": {
        "id": @rule.property,
        "field": @rule.property,
        "type": @rule.type,
        "input": nil,
        "operator": @rule.operator,
        "value": @rule.value,
        "data": nil,
        "property": nil,
        "primaryField": nil,
        "condition": nil,
        "not": nil,
        "rules": nil,
        "group": false
      }
    }
  end
end
