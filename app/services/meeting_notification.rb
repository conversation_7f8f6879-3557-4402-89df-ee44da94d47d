# frozen_string_literal: true

class MeetingNotification
  def self.schedule(meeting)
    next_3am = '03:00 AM'.to_time
    if next_3am < Time.now
      next_3am += 1.day
    end

    if meeting.from > DateTime.now + 15.minutes && meeting.from < next_3am
      perform_in = (((meeting.from - 15.minutes) - DateTime.now) / 60)
      MeetingScheduledPublisherJob.set(wait: perform_in.minutes).perform_later([meeting.id])
    end
  end
end
