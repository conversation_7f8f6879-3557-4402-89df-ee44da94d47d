class LookUp < ApplicationRecord
  attribute :public_id, :string

  has_many :meeting_look_ups
  has_many :meetings, through: :meeting_look_ups

  validates :entity, :public_id, presence: true
  validate :entity_data
  validates :email, allow_nil: true, format: {with: URI::MailTo::EMAIL_REGEXP}
  validate :freeze_entity_once_created, on: :update

  after_save :save_external_entity_id

  after_initialize do
    self.public_id = SecureRandom.uuid unless self.public_id
  end

  def entity_id
    entity.split("_")[1].to_i
  end

  def entity_type
    entity.split("_")[0]
  end

  def is_a_user?
    entity_type == LOOKUP_USER
  end

  def is_a_lead?
    entity_type == LOOKUP_LEAD
  end

  def is_a_contact?
    entity_type == LOOKUP_CONTACT
  end

  def is_a_deal?
    entity_type == LOOKUP_DEAL
  end

  def is_a_company?
    entity_type == LOOKUP_COMPANY
  end

  def future_scheduled_meeting_time_from_now
    meetings.where(Meeting.arel_table[:from].gt(DateTime.now)).where(status: SCHEDULED).maximum(:from)
  end

  private
  def entity_data
    if entity.present?
      return true if entity.eql? LOOKUP_EXTERNAL
      value = entity.split('_')
      if value.length != 2 || !LOOKUP_TYPES.include?(value[0]) || (value[1].to_i == 0)
        errors.add(:entity, 'Invalid Value')
      end
    end
  end

  def freeze_entity_once_created
    if changes[:entity].present?
      errors.add(:entity, 'Lookup entity cannot be modified')
    end
  end

  def save_external_entity_id
    return true unless entity.eql? LOOKUP_EXTERNAL
    self.update_column(:entity, "external_#{self.reload.id}")
  end
end
