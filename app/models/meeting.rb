# frozen_string_literal: true

class Meeting < ApplicationRecord
  include MeetingAccessHelper

  attribute :status, :string, default: SCHEDULED
  attribute :medium, :string, default: OFFLINE

  has_many :meeting_attendances, dependent: :destroy

  has_one :organizer_look_up,
    -> { where organizer: true },
    class_name: 'MeetingLookUp',
    dependent: :destroy

  has_many :participant_look_ups,
    -> { where(participant: true) },
    class_name: 'MeetingLookUp',
    dependent: :destroy

  has_many :related_to_look_ups,
    -> { where related: true },
    class_name: 'MeetingLookUp',
    dependent: :destroy

  has_many :participants,
    through: :participant_look_ups,
    source: :look_up

  has_one :organizer,
    through: :organizer_look_up,
    source: :look_up

  has_many :related_to,
    through: :related_to_look_ups,
    source: :look_up
  has_many :notes, dependent: :destroy
  belongs_to :conducted_by, class_name: 'User', optional: true
  belongs_to :cancelled_by, class_name: 'User', optional: true
  belongs_to :imported_by, class_name: 'User', optional: true
  belongs_to :owner,
    class_name: 'User'
  belongs_to :created_by,
    class_name: 'User'
  belongs_to :updated_by,
    class_name: 'User'
  belongs_to :time_zone,
    class_name: 'LookUp',
    optional: true

  validates :title, :from, :tenant_id, :owner, :created_by, :updated_by, :public_id, presence: true
  validates :to,
    presence: true,
    unless: Proc.new { |meeting| meeting.all_day }
  validates :all_day,
    inclusion: [true, false]
  validate :duration
  validates :status, inclusion: { in: [SCHEDULED, CONDUCTED, CANCELLED, MISSED], message: 'Invalid status' }
  validates :medium, inclusion: { in: [OFFLINE, GOOGLE_PROVIDER, MICROSOFT_TEAMS_PROVIDER], message: I18n.t('meetings.invalid_medium') }
  validates :location_latitude, numericality: { greater_than_or_equal_to: -90, less_than_or_equal_to: 90 }, allow_nil: true
  validates :location_longitude, numericality: { greater_than_or_equal_to: -180, less_than_or_equal_to: 180 }, allow_nil: true
  validate :cannot_update_cancelled_meeting, on: :update
  validate :conducted_status_and_conducted_fields, on: :update

  after_initialize do
    unless persisted?
      self.public_id = SecureRandom.uuid unless self.public_id
    end
  end

  def missed?
    (
      (status == MISSED) ||
      (
        (status == SCHEDULED) && (to.present? && to < Time.now)
      )
    )
  end

  def online?
    [GOOGLE_PROVIDER, MICROSOFT_TEAMS_PROVIDER].include?(medium)
  end

  def self.string_fields
    ['location', 'status', 'title']
  end

  def self.boolean_fields
    ['all_day']
  end

  def self.date_fields
    ['from', 'to', 'conducted_at', 'cancelled_at', 'created_at', 'updated_at']
  end

  def self.long_double_fields
    [
      'id', 'owner', 'created_by', 'updated_by', 'conducted_by', 'cancelled_by',
      'imported_by', 'created_by_fields', 'updated_by_fields', 'conducted_by_fields',
      'cancelled_by_fields', 'organizer_fields'
    ]
  end

  def self.owned
    auth_data = GetSecurityContext.call.result
    if auth_data
      where(owner_id: auth_data.user_id).where(tenant_id: auth_data.tenant_id)
    else
      nil
    end
  end

  def participant_for_user user
    participants.where(entity: "#{LOOKUP_USER}_#{user.id}").first
  end

  def get_record_actions
    @auth_data = GetSecurityContext.call.result

    # TODO: Need to refactor user read check as N+1 query and multiple api calls
    # This will work for the time being as this is used only in meeting serializer where
    # meeting is retrieved from search results. This being false is irrelevant as this
    # meeting data will be present in the response api.
    {
      read: true,
      update: user_can_update_meeting?(self)
    }
  end

  def upcoming?
    from && from > Time.current
  end

  def ics
    BuildIcsForMeeting.call(self).result
  end

  def self.usage_per_tenant(tenant_id = nil)
    meeting_data = tenant_id.present? ? where(tenant_id: tenant_id).group(:tenant_id).select("tenant_id, count(*)") : group(:tenant_id).select("tenant_id, count(*)")
    data = []
    meeting_data.each do |d|
      data << { tenantId: d.tenant_id, count: d.count, usageEntity: 'MEETING'}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: 'MEETING'} if (data.blank? && tenant_id.present?)
    data
  end

  def publish_entity_metadata
    participant_look_ups.collect{ |l| l.publish_entity_metadata }
    related_to_look_ups.collect{ |l| l.publish_entity_metadata }
  end

  private

  def duration
    if from && to && from >= to
      errors.add(:base, 'cannot end before it starts')
    end
  end

  def cannot_update_cancelled_meeting
    if status_was == CANCELLED && changed?
      errors.add(:base, 'cannot update cancelled meeting')
    end
  end

  def publish_entity_metadata_on_schedule_change
    if from_changed? || to_changed?
      publish_entity_metadata
    end
  end

  def conducted_status_and_conducted_fields
    if status != CONDUCTED && (conducted_at.present? || conducted_by.present?)
      errors.add(:base, 'cannot have conducted at or conducted by for unconducted meetings')
    elsif status == CONDUCTED && (conducted_at.blank? || conducted_by.blank?)
      errors.add(:base, 'cannot have conducted at or conducted by blank for conducted meetings')
    end
  end
end
