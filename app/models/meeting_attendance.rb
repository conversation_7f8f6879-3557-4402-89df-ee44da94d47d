class MeetingAttendance  < ApplicationRecord
  belongs_to :meeting
  belongs_to :user

  validate :check_presence_of_dependent_fields
  validate :cannot_check_out_without_check_in

  def checked_in?
    checked_in_at.present?
  end

  def checked_out?
    checked_out_at.present?
  end

  def set_is_checked_in_outside_geofence(checkin_details, user, meeting)
    meeting_lat = meeting.location_latitude
    meeting_long = meeting.location_longitude
    checkin_lat = checkin_details.dig(:latitude)
    checkin_long = checkin_details.dig(:longitude)
    self.is_checked_in_outside_geofence = false

    return unless meeting_lat && meeting_long && checkin_lat && checkin_long

    geofence_config = user.get_geofence_config
    
    return unless (geofence_config.dig('meetingCheckInCheckOut').present? && geofence_config.dig('fieldSalesEnabled'))

    restrict_check_in = geofence_config.dig("meetingCheckInCheckOut", "restrictCheckIn")
    radius = geofence_config.dig("meetingCheckInCheckOut", "radius")

    distance = Geocoder::Calculations.distance_between([meeting_lat, meeting_long], [checkin_lat, checkin_long], units: :km)
    is_outside_geofence = (distance * 1000) > radius

    Rails.logger.info "Distance between meeting and checkin location: #{distance * 1000} meters for meeting: #{meeting_id}"
    self.is_checked_in_outside_geofence = is_outside_geofence

    return unless is_outside_geofence

    if restrict_check_in
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_checkin_location}||#{I18n.t('error.invalid.checkin_location')}")
    end

    MeetingCheckedinBeyondGeofenceEventPublisher.call(meeting, user, meeting.tenant_id) unless meeting.new_record?
  end

  def set_is_checked_out_outside_geofence(checkout_details, user, meeting)
    meeting_lat = meeting.location_latitude
    meeting_long = meeting.location_longitude
    checkout_lat = checkout_details.dig(:latitude)
    checkout_long = checkout_details.dig(:longitude)

    self.is_checked_out_outside_geofence = false
    return unless meeting_lat && meeting_long && checkout_lat && checkout_long

    geofence_config = user.get_geofence_config
    
    return unless (geofence_config.dig('meetingCheckInCheckOut').present? && geofence_config.dig('fieldSalesEnabled'))

    radius = geofence_config.dig("meetingCheckInCheckOut", "radius")
    distance = Geocoder::Calculations.distance_between([meeting_lat, meeting_long], [checkout_lat, checkout_long], units: :km)
    
    Rails.logger.info "Distance between meeting and checkout location: #{distance * 1000} meters for meeting: #{meeting_id} user: #{user.id}"
    
    self.is_checked_out_outside_geofence = (distance * 1000) > radius
  end

  private

  def check_presence_of_dependent_fields
    checked_in_fields = [checked_in_at, checked_in_latitude, checked_in_longitude]
    if checked_in_fields.any?(&:present?) && !checked_in_fields.all?(&:present?)
      errors.add(:base, 'cannot check in without time or location details')
    end

    checked_out_fields = [checked_out_at, checked_out_latitude, checked_out_longitude]
    if checked_out_fields.any?(&:present?) && !checked_out_fields.all?(&:present?)
      errors.add(:base, 'cannot check out without time or location details')
    end
  end

  def cannot_check_out_without_check_in
    if checked_out? && !checked_in?
      errors.add(:base, 'cannot check out without checking in')
    end
  end
end
