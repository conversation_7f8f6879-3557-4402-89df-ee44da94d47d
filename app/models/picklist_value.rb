class PicklistValue < ApplicationRecord
  validates :internal_name, presence: true, uniqueness: { scope: :picklist_id, case_sensitive: false }
  validates :display_name, presence: true
  validates :tenant_id, presence: true

  # TODO Add field system_default when custom picklist values required

  belongs_to :picklist

  validate :freeze_picklist_value, on: :update
  validate :tenant, on: [:create, :update]

  private

  def freeze_picklist_value
    if self.changes.present? && self.picklist.field&.is_standard?
      errors.add(:base, 'cannot update system default picklist values.')
    end
  end

  def tenant
    unless tenant_id == self.picklist&.tenant_id
      errors.add(:tenant_id, 'value cannot be mismatched between picklist and value.')
    end
  end
end
