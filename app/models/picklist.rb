class Picklist < ApplicationRecord
  validates :internal_name, presence: true, uniqueness: { scope: [:tenant_id] }
  validates :display_name, presence: true
  validates :tenant_id, presence: true

  has_many :picklist_values, class_name: 'PicklistValue', dependent: :destroy
  belongs_to :field

  validate :tenant, on: [:create, :update]
  validate :field_type

  private

  def tenant
    unless tenant_id == self.field&.tenant_id
      errors.add(:tenant_id, 'value cannot be mismatched between picklist and value.')
    end
  end

  def field_type
    if self.field.present? && PICKLIST_FIELD_TYPES.exclude?(self.field.field_type)
      errors.add(:field, "cannot have picklist for field type other than #{PICKLIST_FIELD_TYPES.join(' or ')}.")
    end
  end
end
