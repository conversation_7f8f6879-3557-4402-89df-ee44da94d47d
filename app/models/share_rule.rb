# frozen_string_literal: true

class ShareRule < ApplicationRecord
  validates :tenant_id, :from_id, :to_id, presence: true

  validates :from_type, inclusion: { in: [USER] }
  validates :to_type, inclusion: { in: [USER, TEAM] }

  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User'

  def self.string_fields
    ['name', 'from_type', 'to_type']
  end

  def self.boolean_fields
    ['system_default']
  end

  def self.date_fields
    ['created_at', 'updated_at']
  end

  def self.long_double_fields
    ['from_id', 'to_id', 'meeting_id', 'created_by_id', 'updated_by_id']
  end

  def self.for_user(tenant_id, user_id)
    return unless user_id.present?

    share_rule_scope = ShareRule.where(tenant_id: tenant_id, to_id: user_id, to_type: 'USER')
    user_team_ids = Team.where(tenant_id: tenant_id).where("#{user_id} = ANY (user_ids)").pluck(:id)
    if user_team_ids.any?
      share_rule_scope = share_rule_scope.or(
        ShareRule.where(tenant_id: tenant_id, to_id: user_team_ids, to_type: 'TEAM')
      )
    end

    share_rule_scope
  end
end
