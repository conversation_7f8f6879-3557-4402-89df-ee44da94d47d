class MeetingLookUp < ApplicationRecord
  belongs_to :meeting
  belongs_to :look_up

  validates :meeting, :look_up, presence: true
  validates :rsvp_response, allow_nil: true, inclusion: { in: [RSVP_YES, RSVP_NO, RSVP_MAYBE], message: 'Invalid Response' }

  def publish_entity_metadata
    if [LOOKUP_DEAL, LOOKUP_LEAD].include? look_up.entity_type
      EntityMetadataPublisher.call(look_up, meeting.owner_id)
    end
  end
end
