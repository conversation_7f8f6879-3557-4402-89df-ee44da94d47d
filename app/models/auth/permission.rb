class Auth::Permission
  include ActiveModel::Model

  attr_accessor(
    :id,
    :name,
    :description,
    :limits,
    :units,
    :action
  )

  validates :id, :name, :description, :limits, :units, :action, presence: true

  def can_access? action_name
    action.send(action_name)
  end

  def initialize options = {}
    UnderscorizeHashKeys.call(options)
    valid_options = permit(options)
    super(valid_options)
    @action = Auth::PermissionAction.new(valid_options['action']) unless valid_options['action'].nil?
  end

  private
  def permit options
    options = ActionController::Parameters.new(options)
    options.permit(
      :id,
      :name,
      :description,
      :limits,
      :units,
      action: [
        :read,
        :write,
        :update,
        :delete,
        :email,
        :call,
        :sms,
        :task,
        :note,
        :reshare,
        :read_all,
        :update_all,
        :delete_all
      ]
    ).to_hash
  end
end