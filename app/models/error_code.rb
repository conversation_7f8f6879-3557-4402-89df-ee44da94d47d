class ErrorCode
  def self.not_found(record = 'record')
    '01502001'
  end

  def self.bad_data
    '01502002'
  end

  def self.internal_error
    '01502003'
  end

  def self.invalid_credentials
    '01501001'
  end

  def self.invalid_token
    '********'
  end

  def self.missing_token
    '********'
  end

  def self.expired_token
    '********'
  end

  def self.unauthorized
    '********'
  end

  def self.invalid
    '********'
  end

  def self.participant_not_found
    '********'
  end

  def self.meeting_expired
    '********'
  end

  def self.meeting_cancelled
    '********'
  end

  def self.note_create_not_allowed
    '********'
  end

  def self.note_access_not_allowed
    '********'
  end

  def self.connected_account_not_found
    '********'
  end

  def self.provider_internal_server
    '********'
  end

  def self.provider_forbidden
    '********'
  end

  def self.provider_unauthorized
    '********'
  end

  def self.provider_invalid
    '********'
  end

  def self.provider_not_found
    '********'
  end

  def self.picklist_value_cannot_be_deleted
    '********'
  end

  def self.cannot_create_entity
    '********'
  end

  def self.invalid_share_actions
    '********'
  end

  def self.invalid_target_user
    '********'
  end

  def self.duplicate_share_rule
    '********'
  end

  def self.invalid_share_rule
    '********'
  end

  def self.invalid_from_and_to_users
    '********'
  end

  def self.share_rule_not_found
    '********'
  end

  def self.invalid_source_user
    '********'
  end

  def self.invalid_checkin_location
    '********'
  end

  def self.usage_limit_exceeded
    '********'
  end
end
