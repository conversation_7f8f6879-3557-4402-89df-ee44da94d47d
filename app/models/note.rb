class Note < ApplicationRecord
  belongs_to :meeting
  belongs_to :created_by,
    class_name: 'User'
  validates :description, :created_by, presence: true

  def self.usage_per_tenant(tenant_id = nil)
    data = []
    note_data = if tenant_id.present?
                  where(tenant_id: tenant_id).group(:tenant_id).select("tenant_id, count(notes.id) as count").as_json
                else
                  group(:tenant_id).select("tenant_id, count(notes.id) as count").as_json
                end

    note_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'].to_i, usageEntity: 'MEETING_NOTE'}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: 'MEETING_NOTE'} if (data.blank? && tenant_id.present?)
    data
  end
end
