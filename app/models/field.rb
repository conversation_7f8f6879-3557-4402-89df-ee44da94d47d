# frozen_string_literal: true

class Field < ApplicationRecord
  validates :internal_name, presence: true
  validates :internal_name, uniqueness: { case_sensitive: false, scope: :tenant_id }
  validates :display_name, presence: true
  validates :tenant_id, presence: true
  validate :custom_field_name

  validates_inclusion_of :field_type, presence: true,
                                      in: FIELD_TYPES,
                                      message: 'Invalid Field Type'

  validate :sortable_custom_fields

  has_one :picklist, dependent: :destroy
  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User'

  def system_default?
    is_standard? && %w(timezone status).include?(internal_name)
  end

  def self.usage_per_tenant(tenant_id = nil)
    data = []
    field_data = if tenant_id.present?
                  where(tenant_id: tenant_id, active: true, is_standard: false).group(:tenant_id).select('tenant_id, count(id) as count').as_json
                else
                  where(active: true, is_standard: false).group(:tenant_id).select('tenant_id, count(id) as count').as_json
                end

    field_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'].to_i, usageEntity: MEETING_CUSTOM_FIELD}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: MEETING_CUSTOM_FIELD} if (data.blank? && tenant_id.present?)
    data
  end

  private

  def custom_field_name
    unless is_standard? || (/cf.+/ =~ internal_name).presence
      errors.add(:internal_name, 'custom field name should start with cf or should have valid characters')
    end
  end

  def sortable_custom_fields
    if !is_standard? && is_sortable? && SORTABLE_FIELD_TYPES.exclude?(field_type)
      errors.add(:is_sortable, "cannot make field with type #{field_type} sortable")
    end
  end
end
