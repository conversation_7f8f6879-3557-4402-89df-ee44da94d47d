class Event::MeetingParticipantRsvpUpdated
  include ActiveModel::Model

  attr_accessor(
    :ownerId,
    :tenantId,
    :id,
    :title,
    :description,
    :from,
    :to,
    :timezone,
    :location,
    :organizer,
    :toName,
    :toEmail,
    :invitees,
    :rsvpResponse,
    :rsvpMessage,
    :rsvpParticipant
  )

  def initialize meeting, participant
    @id = meeting.id
    @tenantId = meeting.tenant_id
    @ownerId = meeting.owner_id
    @title = meeting.title
    @description = meeting.description
    @from = meeting.from
    @to = meeting.to
    @timezone = meeting.time_zone
    @location = meeting.location
    @organizer = meeting.owner.name
    @toName = meeting.owner.name
    @toEmail = meeting.organizer.email
    @invitees = meeting.participants.reject{|x| x.entity_id == meeting.owner_id}.collect{|x| {name: x.name, email: x.email}}
    meeting_look_up = participant.meeting_look_ups.where(meeting: meeting).first
    @rsvpResponse = meeting_look_up.rsvp_response
    @rsvpMessage = meeting_look_up.rsvp_message
    @rsvpParticipant = { id: participant.entity_id, type: participant.entity_type, name: participant.name, email: participant.email}
  end

  def routing_key
    "meeting.participant.rsvp.updated"
  end

  def as_json(options = {})
  result = super(only: ['ownerId', 'tenantId', 'ics'])
    inner_payload = { "meeting" => super(except: ['ownerId', 'tenantId', 'ics']) }
    result = result.merge inner_payload
    result
  end

  def to_json(options = {})
    result = as_json
    result.to_json
  end

end
