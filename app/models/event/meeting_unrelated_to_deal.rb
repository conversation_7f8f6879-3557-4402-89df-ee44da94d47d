class Event::MeetingUnrelatedToDeal
  include ActiveModel::Model

  attr_accessor(
    :meetingId,
    :tenantId,
    :entityType,
    :entityId,
    :fromType,
    :fromId,
    :toType,
    :toId
  )

  def initialize meeting, participant, related_to
    @meetingId = meeting.id
    @tenantId = meeting.tenant_id
    @entityType = related_to.entity_type.upcase
    @entityId = related_to.entity_id
    @fromType = LOOKUP_USER.upcase
    @fromId = meeting.owner.id
    @toType = participant.entity_type.upcase
    @toId = participant.entity_id
  end

  def routing_key
    "meeting.relatedto.deal.removed"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end

end
