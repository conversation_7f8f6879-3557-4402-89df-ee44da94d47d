# frozen_string_literal: true

class Event::MeetingFieldUpdatedV2
  include ActiveModel::Model

  def initialize(field, old_serialized_field_data)
    @field = field
    @old_serialized_field_data = old_serialized_field_data
  end

  def routing_key
    'meeting.field.updated.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      entity: V2::FieldSerializer.call(@field).result,
      oldEntity: @old_serialized_field_data,
      metadata: {
        tenantId: @field.tenant_id,
        userId: @field.updated_by_id,
        entityType: "MEETING",
        entityId: @field.id,
        entityAction: "UPDATED"
      }
    }
  end
end
