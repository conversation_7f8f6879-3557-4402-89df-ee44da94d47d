# frozen_string_literal: true

class Event::ShareRuleCreatedV2
  include ActiveModel::Model

  attr_accessor :share_rule

  def initialize(share_rule)
    @share_rule = share_rule
  end

  def routing_key
    'shareRule.created.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => ShareRuleSerializer.new(share_rule, payload_for_event: true).call,
      'oldEntity' => nil,
      'metadata' => {
        'tenantId': share_rule.tenant_id,
        'userId': share_rule.created_by_id,
        'entityType': 'SHARE_RULE',
        'entityId': share_rule.id,
        'entityAction': 'CREATED'
      }
    }
  end
end
