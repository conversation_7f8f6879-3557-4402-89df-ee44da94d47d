# frozen_string_literal: true

class Event::MeetingFieldCreatedV2
  include ActiveModel::Model

  def initialize(field)
    @field = field
  end

  def routing_key
    'meeting.field.created.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      entity: V2::FieldSerializer.call(@field).result,
      oldEntity: nil,
      metadata: {
        tenantId: @field.tenant_id,
        userId: @field.updated_by_id,
        entityType: "MEETING",
        entityId: @field.id,
        entityAction: "CREATED"
      }
    }
  end
end
