class Event::MeetingScheduled
  include ActiveModel::Model

  attr_accessor(
    :ownerId,
    :tenantId,
    :id,
    :title,
    :description,
    :from,
    :to,
    :timezone,
    :location,
    :rsvpYes,
    :rsvpNo,
    :rsvpMaybe,
    :organizer,
    :toName,
    :toEmail,
    :invitees,
    :ics
  )

  def initialize meeting
    @id = meeting.id
    @tenantId = meeting.tenant_id
    @ownerId = meeting.owner_id
    @title = meeting.title
    @description = meeting.description
    @medium = meeting.medium
    @from = meeting.from
    @to = meeting.to
    @timezone = meeting.time_zone
    @location = meeting.location
    @organizer = meeting.owner.name
    @toName = meeting.owner.name
    @toEmail = meeting.organizer.email
    @invitees = meeting.participants.reject{|x| x.entity_id == meeting.owner_id}.collect{|x| { name: x.name, email: x.email}}
    @ics = meeting.ics.bytes
  end

  def routing_key
    "meeting.scheduled"
  end

  def as_json(options = {})
  result = super(only: ['ownerId', 'tenantId', 'ics'])
    inner_payload = { "meeting" => super(except: ['ownerId', 'tenantId', 'ics']) }
    result = result.merge inner_payload
    result
  end

  def to_json(options = {})
    result = as_json
    result.to_json
  end

end
