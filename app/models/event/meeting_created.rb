class Event::MeetingCreated
  include ActiveModel::Model

  attr_accessor :meeting, :user_id

  def initialize(meeting, user_id = nil)
    @meeting = meeting
    @user_id = user_id
  end

  def routing_key
    'meeting.created.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => MeetingSerializer.call(meeting, nil, false, nil, true).result,
      'oldEntity' => nil,
      'metadata' => {
        'tenantId': meeting.tenant_id,
        'userId': user_id,
        'entityType': 'MEETING',
        'entityId': meeting.id,
        'entityAction': 'CREATED'
      }
    }
  end
end
