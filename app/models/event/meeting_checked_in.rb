# frozen_string_literal: true

class Event::MeetingCheckedIn
  include ActiveModel::Model

  def initialize(tenant_id, meeting_ids, user_id, meeting_attendance)
    @tenant_id = tenant_id
    @meeting_ids = meeting_ids
    @user_id = user_id
    @meeting_attendance = meeting_attendance
  end

  def routing_key
    'meeting.checkedIn'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'ids' => @meeting_ids,
      'tenantId' => @tenant_id,
      'userId' => @user_id,
      'markedAt' => @meeting_attendance.checked_in_at,
      'latitude' => @meeting_attendance.checked_in_latitude.to_f,
      'longitude' => @meeting_attendance.checked_in_longitude.to_f
    }
  end
end
