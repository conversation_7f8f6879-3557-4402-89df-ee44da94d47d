class Event::MeetingScheduledInMinutes
  include ActiveModel::Model

  def initialize meetings
    @meetings = meetings
  end

  def routing_key
    "meeting.scheduled.inMinutes"
  end

  def as_json(options = {})
    result = @meetings.map do |m|
      {
        id: m['id'],
        tenantId: m.tenant_id,
        ownerId: m.owner_id,
        ownerName: m.owner.name,
        title: m.title,
        startTime: m.from,
        invitees: m.participants.collect do |x|
          meeting_look_up = x.meeting_look_ups.where(meeting: m).first
          { id: x.entity_id, type: x.entity_type, name: x.name, email: x.email, rsvpResponse: meeting_look_up.rsvp_response }
        end
      }
    end
  end

  def to_json(options = {})
    result = as_json
    result.to_json
  end

end
