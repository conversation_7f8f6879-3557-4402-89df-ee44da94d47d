class Event::MeetingEntityDisassociated
  include ActiveModel::Model

  def initialize(meeting, lookup)
    @meeting = meeting
    @lookup = lookup
  end

  def routing_key
    'meeting.entity.disassociated'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      tenantId: @meeting.tenant_id,
      meetingId: @meeting.id,
      entity: @lookup.entity_type.upcase,
      entityId: @lookup.entity_id
    }
  end
end
