class Event::MeetingParticipantUserRemovedEvent
  include ActiveModel::Model

  attr_accessor(
    :tenantId,
    :entityType,
    :entityId,
    :fromType,
    :fromId,
    :toType,
    :toId
  )

  def initialize meeting, participant
    @tenantId = meeting.tenant_id
    @entityType = "MEETING"
    @entityId = meeting.id
    @fromType = "USER"
    @fromId = meeting.owner.id
    @toType = participant.entity_type
    @toId = participant.entity_id
  end

  def routing_key
    "meeting.user.participant.removed"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
