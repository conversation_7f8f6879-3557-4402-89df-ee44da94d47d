class Event::MeetingFieldCreated
  include ActiveModel::Model

  def initialize(field)
    @field = field
  end

  def routing_key
    'meeting.field.created'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    json = {
      tenantId: @field.tenant_id,
      fields: FieldsSerializer.call([@field]).result
    }.with_indifferent_access

    if json.dig(:fields, 0, :picklist).present?
      json[:fields][0][:picklist][:values] = json[:fields][0][:picklist].delete(:picklistValues)
    end

    json
  end
end
