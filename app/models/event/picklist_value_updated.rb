class Event::PicklistValueUpdated
  include ActiveModel::Model

  def initialize(picklist_value_hash, old_picklist_value_hash, user = nil)
    @picklist_value_hash = picklist_value_hash
    @old_picklist_value_hash = old_picklist_value_hash
    @user = user
  end

  def routing_key
    'meeting.picklist.value.updated'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      newEntity: @picklist_value_hash,
      oldEntity: @old_picklist_value_hash,
      metadata: {
        userId: @user&.id,
        tenantId: @user&.tenant_id
      }
    }.with_indifferent_access
  end
end
