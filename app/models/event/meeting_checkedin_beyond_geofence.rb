# frozen_string_literal: true

class Event::MeetingCheckedinBeyondGeofence
  include ActiveModel::Model

  def initialize(meeting, user, tenant_id)
    @user = user
    @meeting = meeting
    @tenant_id = tenant_id
  end

  def routing_key
    "meeting.checkedin.beyond.geofence"
  end

  def as_json
    {
      tenantId: @tenant_id,
      user: {
        id: @user.id,
        name: @user.name
      },
      meeting: {
        id: @meeting.id,
        title: @meeting.title
      }
    }
  end

  def to_json
    as_json.to_json
  end
end