class Event::MeetingDeleted
  include ActiveModel::Model

  attr_accessor :old_serialized_meeting, :user_id

  def initialize(old_serialized_meeting, user_id = nil)
    @old_serialized_meeting = old_serialized_meeting
    @user_id = user_id
  end

  def routing_key
    'meeting.deleted.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => nil,
      'oldEntity' => old_serialized_meeting,
      'metadata' => {
        'tenantId': old_serialized_meeting['tenantId'],
        'userId': user_id,
        'entityType': 'MEETING',
        'entityId': old_serialized_meeting['id'],
        'entityAction': 'DELETED'
      }
    }
  end
end
