# frozen_string_literal: true

class Event::ShareRuleUpdatedV2
  include ActiveModel::Model

  attr_accessor :new_share_rule, :old_serialized_share_rule

  def initialize(new_share_rule, old_serialized_share_rule)
    @new_share_rule = new_share_rule
    @old_serialized_share_rule = old_serialized_share_rule
  end

  def routing_key
    'shareRule.updated.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => ShareRuleSerializer.new(@new_share_rule, payload_for_event: true).call,
      'oldEntity' => @old_serialized_share_rule,
      'metadata' => {
        'tenantId': @new_share_rule.tenant_id,
        'userId': @new_share_rule.updated_by_id,
        'entityType': 'SHARE_RULE',
        'entityId': @new_share_rule.id,
        'entityAction': 'UPDATED'
      }
    }
  end
end
