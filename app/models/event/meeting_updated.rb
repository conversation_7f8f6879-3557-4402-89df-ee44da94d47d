class Event::MeetingUpdated
  include ActiveModel::Model

  attr_accessor :new_meeting, :old_serialized_meeting, :user_id
  attr_reader :metadata

  def initialize(new_meeting, old_serialized_meeting, user_id = nil, metadata = {})
    @new_meeting = new_meeting
    @old_serialized_meeting = old_serialized_meeting || {}
    @user_id = user_id
    @metadata = metadata&.with_indifferent_access || {}
  end

  def routing_key
    'meeting.updated.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => MeetingSerializer.call(new_meeting, nil, false, nil, true).result,
      'oldEntity' => old_serialized_meeting,
      'metadata' => {
        'tenantId': new_meeting.tenant_id,
        'userId': user_id,
        'entityType': 'MEETING',
        'entityId': new_meeting.id,
        'entityAction': 'UPDATED',
        'workflowId': metadata['workflowId'],
        'executedWorkflows': metadata['executedWorkflows'],
        'executeWorkflow': metadata['executeWorkflow'].nil? ? true : metadata['executeWorkflow'],
        'workflowName': metadata['workflowName']
      }
    }
  end
end
