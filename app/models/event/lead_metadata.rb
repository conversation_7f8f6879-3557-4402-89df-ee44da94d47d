class Event::LeadMetadata
  include ActiveModel::Model

  attr_accessor(
    :createdAt,
    :leadId,
    :tenantId,
    :entityType,
    :scheduledOn,
    :ownerId
  )

  def initialize look_up, scheduled_on, owner_id
    @createdAt = Time.now.utc
    @leadId = look_up.entity_id
    @tenantId = look_up.tenant_id
    @scheduledOn = scheduled_on
    @entityType = 'MEETING'
    @ownerId = owner_id
  end

  def routing_key
    "meeting.update.lead.metaInfo"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
