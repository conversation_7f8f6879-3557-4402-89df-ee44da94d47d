# frozen_string_literal: true

class Event::ShareRuleDeletedV2
  include ActiveModel::Model

  attr_accessor :share_rule_serialized_data, :user_id, :tenant_id

  def initialize(share_rule_serialized_data, user_id, tenant_id)
    @share_rule_serialized_data = share_rule_serialized_data
    @tenant_id = tenant_id
    @user_id = user_id
  end

  def routing_key
    'shareRule.deleted.v2'
  end

  def to_json
    as_json.to_json
  end

  private

  def as_json
    {
      'entity' => nil,
      'oldEntity' => share_rule_serialized_data,
      'metadata' => {
        'tenantId': tenant_id,
        'userId': user_id,
        'entityType': 'SHARE_RULE',
        'entityId': share_rule_serialized_data['id'],
        'entityAction': 'DELETED'
      }
    }
  end
end
