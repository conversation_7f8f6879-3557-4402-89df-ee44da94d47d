# frozen_string_literal: true

class ConnectedAccount < ApplicationRecord
  # Validations
  validates :access_token, :tenant_id, :expires_at, :email, :provider_name, presence: true
  # validates_uniqueness_of :email, scope: :active
  validates :calendar_id, length: { maximum: 320 }

  # Associations
  belongs_to :user, foreign_key: 'user_id'

  def fetch_access_token(organizer_token = nil)
    return decrypt_token if access_token_valid?

    thread = Thread.current
    unless thread[:token].present?
      thread[:token] = GenerateToken.call(user_id, user.tenant_id).result
    end

    response = Calendar::GetProviderAccessToken.call(provider_name, organizer_token).result

    update_tokens_details!(response)
    decrypt_token
  end

  def google_provider?
    provider_name.eql? GOOGLE_PROVIDER
  end

  def microsoft_provider?
    provider_name.eql?(MICROSOFT_TEAMS_PROVIDER)
  end

  def get_client_secret_value
    "#{id}#{user_id}#{tenant_id}_secret"
  end

  private

  def update_tokens_details!(response)
    update(
      access_token: response['accessToken'],
      expires_at: (DateTime.now + 50.minutes).to_i
    )
  end

  def access_token_valid?
    expires_at.to_i > DateTime.now.to_i
  end

  def decrypt_token
    begin
      decipher = OpenSSL::Cipher::AES.new(128, :CBC)
      decipher.decrypt
      decipher.padding=1
      decipher.key = ENV['CALENDAR_CREDENTIAL_ENCRYPTION_SECRET']
      decipher.iv = ENV['CALENDAR_CREDENTIAL_ENCRYPTION_IV']
      decipher.update(Base64.decode64(access_token)) + decipher.final
    rescue Exception => e
      Rails.logger.error "==Error while decrypting token=#{e.message}=="
      return access_token
    end
  end
end
