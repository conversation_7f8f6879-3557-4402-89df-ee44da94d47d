class JsonRule
  include ActiveModel::Model
  attr_accessor(
    :operator,
    :field,
    :type,
    :value,
    :is_custom_field,
    :primary_field,
    :property,
    :meeting_or_share_rule_model
  )

  validates :operator, :field, :type, presence: true
  validates :value, presence: true, if: :not_blank_value_operators?
  validates :value, inclusion: [true, false, 'true', 'false'], if: :boolean_type?
  validates :type, inclusion: %w[string long double date boolean picklist participants_lookup related_lookup associated_lookup organizer_lookup meeting_attendance]

  validate :operator_based_on_type

  def initialize(options = {})
    options = options.with_indifferent_access
    @is_custom_field = options[:field]&.starts_with?('customFieldValues')
    options[:field] = @is_custom_field ? options[:field]&.split('.')&.last : options[:field]&.underscore
    @meeting_or_share_rule_model =  options.delete(:meeting_or_share_rule_model) || 'Meeting'
    UnderscorizeHashKeys.call(options)
    valid_options = permit(options)
    super(valid_options)
    modify_rules
  end

  private

  def modify_rules
    if self.type == 'string' and %w[is_null is_not_null].include?(self.operator)
      self.operator = self.operator.gsub('null', 'empty')
    end
    self.type = 'long' if self.type == 'double'
  end

  def boolean_type?
    type == 'boolean'
  end

  def not_blank_value_operators?
    %w[is_empty is_not_empty is_null is_not_null in].exclude?(operator) && !boolean_type?
  end

  def operator_based_on_type
    if type == "string"
      validate_string_operators
      validate_string_rule
    elsif ["double", "long", "picklist"].include? (type)
      validate_long_double_operators
      validate_long_double_rule
    elsif ["date", "time", "datetime"].include? (type)
      validate_date_time_operators
      validate_date_rule
    elsif ["boolean"].include? (type)
      validate_boolean_operators
      validate_boolean_rule
    elsif ["participants_lookup"].include? (type)
      validate_participants_lookup_operators
      validate_participants_lookup_rule
    elsif ["organizer_lookup"].include? (type)
      validate_organizer_lookup_operators
      validate_organizer_lookup_rule
    elsif ["related_lookup"].include? (type)
      validate_related_lookup_operators
      validate_related_lookup_rule
    elsif ["associated_lookup"].include? (type)
      validate_associated_lookup_operators
      validate_associated_lookup_rule
    end
  end

  def validate_string_rule
    unless @meeting_or_share_rule_model.constantize.string_fields.include?(field) || field&.starts_with?('cf')
      errors.add(:field, "not applicable for string")
    end
  end

  def validate_boolean_rule
    unless @meeting_or_share_rule_model.constantize.boolean_fields.include?(field) || field&.starts_with?('cf')
      errors.add(:field, "not applicable for boolean")
    end
  end

  def validate_date_rule
    unless @meeting_or_share_rule_model.constantize.date_fields.include?(field) || field&.starts_with?('cf')
      errors.add(:field, "not applicable for date")
    end
  end

  def validate_participants_lookup_rule
    unless ["participants"].include? field
      errors.add(:field, "not applicable for participants_lookup")
    end
  end

  def validate_organizer_lookup_rule
    unless ["organizer"].include? field
      errors.add(:field, "not applicable for organizer_lookup")
    end
  end

  def validate_related_lookup_rule
    unless ["related_to"].include? field
      errors.add(:field, "not applicable for related_lookup")
    end
  end

  def validate_associated_lookup_rule
    unless ["associated_to"].include? field
      errors.add(:field, "not applicable for associated_lookup")
    end
  end

  def validate_long_double_rule
    unless @meeting_or_share_rule_model.constantize.long_double_fields.include?(field) || field&.starts_with?('cf')
      errors.add(:field, "not applicable for long/double fields")
    end
  end

  def validate_boolean_operators
    errors.add(:operator, "invalid for boolean") unless ['equal', 'not_equal'].include?(operator)
  end

  def validate_string_operators
    errors.add(:operator, "invalid for string") unless ['equal',
      'not_equal',
      'contains',
      'not_contains',
      'in',
      'not_in',
      'is_empty',
      'is_not_empty',
      'begins_with']
      .include? operator
  end

  def validate_long_double_operators
    errors.add(:operator, "invalid for long") unless ['equal',
      'not_equal',
      'greater',
      'greater_or_equal',
      'less',
      'less_or_equal',
      'between',
      'not_between',
      'in',
      'not_in',
      'is_not_null',
      'is_null'
      ]
      .include? operator
  end

  def validate_date_time_operators
    errors.add(:operator, "invalid for date") unless ['equal',
        'greater',
        'greater_or_equal',
        'less',
        'less_or_equal',
        'between',
        'not_between',
        'is_not_null',
        'is_null']
        .include? operator
  end

  def validate_participants_lookup_operators
    errors.add(:operator, "invalid for participants_lookup") unless [
      'equal',
      'not_equal',
      'in',
      'not_in'
    ].include?(operator)
  end

  def validate_organizer_lookup_operators
    errors.add(:operator, "invalid for organizer_lookup") unless [
      'equal',
      'not_equal',
      'in',
      'not_in'
    ].include?(operator)
  end

  def validate_associated_lookup_operators
    errors.add(:operator, "invalid for associated_lookup") unless [
      'equal',
      'not_equal',
      'in',
      'not_in',
      'is_not_null',
      'is_null'
    ].include?(operator)
  end

  def validate_related_lookup_operators
    errors.add(:operator, "invalid for related_lookup") unless operator == 'equal'
  end

  def permit options
    options = ActionController::Parameters.new(options)
    options.permit(
      :operator,
      :field,
      :type,
      :value,
      :primary_field,
      :property,
      {value: [
        :id,
        :entity
      ]},
      {value: [] }
    ).to_hash
  end
end
