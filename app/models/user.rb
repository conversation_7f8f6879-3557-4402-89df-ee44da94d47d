# frozen_string_literal: true

class User < ApplicationRecord
  # Validations
  validates :name, :tenant_id, presence: true

  # Associations
  has_many :connected_accounts

  def get_geofence_config
    geofence_config = self.geofence_config

    return geofence_config if geofence_config.present?

    geofence_config = FetchGeofenceConfigurationForUser.call.result || { meetingCheckInCheckOut: nil, fieldSalesEnabled: false }

    self.geofence_config = geofence_config
    self.save!
    geofence_config.with_indifferent_access
  end
end
