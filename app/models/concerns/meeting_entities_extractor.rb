module MeetingEntitiesExtractor
  private

  def get_validated_participants
    validated_participants = []
    uniq_participants = @params[:participants].uniq{|p| "#{p['entity']}_#{p['id']}"}
    uniq_participants.each do |participant|
      validated_participants << @validated_entities.find{|v_p| v_p.entity == "#{participant['entity']}_#{participant['id']}" }
    end
    validated_participants << @validated_entities.find{ |v_p| v_p.entity == "user_#{@user.id}" }
    validated_participants.reject(&:nil?).uniq{|p| p.entity }
  end

  def get_validated_related_to
    validated_related_to = []
    @params[:related_to].each do |related_to|
      validated_related_to << @validated_entities.find{|v_p| v_p.entity == "#{related_to['entity']}_#{related_to['id']}" }
    end
    validated_related_to
  end

  def valid_value?(field, value)
    case field.field_type
    when 'TEXT_FIELD', 'PARAGRAPH_TEXT', 'URL'
      value.is_a?(String)
    when 'CHECKBOX'
      value == true || value == false
    when 'NUMBER'
      value.is_a?(Numeric)
    when 'PICK_LIST'
      begin
        value = value.to_h
        value['id'].is_a?(Integer) && !field.picklist.picklist_values.find_by(id: value['id']).disabled?
      rescue
        false
      end
    when 'DATETIME_PICKER', 'DATE_PICKER'
      DateTime.parse(value).present? rescue false
    end
  end

  def get_valid_external_entities
    external_entities = @params[:participants].select{ |p| p['entity'] == LOOKUP_EXTERNAL }
    external_entities = external_entities.collect do |data|
      data[:tenant_id] = @user.tenant_id
      look_up = GetLookUp.call(data).result
      look_up.save unless look_up.new_record?
      look_up
    end
  end
end
