# frozen_string_literal: true

module MeetingAccessHelper

  private

  def user_can_read_meeting?(meeting)
    (
      @auth_data.can_access?('meeting', 'read_all') ||
      meeting.owner_id == @auth_data.user_id ||
      (meeting.participants + [meeting.organizer].compact).select { |lookup| lookup.is_a_user? }.map(&:entity_id).include?(@auth_data.user_id) ||
      (meeting.participants + meeting.related_to + [meeting.organizer].compact).select { |lookup| PARENT_ENTITY_TYPES.include?(lookup.entity_type) }.map(&:owner_id).compact.uniq.include?(@auth_data.user_id) ||
      (
        share_rules_for_read(meeting.id).select(&:share_all_records).pluck(:from_id) &
        ((meeting.participants + [meeting.organizer].compact).select { |lookup| lookup.is_a_user? }.map(&:entity_id) + [meeting.owner_id]).uniq
      ).present? ||
      (share_rules_for_read(meeting.id).reject(&:share_all_records).present?) ||
      parent_entity_access(LOOKUP_LEAD, meeting) ||
      parent_entity_access(LOOKUP_CONTACT, meeting) ||
      parent_entity_access(LOOKUP_DEAL, meeting) ||
      parent_entity_access(LOOKUP_COMPANY, meeting)
    )
  end

  def user_can_share_meeting?(meeting)
    (
      @auth_data.can_access?('meeting', 'update_all') ||
      @auth_data.can_access?('meeting', 'read_all') && @auth_data.can_access?('meeting', 'reshare') ||
      [meeting.owner_id, meeting.created_by_id].include?(@auth_data.user_id) ||
      meeting.organizer.entity == "#{LOOKUP_USER}_#{@auth_data.user_id}" ||
      (
        @auth_data.can_access?('meeting', 'reshare') &&
        (
          meeting.participants.select { |lookup| lookup.is_a_user? }.map(&:entity_id).include?(@auth_data.user_id) ||
          (meeting.participants + meeting.related_to + [meeting.organizer].compact).select { |lookup| PARENT_ENTITY_TYPES.include?(lookup.entity_type) }.map(&:owner_id).compact.uniq.include?(@auth_data.user_id) ||
          (
            share_rules_for_read(meeting.id).select(&:share_all_records).pluck(:from_id) &
            ((meeting.participants + [meeting.organizer].compact).select { |lookup| lookup.is_a_user? }.map(&:entity_id) + [meeting.owner_id]).uniq
          ).present? ||
          (share_rules_for_read(meeting.id).reject(&:share_all_records).present?) ||
          parent_entity_access(LOOKUP_LEAD, meeting) ||
          parent_entity_access(LOOKUP_CONTACT, meeting) ||
          parent_entity_access(LOOKUP_DEAL, meeting) ||
          parent_entity_access(LOOKUP_COMPANY, meeting)
        )
      )
    )
  end

  def user_can_update_meeting?(meeting)
    (
      @auth_data.can_access?('meeting', 'update_all') ||
      meeting.owner_id == @auth_data.user_id
    )
  end

  def parent_entity_access(entity_type, meeting)
    entity_type_lookups = (meeting.participants + meeting.related_to + [meeting.organizer].compact).select { |lookup| lookup.send("is_a_#{entity_type}?") }

    return false unless entity_type_lookups.present?

    @shared_entity_data ||= {}
    unless @shared_entity_data[entity_type].present?
      load_shared_entity_data(entity_type)
    end

    (
      (
        entity_type_lookups.map(&:entity_id) &
        @shared_entity_data[entity_type][:shared_entity_ids]
      ).present? ||
      (
        entity_type_lookups.map(&:owner_id) &
        @shared_entity_data[entity_type][:shared_entity_owners]
      ).present?
    )
  end

  def load_shared_entity_data(entity_type)
    shared_response = ShareRules::ParentEntityAccess.new(entity_type).fetch
    @shared_entity_data[entity_type] = {
      shared_entity_owners: shared_response['accessByOwners'].keys.map(&:to_i),
      shared_entity_ids: shared_response['accessByRecords'].keys.map(&:to_i)
    }
  end

  def share_rules_for_read(meeting_id)
    return @share_rules_with_read if @share_rules_with_read.present?

    query = ShareRule.for_user(@auth_data.tenant_id, @auth_data.user_id)
    @share_rules_with_read = query.where(meeting_id: nil).or(query.where(meeting_id: meeting_id)).to_a
  end
end
