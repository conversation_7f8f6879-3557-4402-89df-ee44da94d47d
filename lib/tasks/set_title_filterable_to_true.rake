namespace :meeting do
  desc 'Update is filterable to true for meeting title field'
  task :set_title_filterable_to_true, [:tenant_id] => [:environment] do |_task, args|

    fields = Field.where(internal_name: 'title', is_filterable: false)
    if args[:tenant_id].present?
      fields = fields.where(tenant_id: args[:tenant_id])
    end

    fields.update_all(is_filterable: true)
    puts "Meeting title is_filterable updated to true #{args[:tenant_id].present? ? "for tenant id #{args[:tenant_id]}" : "for all tenants"}"
  end
end

# RAILS_ENV=<Environment> bundle exec rake meeting:set_title_filterable_to_true[<tenant_id?>]
