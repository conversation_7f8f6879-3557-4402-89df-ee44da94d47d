namespace :meeting do
  task :set_organizer, [:tenant_id] => [:environment] do |_task, args|

    meetings = Meeting.all
    if args[:tenant_id].present?
      meetings = meetings.where(tenant_id: args[:tenant_id])
    end
    meetings.each do |meeting|
      meeting.participant_look_ups.joins(:look_up).find_by("look_ups.entity": "user_#{meeting.owner_id}").update_columns(organizer: true)
      p "Updated meeting: #{meeting.id}"
    end
  end
end

