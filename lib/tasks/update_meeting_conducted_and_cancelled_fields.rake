namespace :meeting do
  desc 'Update Past Meeting & Meeting Cancelled fields'
  task :meeting_fields_update, [:all_tenants] => [:environment] do |_task, args|
    tenant_ids = args.extras.map(&:to_i).flatten

    current_time = DateTime.now.utc
    twenty_four_hours_ago = (current_time - 24.hours)

    if ActiveModel::Type::Boolean.new.cast(args[:all_tenants]).present?
      meetings_passed = Meeting.where(to: ...current_time, status: SCHEDULED, all_day: false)
      all_day_meetings_passed = Meeting.where(from: ...twenty_four_hours_ago, status: SCHEDULED, all_day: true)
      cancelled_meetings = Meeting.where(status: CANCELLED, cancelled_at: nil)
    else
      meetings_passed = Meeting.where(tenant_id: tenant_ids, to: ...current_time, status: SCHEDULED, all_day: false)
      all_day_meetings_passed = Meeting.where(tenant_id: tenant_ids, from: ...twenty_four_hours_ago, status: SCHEDULED, all_day: true)
      cancelled_meetings = Meeting.where(tenant_id: tenant_ids, status: CANCELLED, cancelled_at: nil)
    end

    meetings = meetings_passed.or(all_day_meetings_passed).or(cancelled_meetings)

    meetings.find_in_batches do |meeting_group|
      meeting_group.each do |meeting|
        if meeting.status == CANCELLED
          meeting.assign_attributes(cancelled_by: meeting.owner, cancelled_at: meeting.updated_at)
          meeting.save(validate: false)
        elsif meeting.status == SCHEDULED
          if meeting.all_day?
            meeting.update(status: MISSED)
          else
            meeting.update(status: MISSED)
          end
        end
        puts "Meeting with ID #{meeting.id} Tenant ID #{meeting.tenant_id} and Status #{meeting.status} updated successfully."
      end
    end
  end
end

# RAILS_ENV=Staging bundle exec rake meeting:meeting_fields_update[false,2291]
