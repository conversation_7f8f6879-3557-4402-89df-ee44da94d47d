namespace :field do
  task :add_imported_by, [:tenant_id] => [:environment] do |_task, args|

    tenant_ids = User.all.pluck(:tenant_id).uniq.sort

    tenant_ids.each do |t_id|
      next if Field.where(internal_name: 'importedBy').where(tenant_id: t_id).exists?
      begin
        user_id = Field.find_by(internal_name: 'owner', tenant_id: t_id).created_by_id
        Field.create!(tenant_id: t_id,
                      created_by_id: user_id,
                      updated_by_id: user_id,
                      internal_name: 'importedBy',
                      display_name: 'Imported By',
                      field_type: 'LOOK_UP',
                      is_standard: true,
                      is_sortable: false,
                      is_filterable: true,
                      is_internal: true,
                      is_required: false,
                      active: true)

      rescue Exception => e
        p "Exception while creating field for #{t_id} | #{e.to_s}"
      end
    end
  end
end
