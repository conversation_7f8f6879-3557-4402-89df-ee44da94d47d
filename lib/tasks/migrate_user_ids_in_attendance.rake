namespace :migrate_attendances do
  task :set_user_ids, [:start_tenant_id, :end_tenant_id] => [:environment] do |_task, args|

    if args[:start_tenant_id].present?
      res = ActiveRecord::Base.connection.execute(<<-SQL
        UPDATE meeting_attendances
          SET user_id = meetings.owner_id
          FROM meetings
          WHERE meeting_attendances.meeting_id = meetings.id
            AND meetings.tenant_id BETWEEN #{args[:start_tenant_id]} AND #{args[:end_tenant_id]}
            AND meeting_attendances.user_id IS NULL
        SQL
      )

      puts "UPDATED = #{res.check.cmd_tuples}"
    else
      res = ActiveRecord::Base.connection.execute(<<-SQL
        UPDATE meeting_attendances
          SET user_id = meetings.owner_id
          FROM meetings
          WHERE meeting_attendances.meeting_id = meetings.id
            AND meeting_attendances.user_id IS NULL
        SQL
      )

      puts "UPDATED = #{res.check.cmd_tuples}"
    end
  end
end
