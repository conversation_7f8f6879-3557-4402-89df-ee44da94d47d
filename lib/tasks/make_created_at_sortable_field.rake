# frozen_string_literal: true

namespace :field do
  desc 'Make Created At Sortable Field'
  task :make_created_at_sortable_field, [:all_tenants] => [:environment] do |_task, args|
    begin
      if ActiveModel::Type::Boolean.new.cast(args[:all_tenants]).present?
        Field.where(internal_name: 'createdAt').update_all(is_sortable: true)

        puts "\n=======Make createdAt sortable field for all tenants=======\n"
      else
        tenant_ids = args.extras.map(&:to_i).flatten
        Field.where(internal_name: 'createdAt', tenant_id: tenant_ids).update_all(is_sortable: true)

        puts "\n=======Make createdAt sortable field for tenant_ids: #{tenant_ids}=======\n"
      end
    rescue StandardError => e
      puts "\n=======Make createdAt sortable field: ERROR Message: #{e.message}\n======="
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake "field:make_created_at_sortable_field[<all_tenants?>,<tenant_ids>]"
