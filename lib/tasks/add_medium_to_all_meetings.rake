# frozen_string_literal: true

namespace :meeting do
  desc 'Add medium to all meetings'
  task :add_medium_to_all_meetings, [:all_tenants] => [:environment] do |_task, args|
    tenant_ids = args.extras.map(&:to_i).flatten

    begin
      if ActiveModel::Type::Boolean.new.cast(args[:all_tenants]).present?
        Meeting.update_all(medium: OFFLINE)
      else
        Meeting.where(tenant_id: tenant_ids).update_all(medium: OFFLINE)
      end
    rescue StandardError => e
      puts "Add medium to all meetings: ERROR Message: #{e}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake meeting:add_medium_to_all_meetings[<all_tenants?>,<tenant_ids>]
