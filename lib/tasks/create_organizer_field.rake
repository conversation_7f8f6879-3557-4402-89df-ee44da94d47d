namespace :field do
  task :create_organizer_field, [:tenant_id] => [:environment] do |_task, args|

    tenant_ids = User.all.pluck(:tenant_id).uniq.sort

    tenant_ids.each do |t_id|
      next if Field.where(internal_name: 'organizer').where(tenant_id: t_id).exists?
      begin
        user_id = Field.find_by(internal_name: 'owner', tenant_id: t_id).created_by_id
        Field.create!(tenant_id: t_id,
                      created_by_id: user_id,
                      updated_by_id: user_id,
                      internal_name: 'organizer',
                      display_name: 'Organizer',
                      field_type: 'MEETING_INVITEES',
                      is_standard: true,
                      is_sortable: false,
                      is_filterable: true,
                      is_internal: false,
                      is_required: true,
                      active: true)

      rescue Exception => e
        p "Exception while creating field for #{t_id} | #{e.to_s}"
      end
    end
  end
end
