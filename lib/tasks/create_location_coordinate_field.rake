namespace :field do
  desc 'Create location coordinate field'
  task :create_location_coordinate_field, [:from_tenant_id, :to_tenant_id] => :environment do |task, args|

    (args.from_tenant_id..args.to_tenant_id).each do |t_id|
      if Field.where(internal_name: 'locationCoordinate', tenant_id: t_id).exists?
        p "create_location_coordinate_field: Field already created tenant_id #{t_id}"
        next
      end

      begin
        field = Field.order(created_at: :asc).find_by(tenant_id: t_id)

        unless field
          p "create_location_coordinate_field: Fields not found for tenant_id #{t_id}"
          next
        end
        
        user_id = field.created_by_id

        Field.create!(tenant_id: t_id,
                      created_by_id: user_id,
                      updated_by_id: user_id,
                      internal_name: 'locationCoordinate',
                      display_name: 'Location Coordinate',
                      field_type: 'GPS_COORDINATES',
                      is_standard: true,
                      is_sortable: false,
                      is_filterable: false,
                      is_internal: false,
                      is_required: false,
                      active: true)
        p "create_location_coordinate_field: Created field for #{t_id}"

      rescue Exception => e
        p "create_location_coordinate_field: Exception for #{t_id} | #{e.to_s}"
      end
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake "field:create_location_coordinate_field[<from_tenant_id>,<to_tenant_id>]"
