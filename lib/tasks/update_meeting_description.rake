namespace :meeting do
  desc 'Update existing meeting description field type to "RICH_TEXT"'
  task :update_meeting_description_type, [:tenant_id] => [:environment] do |_task, args|

    fields = Field.where(internal_name: 'description', field_type: 'PARAGRAPH_TEXT')
    if args[:tenant_id].present?
      fields = fields.where(tenant_id: args[:tenant_id])
    end

    puts "Fields query #{fields.to_sql}"
    updated_count = fields.update_all(field_type: 'RICH_TEXT')
    puts "Meeting Description Fields updated count #{updated_count}"
  end
end

# RAILS_ENV=<Environment> bundle exec rake meeting:update_meeting_description_type[<tenant_id>]
