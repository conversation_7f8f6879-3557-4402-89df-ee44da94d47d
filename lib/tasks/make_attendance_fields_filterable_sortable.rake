namespace :field do
  task :make_attendance_fields_filterable_sortable, [:tenant_id] => [:environment] do |_task, args|

    fields = Field.where(internal_name: ['checkedInAt', 'checkedOutAt']).where("(is_filterable = ? OR is_sortable = ?)", false, false)

    if args[:tenant_id].present?
      fields = fields.where(tenant_id: args[:tenant_id])
    end
    updated_count = fields.update_all(is_filterable: true, is_sortable: true)
    puts "Updated #{updated_count} fields"
  end
end

# RAILS_ENV=<Environment> bundle exec rake field:make_attendance_fields_filterable_sortable[<tenant_id>]
