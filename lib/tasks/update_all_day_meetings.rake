namespace :meeting do
  desc 'Update All Day Meetings to update "to" attribute'
  task :all_day_meetings_update, [:all_tenants] => [:environment] do |_task, args|
    tenant_ids = args.extras.map(&:to_i).flatten

    if ActiveModel::Type::Boolean.new.cast(args[:all_tenants]).present?
      meetings = Meeting.where(all_day: true, to: nil)
    else
      meetings = Meeting.where(tenant_id: tenant_ids, all_day: true, to: nil)
    end

    meetings.find_in_batches(batch_size: 100) do |meeting_group|
      meeting_group.each do |meeting|
        begin
          meeting.to = meeting.from.in_time_zone(meeting.time_zone.name).end_of_day.utc
          meeting.save(validate: false)
          puts "AllDayMeetingsUpdate: Meeting with id #{meeting.id} for tenant id #{meeting.tenant_id} updated all day meeting successfully."
        rescue StandardError => e
          puts "AllDayMeetingsUpdate: ERROR while updating meeting with id #{meeting.id} for tenant id #{meeting.tenant_id}. Message: #{e}"
        end
      end
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake meeting:all_day_meetings_update[<all_tenants?>,<tenant_ids>]
