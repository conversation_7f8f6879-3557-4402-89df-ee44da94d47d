namespace :field do
  task :update_owner_field_internal, [:tenant_id] => [:environment] do |_task, args|

    fields = Field.where(internal_name: 'owner', is_internal: true)
    if args[:tenant_id].present?
      fields = fields.where(tenant_id: args[:tenant_id])
    end
    updated_count = fields.update_all(is_internal: false)
    puts "Updated #{updated_count} fields"
  end
end

# RAILS_ENV=<Environment> bundle exec rake field:update_owner_field_internal[<tenant_id>]
