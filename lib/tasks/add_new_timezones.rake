namespace :meeting do
  task :add_new_timezones, [:tenant_id] => [:environment] do |_task, args|

    timezone_picklists =
      if args[:tenant_id].present?
        Picklist.where(tenant_id: args[:tenant_id], internal_name: 'timezone')
      else
        Picklist.where(internal_name: 'timezone')
      end

    fields = YAML.load_file("#{Rails.root}/config/meeting_fields.yml")
    timezone_data = fields.find { |_, field| field['internal_name'] == 'timezone' }

    timezone_picklists.includes(:picklist_values).find_in_batches(batch_size: 250) do |tz_picklists|
      current_time = DateTime.now
      tz_picklists.each do |timezone_picklist|
        existing_values = timezone_picklist.picklist_values.pluck(:internal_name)
        values_to_create = []
        timezone_data.last['picklist']['picklist_values'].each do |key, value|
          if existing_values.exclude?(value['internal_name'])
            values_to_create << value.merge('picklist_id' => timezone_picklist.id, 'tenant_id' => timezone_picklist.tenant_id, 'created_at' => current_time, 'updated_at' => current_time)
          end
        end
        PicklistValue.insert_all(values_to_create) unless values_to_create.empty?
      end
    end
  end
end
