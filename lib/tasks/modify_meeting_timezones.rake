namespace :meeting do
  task :modify_meeting_timezones, [:tenant_id] => [:environment] do |_task, args|

    timezones = {"(GMT-12:00) International Date Line West"=>"Etc/GMT+12", "(GMT-11:00) Midway Island, Samoa"=>"Pacific/Midway", "(GMT-10:00) Hawaii"=>"Pacific/Honolulu", "(GMT-09:00) Alaska"=>"US/Alaska", "(GMT-08:00) Pacific Time (US & Canada)"=>"America/Los_Angeles", "(GMT-08:00) Tijuana, Baja California"=>"America/Tijuana", "(GMT-07:00) Arizona"=>"US/Arizona", "(GMT-07:00) Chihuahua, La Paz, Mazatlan"=>"America/Chihuahua", "(GMT-07:00) Mountain Time (US & Canada)"=>"US/Mountain", "(GMT-06:00) Central America"=>"America/Managua", "(GMT-06:00) Central Time (US & Canada)"=>"US/Central", "(GMT-06:00) Guadalajara, Mexico City, Monterrey"=>"America/Mexico_City", "(GMT-06:00) Saskatchewan"=>"Canada/Saskatchewan", "(GMT-05:00) Bogota, Lima, Quito, Rio Branco"=>"America/Bogota", "(GMT-05:00) Eastern Time (US & Canada)"=>"US/Eastern", "(GMT-05:00) Indiana (East)"=>"US/East-Indiana", "(GMT-04:00) Atlantic Time (Canada)"=>"Canada/Atlantic", "(GMT-04:00) Caracas, La Paz"=>"America/Caracas", "(GMT-04:00) Manaus"=>"America/Manaus", "(GMT-04:00) Santiago"=>"America/Santiago", "(GMT-03:30) Newfoundland"=>"Canada/Newfoundland", "(GMT-03:00) Brasilia"=>"America/Sao_Paulo", "(GMT-03:00) Buenos Aires, Georgetown"=>"America/Argentina/Buenos_Aires", "(GMT-03:00) Greenland"=>"America/Godthab", "(GMT-03:00) Montevideo"=>"America/Montevideo", "(GMT-02:00) Mid-Atlantic"=>"America/Noronha", "(GMT-01:00) Cape Verde Is."=>"Atlantic/Cape_Verde", "(GMT-01:00) Azores"=>"Atlantic/Azores", "(GMT+00:00) Casablanca, Monrovia, Reykjavik"=>"Africa/Casablanca", "(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London"=>"Etc/Greenwich", "(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna"=>"Europe/Amsterdam", "(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague"=>"Europe/Belgrade", "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris"=>"Europe/Brussels", "(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb"=>"Europe/Sarajevo", "(GMT+01:00) West Central Africa"=>"Africa/Lagos", "(GMT+02:00) Amman"=>"Asia/Amman", "(GMT+02:00) Athens, Bucharest, Istanbul"=>"Europe/Athens", "(GMT+02:00) Beirut"=>"Asia/Beirut", "(GMT+02:00) Cairo"=>"Africa/Cairo", "(GMT+02:00) Harare, Pretoria"=>"Africa/Harare", "(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius"=>"Europe/Helsinki", "(GMT+02:00) Jerusalem"=>"Asia/Jerusalem", "(GMT+02:00) Minsk"=>"Europe/Minsk", "(GMT+02:00) Windhoek"=>"Africa/Windhoek", "(GMT+03:00) Kuwait, Riyadh, Baghdad"=>"Asia/Kuwait", "(GMT+03:00) Moscow, St. Petersburg, Volgograd"=>"Europe/Moscow", "(GMT+03:00) Nairobi"=>"Africa/Nairobi", "(GMT+03:00) Tbilisi"=>"Asia/Tbilisi", "(GMT+03:30) Tehran"=>"Asia/Tehran", "(GMT+04:00) Abu Dhabi, Muscat"=>"Asia/Muscat", "(GMT+04:00) Baku"=>"Asia/Baku", "(GMT+04:00) Yerevan"=>"Asia/Yerevan", "(GMT+04:30) Kabul"=>"Asia/Kabul", "(GMT+05:00) Yekaterinburg"=>"Asia/Yekaterinburg", "(GMT+05:00) Islamabad, Karachi, Tashkent"=>"Asia/Karachi", "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura"=>"Asia/Calcutta", "(GMT+05:45) Kathmandu"=>"Asia/Katmandu", "(GMT+06:00) Almaty, Novosibirsk"=>"Asia/Almaty", "(GMT+06:00) Astana, Dhaka"=>"Asia/Dhaka", "(GMT+06:30) Yangon (Rangoon)"=>"Asia/Rangoon", "(GMT+07:00) Bangkok, Hanoi, Jakarta"=>"Asia/Bangkok", "(GMT+07:00) Krasnoyarsk"=>"Asia/Krasnoyarsk", "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi"=>"Asia/Hong_Kong", "(GMT+08:00) Kuala Lumpur, Singapore"=>"Asia/Kuala_Lumpur", "(GMT+08:00) Irkutsk, Ulaan Bataar"=>"Asia/Irkutsk", "(GMT+08:00) Perth"=>"Australia/Perth", "(GMT+08:00) Taipei"=>"Asia/Taipei", "(GMT+09:00) Osaka, Sapporo, Tokyo"=>"Asia/Tokyo", "(GMT+09:00) Seoul"=>"Asia/Seoul", "(GMT+09:00) Yakutsk"=>"Asia/Yakutsk", "(GMT+09:30) Adelaide"=>"Australia/Adelaide", "(GMT+09:30) Darwin"=>"Australia/Darwin", "(GMT+10:00) Brisbane"=>"Australia/Brisbane", "(GMT+10:00) Canberra, Melbourne, Sydney"=>"Australia/Canberra", "(GMT+10:00) Hobart"=>"Australia/Hobart", "(GMT+10:00) Guam, Port Moresby"=>"Pacific/Guam", "(GMT+10:00) Vladivostok"=>"Asia/Vladivostok", "(GMT+11:00) Magadan, Solomon Is., New Caledonia"=>"Asia/Magadan", "(GMT+12:00) Auckland, Wellington"=>"Pacific/Auckland", "(GMT+12:00) Fiji, Kamchatka, Marshall Is."=>"Pacific/Fiji", "(GMT+13:00) Nuku'alofa"=>"Pacific/Tongatapu", "(GMT+01:00) Europe/Andorra"=>"Europe/Andorra", "(GMT+04:00) Asia/Dubai"=>"Asia/Dubai", "(GMT-04:00) America/Antigua"=>"America/Antigua", "(GMT-04:00) America/Anguilla"=>"America/Anguilla", "(GMT+01:00) Europe/Tirane"=>"Europe/Tirane", "(GMT+01:00) Africa/Luanda"=>"Africa/Luanda", "(GMT+13:00) New Zealand time - McMurdo, South Pole"=>"Antarctica/McMurdo", "(GMT+11:00) Casey"=>"Antarctica/Casey", "(GMT+07:00) Davis"=>"Antarctica/Davis", "(GMT+10:00) Dumont-d Urville"=>"Antarctica/DumontDUrville", "(GMT+05:00) Mawson"=>"Antarctica/Mawson", "(GMT-03:00) Palmer"=>"Antarctica/Palmer", "(GMT-03:00) Rothera"=>"Antarctica/Rothera", "(GMT+03:00) Syowa"=>"Antarctica/Syowa", "(GMT+00:00) Troll"=>"Antarctica/Troll", "(GMT+06:00) Antarctica/Vostok"=>"Antarctica/Vostok", "(GMT-03:00) Argentina (most areas: CB, CC, CN, ER, FM, MN, SE, SF)"=>"America/Argentina/Cordoba", "(GMT-03:00) Salta (SA, LP, NQ, RN)"=>"America/Argentina/Salta", "(GMT-03:00) Jujuy (JY)"=>"America/Argentina/Jujy", "(GMT-03:00) Tucuman (TM)"=>"America/Argentina/Tucumn", "(GMT-03:00) Catamarca (CT), Chubut (CH)"=>"America/Argentina/Catamarca", "(GMT-03:00) La Rioja (LR)"=>"America/Argentina/La_Rioa", "(GMT-03:00) San Juan (SJ)"=>"America/Argentina/San_Jun", "(GMT-03:00) Mendoza (MZ)"=>"America/Argentina/Mendoa", "(GMT-03:00) San Luis (SL)"=>"America/Argentina/San_Lus", "(GMT-03:00) Santa Cruz (SC)"=>"America/Argentina/Rio_Gallgos", "(GMT-03:00) Tierra del Fuego (TF)"=>"America/Argentina/Ushuaia", "(GMT-11:00) Pacific/Pago_Pago"=>"Pacific/Pago_Pago", "(GMT+01:00) Europe/Vienna"=>"Europe/Vienna", "(GMT+11:00) Lord Howe Island"=>"Australia/Lord_Howe", "(GMT+11:00) Macquarie Island"=>"Antarctica/Macquarie", "(GMT+11:00) Victoria"=>"Australia/Melbourne", "(GMT+11:00) New South Wales (most areas)"=>"Australia/Sydney", "(GMT+10:30) New South Wales (Yancowinna)"=>"Australia/Broken_Hill", "(GMT+10:00) Queensland (Whitsunday Islands)"=>"Australia/Lindeman", "(GMT+08:45) Western Australia (Eucla)"=>"Australia/Eucla", "(GMT-04:00) America/Aruba"=>"America/Aruba", "(GMT+02:00) Europe/Mariehamn"=>"Europe/Mariehamn", "(GMT-04:00) America/Barbados"=>"America/Barbados", "(GMT+00:00) Africa/Ouagadougou"=>"Africa/Ouagadougou", "(GMT+02:00) Europe/Sofia"=>"Europe/Sofia", "(GMT+03:00) Asia/Bahrain"=>"Asia/Bahrain", "(GMT+02:00) Africa/Bujumbura"=>"Africa/Bujumbura", "(GMT+01:00) Africa/Porto-Novo"=>"Africa/Porto-Novo", "(GMT-04:00) America/St_Barthelemy"=>"America/St_Barthelemy", "(GMT-04:00) Atlantic/Bermuda"=>"Atlantic/Bermuda", "(GMT+08:00) Asia/Brunei"=>"Asia/Brunei", "(GMT-04:00) America/La_Paz"=>"America/La_Paz", "(GMT+08:45) America/Kralendijk"=>"America/Kralendijk", "(GMT-03:00) Para (east), Amapa"=>"America/Belem", "(GMT-03:00) Brazil (northeast: MA, PI, CE, RN, PB)"=>"America/Fortaleza", "(GMT-03:00) Pernambuco"=>"America/Recife", "(GMT-03:00) Tocantins"=>"America/Araguaina", "(GMT-03:00) Alagoas, Sergipe"=>"America/Maceio", "(GMT-03:00) Bahia"=>"America/Bahia", "(GMT-04:00) Mato Grosso do Sul"=>"America/Campo_Grande", "(GMT-04:00) Mato Grosso"=>"America/Cuiaba", "(GMT-03:00) Para (west)"=>"America/Santarem", "(GMT-04:00) Rondonia"=>"America/Porto_Velho", "(GMT-04:00) Roraima"=>"America/Boa_Vista", "(GMT-05:00) Amazonas (west)"=>"America/Eirunepe", "(GMT-05:00) Acre"=>"America/Rio_Braco", "(GMT-05:00) America/Nassau"=>"America/Nassau", "(GMT+06:00) Asia/Thimphu"=>"Asia/Thimphu", "(GMT+02:00) Africa/Gaborone"=>"Africa/Gaborone", "(GMT-06:00) America/Belize"=>"America/Belize", "(GMT-03:30) Newfoundland, Labrador (southeast)"=>"America/St_Johns", "(GMT-04:00) Atlantic - NS (most areas), PE"=>"America/Halifax", "(GMT-04:00) Atlantic - NS (Cape Breton)"=>"America/Glace_Bay", "(GMT-04:00) Atlantic - New Brunswick"=>"America/Moncton", "(GMT-04:00) Atlantic - Labrador (most areas)"=>"America/Goose_Bay", "(GMT-04:00) AST - QC (Lower North Shore)"=>"America/Blanc-Sablon", "(GMT-05:00) Eastern - ON, QC (most areas)"=>"America/Toronto", "(GMT-05:00) Eastern - NU (most areas)"=>"America/Iqaluit", "(GMT-05:00) EST - ON (Atikokan), NU (Coral H)"=>"America/Atikokan", "(GMT-06:00) Central - ON (west), Manitoba"=>"America/Winnipeg", "(GMT-06:00) Central - NU (Resolute)"=>"America/Resolute", "(GMT-06:00) Central - NU (central)"=>"America/Rankin_Inlet", "(GMT-06:00) CST - SK (most areas)"=>"America/Regina", "(GMT-06:00) CST - SK (midwest)"=>"America/Swift_Current", "(GMT-07:00) Mountain - AB, BC (E), SK (W)"=>"America/Edmonton", "(GMT-07:00) Mountain - NU (west)"=>"America/Cambridge_Bay", "(GMT-07:00) Mountain - NT (central)"=>"America/Yellowknife", "(GMT-07:00) Mountain - NT (west)"=>"America/Inuvik", "(GMT-07:00) MST - BC (Creston)"=>"America/Creston", "(GMT-07:00) MST - BC (Dawson Cr, Ft St John)"=>"America/Dawson_Creek", "(GMT-07:00) MST - BC (Ft Nelson)"=>"America/Fort_Nelson", "(GMT-07:00) MST - Yukon (east)"=>"America/Whitehorse", "(GMT-07:00) MST - Yukon (west)"=>"America/Dawson", "(GMT-08:00) Pacific - BC (most areas)"=>"America/Vancouver", "(GMT+06:30) Indian/Cocos"=>"Indian/Cocos", "(GMT+01:00) Dem. Rep. of Congo (west)"=>"Africa/Kinshasa", "(GMT+02:00) Dem. Rep. of Congo (east)"=>"Africa/Lubumbashi", "(GMT+01:00) Africa/Bangui"=>"Africa/Bangui", "(GMT+01:00) Africa/Brazzaville"=>"Africa/Brazzaville", "(GMT+01:00) Europe/Zurich"=>"Europe/Zurich", "(GMT+00:00) Africa/Abidjan"=>"Africa/Abidjan", "(GMT-10:00) Pacific/Rarotonga"=>"Pacific/Rarotonga", "(GMT-03:00) Region of Magallanes"=>"America/Punta_Arenas", "(GMT-05:00) Easter Island"=>"Pacific/Easter", "(GMT-06:00) Africa/Douala"=>"Africa/Douala", "(GMT+08:00) Beijing Time"=>"Asia/Shanghai", "(GMT+06:00) Xinjiang Time"=>"Asia/Urumqi", "(GMT-06:00) America/Costa_Rica"=>"America/Costa_Rica", "(GMT-05:00) America/Havana"=>"America/Havana", "(GMT-04:00) America/Curacao"=>"America/Curacao", "(GMT+07:00) Indian/Christmas"=>"Indian/Christmas", "(GMT+02:00) Cyprus (most areas)"=>"Asia/Nicosia", "(GMT+02:00) Northern Cyprus"=>"Asia/Famagusta", "(GMT+01:00) Europe/Prague"=>"Europe/Prague", "(GMT+01:00) Germany (most areas)"=>"Europe/Berlin", "(GMT+01:00) Busingen"=>"Europe/Busingen", "(GMT+03:00) Africa/Djibouti"=>"Africa/Djibouti", "(GMT+01:00) Europe/Copenhagen"=>"Europe/Copenhagen", "(GMT-04:00) America/Dominica"=>"America/Dominica", "(GMT-04:00) America/Santo_Domingo"=>"America/Santo_Domingo", "(GMT+01:00) Africa/Algiers"=>"Africa/Algiers", "(GMT-05:00) Ecuador (mainland)"=>"America/Guayaquil", "(GMT-06:00) Galapagos Islands"=>"Pacific/Galapagos", "(GMT+02:00) Europe/Tallinn"=>"Europe/Tallinn", "(GMT-00:00) Africa/El_Aaiun"=>"Africa/El_Aaiun", "(GMT+03:00) Africa/Asmara"=>"Africa/Asmara", "(GMT+01:00) Spain (mainland)"=>"Europe/Madrid", "(GMT+01:00) Ceuta, Melilla"=>"Africa/Ceuta", "(GMT+00:00) Canary Islands"=>"Atlantic/Canary", "(GMT+03:00) Africa/Addis_Ababa"=>"Africa/Addis_Ababa", "(GMT-03:00) Atlantic/Stanley"=>"Atlantic/Stanley", "(GMT+10:00) Chuuk/Truk, Yap"=>"Pacific/Chuuk", "(GMT+11:00) Pohnpei/Ponape"=>"Pacific/Pohnpei", "(GMT+11:00) Kosrae"=>"Pacific/Kosrae", "(GMT+00:00) Atlantic/Faroe"=>"Atlantic/Faroe", "(GMT+01:00) Europe/Paris"=>"Europe/Paris", "(GMT+01:00) Africa/Libreville"=>"Africa/Libreville", "(GMT+00:00) Europe/London"=>"Europe/London", "(GMT-04:00) America/Grenada"=>"America/Grenada", "(GMT-04:00) America/Cayenne"=>"America/Cayenne", "(GMT+00:00) Europe/Guernsey"=>"Europe/Guernsey", "(GMT+00:00) Africa/Accra"=>"Africa/Accra", "(GMT+01:00) Europe/Gibraltar"=>"Europe/Gibraltar", "(GMT-03:00) Greenland (most areas)"=>"America/Nuuk", "(GMT+00:00) National Park (east coast)"=>"America/Danmarkshavn", "(GMT-01:00) Scoresbysund/Ittoqqortoormiit"=>"America/Scoresbysund", "(GMT-01:00) Thule/Pituffik"=>"America/Thule", "(GMT+00:00) Africa/Banjul"=>"Africa/Banjul", "(GMT+00:00) Africa/Conakry"=>"Africa/Conakry", "(GMT-04:00) America/Guadeloupe"=>"America/Guadeloupe", "(GMT+01:00) Africa/Malabo"=>"Africa/Malabo", "(GMT-02:00) Atlantic/South_Georgia"=>"Atlantic/South_Georgia", "(GMT-06:00) America/Guatemala"=>"America/Guatemala", "(GMT+00:00) Africa/Bissau"=>"Africa/Bissau", "(GMT-04:00) America/Guyana"=>"America/Guyana", "(GMT-06:00) America/Tegucigalpa"=>"America/Tegucigalpa", "(GMT+01:00) Europe/Zagreb"=>"Europe/Zagreb", "(GMT-05:00) America/Port-au-Prince"=>"America/Port-au-Prince", "(GMT+01:00) Europe/Budapest"=>"Europe/Budapest", "(GMT+07:00) Java, Sumatra"=>"Asia/Jakarta", "(GMT+07:00) Borneo (west, central)"=>"Asia/Pontianak", "(GMT+08:00) Borneo (east, south), Sulawesi/Celebes, Bali, Nusa Tengarra, Tim (west)"=>"Asia/Makassar", "(GMT+09:00) New Guinea (West Papua / Irian Jaya), Malukus/Moluccas"=>"Asia/Jayapura", "(GMT+00:00) Europe/Dublin"=>"Europe/Dublin", "(GMT+00:00) Europe/Isle_of_Man"=>"Europe/Isle_of_Man", "(GMT+5:30) Asia/Kolkata"=>"Asia/Kolkata", "(GMT+06:00) Indian/Chagos"=>"Indian/Chagos", "(GMT+03:00) Asia/Baghdad"=>"Asia/Baghdad", "(GMT+00:00) Atlantic/Reykjavik"=>"Atlantic/Reykjavik", "(GMT+01:00) Europe/Rome"=>"Europe/Rome", "(GMT+00:00) Europe/Jersey"=>"Europe/Jersey", "(GMT-05:00) America/Jamaica"=>"America/Jamaica", "(GMT+06:00) Asia/Bishkek"=>"Asia/Bishkek", "(GMT+07:00) Asia/Phnom_Penh"=>"Asia/Phnom_Penh", "(GMT+12:00) Gilbert Islands"=>"Pacific/Tarawa", "(GMT+013:00) Phoenix Islands"=>"Pacific/Kanton", "(GMT+14:00) Line Islands"=>"Pacific/Kiritimati", "(GMT+03:00) Indian/Comoro"=>"Indian/Comoro", "(GMT-04:00) America/St_Kitts"=>"America/St_Kitts", "(GMT+09:00) Asia/Pyongyang"=>"Asia/Pyongyang", "(GMT-05:00) America/Cayman"=>"America/Cayman", "(GMT+05:00) Qyzylorda/Kyzylorda/Kzyl-Orda"=>"Asia/Qyzylorda", "(GMT+06:00) Qostanay/Kostanay/Kustanay"=>"Asia/Qostanay", "(GMT+05:00) Aqtobe/Aktobe"=>"Asia/Aqtobe", "(GMT+05:00) Mangghystau/Mankistau"=>"Asia/Aqtau", "(GMT+05:00) Atyrau/Atirau/Gur yev"=>"Asia/Atyrau", "(GMT+05:00) West Kazakhstan"=>"Asia/Oral", "(GMT+07:00) Asia/Vientiane"=>"Asia/Vientiane", "(GMT-04:00) America/St_Lucia"=>"America/St_Lucia", "(GMT+1:00) Europe/Vaduz"=>"Europe/Vaduz", "(GMT+05:30) Asia/Colombo"=>"Asia/Colombo", "(GMT+00:00) Africa/Monrovia"=>"Africa/Monrovia", "(GMT+02:00) Africa/Maseru"=>"Africa/Maseru", "(GMT+02:00) Europe/Vilnius"=>"Europe/Vilnius", "(GMT+01:00) Europe/Luxembourg"=>"Europe/Luxembourg", "(GMT+02:00) Europe/Riga"=>"Europe/Riga", "(GMT+02:00) Africa/Tripoli"=>"Africa/Tripoli", "(GMT+01:00) Europe/Monaco"=>"Europe/Monaco", "(GMT+02:00) Europe/Chisinau"=>"Europe/Chisinau", "(GMT+01:00) Europe/Podgorica"=>"Europe/Podgorica", "(GMT-04:00) America/Marigot"=>"America/Marigot", "(GMT-04:00) Indian/Antananarivo"=>"Indian/Antananarivo", "(GMT+12:00) Marshall Islands (most areas)"=>"Pacific/Majuro", "(GMT+12:00) Kwajalein"=>"Pacific/Kwajalein", "(GMT+01:00) Europe/Skopje"=>"Europe/Skopje", "(GMT+00:00) Africa/Bamako"=>"Africa/Bamako", "(GMT++6:30) Asia/Yangon"=>"Asia/Yangon", "(GMT+08:00) Mongolia (most areas)"=>"Asia/Ulaanbaatar", "(GMT+07:00) Bayan-Olgiy, Govi-Altai, Hovd, Uvs, Zavkhan"=>"Asia/Hovd", "(GMT+08:00) Dornod, Sukhbaatar"=>"Asia/Choibalsan", "(GMT+08:00) Asia/Macau"=>"Asia/Macau", "(GMT+10:00) Pacific/Saipan"=>"Pacific/Saipan", "(GMT-04:00) America/Martinique"=>"America/Martinique", "(GMT+00:00) Africa/Nouakchott"=>"Africa/Nouakchott", "(GMT-04:00) America/Montserrat"=>"America/Montserrat", "(GMT+01:00) Europe/Malta"=>"Europe/Malta", "(GMT+04:00) Indian/Mauritius"=>"Indian/Mauritius", "(GMT+05:00) Indian/Maldives"=>"Indian/Maldives", "(GMT+02:00) Africa/Blantyre"=>"Africa/Blantyre", "(GMT-05:00) Quintana Roo"=>"America/Cancun", "(GMT-06:00) Campeche, Yucatan"=>"America/Merida", "(GMT-06:00) Durango Coahuila, Nuevo Leon, Tamaulipas (most areas)"=>"America/Monterrey", "(GMT-06:00) Coahuila, Nuevo Leon, Tamaulipas (US border)"=>"America/Matamoros", "(GMT-07:00) Chihuahua (US border - west)"=>"America/Ciudad_Juarez", "(GMT-07:00) Chihuahua (US border - east)"=>"America/Ojinaga", "(GMT-07:00) Baja California Sur, Nayarit (most areas), Sinaloa"=>"America/Mazatlan", "(GMT-06:00) Bahia de Banderas"=>"America/Bahia_Banderas", "(GMT-07:00) Sonora"=>"America/Hermosill", "(GMT+08:00) Sabah, Sarawak"=>"Asia/Kuching", "(GMT+02:00) Africa/Maputo"=>"Africa/Maputo", "(GMT+11:00) Pacific/Noumea"=>"Pacific/Noumea", "(GMT+01:00) Africa/Niamey"=>"Africa/Niamey", "(GMT+11:00) Pacific/Norfolk"=>"Pacific/Norfolk", "(GMT+01:00) Europe/Oslo"=>"Europe/Oslo", "(GMT+05:45) Asia/Kathmandu"=>"Asia/Kathmandu", "(GMT+12:00) Pacific/Nauru"=>"Pacific/Nauru", "(GMT-11:00) Pacific/Niue"=>"Pacific/Niue", "(GMT+13:45) Chatham Islands"=>"Pacific/Chatham", "(GMT-05:00) America/Panama"=>"America/Panama", "(GMT-05:00) America/Lima"=>"America/Lima", "(GMT-10:00) Society Islands"=>"Pacific/Tahiti", "(GMT-09:30) Marquesas Islands"=>"Pacific/Marquesas", "(GMT-09:00) Gambier Islands"=>"Pacific/Gambier", "(GMT+10:00) Papua New Guinea (most areas)"=>"Pacific/Port_Moresby", "(GMT+11:00) Bougainville"=>"Pacific/Bougainville", "(GMT+08:00) Asia/Manila"=>"Asia/Manila", "(GMT+01:00) Europe/Warsaw"=>"Europe/Warsaw", "(GMT-03:00) America/Miquelon"=>"America/Miquelon", "(GMT-08:00) Pacific/Pitcairn"=>"Pacific/Pitcairn", "(GMT-04:00) America/Puerto_Rico"=>"America/Puerto_Rico", "(GMT+02:00) Gaza Strip"=>"Asia/Gaza", "(GMT+02:00) West Bank"=>"Asia/Hebron", "(GMT+00:00) Portugal (mainland)"=>"Europe/Lisbon", "(GMT+00:00) Madeira Islands"=>"Atlantic/Madeira", "(GMT+09:00) Pacific/Palau"=>"Pacific/Palau", "(GMT-04:00) America/Asuncion"=>"America/Asuncion", "(GMT+03:00) Asia/Qatar"=>"Asia/Qatar", "(GMT+04:00) Indian/Reunion"=>"Indian/Reunion", "(GMT+02:00) Europe/Bucharest"=>"Europe/Bucharest", "(GMT+02:00) MSK-01 - Kaliningrad"=>"Europe/Kaliningrad", "(GMT+03:00) Crimea"=>"Europe/Simferopol", "(GMT+03:00) MSK+00 - Kirov"=>"Europe/Kirov", "(GMT+03:00) MSK+00 - Volgograd"=>"Europe/Volgograd", "(GMT+04:00) MSK+01 - Astrakhan"=>"Europe/Astrakhan", "(GMT+04:00) MSK+01 - Saratov"=>"Europe/Saratov", "(GMT+04:00) MSK+01 - Ulyanovsk"=>"Europe/Ulyanovsk", "(GMT+04:00) MSK+01 - Samara, Udmurtia"=>"Europe/Samara", "(GMT+06:00) MSK+03 - Omsk"=>"Asia/Omsk", "(GMT+07:00) MSK+04 - Novosibirsk"=>"Asia/Novosibirsk", "(GMT+07:00) MSK+04 - Altai"=>"Asia/Barnaul", "(GMT+07:00) MSK+04 - Tomsk"=>"Asia/Tomsk", "(GMT+07:00) MSK+04 - Kemerovo"=>"Asia/Novokuznetsk", "(GMT+09:00) MSK+06 - Zabaykalsky"=>"Asia/Chita", "(GMT+09:00) MSK+06 - Tomponsky, Ust-Maysky"=>"Asia/Khandyga", "(GMT+10:00) MSK+07 - Oymyakonsky"=>"Asia/Ust-Nera", "(GMT+11:00) MSK+08 - Sakhalin Island"=>"Asia/Sakhalin", "(GMT+11:00) MSK+08 - Sakha (E) North Kuril Is"=>"Asia/Srednekolymsk", "(GMT+12:00) MSK+09 - Kamchatka"=>"Asia/Kamchatka", "(GMT+12:00) MSK+09 - Bering Sea"=>"Asia/Anadyr", "(GMT+02:00) Africa/Kigali"=>"Africa/Kigali", "(GMT+03:00) Asia/Riyadh"=>"Asia/Riyadh", "(GMT+11:00) Pacific/Guadalcanal"=>"Pacific/Guadalcanal", "(GMT+04:00) Indian/Mahe"=>"Indian/Mahe", "(GMT+02:00) Africa/Khartoum"=>"Africa/Khartoum", "(GMT+01:00) Europe/Stockholm"=>"Europe/Stockholm", "(GMT+01:00) Asia/Singapore"=>"Asia/Singapore", "(GMT+08:00) Atlantic/St_Helena"=>"Atlantic/St_Helena", "(GMT+00:00) Europe/Ljubljana"=>"Europe/Ljubljana", "(GMT+01:00) Arctic/Longyearbyen"=>"Arctic/Longyearbyen", "(GMT+01:00) Europe/Bratislava"=>"Europe/Bratislava", "(GMT+00:00) Africa/Freetown"=>"Africa/Freetown", "(GMT+01:00) Europe/San_Marino"=>"Europe/San_Marino", "(GMT+00:00) Africa/Dakar"=>"Africa/Dakar", "(GMT+03:00) Africa/Mogadishu"=>"Africa/Mogadishu", "(GMT-03:00) America/Paramaribo"=>"America/Paramaribo", "(GMT+02:00) Africa/Juba"=>"Africa/Juba", "(GMT+00:00) Africa/Sao_Tome"=>"Africa/Sao_Tome", "(GMT-06:00) America/El_Salvador"=>"America/El_Salvador", "(GMT-04:00) America/Lower_Princes"=>"America/Lower_Princes", "(GMT+02:00) Asia/Damascus"=>"Asia/Damascus", "(GMT+02:00) Africa/Mbabane"=>"Africa/Mbabane", "(GMT-05:00) America/Grand_Turk"=>"America/Grand_Turk", "(GMT+01:00) Africa/Ndjamena"=>"Africa/Ndjamena", "(GMT+05:00) Indian/Kerguelen"=>"Indian/Kerguelen", "(GMT+00:00) Africa/Lome"=>"Africa/Lome", "(GMT+05:00) Asia/Dushanbe"=>"Asia/Dushanbe", "(GMT+13:00) Pacific/Fakaofo"=>"Pacific/Fakaofo", "(GMT+09:00) Asia/Dili"=>"Asia/Dili", "(GMT+05:00) Asia/Ashgabat"=>"Asia/Ashgabat", "(GMT+01:00) Africa/Tunis"=>"Africa/Tunis", "(GMT+01:00) Europe/Istanbul"=>"Europe/Istanbul", "(GMT-04:00) America/Port_of_Spain"=>"America/Port_of_Spain", "(GMT+12:00) Pacific/Funafuti"=>"Pacific/Funafuti", "(GMT+03:00) Africa/Dar_es_Salaam"=>"Africa/Dar_es_Salaam", "(GMT+02:00) Ukraine (most areas)"=>"Europe/Kyiv", "(GMT+03:00) Africa/Kampala"=>"Africa/Kampala", "(GMT+12:00) Wake Island"=>"Pacific/Wake", "(GMT-05:00) Eastern (most areas)"=>"America/New_York", "(GMT-05:00) Eastern - MI (most areas)"=>"America/Detroit", "(GMT-05:00) Eastern - KY (Louisville area)"=>"America/Kentucky/Louisville", "(GMT-05:00) Eastern - KY (Wayne)"=>"America/Kentucky/Monticello", "(GMT-05:00) Eastern - IN (most areas)"=>"America/Indiana/Indianapolis", "(GMT-05:00) Eastern - IN (Da, Du, K, Mn)"=>"America/Indiana/Vincennes", "(GMT-05:00) Eastern - IN (Pulaski)"=>"America/Indiana/Winamac", "(GMT-05:00) Eastern - IN (Crawford)"=>"America/Indiana/Marengo", "(GMT-05:00) Eastern - IN (Pike)"=>"America/Indiana/Petersburg", "(GMT-05:00) Eastern - IN (Switzerland:)"=>"America/Indiana/Vevay", "(GMT-06:00) Central (most areas)"=>"America/Chicag", "(GMT-06:00) Central - IN (Perry)"=>"America/Indiana/Tell_City", "(GMT-06:00) Central - IN (Starke)"=>"America/Indiana/Knox", "(GMT-06:00) Central - MI (Wisconsin border)"=>"America/Menominee", "(GMT-06:00) Central - ND (Oliver)"=>"America/North_Dakota/Center", "(GMT-06:00) Central - ND (Morton rural)"=>"America/North_Dakota/New_Salem", "(GMT-06:00) Central - ND (Mercer)"=>"America/North_Dakota/Beulah", "(GMT-07:00) Mountain (most areas)"=>"America/Denver", "(GMT-07:00) Mountain - ID (south) OR (east)"=>"America/Boise", "(GMT-09:00) MST - Arizona (except Navajo)"=>"America/Phoenix", "(GMT-09:00) Alaska (most areas)"=>"America/Anchorage", "(GMT-09:00) Alaska - Juneau area"=>"America/Juneau", "(GMT-09:00) Alaska - Sitka area"=>"America/Sitka", "(GMT-09:00) Alaska - Annette Island"=>"America/Metlakatla", "(GMT-09:00) Alaska - Yakutat"=>"America/Yakutat", "(GMT-09:00) Alaska (west)"=>"America/Nome", "(GMT-10:00) Aleutian Islands"=>"America/Adak", "(GMT+05:00) Uzbekistan (west)"=>"Asia/Samarkand", "(GMT+05:00) Uzbekistan (east)"=>"Asia/Tashkent", "(GMT+02:00) Europe/Vatican"=>"Europe/Vatican", "(GMT-04:00) America/St_Vincent"=>"America/St_Vincent", "(GMT-04:00) America/Tortola"=>"America/Tortola", "(GMT-04:00) America/St_Thomas"=>"America/St_Thomas", "(GMT+07:00) Asia/Ho_Chi_Minh"=>"Asia/Ho_Chi_Minh", "(GMT+11:00) Pacific/Efate"=>"Pacific/Efate", "(GMT+12:00) Pacific/Wallis"=>"Pacific/Wallis", "(GMT+14:00) Pacific/Apia"=>"Pacific/Apia", "(GMT+03:00) Asia/Aden"=>"Asia/Aden", "(GMT+03:00) Indian/Mayotte"=>"Indian/Mayotte", "(GMT+02:00) Africa/Johannesburg"=>"Africa/Johannesburg", "(GMT+02:00) Africa/Lusaka"=>"Africa/Lusaka"}

    timezones.each do |tz_display_name, tz_internal_name|
      timezone_lookups =
        if args[:tenant_id].present?
          LookUp.where("entity LIKE 'timezone_%'").where(tenant_id: args[:tenant_id], name: tz_display_name)
        else
          LookUp.where("entity LIKE 'timezone_%'").where(name: tz_display_name)
        end

      puts timezone_lookups.update_all(name: tz_internal_name)
    end
  end
end
