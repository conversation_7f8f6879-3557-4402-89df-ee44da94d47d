# frozen_string_literal: true

namespace :meeting do
  desc 'Add tenant_id on notes'
  task :add_tenant_id_on_notes, [:from_tenant_id, :to_tenant_id] => :environment do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        begin
          Note.joins(:meeting).where("notes.tenant_id IS NULL").where("meetings.tenant_id = ?", tenant_id).select(:id)
              .find_in_batches.each_with_index do |note_batch, index|
                Note.where(id: note_batch.pluck(:id)).update_all(tenant_id: tenant_id)
                puts "Note batch processed for tenant #{tenant_id} batch index #{index}"
              end
          puts "Notes updated for tenant #{tenant_id}"
        rescue => e
          puts "Error while updating notes for tenant id #{tenant_id}. Message: #{e.message}"
        end
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake "meeting:add_tenant_id_on_notes[<from_tenant_id>,<to_tenant_id>]"
