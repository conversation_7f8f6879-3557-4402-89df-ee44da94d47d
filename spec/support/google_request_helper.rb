module GoogleRequestHelper
  def stub_watch_google_meeting_request(status, request_body, response_body)
    headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
    stub_request(:post, "https://www.googleapis.com/calendar/v3/calendars/primary/events/watch")
      .with(body: request_body, headers: headers)
      .to_return(status: status, body: response_body)
  end

  def stub_fetch_events_request(status, response_body)
    headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
    stub_request(:get, "https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events?timeMax=#{(DateTime.now + 3.months).utc.iso8601}&timeMin=#{(DateTime.now-3.months).utc.iso8601}&maxResults=2500&showDeleted=true")
      .with(headers: headers)
      .to_return(status: status, body: response_body)
  end
end
