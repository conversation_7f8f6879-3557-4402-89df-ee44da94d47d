# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BuildQueryForShareRules, type: :service do
  describe '#call' do
    let(:user) { create(:user) }
    let(:scope) { ShareRule.where(tenant_id: user.tenant_id) }

    context 'when string type' do
      context 'when equal operator' do
        let(:string_rule) { build(:json_rule_string_equal, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: "Mc%'_\"O Keefe") }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"name\" ILIKE 'Mc\\%''\\_\"O Keefe'")
        end
      end

      context 'when not equal operator' do
        let(:string_rule) { build(:json_rule_string_not_equal, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: "Mc%'_\"O Keefe") }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND NOT (\"share_rules\".\"name\" ILIKE 'Mc\\%''\\_\"O Keefe')")
        end
      end

      context 'when contains operator' do
        let(:string_rule) { build(:json_rule_string_contains, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: "Mc%'_\"O Keefe") }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"name\" ILIKE '%Mc\\%''\\_\"O Keefe%'")
        end
      end

      context 'when not contains operator' do
        let(:string_rule) { build(:json_rule_string_not_contains, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: "Mc%'_\"O Keefe") }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND NOT (\"share_rules\".\"name\" ILIKE '%Mc\\%''\\_\"O Keefe%')")
        end
      end

      context 'when begins with operator' do
        let(:string_rule) { build(:json_rule_string_begins_with, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: "Mc%'_\"O Keefe") }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"name\" ILIKE 'Mc\\%''\\_\"O Keefe%'")
        end
      end

      context 'when is empty operator' do
        let(:string_rule) { build(:json_rule_string_is_empty, field: 'name', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND (\"share_rules\".\"name\" = '' OR \"share_rules\".\"name\" IS NULL)")
        end
      end

      context 'when is not empty operator' do
        let(:string_rule) { build(:json_rule_string_is_not_empty, field: 'name', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND NOT ((\"share_rules\".\"name\" = '' OR \"share_rules\".\"name\" IS NULL))")
        end
      end

      context 'when in operator' do
        let(:string_rule) { build(:json_rule_string_in, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: ["Mc%'_\"O Keefe", "lorem ipsum"]) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND (\"share_rules\".\"name\" ILIKE 'Mc\\%''\\_\"O Keefe' OR \"share_rules\".\"name\" ILIKE 'lorem ipsum')")
        end
      end

      context 'when not in operator' do
        let(:string_rule) { build(:json_rule_string_not_in, field: 'name', meeting_or_share_rule_model: 'ShareRule', value: ["Mc%'_\"O Keefe", "lorem ipsum"]) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [string_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND NOT ((\"share_rules\".\"name\" ILIKE 'Mc\\%''\\_\"O Keefe' OR \"share_rules\".\"name\" ILIKE 'lorem ipsum'))")
        end
      end
    end

    context 'when date type' do
      context 'when greater operator' do
        let(:date_rule) { build(:json_rule_date_greater, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" > '2023-12-20 18:30:00'")
        end
      end

      context 'when equal operator' do
        let(:date_rule) { build(:json_rule_date_equal, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" = '2023-12-20 18:30:00'")
        end
      end

      context 'when less operator' do
        let(:date_rule) { build(:json_rule_date_less, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" < '2023-12-20 18:30:00'")
        end
      end

      context 'when less or equal operator' do
        let(:date_rule) { build(:json_rule_date_less_or_equal, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" <= '2023-12-20 18:30:00'")
        end
      end

      context 'when greater or equal operator' do
        let(:date_rule) { build(:json_rule_date_greater_or_equal, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" >= '2023-12-20 18:30:00'")
        end
      end

      context 'when between operator' do
        let(:date_rule) { build(:json_rule_date_between, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: ['2023-12-20T18:30:00Z', '2023-12-21T18:29:00Z']) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" BETWEEN '2023-12-20 18:30:00' AND '2023-12-21 18:29:00'")
        end
      end

      context 'when not between operator' do
        let(:date_rule) { build(:json_rule_date_not_between, field: 'created_at', meeting_or_share_rule_model: 'ShareRule', value: ['2023-12-20T18:30:00Z', '2023-12-21T18:29:00Z']) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND (\"share_rules\".\"created_at\" < '2023-12-20 18:30:00' OR \"share_rules\".\"created_at\" > '2023-12-21 18:29:00')")
        end
      end

      context 'when is null operator' do
        let(:date_rule) { build(:json_rule_date_is_null, field: 'created_at', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" IS NULL")
        end
      end

      context 'when is not null operator' do
        let(:date_rule) { build(:json_rule_date_is_not_null, field: 'created_at', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_at\" IS NOT NULL")
        end
      end
    end

    context 'when boolean type' do
      context 'when equal operator' do
        let(:boolean_rule) { build(:json_rule_boolean_equal, field: 'systemDefault', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [boolean_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"systemDefault\" = TRUE")
        end
      end

      context 'when not equal operator' do
        let(:boolean_rule) { build(:json_rule_boolean_not_equal, field: 'systemDefault', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [boolean_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"systemDefault\" != TRUE")
        end
      end
    end

    context 'when long type' do
      context 'when equal operator' do
        let(:long_rule) { build(:json_rule_long_equal, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" = 123")
        end
      end

      context 'when not equal operator' do
        let(:long_rule) { build(:json_rule_long_not_equal, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" != 123")
        end
      end

      context 'when is null operator' do
        let(:long_rule) { build(:json_rule_long_is_null, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" IS NULL")
        end
      end

      context 'when is not null operator' do
        let(:long_rule) { build(:json_rule_long_is_not_null, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" IS NOT NULL")
        end
      end

      context 'when greater operator' do
        let(:long_rule) { build(:json_rule_long_greater, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" > 123")
        end
      end

      context 'when greater or equal operator' do
        let(:long_rule) { build(:json_rule_long_greater_or_equal, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" >= 123")
        end
      end

      context 'when less operator' do
        let(:long_rule) { build(:json_rule_long_less, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" < 123")
        end
      end

      context 'when less or equal operator' do
        let(:long_rule) { build(:json_rule_long_less_or_equal, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" <= 123")
        end
      end

      context 'when in operator' do
        let(:long_rule) { build(:json_rule_long_in, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: '123,234') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" IN (123, 234)")
        end
      end

      context 'when not in operator' do
        let(:long_rule) { build(:json_rule_long_not_in, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: '123,234') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" NOT IN (123, 234)")
        end
      end

      context 'when between operator' do
        let(:long_rule) { build(:json_rule_long_between, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: [123,234]) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"created_by_id\" >= 123 AND \"share_rules\".\"created_by_id\" <= 234")
        end
      end

      context 'when not between operator' do
        let(:long_rule) { build(:json_rule_long_not_between, field: 'created_by_id', meeting_or_share_rule_model: 'ShareRule', value: [123,234]) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND NOT (\"share_rules\".\"created_by_id\" >= 123 AND \"share_rules\".\"created_by_id\" <= 234)")
        end
      end
    end
  end
end
