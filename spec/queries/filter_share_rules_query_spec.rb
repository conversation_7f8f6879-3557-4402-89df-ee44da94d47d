# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FilterShareRulesQuery, type: :service do
  describe '#call' do
    let(:user) { create(:user) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:auth_data) { build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }

    context 'valid' do
      before do
        @share_rule = create(:share_rule, tenant_id: user.tenant_id, created_by: user, updated_by: user)
        @another_share_rule = create(:share_rule, tenant_id: another_user.tenant_id, created_by: another_user, updated_by: another_user)
      end

      context 'when user has read all on share rule' do
        let(:share_rule_read_all) { true }

        it 'returns all share rules for tenant' do
          search_response = described_class.new(user.tenant_id, user.id, {}, share_rule_read_all).call

          expect(search_response.map(&:id)).to match_array([@share_rule.id, @another_share_rule.id])
        end
      end

      context 'when user does not have read all on share rule' do
        let(:share_rule_read_all) { false }

        it 'returns all share rules owned by user' do
          search_response = described_class.new(user.tenant_id, user.id, {}, share_rule_read_all).call

          expect(search_response.map(&:id)).to match_array([@share_rule.id])
        end
      end

      context 'filters' do
        context 'when equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'equal', value: USER },
                  { field: 'toType', type: 'string', operator: 'equal', value: USER },
                  { field: 'name', type: 'string', operator: 'equal', value: 'Meeting Share Rule' },
                  { field: 'systemDefault', type: 'boolean', operator: 'equal', value: false },
                  { field: 'from', type: 'long', operator: 'equal', value: @share_rule.from_id },
                  { field: 'to', type: 'long', operator: 'equal', value: @share_rule.to_id },
                  { field: 'entity', type: 'long', operator: 'equal', value: @share_rule.meeting_id },
                  { field: 'owner', type: 'long', operator: 'equal', value: @share_rule.created_by_id },
                  { field: 'createdBy', type: 'long', operator: 'equal', value: @share_rule.created_by_id },
                  { field: 'updatedBy', type: 'long', operator: 'equal', value: @share_rule.updated_by_id },
                  { field: 'createdAt', type: 'date', operator: 'equal', value: @share_rule.created_at.iso8601(6) },
                  { field: 'updatedAt', type: 'date', operator: 'equal', value: @share_rule.updated_at.iso8601(6) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when not equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'not_equal', value: TEAM },
                  { field: 'toType', type: 'string', operator: 'not_equal', value: TEAM },
                  { field: 'name', type: 'string', operator: 'not_equal', value: 'Share Rule' },
                  { field: 'systemDefault', type: 'boolean', operator: 'not_equal', value: true },
                  { field: 'from', type: 'long', operator: 'not_equal', value: @another_share_rule.from_id },
                  { field: 'to', type: 'long', operator: 'not_equal', value: @another_share_rule.to_id },
                  { field: 'entity', type: 'long', operator: 'not_equal', value: @another_share_rule.meeting_id },
                  { field: 'owner', type: 'long', operator: 'not_equal', value: @another_share_rule.created_by_id },
                  { field: 'createdBy', type: 'long', operator: 'not_equal', value: @another_share_rule.created_by_id },
                  { field: 'updatedBy', type: 'long', operator: 'not_equal', value: @another_share_rule.updated_by_id }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when contains operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'contains', value: 'ser' },
                  { field: 'toType', type: 'string', operator: 'contains', value: 'ser' },
                  { field: 'name', type: 'string', operator: 'contains', value: 'Share' }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when not contains operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'not_contains', value: 'tea' },
                  { field: 'toType', type: 'string', operator: 'not_contains', value: 'tea' },
                  { field: 'name', type: 'string', operator: 'not_contains', value: 'rule share' }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when begins with operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'begins_with', value: 'use' },
                  { field: 'toType', type: 'string', operator: 'begins_with', value: 'use' },
                  { field: 'name', type: 'string', operator: 'begins_with', value: 'meeting' }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when in operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'in', value: 'user,team' },
                  { field: 'toType', type: 'string', operator: 'in', value: 'user,team' },
                  { field: 'name', type: 'string', operator: 'in', value: 'meeting share rule,rule' },
                  { field: 'from', type: 'long', operator: 'in', value: [@share_rule.from_id] },
                  { field: 'to', type: 'long', operator: 'in', value: [@share_rule.to_id] },
                  { field: 'entity', type: 'long', operator: 'in', value: [@share_rule.meeting_id] },
                  { field: 'owner', type: 'long', operator: 'in', value: [@share_rule.created_by_id] },
                  { field: 'createdBy', type: 'long', operator: 'in', value: [@share_rule.created_by_id] },
                  { field: 'updatedBy', type: 'long', operator: 'in', value: [@share_rule.updated_by_id] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when not in operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'not_in', value: 'team' },
                  { field: 'toType', type: 'string', operator: 'not_in', value: 'team' },
                  { field: 'name', type: 'string', operator: 'not_in', value: 'share rule,rule' },
                  { field: 'from', type: 'long', operator: 'not_in', value: [@another_share_rule.from_id] },
                  { field: 'to', type: 'long', operator: 'not_in', value: [@another_share_rule.to_id] },
                  { field: 'entity', type: 'long', operator: 'not_in', value: [@another_share_rule.meeting_id] },
                  { field: 'owner', type: 'long', operator: 'not_in', value: [@another_share_rule.created_by_id] },
                  { field: 'createdBy', type: 'long', operator: 'not_in', value: [@another_share_rule.created_by_id] },
                  { field: 'updatedBy', type: 'long', operator: 'not_in', value: [@another_share_rule.updated_by_id] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when is empty/is null operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'is_empty', value: nil },
                  { field: 'toType', type: 'string', operator: 'is_empty', value: nil },
                  { field: 'name', type: 'string', operator: 'is_empty', value: nil },
                  { field: 'from', type: 'long', operator: 'is_null', value: nil },
                  { field: 'to', type: 'long', operator: 'is_null', value: nil },
                  { field: 'entity', type: 'long', operator: 'is_null', value: nil },
                  { field: 'owner', type: 'long', operator: 'is_null', value: nil },
                  { field: 'createdBy', type: 'long', operator: 'is_null', value: nil },
                  { field: 'updatedBy', type: 'long', operator: 'is_null', value: nil },
                  { field: 'createdAt', type: 'date', operator: 'is_null', value: nil },
                  { field: 'updatedAt', type: 'date', operator: 'is_null', value: nil }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.count).to eq(0)
          end
        end

        context 'when is not empty/is not null operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'fromType', type: 'string', operator: 'is_not_empty', value: nil },
                  { field: 'toType', type: 'string', operator: 'is_not_empty', value: nil },
                  { field: 'name', type: 'string', operator: 'is_not_empty', value: nil },
                  { field: 'from', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'to', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'entity', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'owner', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'createdBy', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'updatedBy', type: 'long', operator: 'is_not_null', value: nil },
                  { field: 'createdAt', type: 'date', operator: 'is_not_null', value: nil },
                  { field: 'updatedAt', type: 'date', operator: 'is_not_null', value: nil }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          before { @share_rule.update_column(:name, 'Meeting Share Rule') }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when greater operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'greater', value: (@share_rule.from_id - 1) },
                  { field: 'to', type: 'long', operator: 'greater', value: (@share_rule.to_id - 1) },
                  { field: 'entity', type: 'long', operator: 'greater', value: (@share_rule.meeting_id - 1) },
                  { field: 'owner', type: 'long', operator: 'greater', value: (@share_rule.created_by_id - 1) },
                  { field: 'createdBy', type: 'long', operator: 'greater', value: (@share_rule.created_by_id - 1) },
                  { field: 'updatedBy', type: 'long', operator: 'greater', value: (@share_rule.updated_by_id - 1) },
                  { field: 'createdAt', type: 'date', operator: 'greater', value: @share_rule.created_at.iso8601(3) },
                  { field: 'updatedAt', type: 'date', operator: 'greater', value: @share_rule.updated_at.iso8601(3) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when greater or equal to operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'greater_or_equal', value: @share_rule.from_id },
                  { field: 'to', type: 'long', operator: 'greater_or_equal', value: @share_rule.to_id },
                  { field: 'entity', type: 'long', operator: 'greater_or_equal', value: @share_rule.meeting_id },
                  { field: 'owner', type: 'long', operator: 'greater_or_equal', value: @share_rule.created_by_id },
                  { field: 'createdBy', type: 'long', operator: 'greater_or_equal', value: @share_rule.created_by_id },
                  { field: 'updatedBy', type: 'long', operator: 'greater_or_equal', value: @share_rule.updated_by_id },
                  { field: 'createdAt', type: 'date', operator: 'greater_or_equal', value: @share_rule.created_at.iso8601(3) },
                  { field: 'updatedAt', type: 'date', operator: 'greater_or_equal', value: @share_rule.updated_at.iso8601(3) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when less operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'less', value: (@share_rule.from_id + 1) },
                  { field: 'to', type: 'long', operator: 'less', value: (@share_rule.to_id + 1) },
                  { field: 'entity', type: 'long', operator: 'less', value: (@share_rule.meeting_id + 1) },
                  { field: 'owner', type: 'long', operator: 'less', value: (@share_rule.created_by_id + 1) },
                  { field: 'createdBy', type: 'long', operator: 'less', value: (@share_rule.created_by_id + 1) },
                  { field: 'updatedBy', type: 'long', operator: 'less', value: (@share_rule.updated_by_id + 1) },
                  { field: 'createdAt', type: 'date', operator: 'less', value: (@share_rule.created_at + 1.second).iso8601(3) },
                  { field: 'updatedAt', type: 'date', operator: 'less', value: (@share_rule.updated_at + 1.second).iso8601(3) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when less or equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'less_or_equal', value: @share_rule.from_id },
                  { field: 'to', type: 'long', operator: 'less_or_equal', value: @share_rule.to_id },
                  { field: 'entity', type: 'long', operator: 'less_or_equal', value: @share_rule.meeting_id },
                  { field: 'owner', type: 'long', operator: 'less_or_equal', value: @share_rule.created_by_id },
                  { field: 'createdBy', type: 'long', operator: 'less_or_equal', value: @share_rule.created_by_id },
                  { field: 'updatedBy', type: 'long', operator: 'less_or_equal', value: @share_rule.updated_by_id },
                  { field: 'createdAt', type: 'date', operator: 'less_or_equal', value: (@share_rule.created_at + 1.second).iso8601(3) },
                  { field: 'updatedAt', type: 'date', operator: 'less_or_equal', value: (@share_rule.updated_at + 1.second).iso8601(3) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when between operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'between', value: [@share_rule.from_id, (@share_rule.from_id + 1)] },
                  { field: 'to', type: 'long', operator: 'between', value: [@share_rule.to_id, (@share_rule.to_id + 1)] },
                  { field: 'entity', type: 'long', operator: 'between', value: [@share_rule.meeting_id, (@share_rule.meeting_id + 1)] },
                  { field: 'owner', type: 'long', operator: 'between', value: [@share_rule.created_by_id, (@share_rule.created_by_id + 1)] },
                  { field: 'createdBy', type: 'long', operator: 'between', value: [@share_rule.created_by_id, (@share_rule.created_by_id + 1)] },
                  { field: 'updatedBy', type: 'long', operator: 'between', value: [@share_rule.updated_by_id, (@share_rule.updated_by_id + 1)] },
                  { field: 'createdAt', type: 'date', operator: 'between', value: [@share_rule.created_at.iso8601(3), (@share_rule.created_at + 1.second).iso8601(3)] },
                  { field: 'updatedAt', type: 'date', operator: 'between', value: [@share_rule.updated_at.iso8601(3), (@share_rule.updated_at + 1.second).iso8601(3)] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end

        context 'when not between operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'from', type: 'long', operator: 'not_between', value: [(@share_rule.from_id + 1), (@share_rule.from_id + 2)] },
                  { field: 'to', type: 'long', operator: 'not_between', value: [(@share_rule.to_id + 1), (@share_rule.to_id + 2)] },
                  { field: 'entity', type: 'long', operator: 'not_between', value: [(@share_rule.meeting_id + 1), (@share_rule.meeting_id + 2)] },
                  { field: 'owner', type: 'long', operator: 'not_between', value: [(@share_rule.created_by_id + 1), (@share_rule.created_by_id + 2)] },
                  { field: 'createdBy', type: 'long', operator: 'not_between', value: [(@share_rule.created_by_id + 1), (@share_rule.created_by_id + 2)] },
                  { field: 'updatedBy', type: 'long', operator: 'not_between', value: [(@share_rule.updated_by_id + 1), (@share_rule.updated_by_id + 2)] },
                  { field: 'createdAt', type: 'date', operator: 'not_between', value: [(@share_rule.created_at + 1.second).iso8601(3), (@share_rule.created_at + 2.second).iso8601(3)] },
                  { field: 'updatedAt', type: 'date', operator: 'not_between', value: [(@share_rule.updated_at + 1.second).iso8601(3), (@share_rule.updated_at + 2.second).iso8601(3)] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns share rules' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@share_rule.id])
          end
        end
      end

      context 'sort' do
        context 'when sort params are present' do
          let(:filter_params) { { sort: 'createdAt,asc' } }

          it 'returns sorted response' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.to_sql).to include("ORDER BY created_at asc")
          end
        end

        context 'when sort params are not present' do
          it 'sorts by updated at desc' do
            search_response = described_class.new(user.tenant_id, user.id, {}, false).call

            expect(search_response.to_sql).to include("ORDER BY updated_at desc")
          end
        end
      end

      context 'pagination' do
        context 'when pagination params are present' do
          let(:filter_params) { { page: 2, size: 2 } }

          it 'returns paginated response as per params' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.current_page.to_i).to eq(2)
            expect(search_response.per_page).to eq(2)
          end
        end

        context 'when pagination params are present' do
          it 'returns page 1 and size 10' do
            search_response = described_class.new(user.tenant_id, user.id, {}, false).call

            expect(search_response.current_page.to_i).to eq(1)
            expect(search_response.per_page).to eq(10)
          end
        end
      end
    end

    context 'invalid' do
      context 'when invalid sort params' do
        let(:filter_params) { { sort: 'createdBy,desc' } }

        it 'raises error' do
          expect { described_class.new(user.tenant_id, user.id, filter_params, false).call }.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Invalid sortable field or order.')
        end
      end
    end
  end
end
