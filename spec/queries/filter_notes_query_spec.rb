# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FilterNotesQuery, type: :service do
  describe '#call' do
    let(:user) { create(:user) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:auth_data) { build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }

    context 'valid' do
      before do
        @meeting = create(:meeting, owner: user)
        @user_note = create(:note, created_by: user, meeting_id: @meeting.id, tenant_id: user.tenant_id)
        @another_user_note = create(:note, created_by: another_user, meeting_id: @meeting.id, tenant_id: user.tenant_id)
      end

      context 'when user has read all on notes' do
        let(:note_read_all) { true }

        it 'returns all notes for tenant' do
          search_response = described_class.new(user.tenant_id, user.id, {}, note_read_all).call

          expect(search_response.map(&:id)).to match_array([@user_note.id, @another_user_note.id])
        end
      end

      context 'when user does not have read all on notes' do
        let(:note_read_all) { false }

        it 'returns all notes created by user' do
          search_response = described_class.new(user.tenant_id, user.id, {}, note_read_all).call

          expect(search_response.map(&:id)).to match_array([@user_note.id])
        end
      end

      context 'filters' do
        context 'when equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                { field: 'createdBy', type: 'long', operator: 'equal', value: another_user.id },
              ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns note' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@another_user_note.id])
          end
        end

        context 'when not equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                { field: 'createdBy', type: 'long', operator: 'not_equal', value: another_user.id },
              ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@user_note.id])
          end
        end

        context 'when in operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdBy', type: 'long', operator: 'in', value: [another_user.id] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@another_user_note.id])
          end
        end

        context 'when not in operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdBy', type: 'long', operator: 'not_in', value: [another_user.id] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@user_note.id])
          end
        end

        context 'when is empty/is null operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'is_null', value: nil }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.count).to eq(0)
          end
        end

        context 'when is not empty/is not null operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'is_not_null', value: nil }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.map(&:id)).to match_array([@user_note.id])
          end
        end

        context 'when greater operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'greater', value: @user_note.created_at.iso8601(6) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@another_user_note.id])
          end
        end

        context 'when greater or equal to operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'greater_or_equal', value: @user_note.created_at.iso8601(6) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@user_note.id, @another_user_note.id])
          end
        end

        context 'when less operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'less', value: @another_user_note.created_at.iso8601(6) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@user_note.id])
          end
        end

        context 'when less or equal operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'less_or_equal', value: @user_note.created_at.iso8601(6) }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@user_note.id])
          end
        end

        context 'when between operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'between', value: [@user_note.created_at.iso8601(6), @another_user_note.created_at.iso8601(6)] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns notes' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([@user_note.id, @another_user_note.id])
          end
        end

        context 'when not between operator' do
          let(:filter_params) { ActionController::Parameters.new({
              jsonRule: {
                rules: [
                  { field: 'createdAt', type: 'date', operator: 'not_between', value: [@user_note.created_at.iso8601(6), @another_user_note.created_at.iso8601(6)] }
                ],
                condition: 'AND',
                valid: true
              }
          }).permit! }

          it 'returns blank' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, true).call

            expect(search_response.map(&:id)).to match_array([])
          end
        end
      end

      context 'sort' do
        context 'when sort params are present' do
          let(:filter_params) { { sort: 'createdAt,asc' } }

          it 'returns sorted response' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.to_sql).to include("ORDER BY created_at asc")
          end
        end

        context 'when sort params are not present' do
          it 'sorts by created at desc' do
            search_response = described_class.new(user.tenant_id, user.id, {}, false).call

            expect(search_response.to_sql).to include("ORDER BY created_at desc")
          end
        end
      end

      context 'pagination' do
        context 'when pagination params are present' do
          let(:filter_params) { { page: 2, size: 2 } }

          it 'returns paginated response as per params' do
            search_response = described_class.new(user.tenant_id, user.id, filter_params, false).call

            expect(search_response.current_page.to_i).to eq(2)
            expect(search_response.per_page).to eq(2)
          end
        end

        context 'when pagination params are present' do
          it 'returns page 1 and size 10' do
            search_response = described_class.new(user.tenant_id, user.id, {}, false).call

            expect(search_response.current_page.to_i).to eq(1)
            expect(search_response.per_page).to eq(10)
          end
        end
      end
    end

    context 'invalid' do
      context 'when invalid sort params' do
        let(:filter_params) { { sort: 'createdBy,desc' } }

        it 'raises error' do
          expect { described_class.new(user.tenant_id, user.id, filter_params, false).call }.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Invalid sortable field or order.')
        end
      end
    end
  end
end
