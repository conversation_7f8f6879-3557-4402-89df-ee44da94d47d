require 'rails_helper'

RSpec.describe FilterMeetingsQuery do
  let!(:user) { create(:user)}
  let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:headers) { valid_headers(valid_auth_token) }

  before do
    Thread.current[:token] = valid_auth_token
    create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true)
    [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
      stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
        .with(
          headers: {
            Authorization: "Bearer #{valid_auth_token}"
          }
        )
        .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
    end
  end

  describe '#call' do
    context "pagination params in filter_params" do
      before do
        participant = build(:user_look_up)
        participant.name = user.name
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        @meetings = create_list(:meeting, 3, tenant_id: user.tenant_id, owner: another_user)
        @meetings.each{|m| m.participants << participant }

        @meetings.each_with_index do |m, index|
          m.participants << participant
          m.created_at = (10 - index).days.ago
          m.from = (5 + index).hours.from_now
          m.to = (6 + index).hours.from_now
          m.save!
        end

        @command = FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!)
      end

      context "when pagination is applied" do
        let(:size)          { 2 }
        let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 2 }) }

        it "returns correct number of records" do
          expect(@command.result.size).to eq(size)
        end

        it "returns correct meetings" do
          meetings_ids = @command.result.map{|m| m['id']}

          expect(meetings_ids).to eq(@meetings.first(size).map(&:id))
        end
      end

      context "when pagiantion is not applied" do
        let(:default_size) { 10 }
        let(:filter_params) { ActionController::Parameters.new({ }) }

        it "returns correct number of records" do
          expect(@command.result.size).to eq(@meetings.count)
        end

        it "returns correct meetings" do
          meetings_ids = @command.result.map{|m| m['id']}

          expect(meetings_ids).to eq(@meetings.first(default_size).map(&:id))
        end
      end

      context "when pagination is skipped" do
        let(:size)          { 2 }
        let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 2 }) }

        before do
          @skip_pagination_result = FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!, { skip_pagination: true })
        end

        it "returns correct number of records" do
          expect(@skip_pagination_result.result.size).to eq(3)
        end

        it "returns correct meetings" do
          meetings_ids = @skip_pagination_result.result.map{|m| m['id']}

          expect(meetings_ids).to match_array(@meetings.map(&:id))
        end
      end
    end

    context "sorting params in filter_params" do
      let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 2, sort: sort_params }) }

      before do
        participant = build(:user_look_up)
        participant.name = user.name
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        @meetings = create_list(:meeting, 3, tenant_id: user.tenant_id, owner: another_user)
        @meetings.each{|m| m.participants << participant }

        @meetings.each_with_index do |m, index|
          m.participants << participant
          m.created_at = (10 - index).days.ago
          m.from = (5 + index).hours.from_now
          m.to = (6 + index).hours.from_now
          m.save!
        end

      end

      context "when sorting is applied" do
        let(:sort_params) { 'from,desc' }

        before { @command = FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!) }

        it "returns meetings in correct sequence" do
          meetings_ids = @command.result.map{|m| m['id']}

          expect(meetings_ids.first).to eq(@meetings[2].id)
          expect(meetings_ids.last).to eq(@meetings[1].id)
        end
      end

      context "when sorting is not applied" do
        let(:filter_params) { ActionController::Parameters.new({ }) }

        before { @command = FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!) }

        it "returns meetings in default sequence" do
          meetings_ids = @command.result.map{|m| m['id']}

          expect(meetings_ids.first).to eq(@meetings.first.id)
          expect(meetings_ids.last).to eq(@meetings.last.id)
        end
      end

      context "when sorting order is not passed" do
        let(:sort_params) { 'from' }

        it "raises error" do
          expect{
            FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!)
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid sortable field or order.")
        end
      end

      context "when sorting column is not passed" do
        let(:sort_params)   { ',desc' }

        it "raises error" do
          expect{
            FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!)
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid sortable field or order.")
        end
      end

      context "when invalid sorting column is passed" do
        let(:sort_params) { 'createdAt,desc' }

        it "raises error" do
          expect{
            FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!)
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid sortable field or order.")
        end
      end

      context "when sorting is skipped" do
        let(:filter_params) { ActionController::Parameters.new({ fields: ['id', 'title'], sort: "from,desc" }) }

        before do
          @command = FilterMeetingsQuery.call(user.tenant_id, user.id, filter_params.permit!, { skip_sorting: true })
        end

        it "returns meetings in default sequence" do
          meetings_ids = @command.result.map{|m| m['id']}
          expect(meetings_ids.first).to eq(@meetings.first.id)
          expect(meetings_ids.last).to eq(@meetings.last.id)
        end
      end
    end
  end
end
