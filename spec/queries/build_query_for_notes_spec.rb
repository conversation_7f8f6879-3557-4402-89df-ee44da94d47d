
# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BuildQueryForNotes, type: :service do
  describe '#call' do
    let(:user) { create(:user) }
    let(:scope) { Note.where(tenant_id: user.tenant_id) }

    context 'when date type' do
      context 'when greater operator' do
        let(:date_rule) { build(:json_rule_date_greater, field: 'created_at', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" > '2023-12-20 18:30:00'")
        end
      end

      context 'when less operator' do
        let(:date_rule) { build(:json_rule_date_less, field: 'created_at', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" < '2023-12-20 18:30:00'")
        end
      end

      context 'when less or equal operator' do
        let(:date_rule) { build(:json_rule_date_less_or_equal, field: 'created_at', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" <= '2023-12-20 18:30:00'")
        end
      end

      context 'when greater or equal operator' do
        let(:date_rule) { build(:json_rule_date_greater_or_equal, field: 'created_at', value: '2023-12-20T18:30:00Z') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" >= '2023-12-20 18:30:00'")
        end
      end

      context 'when between operator' do
        let(:date_rule) { build(:json_rule_date_between, field: 'created_at', value: ['2023-12-20T18:30:00Z', '2023-12-21T18:29:00Z']) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" BETWEEN '2023-12-20 18:30:00' AND '2023-12-21 18:29:00'")
        end
      end

      context 'when not between operator' do
        let(:date_rule) { build(:json_rule_date_not_between, field: 'created_at', value: ['2023-12-20T18:30:00Z', '2023-12-21T18:29:00Z']) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND (\"notes\".\"created_at\" < '2023-12-20 18:30:00' OR \"notes\".\"created_at\" > '2023-12-21 18:29:00')")
        end
      end

      context 'when is null operator' do
        let(:date_rule) { build(:json_rule_date_is_null, field: 'created_at', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" IS NULL")
        end
      end

      context 'when is not null operator' do
        let(:date_rule) { build(:json_rule_date_is_not_null, field: 'created_at', meeting_or_share_rule_model: 'ShareRule') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [date_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_at\" IS NOT NULL")
        end
      end
    end

    context 'when long type' do
      context 'when equal operator' do
        let(:long_rule) { build(:json_rule_long_equal, field: 'created_by', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" = 123")
        end
      end

      context 'when not equal operator' do
        let(:long_rule) { build(:json_rule_long_not_equal, field: 'created_by', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" != 123")
        end
      end

      context 'when is null operator' do
        let(:long_rule) { build(:json_rule_long_is_null, field: 'created_by', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" IS NULL")
        end
      end

      context 'when is not null operator' do
        let(:long_rule) { build(:json_rule_long_is_not_null, field: 'created_by', value: 123) }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" IS NOT NULL")
        end
      end

      context 'when in operator' do
        let(:long_rule) { build(:json_rule_long_in, field: 'created_by', value: '123,234') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" IN (123, 234)")
        end
      end

      context 'when not in operator' do
        let(:long_rule) { build(:json_rule_long_not_in, field: 'created_by', value: '123,234') }

        it 'builds query' do
          updated_scope = described_class.new(scope, [long_rule]).call

          expect(updated_scope.to_sql).to eq("SELECT \"notes\".* FROM \"notes\" WHERE \"notes\".\"tenant_id\" = #{user.tenant_id} AND \"notes\".\"created_by_id\" NOT IN (123, 234)")
        end
      end
    end
  end
end
