require 'rails_helper'

RSpec.describe ParseToken do
  describe '#call' do
    context 'valid input' do
      before do
        @token = build(:auth_token, user_id: 100, tenant_id: 300).token
      end

      it 'accepts valid token' do
        expect(ParseToken.call(@token).success?).to be true
      end
      it 'returns auth_data object' do
        expect(ParseToken.call(@token).result.class).to be Auth::Data
      end
      it 'returns valid auth_data object' do
        result = ParseToken.call(@token).result
        expect(result.user_id).to eq 100
        expect(result.username).to eq '<EMAIL>'
        expect(result.tenant_id).to eq 300
        expect(result.permissions.count).to be 5
        expect(result.permissions[0].name).to eq "lead"
        expect(result.permissions[0].id).to eq 4
        expect(result.permissions[0].action.read).to be true
        expect(result.permissions[0].action.update).to be true
        expect(result.permissions[0].action.email).to be false
      end
    end

    context 'invalid input' do
      it 'rejects any invalid token' do
        expect{ParseToken.call('testing invalid token')}.to raise_error(
          ExceptionHandler::Forbidden,
          "#{ErrorCode.unauthorized}||Unauthorized access."
        )
      end
    end
  end
end
