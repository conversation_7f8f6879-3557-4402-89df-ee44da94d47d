require 'rails_helper'

RSpec.describe TenantUsageChecker do
  describe '#call' do
    before do
      @tenant_id = 123
      @token = FactoryBot.build(:auth_token, tenant_id: @tenant_id).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when tenant_id is blank' do
      it 'returns true without making API call' do
        result = TenantUsageChecker.call(nil)
        expect(result).to be true
      end
    end

    context 'when security context is missing' do
      before do
        Thread.current[:token] = nil
      end

      it 'raises authentication error' do
        expect {
          TenantUsageChecker.call(@tenant_id)
        }.to raise_error(ExceptionHandler::AuthenticationError)
      end
    end

    context 'when usage API returns valid data' do
      context 'when usage limit is not reached' do
        before do
          usage_response = {
            "records" => {
              "used" => 50,
              "total" => 100
            }
          }
          
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: usage_response.to_json, headers: {})
        end

        it 'returns true' do
          result = TenantUsageChecker.call(@tenant_id)
          expect(result).to be true
        end
      end

      context 'when usage limit is reached' do
        before do
          usage_response = {
            "records" => {
              "used" => 100,
              "total" => 100
            }
          }
          
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: usage_response.to_json, headers: {})
        end

        it 'raises usage limit exceeded error' do
          expect {
            TenantUsageChecker.call(@tenant_id)
          }.to raise_error(ExceptionHandler::UsageLimitExceeded)
        end
      end

      context 'when usage limit is exceeded' do
        before do
          usage_response = {
            "records" => {
              "used" => 150,
              "total" => 100
            }
          }
          
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: usage_response.to_json, headers: {})
        end

        it 'raises usage limit exceeded error' do
          expect {
            TenantUsageChecker.call(@tenant_id)
          }.to raise_error(ExceptionHandler::UsageLimitExceeded)
        end
      end

      context 'when total is unlimited (-1)' do
        before do
          usage_response = {
            "records" => {
              "used" => 1000,
              "total" => -1
            }
          }
          
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: usage_response.to_json, headers: {})
        end

        it 'returns true' do
          result = TenantUsageChecker.call(@tenant_id)
          expect(result).to be true
        end
      end

      context 'when total is zero (unlimited)' do
        before do
          usage_response = {
            "records" => {
              "used" => 1000,
              "total" => 0
            }
          }
          
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: usage_response.to_json, headers: {})
        end

        it 'returns true' do
          result = TenantUsageChecker.call(@tenant_id)
          expect(result).to be true
        end
      end
    end

    context 'when usage API returns error responses' do
      context 'when API returns 401 unauthorized' do
        before do
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 401, body: '', headers: {})
        end

        it 'raises authentication error' do
          expect {
            TenantUsageChecker.call(@tenant_id)
          }.to raise_error(ExceptionHandler::AuthenticationError)
        end
      end

      context 'when API returns 404 not found' do
        before do
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 404, body: '', headers: {})
        end

        it 'raises not found error' do
          expect {
            TenantUsageChecker.call(@tenant_id)
          }.to raise_error(ExceptionHandler::NotFound)
        end
      end

      context 'when API returns 500 internal server error' do
        before do
          stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 500, body: '', headers: {})
        end

        it 'raises internal server error' do
          expect {
            TenantUsageChecker.call(@tenant_id)
          }.to raise_error(ExceptionHandler::InternalServerError)
        end
      end
    end
  end
end
