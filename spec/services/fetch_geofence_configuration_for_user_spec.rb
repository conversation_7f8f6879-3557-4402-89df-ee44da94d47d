# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchGeofenceConfigurationForUser do
  before do
    @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
  end

  describe '#call' do
    context 'when the request returns a 500 error' do
      before do
        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
          headers: { Authorization: "Bearer #{@token}" }
        ).to_return(status: 500)
      end

      it 'raises an internal server error' do
        expect do
          described_class.call
        end.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
      end
    end

    context 'when the request returns a 404 error' do
      before do
        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
          headers: { Authorization: "Bearer #{@token}" }
        ).to_return(status: 404)
      end

      it 'returns nil' do
        expect(described_class.call.result).to eq(nil)
      end
    end

    context 'when the request raises a standard error' do
      before do
        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
          headers: { Authorization: "Bearer #{@token}" }
        ).to_raise(StandardError.new('Some error'))
      end

      it 'raises an invalid data error' do
        expect do
          described_class.call
        end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end

    context 'when the request returns 200' do
      before do
        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
          headers: { Authorization: "Bearer #{@token}" }
        ).to_return(status: 200, body: { meetingCheckInCheckOut: { radius: 500, restrictCheckIn: true }}.to_json)
      end

      it 'returns users geofence configuration' do
          expect(described_class.call.result).to eq({ "meetingCheckInCheckOut" => { "radius" => 500, "restrictCheckIn" => true }})
      end
    end
  end
end
