
require 'rails_helper'

RSpec.describe SearchExternal do
  describe "#call" do
    before do
      @tenant_id = 10
    end

    let!(:external_look_up) { create(:look_up, entity: "external_11", tenant_id: @tenant_id, email: '<EMAIL>')}
    context "with valid input " do

      before do
        @result = SearchExternal.call(['<EMAIL>', '<EMAIL>'], @tenant_id).result
      end

      it "return external matched records" do
        expect(@result[:matched]).to eq [{email: external_look_up.email, entity: external_look_up.entity, name: external_look_up.name, tenant_id: @tenant_id}]
      end

      it 'returns external unmacted records' do
        expect(@result[:unmatched]).to eq [{ :email=>"<EMAIL>", :entity=>"external", :tenant_id=>10, :name=> "<EMAIL>" }]
      end
    end
  end
end
