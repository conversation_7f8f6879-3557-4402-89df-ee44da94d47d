require 'rails_helper'

RSpec.describe GetNotes do
  describe '#call' do
    before do
      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)
      auth_data = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data

      @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user)
      @note = create(:note, created_by_id: @user.id, meeting_id: @meeting.id)
      participant = build(:user_look_up)
      participant.name = @user.name
      participant.tenant_id = @user.tenant_id
      participant.entity = "user_#{@user.id}"
      @meeting.participants << participant
    end

    context 'valid input' do
      it "returns notes in array" do
        notes = GetNotes.call(@meeting.id, {}).result
        expect(notes.count).to eq(1)
        expect(notes.first.id).to eq(@note.id)
      end
    end

    context 'invalid input' do
      context "when user is not invitee on owner for the meeting" do
        context 'but user has read all access' do
          before do
            @meeting.update(participants: [])
            auth_data = build(:auth_data, :note_with_read_all_true, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data
          end

          it "returns correct note" do
            notes = GetNotes.call(@meeting.id, {}).result
            expect(notes.count).to eq(1)
            expect(notes.first.id).to eq(@note.id)
            expect(notes.first.meeting_id).to eq(@note.meeting_id)
          end
        end
      end

      context "when user does not have read access on the notes" do
        before do
          auth_data = build(:auth_data, :note_without_read, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          @meeting.update(owner: @another_user)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        it "returns correct error" do
          expect { GetNotes.call(@meeting.id, {}) }.to raise_error(ExceptionHandler::NoteAccessNotAllowed, "#{ErrorCode.note_access_not_allowed}||Do not have access to notes.")
        end
      end

      context 'when note belongs to other tenant' do
        before do
          auth_data = build(:auth_data, :note_with_read_all_true, user_id: @user.id, tenant_id: 111)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        it 'returns correct error' do
          expect { GetNotes.call(@meeting.id, {}) }.to raise_error(ActiveRecord::RecordNotFound, "#{ErrorCode.not_found}||Meeting not found.")
        end
      end
    end

    context 'when user is owner and not in invitees and user does not have read all on notes' do
      before do
        @meeting.update(owner: @user, participants: [])
        create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)
        auth_data = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
      end

      it 'returns notes of the meeting' do
        notes = GetNotes.call(@meeting.id, {}).result
        expect(notes.count).to eq(2)
      end
    end
  end
end
