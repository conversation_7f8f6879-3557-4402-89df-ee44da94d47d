# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BulkCheckout do
  let(:params) {
    {
      "latitude": 18.581730,
      "longitude": 73.760761,
      "time": "2023-12-22T09:59:06.409Z",
      "meetingIds": [123, 324]
    }
  }
  let(:user) { create(:user) }

  before do
    @auth_data = build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
    @token = build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token
  end

  describe '#call' do
    context 'when user context is missing' do
      before { Thread.current[:auth] = nil }

      it 'raises error' do
        expect{
          BulkCheckout.new(ActionController::Parameters.new(params)).call
        }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end

    context 'when meetings are present' do
      before do
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token

        @meeting_with_other_owner = create(:meeting, tenant_id: user.tenant_id, title: 'Meeting with other owner')
        @meeting_with_participant = create(:meeting, tenant_id: user.tenant_id, owner: create(:user, tenant_id: user.tenant_id), title: 'Meeting with user as participant')
        participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: user.id, name: user.name)
        @meeting_with_participant.participants << participant
        @meeting_with_participant.save!
        create(:meeting_checked_in, meeting: @meeting_with_participant, user_id: user.id)
        @meeting_without_attendance = create(:meeting, tenant_id: user.tenant_id, owner: user, title: 'Meeting without checkin')
        @already_checkedout_meeting = create(:meeting, tenant_id: user.tenant_id, owner: user, title: 'Checkedout Meeting')
        create(:meeting_attendance, meeting: @already_checkedout_meeting)
        @previously_checked_in_meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        create(:meeting_checked_in, meeting: @previously_checked_in_meeting)

        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
      with(
        headers: {
          'Authorization'=>'Bearer '+ @token
        }).
        to_return(status: 200, body: {}.to_json, headers: {})
      end

      context 'when parameters are missing' do
        it 'raises invalid data error' do
          expect{
            BulkCheckout.new(ActionController::Parameters.new({})).call
          }.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Parameters are missing')
        end
      end

      it 'checks out for valid meetings and adds error for others' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(2).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedOut)).exactly(1).times

        response = BulkCheckout.new(ActionController::Parameters.new(
          {
            'latitude': 18.581730,
            'longitude': 73.760761,
            'time': '2023-12-22T09:59:06.409Z',
            'meetingIds': [
              404,
              @meeting_with_other_owner.id,
              @meeting_without_attendance.id,
              @already_checkedout_meeting.id,
              @previously_checked_in_meeting.id,
              @meeting_with_participant.id
            ]
          }
        )).call


        expect(response[:successful_meetings]).to match_array([
          {
            id: @previously_checked_in_meeting.id,
            name: @previously_checked_in_meeting.title
          },
          {
            id: @meeting_with_participant.id,
            name: 'Meeting with user as participant'
          }
        ])

        expect(response[:invalid_meetings]).to match_array([
          {
            id: 404,
            name: nil,
            message: 'Meeting not found.'
          },
          {
            id: @meeting_with_other_owner.id,
            name: 'Meeting with other owner',
            message: 'you do not have access to checkout this meeting'
          },
          {
            id: @meeting_without_attendance.id,
            name: 'Meeting without checkin',
            message: 'Cannot checkout without checking in.'
          },
          {
            id: @already_checkedout_meeting.id,
            name: 'Checkedout Meeting',
            message: 'Invalid meeting action.'
          }
        ])
      end
    end
  end
end
