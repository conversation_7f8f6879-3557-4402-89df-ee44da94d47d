# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe CalenderDisconnectedPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic(MEETING_EXCHANGE)
      params = {
        tenantId: 123,
        userId: 234,
        disconnectedBy: 'AUTO',
        userEmail: '<EMAIL>',
        tenantEmail: '<EMAIL>'
      }
      @event = Event::CalenderDisconnected.new(params)
    end

    context 'When publisher is called' do
      it 'raises event with proper data' do
        @queue = @channel.queue('meeting.calendar.disconnected')
        @queue.bind(@exchange, routing_key: 'meeting.calendar.disconnected')
        allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)

        CalenderDisconnectedPublisher.call(123, 234, 'AUTO', '<EMAIL>', '<EMAIL>')
        expect(@queue.message_count).to eq(1)
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq(
          {
            'tenantId' => 123,
            'userId' => 234,
            'disconnectedBy' => 'AUTO',
            'userEmail' => '<EMAIL>',
            'tenantEmail' => '<EMAIL>'
          }
        )
      end
    end
  end
end
