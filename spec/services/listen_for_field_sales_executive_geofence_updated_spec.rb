# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForFieldSalesExecutiveGeofenceUpdated do
  describe '#call' do
    before do
      @user = create(:user)
    end

    context 'valid input' do
      context 'for executive geofence update event' do
        let(:payload) {
          {
            performedBy: 1,
            tenantId: @user.tenant_id,
            executive: {
              id: @user.id,
              name: '<PERSON>'
            },
            entity: {
              meetingCheckInCheckOut: {
                restrictCheckIn: true,
                radius: 500
              }
            },
            oldEntity: {
              meetingCheckInCheckOut: {
                restrictCheckIn: true,
                radius: 1500
              }
            }
          }.to_json
        }

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(FIELD_SALES_EXCHANGE, 
                  FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_EVENT, 
                  FIELD_SALES_EXECUTIVE_GEOFENCE_UPDATED_QUEUE)
            .and_yield(payload.to_s)
        end

        context "When event is consumed for executive geofence update" do
          it "updates user geofence information" do
            @user.update(geofence_config: { fieldSalesEnabled: true })
            ListenForFieldSalesExecutiveGeofenceUpdated.call
            expect(@user.reload.geofence_config).to eq({
              'fieldSalesEnabled' => true,
              'meetingCheckInCheckOut' => { 'restrictCheckIn' => true, 'radius' => 500 }
            })
          end
        end
      end
    end
  end
end
