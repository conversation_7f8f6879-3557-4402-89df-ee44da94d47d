require 'rails_helper'

RSpec.describe BuildIcsForMeeting do
  describe '#call' do
    before do
      @user = create(:user)
      @meeting = create(:meeting, all_day:false, owner: @user)
      @participant = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
      @meeting.participants << @participant
      @command = BuildIcsForMeeting.call(@meeting)
    end
  
    it 'is successful' do
      expect(@command.success?).to be true
    end
    it 'returns valid data ' do
      event = Icalendar::Event.parse(@command.result).first
      expect(event.summary).to be == @meeting.title
      expect(event.description).to be == @meeting.description
      expect(event.organizer.to).to be == @meeting.organizer.email
      expect(event.location).to be == @meeting.location
      expect(event.status).to be == "CONFIRMED"
      expect(event.last_modified).to eq(@meeting.updated_at.utc.strftime("%Y%m%dT%H%M%SZ"))
      expect(event.uid).to eq(@meeting.public_id)
      if @meeting.all_day
        expect(event.dtstart).to be == @meeting.from.to_date
        expect(event.dtend).to be == (@meeting.from + 1.day).to_date
      else
        expect(event.dtstart.time.to_i).to be == @meeting.from.to_i
        expect(event.dtend.time.to_i).to be == @meeting.to.to_i
      end
    end
  end
end
