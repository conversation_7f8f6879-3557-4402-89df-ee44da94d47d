# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchUserIdsByProperty do
  before do
    @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
  end

  describe '#call' do
    let(:rule) do
      {
        "operator": 'equal',
        "id": 'createdByFields',
        "field": 'createdByFields',
        "type": 'long',
        "value": 123,
        "primaryField": 'user',
        "property": 'teams'
      }.with_indifferent_access
    end

    let(:payload) do
      {
        "fields": [],
        "jsonRule": {
          "id": 'teams',
          "field": 'teams',
          "type": 'long',
          "input": nil,
          "operator": 'equal',
          "value": 123,
          "data": nil,
          "property": nil,
          "primaryField": nil,
          "condition": nil,
          "not": nil,
          "rules": nil,
          "group": false
        }
      }.with_indifferent_access
    end

    context 'when json rule is invalid' do
      before do
        updated_payload = payload
        updated_payload['jsonRule']['id'] = 'invalid_field'
        updated_payload['jsonRule']['field'] = 'invalid_field'

        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
          headers: { Authorization: "Bearer #{@token}" },
          body: updated_payload.to_json
        ).to_return(status: 400)
      end

      it 'returns array of user ids' do
        updated_rule = rule
        updated_rule['property'] = 'invalid_field'

        expect do
          described_class.call(JsonRule.new(updated_rule))
        end.to raise_error(ExceptionHandler::InvalidDataError, '01503001')
      end
    end

    context 'when json rule is valid' do
      before do
        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
          headers: { Authorization: "Bearer #{@token}" },
          body: payload.to_json
        ).to_return(status: 200, body: [89].to_json)
      end

      it 'returns array of user ids' do
        command = described_class.call(JsonRule.new(rule))
        expect(command.result).to eq([89])
      end
    end
  end
end
