require 'rails_helper'
require 'bunny-mock'

RSpec.describe MeetingsScheduledInMinutesPublisher do
  describe '#call' do
    before do
      connection = BunnyMock.new
      channel = connection.start.channel
      exchange = channel.topic MEETING_EXCHANGE
      @queue = channel.queue 'q.meeting.scheduled.inMinutes.notification'
      @queue.bind exchange, routing_key: 'meeting.scheduled.inMinutes'
      allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
    end

    context 'when called' do
      before do
        @user1 = create(:user, id:99, tenant_id: 10, name: '<PERSON><PERSON>')
        @user2 = create(:user, id:98, tenant_id: 11, name: '<PERSON>')
        @from_time = DateTime.parse('2023-04-17T05:38:42.955Z')

        @meeting1 = create(:meeting, id: 7_494_080_484, title: 'Meeting 1', owner: @user1, from: @from_time, tenant_id: @user1.tenant_id, organizer_email: 'jane<PERSON><EMAIL>', organizer_name: '<PERSON><PERSON>')
        @meeting1.participants << create(:look_up, email: '<EMAIL>',  entity: 'contact_123', id: 123, tenant_id: @meeting1.tenant_id, name: '<PERSON>')

        @meeting2 = create(:meeting, id: 7_494_080_485, title: 'Meeting 2', owner: @user2, from: @from_time, tenant_id: @user2.tenant_id)
        @meeting2.participants << create(:look_up, email: '<EMAIL>', entity: 'contact_125', id: 125, tenant_id: @meeting2.tenant_id, name: 'John Contact')

        MeetingsScheduledInMinutesPublisher.call([@meeting1, @meeting2]).result
      end

      it 'should publish event marketplace.app.disabled with payload' do
        expect(@queue.message_count).to eq(1)
        meeting_scheduled_event_hash = @queue.all.find { |q| q[:options][:routing_key] == 'meeting.scheduled.inMinutes' }
        expect(JSON(meeting_scheduled_event_hash[:message])).to eq(JSON.parse(file_fixture('meetings_scheduled_in_minutes_event.json').read))
      end
    end
  end
end
