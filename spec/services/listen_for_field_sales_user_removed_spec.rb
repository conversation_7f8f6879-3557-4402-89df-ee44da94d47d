# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForFieldSalesUserRemoved do
  describe '#call' do
    let(:user) { create(:user) }
    let(:user2) { create(:user, tenant_id: user.tenant_id) }
    let(:payload) do
      {
        performedBy: 1,
        userIds: [user.id, 1000, user2.id],
        tenantId: user.tenant_id
      }.to_json
    end

    it 'updates users geofence_config with fieldSalesEnabled false' do
      expect(RabbitmqConnection).to receive(:subscribe)
        .with(FIELD_SALES_EXCHANGE, FIELD_SALES_USER_REMOVED_EVENT, FIELD_SALES_USER_REMOVED_QUEUE)
        .and_yield(payload)

      user.update(geofence_config: { fieldSalesEnabled: true, meetingCheckInCheckOut: { radius: 300, restictCheckIn: true } })
      described_class.call

      expect(user.reload.geofence_config).to eq({
        "fieldSalesEnabled"=>false,
        "meetingCheckInCheckOut"=>{"radius"=>300, "restictCheckIn"=>true}
      })
      expect(user2.reload.geofence_config['fieldSalesEnabled']).to be false
    end
  end
end
