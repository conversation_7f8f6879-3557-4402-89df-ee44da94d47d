# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateConnectedAccount do
  describe '#call' do
    before do
      allow(Calendar::Google::WatchEvent).to receive(:call).and_return({ resource_id: 1234 })
      @user = create(:user)
      expires_at = (Time.now + 1.hour).to_s
      @data = {
        "providerName": 'GOOGLE',
        "accessToken": 'AAAA',
        "expiresAt": expires_at,
        "email": '<EMAIL>',
        "firstName": '<PERSON>',
        "lastName": 'Stark',
        "calendarId": '<EMAIL>',
        "syncType": 'KYLAS_TO_CALENDAR'
      }.with_indifferent_access
    end

    context 'valid input' do
      context 'Existing user' do
        before do
          CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
        end

        context '#connected_account' do
          it 'adds new connected account' do
            expect(ConnectedAccount.count).to eq(1)
          end

          it 'checks for data' do
            acct = ConnectedAccount.last
            expect(acct.tenant_id).to eq(@user.tenant_id)
            expect(acct.expires_at).to eq((DateTime.now + 50.minutes).to_i)
            expect(acct.access_token).to eq('AAAA')
            expect(acct.active).to eq(true)
            expect(acct.calendar_id).to eq(@data[:calendarId])
            expect(acct.sync_type).to eq({ 'calendar_to_kylas' => false, 'kylas_to_calendar' => true })
          end
        end
      end

      context 'New user' do
        before(:each) do
          User.destroy_all
          CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
        end

        it 'adds new user' do
          expect(User.count).to eq 1
        end

        it 'verify new user\'s data' do
          user = User.last
          expect(user.name).to eq(@user.name)
          expect(user.id).to eq(@user.id)
          expect(user.tenant_id).to eq(@user.tenant_id)
        end
      end

      context 'sync_type' do
        context 'when sync type is calendar to kylas' do
          it 'does store correct sync type in connected account' do
            expect(SubscribeToWebhook).to receive(:call)
            @data[:syncType] = 'CALENDAR_TO_KYLAS'
            CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
            acct = ConnectedAccount.last
            expect(acct.tenant_id).to eq(@user.tenant_id)
            expect(acct.expires_at).to eq((DateTime.now + 50.minutes).to_i)
            expect(acct.access_token).to eq('AAAA')
            expect(acct.active).to eq(true)
            expect(acct.calendar_id).to eq(@data[:calendarId])
            expect(acct.sync_type).to eq({ 'calendar_to_kylas' => true, 'kylas_to_calendar' => false })
          end
        end

        context 'when sync type is two way' do
          it 'does store correct sync type in connected account' do
            expect(SubscribeToWebhook).to receive(:call)
            @data[:syncType] = 'TWO_WAY'
            CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
            acct = ConnectedAccount.last
            expect(acct.tenant_id).to eq(@user.tenant_id)
            expect(acct.expires_at).to eq((DateTime.now + 50.minutes).to_i)
            expect(acct.access_token).to eq('AAAA')
            expect(acct.active).to eq(true)
            expect(acct.calendar_id).to eq(@data[:calendarId])
            expect(acct.sync_type).to eq({ 'calendar_to_kylas' => true, 'kylas_to_calendar' => true })
          end
        end

        context 'when sync type is not valid' do
          it 'does not store sync type in connected account and raise invalid data error' do
            @data[:syncType] = 'RANDOM'
            expect do
              CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid sync type.")
          end
        end
      end
    end
  end
end
