require 'rails_helper'

RSpec.describe GetFields do
  describe '#call' do
    context 'success' do
      before(:all) do
        @user = create(:user)
        auth_data = build(:auth_data, :note_with_delete_true, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
      end

      context 'if none of the fields is tenant_id' do
        before do
          @fields = create_list(:field, 3, tenant_id: @user.tenant_id, is_standard: false, created_by: @user, updated_by: @user)
          @fields.last(1).each do |f|
            f.is_standard = true
            f.internal_name = f.internal_name.gsub('cf_', '')
            f.save
          end
          @data = GetFields.call(params).result
        end

        context 'when custom-only parameter is passed' do
          let(:params) { ActionController::Parameters.new({ 'custom-only': 'true' }).permit! }

          it 'return fields list correctly' do
            expect(@data.map(&:id)).to eq(@fields.map(&:id).first(2))
          end
        end

        context 'when custom-only parameter is not passed' do
          let(:params) { ActionController::Parameters.new({ 'custom-only': 'false' }).permit! }

          it 'return fields list correctly' do
            expect(@data.map(&:id)).to eq(@fields.map(&:id))
          end
        end
      end

      context 'when one of the fields is tenant_id' do
        before do
          @fields = create_list(:field, 1, tenant_id: @user.tenant_id, is_standard: true, internal_name: 'tenant_id', created_by: @user, updated_by: @user)
          @data = GetFields.call(params).result
        end

        let(:params) { ActionController::Parameters.new({ 'custom-only': 'true' }).permit! }

        it 'should not return tenant_id field' do
          expect(@data.map(&:id)).to eq([])
        end
      end
    end
  end
end
