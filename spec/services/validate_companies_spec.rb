require 'rails_helper'

RSpec.describe ValidateCompanies do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @company = build(:company_look_up, entity_id: 5257, tenant_id: 10)
      @another_company = build(:company_look_up, entity_id: 6555, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'valid request' do
      before do
        stub_request(:get, "#{SERVICE_SEARCH}/v1/summaries/companies?view=meeting&id=#{@company.entity_id},#{@another_company.entity_id}").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: [{ id: 6555, name: 'All Data', ownerId: 123 }, { id: 5257, name: 'Test Company', ownerId: 234 }].to_json, headers: {})
      end

      it 'should return updated company lookups' do
        updated_company_lookups = described_class.call([@company, @another_company]).result

        updated_company = updated_company_lookups.first
        expect(updated_company.entity_id).to eq(@company.entity_id)
        expect(updated_company.name).to eq('Test Company')
        expect(updated_company.entity).to eq("company_#{@company.entity_id}")
        expect(updated_company.owner_id).to eq(234)

        updated_another_company = updated_company_lookups.last
        expect(updated_another_company.entity_id).to eq(@another_company.entity_id)
        expect(updated_another_company.name).to eq('All Data')
        expect(updated_another_company.entity).to eq("company_#{@another_company.entity_id}")
        expect(updated_another_company.owner_id).to eq(123)
      end
    end

    context 'invalid request' do
      context 'with invalid user' do
        before { Thread.current[:auth] = nil }
        it 'should raise Authentication error with unauthorised error code' do
          expect{
            described_class.call([@company, @another_company]).result
          }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
        after{ Thread.current[:auth] = @auth_data }
      end

      context 'with invalid token' do
        before { Thread.current[:token] = nil }
        it 'should raise Authentication error with unauthorised error code' do
          expect{
            described_class.call([@company, @another_company]).result
          }.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
        after{ Thread.current[:token] = @token }
      end

      context 'with missing or invalid company' do
        before do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/summaries/companies?view=meeting&id=#{@company.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: [{"id": 6555, "name": "All Data"}, {"id": 5257, "name": "Test Company"}].to_json, headers: {})
        end
        it 'should raise invalid data error' do
          expect{
            described_class.call([@company]).result
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid company.")
        end
      end
    end
  end
end
