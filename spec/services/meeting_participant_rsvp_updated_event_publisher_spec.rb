require 'rails_helper'

RSpec.describe MeetingParticipantRsvpUpdatedEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      @another_user = create(:user)
      allow(PublishEvent).to receive(:call)
      @meeting = create(:meeting, owner: @user)
      lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      @participant_user_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name)
      @meeting.participants << [@participant_user_lookup, lead_lookup]
      @meeting.save
    end

    context "for meeting create with participants" do
      it 'should call PublishEvent for each participants with each user' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).once
        MeetingParticipantRsvpUpdatedEventPublisher.call(@meeting, @participant_user_lookup)
      end
    end
  end
end
