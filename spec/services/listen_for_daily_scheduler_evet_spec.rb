# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'
require 'sidekiq/testing'

RSpec.describe ListenForDailySchedulerEvent do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SCHEDULER_EXCHANGE
      OutlookChangeLog.create(resource_id: 'r1', change_key: 'c1')
      OutlookChangeLog.create(resource_id: 'r1', change_key: 'c2')
    end

    context 'valid input' do
      let(:payload) { {}.to_json }

      before do
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: MEETING_SCHEDULER_EVENT
        allow(RabbitmqConnection).to receive(:subscribe).with(SCHEDULER_EXCHANGE, MEETING_SCHEDULER_EVENT, MEETING_SCHEDULER_QUEUE).and_yield(payload.to_s)
      end

      context "when listening to meeting missed event" do
        context 'for missed events' do
          before do
            start_time = (DateTime.now.utc - 2.day)
            meetings = build_list(:meeting, 25, from: start_time, to: (start_time + 1.hour))
            meetings.each { |meeting| meeting.save(validate: false) }

            start_time +=  1.day
            meetings = build_list(:meeting, 25, from: start_time, to: (start_time + 1.hour))
            meetings.each { |meeting| meeting.save(validate: false) }

            start_time += 1.day + 1.hour
            meetings = create_list(:meeting, 25, from: start_time, to: (start_time + 1.hour))
          end

          it "should mark all past scheduled meetings in the last 24 hours to missed" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(25).times
            ListenForDailySchedulerEvent.call()

            expect(Meeting.pluck(:status).count(SCHEDULED)).to eq(50)
            expect(Meeting.pluck(:status).count(MISSED)).to eq(25)
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].lt(DateTime.now.utc)).count).to eq(50)
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].lt(DateTime.now.utc)).pluck(:status).uniq).to match_array([SCHEDULED, MISSED])
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].gt(DateTime.now.utc)).count).to eq(25)
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].gt(DateTime.now.utc)).pluck(:status).uniq).to match_array([SCHEDULED])

            end_time = DateTime.now.utc
            start_time = (end_time - 24.hours)
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].between(start_time..end_time)).count).to eq(25)
            expect(Meeting.where(Meeting.arel_table['to'.to_sym].between(start_time..end_time)).pluck(:status).uniq).to match_array([MISSED])
          end
        end

        context 'for account renewal' do
          it 'calls microsoft account' do
            expect(Calendar::RenewAccounts).to receive(:call).exactly(1).times
            ListenForDailySchedulerEvent.call()
          end
        end

        it 'deletes all OutlookChangeLogs' do
          expect(OutlookChangeLog.count).to eq(2)
          ListenForDailySchedulerEvent.call
          expect(OutlookChangeLog.count).to eq(0)
        end

        context 'for notifications' do
          before do
            Sidekiq::Worker.clear_all
            ActiveJob::Base.queue_adapter = :test
            create(:meeting, from: DateTime.now - 2.days)
            create(:meeting, from: DateTime.now + 1.hour)
            create(:meeting, from: DateTime.now + 2.days)
          end

          it 'only schedules jobs for meetings from current 03:00 AM to next 03:00 AM' do
            expect do
              ListenForDailySchedulerEvent.call()
            end.to have_enqueued_job.on_queue('reminder_queue').exactly(1).times
          end
        end
      end
    end
  end
end
