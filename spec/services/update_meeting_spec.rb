require 'rails_helper'
require 'bunny-mock'
require 'sidekiq/testing'

RSpec.describe UpdateMeeting do
  describe "#call" do

    def meeting_params(params)
      params.permit(
        :id,
        :title,
        :description,
        :from,
        :to,
        :allDay,
        :medium,
        :providerLink,
        :location,
        :status,
        {
          customFieldValues: {},
        },
        {
          timezone: [
            :id,
            :name
          ]
        },
        {
          participants: [
            :entity,
            :id,
            :name,
            :email
          ]
        },
        {
          relatedTo: [
            :entity,
            :id,
            :name,
            :email
          ]
        },
        {
          checkedInDetails: [
            :latitude,
            :longitude
          ]
        },
        {
          checkedOutDetails: [
            :latitude,
            :longitude
          ]
        }
      )
    end

    before do
      connection = BunnyMock.new
      channel = connection.start.channel
      exchange = channel.topic MEETING_EXCHANGE

      queue = channel.queue "meeting.update.deal.metaInfo"
      queue.bind exchange, routing_key: "meeting.update.deal.metaInfo"
      allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(queue)
    end

    before(:all) { LookUp.destroy_all }

    context 'for online meeting' do
      context 'when organizer email is user email' do
        it 'updates all fields' do
          calendar_base = instance_double(Calendar::Base)
          @user = create(:user)
          auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
          @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
          thread[:token] = @token
          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
          }).to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

          organizer_look_up = create(
            :user_look_up,
            email: '<EMAIL>',
            tenant_id: @user.tenant_id,
            entity: "user_#{@user.id}"
          )
          create(
            :connected_account,
            provider_name: MICROSOFT_TEAMS_PROVIDER,
            active: true,
            user_id: @user.id,
            tenant_id: @user.tenant_id,
            sync_type: { "calendar_to_kylas"=>true, "kylas_to_calendar"=>true }
          )
          @meeting = create(
            :meeting,
            owner: @user,
            medium: MICROSOFT_TEAMS_PROVIDER,
            organizer: organizer_look_up,
            provider_meeting_id: 'ms-meeting-id'
          )
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            organizer_look_up
          ])
          allow(Calendar::Base).to receive(:call).and_return(calendar_base)
          allow(calendar_base).to receive(:success?).and_return(true)
          allow(calendar_base).to receive(:result).and_return(
            { provider_link: 'https://teams-link', provider_meeting_id: 'fej8f0ateki87e6lj7d7sj8g' }
          )
          @params = @meeting.as_json
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params[:timezone] = { id: @timezone.entity_id, name: @timezone.name }

          @params['title'] = 'Updated Title'
          @params['description'] = 'Updated Description'
          UpdateMeeting.call(@params)

          expect(@meeting.reload.title).to eq('Updated Title')
          expect(@meeting.description).to eq('Updated Description')
        end
      end

      context 'when owner is current user and organizer is other user' do
        it 'updates all fields' do
          calendar_base = instance_double(Calendar::Base)
          @user = create(:user)
          @another_user = create(:user, tenant_id: @user.tenant_id)
          @third_user = create(:user, tenant_id: @user.tenant_id)
          auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
          @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
          thread[:token] = @token
          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
          }).to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

          organizer_look_up = create(
            :user_look_up,
            email: '<EMAIL>',
            tenant_id: @user.tenant_id,
            entity: "user_#{@another_user.id}"
          )

          owner_look_up = create(
            :user_look_up,
            email: '<EMAIL>',
            tenant_id: @user.tenant_id,
            entity: "user_#{@user.id}"
          )
          third_user_lookup = create(
            :user_look_up,
            email: '<EMAIL>',
            tenant_id: @user.tenant_id,
            entity: "user_#{@third_user.id}"
          )

          create(
            :connected_account,
            provider_name: MICROSOFT_TEAMS_PROVIDER,
            active: true,
            user_id: @another_user.id,
            tenant_id: @another_user.tenant_id,
            sync_type: { "calendar_to_kylas"=>true, "kylas_to_calendar"=>true }
          )
          @meeting = create(
            :meeting,
            owner: @user,
            medium: MICROSOFT_TEAMS_PROVIDER,
            organizer: organizer_look_up,
            provider_meeting_id: 'ms-meeting-id'
          )
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            organizer_look_up, third_user_lookup
          ])
          allow(Calendar::Base).to receive(:call).and_return(calendar_base)
          allow(calendar_base).to receive(:success?).and_return(true)
          allow(calendar_base).to receive(:result).and_return(
            { provider_link: 'https://teams-link', provider_meeting_id: 'fej8f0ateki87e6lj7d7sj8g' }
          )
          @params = @meeting.as_json
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params[:timezone] = { id: @timezone.entity_id, name: @timezone.name }

          @params['title'] = 'Updated Title'
          @params['description'] = 'Updated Description'
          @params['participants'] = [{id: @third_user.id, entity: 'user', name: 'third user', email: '<EMAIL>'}]
          UpdateMeeting.call(@params)

          expect(@meeting.reload.title).to eq('Updated Title')
          expect(@meeting.description).to eq('Updated Description')
        end
      end

      context "when organizer email is user's connected account email" do
        it 'updates all fields' do
          calendar_base = instance_double(Calendar::Base)
          @user = create(:user)
          auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
          @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
          thread[:token] = @token

          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
          }).to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

          organizer_look_up = create(
            :external_look_up,
            email: '<EMAIL>',
            tenant_id: @user.tenant_id,
            entity: "external_12"
          )
          create(
            :connected_account,
            provider_name: MICROSOFT_TEAMS_PROVIDER,
            active: true,
            user_id: @user.id,
            tenant_id: @user.tenant_id,
            sync_type: { "calendar_to_kylas"=>true, "kylas_to_calendar"=>true },
            email: '<EMAIL>'
          )
          @meeting = create(
            :meeting,
            owner: @user,
            medium: MICROSOFT_TEAMS_PROVIDER,
            organizer: organizer_look_up,
            provider_meeting_id: 'ms-meeting-id'
          )
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            organizer_look_up
          ])
          allow(Calendar::Base).to receive(:call).and_return(calendar_base)
          allow(calendar_base).to receive(:success?).and_return(true)
          allow(calendar_base).to receive(:result).and_return(
            { provider_link: 'https://teams-link', provider_meeting_id: 'fej8f0ateki87e6lj7d7sj8g' }
          )
          @params = @meeting.as_json
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params[:timezone] = { id: @timezone.entity_id, name: @timezone.name }

          @params['title'] = 'Updated Title'
          @params['description'] = 'Updated Description'
          UpdateMeeting.call(@params)

          expect(@meeting.reload.title).to eq('Updated Title')
          expect(@meeting.description).to eq('Updated Description')
        end
      end

      context 'when organizer email does not match with current user and connected account' do
        context 'and user have update all permission' do
          it 'only updates custom fields' do
            calendar_base = instance_double(Calendar::Base)
            @user = create(:user)
            @another_user = create(:user, tenant_id: @user.tenant_id)
            auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data
            @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
            thread[:token] = @token
            organizer_look_up = create(
              :external_look_up,
              email: '<EMAIL>',
              tenant_id: @user.tenant_id,
              entity: "external_12"
            )

            create(
              :look_up,
              email: "<EMAIL>",
              tenant_id: @user.tenant_id,
              entity: "user_#{@user.id}"
            )

            create(
              :connected_account,
              provider_name: MICROSOFT_TEAMS_PROVIDER,
              active: true,
              user_id: @user.id,
              tenant_id: @user.tenant_id,
              sync_type: { "calendar_to_kylas"=>true, "kylas_to_calendar"=>true },
              email: '<EMAIL>'
            )
            @meeting = create(
              :meeting,
              title: 'Previous Title',
              description: 'Previous Description',
              owner: @another_user,
              medium: MICROSOFT_TEAMS_PROVIDER,
              organizer: organizer_look_up,
              provider_meeting_id: 'ms-meeting-id',
              custom_field_values: { 'cfTextField' => 'This is custom field', 'cfNumberField' => 123 }
            )
            create(:custom_field, field_type: 'TEXT_FIELD', display_name: 'Text Field', tenant_id: @user.tenant_id)
            create(:custom_field, field_type: 'NUMBER', display_name: 'Number Field', tenant_id: @user.tenant_id)
            allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
              organizer_look_up
            ])
            allow(Calendar::Base).to receive(:call).and_return(calendar_base)
            allow(calendar_base).to receive(:success?).and_return(true)
            allow(calendar_base).to receive(:result).and_return(
              { provider_link: 'https://teams-link', provider_meeting_id: 'fej8f0ateki87e6lj7d7sj8g' }
            )
            @params = @meeting.as_json
            @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
            @params[:timezone] = { id: @timezone.entity_id, name: @timezone.name }

            @params['title'] = 'Updated Title'
            @params['description'] = 'Updated Description'
            @params['customFieldValues'] = { 'cfTextField' => 'This is updated field', 'cfNumberField' => 456 }
            UpdateMeeting.call(meeting_params(ActionController::Parameters.new(@params)))

            expect(@meeting.reload.title).to eq('Previous Title')
            expect(@meeting.description).to eq('Previous Description')
            expect(@meeting.custom_field_values).to eq(
              {"cfTextField"=>"This is updated field", "cfNumberField"=>456}
            )
          end
        end
      end
    end

    context 'when meeting is rescheduled' do
      before do
        Sidekiq::Worker.clear_all
        ActiveJob::Base.queue_adapter = :test
        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        @user = create(:user)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        thread[:token] = @token
        @meeting = create(:meeting, owner: @user)
        user_lookup = @meeting.organizer
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          user_lookup
        ])
        @params = @meeting.as_json
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        @params[:timezone] = {id: @timezone.entity_id, name: @timezone.name}
        @params[:participants] = [
          {
            id: @meeting.organizer.entity_id,
            name: @meeting.organizer.name,
            entity: @meeting.organizer.entity_type,
            email: @meeting.organizer.email
          }
        ]
      end

      context 'and from time is before next 3am' do
        it 'should enqueue job for notification' do
          @params['from'] = '01:00 AM'.to_time
          expect do
            UpdateMeeting.call(@params.with_indifferent_access)
          end.to have_enqueued_job.on_queue('reminder_queue').exactly(1).times
        end
      end

      context 'and from time is after next 3am' do
        it 'should not enqueue job for notification' do
          @params['from'] = '04:00 AM'.to_time + 1.day
          @params['to'] = '04:15 AM'.to_time + 1.day

          expect do
            UpdateMeeting.call(@params.with_indifferent_access)
          end.not_to have_enqueued_job.on_queue('reminder_queue')
        end
      end
    end

    context "when owner try to update meeting" do

      before do
        @user = create(:user)
        @another_user = create(:user, tenant_id: @user.tenant_id)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        thread[:token] = @token
        @meeting = create(:meeting, owner: @user)
        user_lookup = @meeting.organizer
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          user_lookup,
          build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: 'Another owner user')
        ])
        @params = @meeting.as_json
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        @params[:timezone] = {id: @timezone.entity_id, name: @timezone.name}
        @params[:owner] = { id: @another_user.id, name: @another_user.name }
        allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
      end

      context "without participants" do
        it 'should update it' do
          @params[:title] = 'New title'
          @params[:to] = (DateTime.parse(@params['from']) + 2.hours).to_s
          @params[:participants] = [
            {
              id: @meeting.organizer.entity_id,
              name: @meeting.organizer.name,
              entity: 'user',
              email: @meeting.organizer.email
            }
          ]
          command = UpdateMeeting.call(@params.with_indifferent_access)
          expect( command.success? ).to be true
          updated_meeting = command.result
          expect(updated_meeting.title).to eq('New title')
          expect(updated_meeting.to).to eq((updated_meeting.from + 2.hours).to_s)
          expect(updated_meeting.updated_by.id).to eq(@user.id)
          expect(updated_meeting.updated_at).to be_between(updated_meeting.created_at, Time.now)
          expect(updated_meeting.status).to eq(SCHEDULED)
          expect(updated_meeting.conducted_at).to eq(nil)
          expect(updated_meeting.owner).to eq(@another_user)
        end
      end

      context "with participants" do
        let (:participant_user) {}
        before do
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:lead_look_up, entity_id: 20, tenant_id: @user.tenant_id, name: 'Jane')
          ])
          user_lookup = build(:user_look_up, entity_id: 24, tenant_id: @user.tenant_id, name: 'John', email: '<EMAIL>')
          logged_in_user_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            logged_in_user_lookup, user_lookup
          ])
        end

        xit 'should update it' do
          @params[:title] = 'New title'
          @params[:to] = '2020-10-24T14:19:08.182Z'
          @params[:participants] = [
            {
              "entity": LOOKUP_USER,
              "id": 24,
              "name": "John",
              "email": '<EMAIL>'
            },
            {
              'entity': LOOKUP_EXTERNAL,
              'email': '<EMAIL>'
            },
            {
              "entity": LOOKUP_LEAD,
              "id": 20,
              "name": "Jane"
            }
          ]
          command = UpdateMeeting.call(@params.with_indifferent_access)
          expect( command.success? ).to be true
          update_meeting_response = command.result
          updated_meeting = update_meeting_response[:meeting]
          expect(updated_meeting.title).to eq('New title')
          expect(updated_meeting.to).to eq('2020-10-24T14:19:08.182Z')
          expect(updated_meeting.participants.count).to eq(3)
          expect(updated_meeting.participants.first.name).to eq(@user.name)
          expect(updated_meeting.participants.second.name).to eq('John')
          expect(updated_meeting.participants.last.name).to eq('Jane')
          expect(update_meeting_response[:added_participants].count).to eq(4)
          expect(update_meeting_response[:removed_participants].count).to eq(0)
        end
      end

      context 'with related to' do
        before do
          @params[:related_to] = [{id: 9, entity: LOOKUP_COMPANY, name: "Test Company"}]
          @params[:participants] = [{id: @meeting.organizer.entity_id, name: @meeting.organizer.name, entity: 'user'}]
          allow(ValidateCompanies).to receive_message_chain(:call, :result).and_return([
            build(:company_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Updated Test Company")
          ])
        end

        context 'when adding new entity' do
          it 'should add related to entity lookup' do
            command = UpdateMeeting.call(@params.with_indifferent_access)
            expect( command.success? ).to be true
            meeting_related_to = command.result.related_to
            expect(meeting_related_to.count).to be(1)
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.name).to eq('Updated Test Company')
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.entity).to eq('company_9')
          end
        end

        context 'when removing and adding entities' do
          before do
            company_look_up = build(:company_look_up, entity_id: 10, name: 'Some other company')
            @meeting = Meeting.find(@params['id'])
            @meeting.related_to << company_look_up
          end

          it 'should remove old entity and add new related to entity lookup' do
            meeting_related_to = @meeting.related_to
            expect(meeting_related_to.count).to be(1)
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.name).to eq('Some other company')
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.entity).to eq('company_10')

            command = UpdateMeeting.call(@params.with_indifferent_access)
            expect( command.success? ).to be true
            meeting_related_to = command.result.related_to
            expect(meeting_related_to.count).to be(1)
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.name).to eq('Updated Test Company')
            expect(meeting_related_to.find { |lookup| lookup.is_a_company? }.entity).to eq('company_9')
          end
        end
      end
    end

    context "when user is not owner or don't have update access of meeting" do
      before do
        @user = create(:user)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name, permissions: [Auth::Permission.new(permission)])
        thread = Thread.current
        thread[:auth] = auth_data

        @another_user = create(:user, tenant_id: @user.tenant_id)
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name)
        ])
        meeting = create(:meeting, owner: @another_user)
        @meeting_organizer = meeting.organizer
        @meeting_organizer_entity_id = @meeting_organizer.entity_id
        @params = meeting.as_json.with_indifferent_access
        @timezone = create(:timezone_look_up, tenant_id: @another_user.tenant_id)
        @params[:timezone] = {id: @timezone.entity_id, name: @timezone.name}

        @params[:participants] = [
          {
            id: @another_user.id,
            entity: 'user',
            name: @another_user.name,
            email: '<EMAIL>'
          },
          {
            id: @meeting_organizer_entity_id,
            entity: 'user',
            name: @meeting_organizer.name,
            email: @meeting_organizer.email
          }
        ]
      end

      context 'when user does not have update all permission' do
        let(:permission) {
          {
            "id" => 7,
            "name" =>  "meeting",
            "description" =>  "has access to team resource",
            "limits" =>  -1,
            "units" =>  "count",
            "action" =>  {
              "read" => true,
              "write" => true,
              "update" => true,
              "delete" => false,
              "email" => false,
              "call" => false,
              "sms" => false,
              "task" => false,
              "note" => false,
              "readAll" => true,
              "updateAll" => false,
              "deleteAll" => false
            }
          }
        }

        it 'should raise unauthorized error' do
          @params[:title] = 'Some New Title'
          expect{UpdateMeeting.call(@params.with_indifferent_access)}.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to update meeting.")
        end
      end

      context 'when user does have update all permission' do
        let(:permission) {
          {
            "id" => 7,
            "name" =>  "meeting",
            "description" =>  "has access to team resource",
            "limits" =>  -1,
            "units" =>  "count",
            "action" =>  {
              "read" => true,
              "write" => true,
              "update" => true,
              "delete" => false,
              "email" => false,
              "call" => false,
              "sms" => false,
              "task" => false,
              "note" => false,
              "readAll" => true,
              "updateAll" => true,
              "deleteAll" => false
            }
          }
        }

        it 'should update meeting' do
          @params[:title] = 'Some New Title'
          %i(created_at updated_at from to).each { |f| @params.delete(f) }
          updated_meeting = UpdateMeeting.call(@params.with_indifferent_access).result
          expect(updated_meeting.title).to eq('Some New Title')
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.time_zone_id).to eq(@timezone.id)
        end

        context 'and removes meeting organizer from participants' do
          it 'should raise invalid error' do
            @params[:title] = 'Some New Title'
            @params[:participants] = @params[:participants].reject do |participant|
              participant[:id] == @meeting_organizer_entity_id
            end

            expect do
              UpdateMeeting.call(@params.with_indifferent_access).result
            end.to raise_error(
              ExceptionHandler::InvalidDataError,
              "#{ErrorCode.invalid}||Organizer is not a participant."
            )
          end
        end
      end
    end

    context 'when updating meeting status' do
      before do
        @user = create(:user)
        @meeting = create(:meeting, owner: @user)
        @updated_params = @meeting.as_json.with_indifferent_access
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          @meeting.organizer
        ])
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        @updated_params[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
        @meeting_organizer = @meeting.organizer
        @meeting_organizer_entity_id = @meeting_organizer.entity_id
      end

      context 'from scheduled to conducted' do
        before do
          @updated_params['status'] = CONDUCTED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should conduct meeting' do
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CONDUCTED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_at.present?).to be(true)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.missed?).to eq(false)
        end
      end

      context 'from scheduled to cancelled' do
        before do
          @updated_params['status'] = CANCELLED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should cancel meeting' do
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CANCELLED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(@user.id)
          expect(updated_meeting.cancelled_at.present?).to be(true)
          expect(updated_meeting.missed?).to eq(false)
        end

        context 'when meeting provider is MICROSOFT' do
          before do
            @meeting = create(:meeting, owner: @user, medium: MICROSOFT_TEAMS_PROVIDER)
            @meeting_organizer = @meeting.organizer
            @meeting_organizer_entity_id = @meeting_organizer.entity_id
            @meeting.provider_link = 'https://teams.microsoft.com/l/meetup-join/19%3ameeting'
            @meeting.provider_meeting_id = 'AAMkAGNhMWE2'
            @updated_params = @meeting.as_json.with_indifferent_access
            @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
            @updated_params[:timezone] = {id: @timezone.entity_id, name: @timezone.name}
            @updated_params['status'] = CANCELLED
            @updated_params['participants'] = [
              {
                id: @user.id,
                name: @user.name,
                entity: 'user'
              },
              {
                id: @meeting_organizer_entity_id,
                name: @meeting_organizer.name,
                entity: 'user',
                email: @meeting_organizer.email
              }
            ]
            @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                            provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: Faker::Internet.email)
          end

          def stub_cancel_microsoft_meeting_request(status, response_body)
            headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
            stub_request(:post, "https://graph.microsoft.com/v1.0/me/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@meeting.provider_meeting_id)}/cancel")
              .with(headers: headers)
              .to_return(status: status, body: response_body)
          end

          it 'does cancel meeting at MICROSOFT side' do
            ConnectedAccount.last.update(email: @meeting.organizer.email)
            expect(Calendar::Base).to receive(:call).and_call_original
            expect(Calendar::Microsoft::CancelEvent).to receive(:call).and_call_original
            stub_cancel_microsoft_meeting_request(202, {}.to_json)
            updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
            expect(updated_meeting.status).to eq(CANCELLED)
            expect(updated_meeting.provider_link).to eq('https://teams.microsoft.com/l/meetup-join/19%3ameeting')
            expect(updated_meeting.provider_meeting_id).to eq('AAMkAGNhMWE2')
          end
        end
      end

      context 'from scheduled to missed' do
        before do
          lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "John Lead")
          @meeting.related_to << lead_lookup
          @updated_params['from'] = @meeting.from - 1.month
          @updated_params['to'] = @meeting.to - 1.month
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @updated_params['relatedTo'] = [
            {
              id: lead_lookup.entity_id,
              name: lead_lookup.name,
              entity: 'lead'
            }
          ]
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            lead_lookup
          ])
        end

        it 'should mark meeting as missed' do
          expect(@meeting.status).to eq(SCHEDULED)
          expect(@meeting.missed?).to eq(false)

          expect(EntityMetadataPublisher).to receive(:call).once
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(MISSED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be < Time.now
          expect(updated_meeting.to).to be < Time.now
          expect(updated_meeting.missed?).to be(true)
        end
      end

      context 'from scheduled to scheduled' do
        before do
          @updated_params['from'] = @meeting.from + 1.day
          @updated_params['to'] = @meeting.to + 1.day
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should mark meeting as scheduled' do
          expect(@meeting.status).to eq(SCHEDULED)
          expect(@meeting.missed?).to eq(false)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(SCHEDULED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be > Time.now
          expect(updated_meeting.to).to be > Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from missed to scheduled' do
        before do
          @meeting.update(from: (@meeting.from - 1.month), to: (@meeting.to - 1.month), status: MISSED)
          @updated_params['from'] = @meeting.from + 2.month
          @updated_params['to'] = @meeting.to + 2.month
          @updated_params['status'] = MISSED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should mark meeting as scheduled' do
          expect(@meeting.status).to eq(MISSED)
          expect(@meeting.missed?).to eq(true)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(SCHEDULED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be > Time.now
          expect(updated_meeting.to).to be > Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from missed to missed' do
        before do
          @meeting.update(from: (@meeting.from - 1.month), to: (@meeting.to - 1.month), status: MISSED)
          @updated_params['from'] = @meeting.from - 2.month
          @updated_params['to'] = @meeting.to - 2.month
          @updated_params['status'] = SCHEDULED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should mark meeting as missed' do
          expect(@meeting.status).to eq(MISSED)
          expect(@meeting.missed?).to eq(true)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(MISSED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be < Time.now
          expect(updated_meeting.to).to be < Time.now
          expect(updated_meeting.missed?).to be(true)
        end
      end

      context 'from missed to conducted' do
        before do
          @meeting.update(from: (@meeting.from - 1.month), to: (@meeting.to - 1.month), status: MISSED)
          @updated_params['from'] = @meeting.from - 2.month
          @updated_params['to'] = @meeting.to - 2.month
          @updated_params['status'] = CONDUCTED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should mark meeting as conducted' do
          expect(@meeting.status).to eq(MISSED)
          expect(@meeting.missed?).to eq(true)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CONDUCTED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_at.present?).to be(true)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be < Time.now
          expect(updated_meeting.to).to be < Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from missed to cancelled' do
        before do
          @meeting.update(from: (@meeting.from - 1.month), to: (@meeting.to - 1.month + 1.seconds), status: MISSED)
          @updated_params['from'] = @meeting.from + 2.month
          @updated_params['to'] = @meeting.to + 2.month + 1.seconds
          @updated_params['status'] = CANCELLED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
        end

        it 'should mark meeting as cancelled' do
          expect(@meeting.status).to eq(MISSED)
          expect(@meeting.missed?).to eq(true)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CANCELLED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(@user.id)
          expect(updated_meeting.cancelled_at.present?).to be(true)
          expect(updated_meeting.from).to be > Time.now
          expect(updated_meeting.to).to be > Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from conducted to scheduled' do
        before do
          @meeting.update(conducted_at: DateTime.now.utc, conducted_by: @user, status: CONDUCTED)
          @updated_params = @meeting.as_json.with_indifferent_access
          @updated_params['from'] = @meeting.from + 1.month
          @updated_params['to'] = @meeting.to + 1.month + 1.seconds
          @updated_params['status'] = SCHEDULED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        end

        it 'should mark meeting as scheduled' do
          expect(@meeting.status).to eq(CONDUCTED)
          expect(@meeting.missed?).to eq(false)
          expect(@meeting.conducted_at.present?).to eq(true)
          expect(@meeting.conducted_by_id).to be(@user.id)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(SCHEDULED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be > Time.now
          expect(updated_meeting.to).to be > Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from conducted to cancelled' do
        before do
          @meeting.update(conducted_at: DateTime.now.utc, conducted_by: @user, status: CONDUCTED)
          @updated_params = @meeting.as_json.with_indifferent_access
          @updated_params['from'] = @meeting.from + 1.month
          @updated_params['to'] = @meeting.to + 1.month + 1.hour
          @updated_params['status'] = CANCELLED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        end

        it 'should mark meeting as cancelled' do
          expect(@meeting.status).to eq(CONDUCTED)
          expect(@meeting.missed?).to eq(false)
          expect(@meeting.conducted_at.present?).to eq(true)
          expect(@meeting.conducted_by_id).to be(@user.id)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CANCELLED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(@user.id)
          expect(updated_meeting.cancelled_at.present?).to be(true)
          expect(updated_meeting.from).to be > Time.now
          expect(updated_meeting.to).to be > Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from conducted to missed' do
        before do
          @meeting.update(conducted_at: DateTime.now.utc, conducted_by: @user, status: CONDUCTED)
          @updated_params = @meeting.as_json.with_indifferent_access
          @updated_params['from'] = @meeting.from - 1.month
          @updated_params['to'] = @meeting.to - 1.month
          @updated_params['status'] = SCHEDULED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        end

        it 'should mark meeting as missed' do
          expect(@meeting.status).to eq(CONDUCTED)
          expect(@meeting.missed?).to eq(false)
          expect(@meeting.conducted_at.present?).to eq(true)
          expect(@meeting.conducted_by_id).to be(@user.id)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(MISSED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(nil)
          expect(updated_meeting.conducted_at.present?).to be(false)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be < Time.now
          expect(updated_meeting.to).to be < Time.now
          expect(updated_meeting.missed?).to be(true)
        end
      end

      context 'from conducted to conducted' do
        before do
          @meeting.update(conducted_at: DateTime.now.utc, conducted_by: @user, status: CONDUCTED)
          @updated_params = @meeting.as_json.with_indifferent_access.slice!(:conducted_at, :conducted_by_id)
          @updated_params['from'] = @meeting.from - 1.month
          @updated_params['to'] = @meeting.to - 1.month
          @updated_params['status'] = CONDUCTED
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        end

        it 'should mark meeting as conducted' do
          expect(@meeting.status).to eq(CONDUCTED)
          expect(@meeting.missed?).to eq(false)
          expect(@meeting.conducted_at.present?).to eq(true)
          expect(@meeting.conducted_by_id).to be(@user.id)
          updated_meeting = UpdateMeeting.call(@updated_params.with_indifferent_access).result
          expect(updated_meeting.status).to eq(CONDUCTED)
          expect(updated_meeting.owner_id).to eq(@user.id)
          expect(updated_meeting.updated_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_by_id).to eq(@user.id)
          expect(updated_meeting.conducted_at).to eq(@meeting.conducted_at)
          expect(updated_meeting.cancelled_by_id).to eq(nil)
          expect(updated_meeting.cancelled_at.present?).to be(false)
          expect(updated_meeting.from).to be < Time.now
          expect(updated_meeting.to).to be < Time.now
          expect(updated_meeting.missed?).to be(false)
        end
      end

      context 'from cancelled to scheduled' do
        before do
          expect(ParticipantAddedEventPublisher).not_to receive(:call)
          expect(MeetingScheduledEventPublisher).not_to receive(:call)
          expect(MeetingReScheduledEventPublisher).not_to receive(:call)
          expect(ParticipantRemovedEventPublisher).not_to receive(:call)
          expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
          @meeting.update(status: CANCELLED, medium: GOOGLE_PROVIDER)
          @updated_params['participants'] = [
            {
              id: @user.id,
              name: @user.name,
              entity: 'user'
            },
            {
              id: @meeting_organizer_entity_id,
              name: @meeting_organizer.name,
              entity: 'user',
              email: @meeting_organizer.email
            }
          ]
          @updated_params['status'] = SCHEDULED
        end

        it 'does not allow to update meeting and does not invoke call method of calendar base class' do
          expect(Calendar::Base).not_to receive(:call)
          expect do
            UpdateMeeting.call(@updated_params.with_indifferent_access)
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting medium.")
        end
      end
    end

    context 'when user try to update meeting which do not exists' do
      before do
        @user = create(:user)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        @params = {}
      end

      it 'should throw not found error' do
        @params[:id] = 'not_existing_id'
        expect{
          UpdateMeeting.call(@params.with_indifferent_access)
        }.to raise_error( ExceptionHandler::NotFound )

      end
    end

    context 'when user try to update meeting which already have participants' do
      before do
        @user = create(:user, id: 101)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        @meeting = create(:meeting, owner: @user)
        lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "John Lead")
        owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
        @meeting.participants << @meeting.organizer
        @meeting.participants << owner_lookup
        @meeting.participants << lead_lookup
        @meeting.related_to << lead_lookup
        @meeting.save
      end
      context "trying to add new participants " do
        before do
          @new_lead = build(:lead_look_up,entity_id: 13, tenant_id: @user.tenant_id, name: "New Lead")

          @params = @meeting.as_json
          %w(from to).each { |attr| @params.delete(attr) }
          new_lead = @new_lead.as_json.with_indifferent_access
          new_lead[:id] = 13
          new_lead[:entity] = 'lead'
          @params[:participants] = participants_to_json @meeting.participants.as_json
          @params[:participants] = @params[:participants].union(participants_to_json [new_lead])
          @params[:participants] << {id: @meeting.owner_id, name: @meeting.owner.name, entity: 'user', email: '<EMAIL>'}
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
          @another_user = @meeting.participants.find{|p| p.is_a_user?}
          @another_lead = @meeting.participants.find{|p| p.is_a_lead?}

          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:lead_look_up, entity_id: @another_lead.id, tenant_id: @user.tenant_id, name: "John Lead"),
            @new_lead
          ])
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            @meeting.organizer,
            build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: "John user")
          ])
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
        end
        it 'should add it' do
          expect(@meeting.participants.count).to eq(4)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(2).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
          command = UpdateMeeting.call(@params.with_indifferent_access)
          expect( command.success? ).to be true
          updated_meeting = command.result
          expect(updated_meeting.participants.count).to eq(4)
          expect(updated_meeting.participant_look_ups.count).to eq(4)
          expect(updated_meeting.participants.map(&:entity)).to match_array(["user_#{@another_user.id}", "lead_#{@another_lead.id}", "lead_13", "user_#{@user.id}"])
        end
      end
      context "trying to remove old participants " do
        before do
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            @meeting.organizer
          ])
        end
        it 'should remove it' do
          expect(@meeting.participants.count).to eq(4)
          @params = @meeting.as_json
          @params[:participants] = participants_to_json @meeting.participants.as_json
          @params[:participants].pop
          @params[:participants] << {id: @meeting.owner_id, name: @meeting.owner.name, entity: 'user', email: '<EMAIL>'}
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
          command = UpdateMeeting.call(@params.with_indifferent_access)
          expect( command.success? ).to be true
          updated_meeting = command.result
          expect(updated_meeting.participants.count).to eq(1)
          expect(updated_meeting.participant_look_ups.count).to eq(1)
          expect(updated_meeting.participants.find { |p| p.entity == 'lead_11' }).to eq(nil)
        end
      end
      context "trying to add new and remove old participants " do
        before do
          @new_lead = build(:lead_look_up,entity_id: 13, tenant_id: @user.tenant_id, name: "New Lead")
          @params = @meeting.as_json
          %w(from to).each { |attr| @params.delete(attr) }
          new_lead = @new_lead.as_json.with_indifferent_access
          new_lead[:id] = 13
          @params[:participants] = participants_to_json @meeting.participants.as_json
          @params[:participants].pop
          @params[:participants] = @params[:participants].union(participants_to_json [new_lead])
          @params[:participants] << {id: @meeting.owner_id, name: @meeting.owner.name, entity: 'user', email: '<EMAIL>'}
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            @new_lead
          ])
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            @meeting.organizer
          ])
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
        end

        it 'should add new and remove old' do
          expect(@meeting.participants.count).to eq(4)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

          command = UpdateMeeting.call(@params.with_indifferent_access)
          expect( command.success? ).to be true
          updated_meeting = command.result
          expect(updated_meeting.participants.count).to eq(2)
          # Old Through relation is deleted
          expect(updated_meeting.participant_look_ups.count).to eq(2)
          participant = updated_meeting.participants.find_by(entity: 'lead_13')
          expect(participant.entity_id).to eq(13)
          expect(updated_meeting.organizer.entity_id).to eq(@user.id)
          # Added entity
          expect(updated_meeting.participants.map(&:entity)).to include('lead_13')
          # Removed entity
          expect(updated_meeting.participants.map(&:entity).exclude?('lead_11')).to be(true)
        end
      end

      context "trying to add new and remove old related_to entities" do
        before do
          @new_lead = build(:lead_look_up,entity_id: 13, tenant_id: @user.tenant_id, name: "New Lead")
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            @new_lead
          ])
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            @meeting.organizer
          ])
        end
        it 'should add new and remove old' do
          expect(@meeting.related_to.count).to eq(1)

          @params = @meeting.as_json
          new_lead = @new_lead.as_json.with_indifferent_access
          new_lead[:id] = 13
          @params[:participants] = [
            {
              id: @meeting.owner_id,
              name: @meeting.owner.name,
              entity: 'user',
              email: '<EMAIL>'
            },
            {
              id: @meeting.organizer.entity_id,
              name: @meeting.organizer.name,
              entity: 'user',
              email: @meeting.organizer.email
            }
          ]
          @params[:related_to] = participants_to_json @meeting.related_to.as_json
          @params[:related_to].pop
          @params[:related_to] = @params[:related_to].union(participants_to_json [new_lead])
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
          command = UpdateMeeting.call(@params.with_indifferent_access)

          expect( command.success? ).to be true
          updated_meeting = command.result
          expect(updated_meeting.related_to.count).to eq(1)
          expect(updated_meeting.related_to.first.entity).to eq('lead_13')
        end
      end

      context 'when meeting medium is present' do
        let(:calendar_base) { instance_double(Calendar::Base) }

        before do
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return(
            [@meeting.organizer]
          )
          @params = @meeting.as_json
          @params[:participants] = [
            {
              id: @meeting.owner_id,
              name: @meeting.owner.name,
              entity: 'user',
              email: '<EMAIL>'
            },
            {
              id: @meeting.organizer.entity_id,
              name: @meeting.organizer.name,
              entity: 'user',
              email: @meeting.organizer.email
            }
          ]
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        end

        context 'when meeting medium is offline' do
          it 'does not invoke call method of base calendar class' do
            expect(Calendar::Base).not_to receive(:call)
            command = UpdateMeeting.call(@params.with_indifferent_access)
            expect(command).to be_success
          end
        end

        context 'when meeting medium is google' do
          before do
            @new_lead = build(:lead_look_up,entity_id: 13, tenant_id: @user.tenant_id, name: "New Lead")
            allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([@new_lead])
            expect(ParticipantAddedEventPublisher).not_to receive(:call)
            expect(MeetingScheduledEventPublisher).not_to receive(:call)
            expect(MeetingReScheduledEventPublisher).not_to receive(:call)
            expect(ParticipantRemovedEventPublisher).not_to receive(:call)
            expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
            # one added participant & 1 removed participant
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
          end

          it 'does invoke call method of base calendar class' do
            @meeting.update(medium: GOOGLE_PROVIDER)
            @params[:medium] = GOOGLE_PROVIDER
            new_lead = @new_lead.as_json.with_indifferent_access
            new_lead[:id] = 13
            @params[:relatedTo] = participants_to_json([new_lead])
            allow(Calendar::Base).to receive(:call).and_return(calendar_base)
            allow(calendar_base).to receive(:success?).and_return(true)
            allow(calendar_base).to receive(:result).and_return(
              { provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7d7sj8g' }
            )
            command = UpdateMeeting.call(@params.with_indifferent_access)
            expect(command).to be_success
          end
        end

        context 'when meeting medium is changed' do
          it 'does not allow to save the meeting and raise the invalid data exception' do
            @meeting.update(medium: OFFLINE)
            @params[:medium] = GOOGLE_PROVIDER
            expect do
              UpdateMeeting.call(@params.with_indifferent_access)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting medium.")
          end
        end
      end
    end

    context 'when user try to check in or check out meeting' do
      before do
        @user = create(:user)
        @meeting = create(:meeting, owner: @user)
        @updated_params = @meeting.as_json.with_indifferent_access
        @updated_params[:participants] = [
          {
            id: @user.id,
            name: @user.name,
            entity: 'user'
          },
          {
            id: @meeting.organizer.entity_id,
            name: @meeting.organizer.name,
            entity: 'user',
            email: @meeting.organizer.email
          }
        ]
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          @meeting.organizer
        ])
      end

      context 'valid details' do
        before do
          @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
          meeting_attendance = build(:meeting_attendance)
          @checked_in_details = {'latitude': meeting_attendance.checked_in_latitude, longitude: meeting_attendance.checked_in_longitude}
          @checked_out_details = {'latitude': meeting_attendance.checked_out_latitude, longitude: meeting_attendance.checked_out_longitude}
        end

        context 'when updating meeting without checking in or checking out' do
          it 'should check in meeting' do
            @updated_params['title'] = 'Some new Updated Title'
            meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: {'latitude': nil, 'longitude': nil}, checked_out_details: {'latitude': nil, 'longitude': nil}).with_indifferent_access).result
            expect(meeting.meeting_attendances).to be_empty
          end
        end

        context 'when checking in' do
          it 'should check in meeting' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).exactly(1).times
            meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details).with_indifferent_access).result
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(@checked_in_details[:latitude])
            expect(meeting_attendance.checked_in_longitude).to eq(@checked_in_details[:longitude])
            expect(meeting_attendance.checked_out_at.present?).to be(false)
            expect(meeting_attendance.checked_out_latitude).to be(nil)
            expect(meeting_attendance.checked_out_longitude).to be(nil)
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
          end

          context 'with geofence validation' do
            context 'when geofence config is not present' do
              it 'allows check in' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
                meeting = UpdateMeeting.call(@updated_params.merge(location_latitude: '18.559658', location_longitude: '73.779938', checked_in_details: @checked_in_details).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
              end
            end

            context 'when check in location is within geofence' do
              it 'allows check in' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
                meeting = UpdateMeeting.call(@updated_params.merge(location_latitude: '18.559658', location_longitude: '73.779938', checked_in_details: { latitude: '18.559658', longitude: '73.779938' }).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
              end
            end

            context 'when check in location is outside geofence' do
              it 'sets is_checked_in_outside_geofence to true' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: false }, fieldSalesEnabled: true })
                outside_location = { latitude: '18.5670563', longitude: '73.7684087' }
                meeting = UpdateMeeting.call(@updated_params.merge(location_latitude: '18.559658', location_longitude: '73.779938', checked_in_details: outside_location).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_in_outside_geofence).to eq(true)
              end

              it 'raises error when restrictCheckIn is true' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
                outside_location = { latitude: '18.5670563', longitude: '73.7684087' }
                
                expect {
                  UpdateMeeting.call(@updated_params.merge(location_latitude: '18.559658', location_longitude: '73.779938', checked_in_details: outside_location).with_indifferent_access)
                }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_checkin_location}||#{I18n.t('error.invalid.checkin_location')}")
              end
            end
          end
        end

        context 'when checked in and not checking out' do
          it 'should update rest of the attributes without checking out or modifying check in details' do
            UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: {'latitude': nil, 'longitude': nil}).with_indifferent_access).result
            @updated_params['title'] = 'New Updated Title'
            meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: {'latitude': nil, 'longitude': nil}).with_indifferent_access).result
            expect(meeting.title).to eq('New Updated Title')
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(@checked_in_details[:latitude])
            expect(meeting_attendance.checked_in_longitude).to eq(@checked_in_details[:longitude])
            expect(meeting_attendance.checked_out_at.present?).to be(false)
            expect(meeting_attendance.checked_out_latitude).to be(nil)
            expect(meeting_attendance.checked_out_longitude).to be(nil)
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
          end
        end

        context 'when checking out' do
          before do
            create(
              :meeting_checked_in,
              meeting: @meeting,
              checked_in_latitude: @checked_in_details[:latitude],
              checked_in_longitude: @checked_in_details[:longitude]
            )
          end

          it 'should check out meeting' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedOut)).exactly(1).times

            meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: @checked_out_details).with_indifferent_access).result
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(@checked_in_details[:latitude])
            expect(meeting_attendance.checked_in_longitude).to eq(@checked_in_details[:longitude])
            expect(meeting_attendance.checked_out_at.present?).to be(true)
            expect(meeting_attendance.checked_out_latitude).to eq(@checked_out_details[:latitude])
            expect(meeting_attendance.checked_out_longitude).to eq(@checked_out_details[:longitude])
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
          end

          context 'with geofence validation' do
            context 'when geofence config is not present' do
              it 'allows check out' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
                meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: @checked_out_details).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
              end
            end

            context 'when check out location is within geofence' do
              it 'allows check out' do
                @checked_out_details = { latitude: '18.559668', longitude: '73.779939' }
                @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
                meeting = UpdateMeeting.call(@updated_params.merge(location_latitude: '18.559658', location_longitude: '73.779938', checked_in_details: @checked_in_details, checked_out_details: @checked_out_details).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
              end
            end

            context 'when check out location is outside geofence' do
              it 'sets is_checked_out_outside_geofence to true' do
                @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
                outside_location = { latitude: '18.5670563', longitude: '73.7684087' }
                meeting = UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: outside_location).with_indifferent_access).result
                meeting_attendance = meeting.meeting_attendances.find_by(user_id: @user.id)
                expect(meeting_attendance.is_checked_out_outside_geofence).to eq(true)
              end
            end
          end
        end
      end

      context 'invalid details' do
        before do
          meeting_attendance = build(:meeting_attendance)
          @checked_in_details = {'latitude': meeting_attendance.checked_in_latitude}
          @checked_out_details = {'latitude': meeting_attendance.checked_out_latitude}
        end

        context 'with invalid check in details' do
          it 'should raise invalid data error' do
            expect{
              UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details).with_indifferent_access)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check in without time or location details")
          end
        end

        context 'with invalid check out details' do
          before { create(:meeting_checked_in, meeting: @meeting) }

          it 'should raise invalid data error' do
            expect{
              UpdateMeeting.call(@updated_params.merge(checked_out_details: @checked_out_details).with_indifferent_access)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check in without time or location details, cannot check out without time or location details")
          end
        end

        context 'with checking in an already checked in meeting' do
          before do
            meeting_attendance = create(:meeting_checked_in, meeting: @meeting)
            @checked_in_details = {'latitude': meeting_attendance.checked_in_latitude + '1', 'longitude': meeting_attendance.checked_in_longitude}
          end

          it 'should raise invalid data error' do
            expect{
              UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details: nil))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check out without time or location details")
          end
        end

        context 'with checking out an already checked out meeting' do
          before do
            meeting_attendance = create(:meeting_attendance, meeting: @meeting)
            @checked_in_details = {'latitude': meeting_attendance.checked_in_latitude, 'longitude': meeting_attendance.checked_in_longitude}
            @checked_out_details = {'latitude': meeting_attendance.checked_out_latitude + '1', 'longitude': meeting_attendance.checked_out_longitude}
          end

          it 'should raise invalid data error' do
            expect{
              UpdateMeeting.call(@updated_params.merge(checked_in_details: @checked_in_details, checked_out_details:  @checked_out_details).with_indifferent_access)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check out without time or location details")
          end
        end

        context 'with checking out without checking in' do
          before { create(:meeting_checked_in, meeting: @meeting, checked_in_at: nil, checked_in_latitude: nil, checked_in_longitude: nil) }

          it 'should raise invalid data error' do
            expect{
              UpdateMeeting.call(@updated_params.merge(checked_out_details: @checked_out_details).with_indifferent_access)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||")
          end
        end
      end
    end

    context 'when updating custom field values' do
      before do
        @user = create(:user)
        @meeting = create(:meeting, owner: @user, custom_field_values: { 'cfTextField' => 'This is custom field', 'cfNumberField' => 123 })
        @updated_params = @meeting.as_json.with_indifferent_access
        @updated_params[:participants] = [
          {
            id: @user.id,
            name: @user.name,
            entity: 'user'
          },
          {
            id: @meeting.organizer.entity_id,
            name: @meeting.organizer.name,
            entity: 'user',
            email: @meeting.organizer.email
          }
        ]
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        @updated_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          @meeting.organizer
        ])
        @custom_field_1 = create(:custom_field, field_type: 'TEXT_FIELD', display_name: 'Text Field', tenant_id: @user.tenant_id, active: false)
        @custom_field_2 = create(:custom_field, field_type: 'NUMBER', display_name: 'Number Field', tenant_id: @user.tenant_id)
        @updated_params = @updated_params.inject({}) { |hash, (k, v)| hash.merge(k.camelize(:lower) => v) }.with_indifferent_access
      end

      context 'when inactive field is updated' do
        it 'should raise invalid data error' do
          @updated_params[:customFieldValues] = { 'cfTextField' => 'Updated custom field value', 'cfNumberField' => 456 }
          expect do
            described_class.call(meeting_params(ActionController::Parameters.new(@updated_params))).result
          end.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Inactive fields - cfTextField')
        end
      end

      it 'should modify active fields' do
        @updated_params[:customFieldValues] = { 'cfNumberField' => 456 }
        meeting = described_class.call(meeting_params(ActionController::Parameters.new(@updated_params))).result

        expect(meeting.custom_field_values.keys).to match_array([@custom_field_2.internal_name])
        expect(meeting.custom_field_values[@custom_field_2.internal_name]).to eq(456)
      end

      it 'should reset custom field values if all custom field values are reset' do
        @custom_field_1.update(active: true)
        @updated_params[:customFieldValues] = {}
        meeting = described_class.call(meeting_params(ActionController::Parameters.new(@updated_params))).result

        expect(meeting.custom_field_values.keys).to match_array([])
        expect(meeting.custom_field_values[@custom_field_1.internal_name]).to be(nil)
        expect(meeting.custom_field_values[@custom_field_2.internal_name]).to be(nil)
      end

      it 'should raise invalid data error if updating invalid values' do
        @updated_params[:customFieldValues] = { 'cfTextField' => 'This is custom field', 'cfNumberField' => 'invalid text for number' }

        expect do
          described_class.call(meeting_params(ActionController::Parameters.new(@updated_params))).result
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid value for given field.")
      end

      it 'should raise invalid data error if there are non existant fields' do
        @updated_params[:customFieldValues] = { 'cfTextField' => 'This is custom field', other_field: 'value', another_field: 'value' }

        expect do
          described_class.call(meeting_params(ActionController::Parameters.new(@updated_params))).result
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid custom fields - other_field, another_field")
      end
    end
  end

  def participants_to_json participants
    participants.map{|participant| participant.tap{|p| p['entity'] = p['entity'].split('_').first}}
  end
end
