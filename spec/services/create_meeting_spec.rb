require 'rails_helper'
require 'bunny-mock'
require 'sidekiq/testing'

RSpec.describe CreateMeeting do
  describe '#call' do
    before do
      connection = BunnyMock.new
      channel = connection.start.channel
      exchange = channel.topic MEETING_EXCHANGE

      queue = channel.queue "meeting.update.deal.metaInfo"
      queue.bind exchange, routing_key: "meeting.update.deal.metaInfo"
      allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE).and_return(queue)
      @user = create(:user)
      @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    end

    def meeting_params(params)
      params.permit(
        :id,
        :title,
        :description,
        :from,
        :to,
        :allDay,
        :medium,
        :providerLink,
        :location,
        :status,
        {
          owner: [:id, :name]
        },
        {
          customFieldValues: {},
        },
        {
          timezone: [
            :id,
            :name
          ]
        },
        {
          participants: [
            :entity,
            :id,
            :name,
            :email
          ]
        },
        {
          relatedTo: [
            :entity,
            :id,
            :name,
            :email
          ]
        },
        {
          checkedInDetails: [
            :latitude,
            :longitude
          ]
        },
        {
          checkedOutDetails: [
            :latitude,
            :longitude
          ]
        }
      )
    end

    context 'with valid input' do
      before do
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
        ])
      end

      context 'for notification jobs' do
        let(:params) { build(:meeting, owner: @user, from: '01:00 AM'.to_time, to: DateTime.now + 2.days + 1.hour).as_json }

        before do
          Sidekiq::Worker.clear_all
          ActiveJob::Base.queue_adapter = :test
          allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          field = create(:field, internal_name: 'timezone', display_name: 'Timezone', field_type: 'PICK_LIST', tenant_id: @user.tenant_id)
          picklist = create(:picklist, tenant_id: @user.tenant_id, internal_name: 'timezone', field: field)
          create(:picklist_value, tenant_id: @user.tenant_id, internal_name: 'Africa/Nairobi', picklist: picklist)
          params['status'] = SCHEDULED
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})
        end

        context 'when meeting is scheduled before next 3am' do
          it 'should enqueue job for notification' do
            expect do
              CreateMeeting.call(params)
            end.to have_enqueued_job.on_queue('reminder_queue').exactly(1).times
          end
        end

        context 'when meeting is scheduled after next 3am' do
          it 'should not enqueue job for notification' do
            params['from'] = '04:00 AM'.to_time + 1.day
            expect do
              CreateMeeting.call(params)
            end.not_to have_enqueued_job.on_queue('reminder_queue')
          end
        end
      end

      context 'when timezone is not present' do
        let(:params) { build(:meeting, owner: @user, from: DateTime.now + 2.days, to: DateTime.now + 2.days + 1.hour).as_json }

        before do
          field = create(:field, internal_name: 'timezone', display_name: 'Timezone', field_type: 'PICK_LIST', tenant_id: @user.tenant_id)
          picklist = create(:picklist, tenant_id: @user.tenant_id, internal_name: 'timezone', field: field)
          create(:picklist_value, tenant_id: @user.tenant_id, internal_name: 'Africa/Nairobi', picklist: picklist)
          params['status'] = SCHEDULED
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})
        end

        it 'adds timezone of logged in user' do
          CreateMeeting.call(params)
          expect(Meeting.last.time_zone.name).to eq('Africa/Nairobi')
        end

        it 'returns success' do
          command = CreateMeeting.call(params)
          expect( command.success? ).to be true
        end
      end

      context 'without participants' do
        before do
          @input = build(:meeting_all_day).as_json(except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at, :time_zone_id])
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input['timezone'] = { id: @timezone.entity_id, name: @timezone.name }
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        context 'when status is scheduled' do
          before do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times

            stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
            with(
              headers: {
                "Authorization" => "Bearer #{@token}",
                'Accept'=>'application/json',
                'Content-Type'=>'application/json'
              }).
              to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})
          end

          it 'returns success' do
            command = CreateMeeting.call(@input)
            expect( command.success? ).to be true
          end

          it 'creates a meeting object' do
            expect{
              CreateMeeting.call(@input)
            }.to change { Meeting.count}.by(1)
          end

          context 'returns meeting object with correct data' do
            before do
              @meeting = CreateMeeting.call(@input).result
            end
            it { expect(@meeting.title) == @input["title"] }
            it { expect(@meeting.description) == @input["description"] }
            it { expect(@meeting.all_day) == @input["all_day"] }
            it { expect(@meeting.location) == @input["location"] }
            it { expect(@meeting.tenant_id) == @user.tenant_id }
            it { expect(@meeting.owner).to be_eql(@user) }
            it { expect(@meeting.created_by).to be_eql(@user) }
            it { expect(@meeting.updated_by).to be_eql(@user) }
            it { expect(@meeting.time_zone).to be_eql(@timezone) }
          end

          context 'when meeting is allday' do
            before do
              @input['timezone'] = { id: @timezone.entity_id, name: 'Asia/Calcutta' }
              @input['from'] = '2030-02-19T03:00:00.000Z'
              @input['to'] = '2030-02-19T04:00:00.000Z'
              @timezone.update(name: 'Asia/Calcutta')
              @meeting = CreateMeeting.call(@input).result
            end

            it "modifies from and to as 'beginning of day' and 'end of day' of given from date" do
              expect(@meeting.from.to_s).to eq('2030-02-18 18:30:00 UTC')
              expect(@meeting.to.to_s).to eq('2030-02-19 18:29:59 UTC')
            end
          end
        end

        context 'when medium is OFFLINE' do
          before do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          end

          it 'does not invoke call method of calendar base class' do
            expect(Calendar::Base).not_to receive(:call)
            command = CreateMeeting.call(@input.merge('medium' => OFFLINE))
            expect(command).to be_success
          end

          it 'creates a meeting object' do
            expect { CreateMeeting.call(@input) }.to change { Meeting.count }.by(1)
          end
        end

        context 'when medium is GOOGLE' do
          let(:create_google_meeting_success_response) do
            {
              kind: 'calendar#event',
              etag: '3308646057738000',
              id: 'fej8f0ateki87e6lj7dub7sj8g',
              summary: @input['title'],
              location: @input['location'],
              status: 'confirmed',
              description: @input['description'],
              start: { dateTime: @input['from'].in_time_zone(@input['timezone']['name']).strftime('%FT%T%:z') },
              end: { dateTime: @input['to'].in_time_zone(@input['timezone']['name']).strftime('%FT%T%:z') },
              source: {
                url: APP_KYLAS_HOST,
                title: KYLAS
              },
              hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
              conferenceData: {
                createRequest: {
                  requestId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
                  conferenceSolutionKey: {
                    type: 'hangoutsMeet'
                  },
                  status: {
                    statusCode: 'success'
                  }
                },
                entryPoints: [
                  {
                    entryPointType: 'video',
                    uri: 'https://meet.google.com/qzz-qpfe-ppc',
                    label: 'meet.google.com/qzz-qpfe-ppc'
                  },
                  {
                    regionCode: 'US',
                    entryPointType: 'phone',
                    uri: 'tel:******-301-8549',
                    label: '******-301-8549',
                    pin: '247728753'
                  }
                ],
                conferenceSolution: {
                  key: {
                    type: 'hangoutsMeet'
                  },
                  name: 'Google Meet',
                  iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
                },
                conferenceId: 'qzz-qpfe-ppc'
              },
              reminders: {
                useDefault: true
              },
              conferenceSolution: {
                key: {
                  type: 'hangoutsMeet'
                },
                name: 'Google Meet',
                iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
              },
              eventType: 'default'
            }
          end

          before do
            allow(SecureRandom).to receive(:uuid).and_return('1e486426-b5dd-494d-9754-b77cdaa7f3f7')
            @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                        provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
          end

          def stub_create_google_meeting_request(status, response_body)
            headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
            stub_request(:post, "https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events?conferenceDataVersion=1&sendUpdates=all")
              .with(body: hash_including(:kind, :summary, :location, :status, :description, :start, :end, :source, :conferenceData, :reminders),
                    headers: headers)
              .to_return(status: status, body: response_body)
          end

          context 'when meeting status is cancelled' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
            end

            it 'does invoke call method of calendar base class' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER, 'status' => CANCELLED))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end

          context 'when meeting status is conducted' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
            end

            it 'does invoke call method of calendar base class' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER, 'status'=> CONDUCTED))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end

          context 'when meeting object is valid' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
            end

            it 'does invoke call method of calendar base class and creates meeting' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end
        end
      end

      context 'with participants' do
        before do
          @input = build(:meeting_with_associated_entities).as_json(
            except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
          ).with_indifferent_access
          @user_participants = [{id: @user.id, entity: LOOKUP_USER, name: 'John Doe', email: '<EMAIL>'}]
          @lead_participants = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
          @deal_participants = [{id: 9, entity: LOOKUP_DEAL, name: "John Doe"}]
          @external_participants = [{entity: LOOKUP_EXTERNAL, email: '<EMAIL>'}]
          @contact_participants = [{id: 9, entity: LOOKUP_CONTACT, name: "John Doe"}]
          @participants = @lead_participants + @deal_participants + @contact_participants + @external_participants
          @input["participants"] = @participants
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:lead_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
            build(:contact_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
            build(:deal_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          expect(EntityMetadataPublisher).to receive(:call).twice
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        it 'returns success' do
          command = CreateMeeting.call(@input)
          expect( command.success? ).to be true
        end

        it 'creates a meeting object' do
          expect{
            CreateMeeting.call(@input)
          }.to change { Meeting.count}.by(1)
        end

        context 'returns meeting object with correct data' do
          before do
            @meeting = CreateMeeting.call(@input).result
          end
          it { expect(@meeting.title) == @input["title"] }
          it { expect(@meeting.description) == @input["description"] }
          it { expect(@meeting.from.to_s) == @input["from"] }
          it { expect(@meeting.to.to_s) == @input["to"] }
          it { expect(@meeting.all_day) == @input["all_day"] }
          it { expect(@meeting.location) == @input["location"] }
          it { expect(@meeting.tenant_id) == @user.tenant_id }
          it { expect(@meeting.owner).to be_eql(@user) }
          it { expect(@meeting.created_by).to be_eql(@user) }
          it { expect(@meeting.updated_by).to be_eql(@user) }
          it { expect(@meeting.participants.count).to be_eql(@participants.count + 1) }
          it { expect(@meeting.time_zone).to be_eql(@timezone) }
          #it { expect(@meeting.participants.first.name).to be_eql("Jane Doe") }

          it 'stores participants with correct names after validation' do
            @meeting.participants.where.not("entity like ?", "#{LOOKUP_EXTERNAL}_%").each do |participant|
              expect(participant.name).to be_eql("Jane Doe")
            end
          end

          it 'sets organizer' do
            participant_look_ups = @meeting.participant_look_ups.where(organizer: true)
            expect(participant_look_ups.count).to eq 1
            organizer = participant_look_ups.first.look_up
            expect(organizer.entity_id).to eq @user.id
            expect(organizer.entity_type).to eq LOOKUP_USER
          end

          it 'creates one external participant' do
            expect(@meeting.participants.where("entity like ?", "#{LOOKUP_EXTERNAL}_%").count).to eq 1
          end
        end

        context 'with duplicate invitees' do
          before do
            @participants += @user_participants
            @input["participants"] = @participants
            @meeting = CreateMeeting.call(@input).result
          end
          it { expect(@meeting.participants.count).to be_eql(@participants.count) }
        end

        context 'when organizer is not present in parameters' do
          it 'should make owner as organizer and add owner in participants' do
            @input['organizer'] = nil

            @meeting = CreateMeeting.call(@input).result
            expect(@meeting.organizer.entity).to eq("user_#{@user.id}")
            expect(@meeting.owner).to eq(@user)
            expect(@meeting.participants.pluck(:entity)).to include("user_#{@user.id}")
          end
        end

        context 'when organizer is present in parameters' do
          it 'should add organizer in participants and owner should not be added to participants' do
            @input['organizer'] = {
              id: 84_985_049,
              entity: 'external',
              name: '<EMAIL>',
              email: '<EMAIL>'
            }

            @meeting = CreateMeeting.call(@input).result
            expect(@meeting.organizer.entity).to eq('external_84985049')
            expect(@meeting.owner).to eq(@user)
            expect(@meeting.participants.pluck(:entity)).not_to include("user_#{@user.id}")
          end
        end
      end

      context 'with participants online meeting' do
        before do
          @input = build(:meeting_with_associated_entities).as_json(
            except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
          ).with_indifferent_access
          @user_participants = [{id: @user.id, entity: LOOKUP_USER, name: 'John Doe', email: '<EMAIL>'}]
          @lead_participants = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
          @deal_participants = [{id: 9, entity: LOOKUP_DEAL, name: "John Doe"}]
          @contact_participants = [{id: 9, entity: LOOKUP_CONTACT, name: "John Doe"}]
          @participants = @lead_participants + @deal_participants + @contact_participants
          @input["participants"] = @participants
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:lead_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
            build(:contact_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
            build(:deal_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        context 'when medium is GOOGLE' do
          let(:create_google_meeting_success_response) do
            {
              kind: 'calendar#event',
              etag: '3308646057738000',
              id: 'fej8f0ateki87e6lj7dub7sj8g',
              summary: @input['title'],
              location: @input['location'],
              status: 'confirmed',
              description: @input['description'],
              start: { dateTime: @input['from'].in_time_zone(@input['timezone']['name']).strftime('%FT%T%:z') },
              end: { dateTime: @input['to'].in_time_zone(@input['timezone']['name']).strftime('%FT%T%:z') },
              source: {
                url: APP_KYLAS_HOST,
                title: KYLAS
              },
              hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
              conferenceData: {
                createRequest: {
                  requestId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
                  conferenceSolutionKey: {
                    type: 'hangoutsMeet'
                  },
                  status: {
                    statusCode: 'success'
                  }
                },
                entryPoints: [
                  {
                    entryPointType: 'video',
                    uri: 'https://meet.google.com/qzz-qpfe-ppc',
                    label: 'meet.google.com/qzz-qpfe-ppc'
                  },
                  {
                    regionCode: 'US',
                    entryPointType: 'phone',
                    uri: 'tel:******-301-8549',
                    label: '******-301-8549',
                    pin: '247728753'
                  }
                ],
                conferenceSolution: {
                  key: {
                    type: 'hangoutsMeet'
                  },
                  name: 'Google Meet',
                  iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
                },
                conferenceId: 'qzz-qpfe-ppc'
              },
              reminders: {
                useDefault: true
              },
              conferenceSolution: {
                key: {
                  type: 'hangoutsMeet'
                },
                name: 'Google Meet',
                iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
              },
              eventType: 'default'
            }
          end

          before do
            allow(SecureRandom).to receive(:uuid).and_return('1e486426-b5dd-494d-9754-b77cdaa7f3f7')
            @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                        provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
          end

          def stub_create_google_meeting_request(status, response_body)
            headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
            stub_request(:post, "https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events?conferenceDataVersion=1&sendUpdates=all")
              .with(body: hash_including(:kind, :summary, :location, :status, :description, :start, :end, :source, :conferenceData, :reminders),
                    headers: headers)
              .to_return(status: status, body: response_body)
          end

          context 'when meeting status is cancelled' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
              expect(EntityMetadataPublisher).to receive(:call).twice
            end

            it 'does invoke call method of calendar base class' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end

          context 'when meeting status is conducted' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
              expect(EntityMetadataPublisher).to receive(:call).twice
            end

            it 'does invoke call method of calendar base class' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end

          context 'when meeting object is valid' do
            before do
              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(ParticipantAddedEventPublisher).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)
              expect(MeetingScheduledRelatedToEntityPublisher).not_to receive(:call)
              expect(EntityMetadataPublisher).to receive(:call).twice
            end

            it 'does invoke call method of calendar base class and creates meeting' do
              stub_create_google_meeting_request(200, create_google_meeting_success_response.to_json)
              command = CreateMeeting.call(@input.merge('medium' => GOOGLE_PROVIDER))
              expect(command).to be_success
              expect(command.result.provider_link).to eq('https://meet.google.com/qzz-qpfe-ppc')
              expect(command.result.provider_meeting_id).to eq('fej8f0ateki87e6lj7dub7sj8g')
              expect(Meeting.count).to eq(1)
            end
          end
        end
      end

      context 'with related_to entities' do
        context "when created new meeting" do
          before do
            @input = build(:meeting_with_associated_entities).as_json(
              except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
            ).with_indifferent_access
            @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
            @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }

            @lead_related_to = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
            @deal_related_to = [{id: 9, entity: LOOKUP_DEAL, name: "John Doe"}]
            @contact_related_to = [{id: 9, entity: LOOKUP_CONTACT, name: "John Doe"}]
            @company_related_to = [{id: 9, entity: LOOKUP_COMPANY, name: "Test Company"}]
            @input["related_to"] = @lead_related_to + @deal_related_to + @contact_related_to + @company_related_to

            allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
              build(:lead_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
            ])
            allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
              build(:contact_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
            ])
            allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
              build(:deal_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
            ])
            allow(ValidateCompanies).to receive_message_chain(:call, :result).and_return([
              build(:company_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Updated Test Company")
            ])

            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(1).times
            expect(EntityMetadataPublisher).to receive(:call).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

            @meeting = CreateMeeting.call(@input).result
          end

          it 'stores related_to entities correctly' do
            expect(@meeting.related_to.count).to eq(4)
            expect(@meeting.related_to.map(&:entity)).to match_array(["lead_9", "contact_9", "deal_9", 'company_9'])
            expect(@meeting.related_to.map(&:owner_id)). to match_array([123, 123, 123, 123])
            expect(@meeting.related_to.find{ |lookup| lookup.is_a_company? }.name).to eq('Updated Test Company')
          end
        end
      end

      context 'with past dates' do
        let(:timezone) { create(:timezone_look_up, tenant_id: @user.tenant_id) }
        let(:params) { build(:meeting, owner: @user, time_zone: timezone, from: DateTime.now - 2.days, to: DateTime.now - 2.days + 1.hour).as_json }
        let(:params_all_day) { build(:meeting, owner: @user, all_day: true, time_zone: timezone, from: (DateTime.now - 2.days).beginning_of_day).as_json }

        before do
          timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          params['timezone'] = { id: timezone.entity_id, name: timezone.name }
          params_all_day['timezone'] = { id: timezone.entity_id, name: timezone.name }
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        context 'with status as scheduled' do
          before do
            Sidekiq::Worker.clear_all
            ActiveJob::Base.queue_adapter = :test
            params['status'] = SCHEDULED
            params_all_day['status'] = SCHEDULED
            params['relatedTo'] = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
            params_all_day['relatedTo'] = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
            allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
              build(:lead_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
            ])
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
            expect(EntityMetadataPublisher).to receive(:call).once
          end

          it 'should not add job for notification' do
            expect do
              CreateMeeting.call(params).result
            end.not_to have_enqueued_job.on_queue('reminder_queue')
          end

          it 'should mark meeting as missed' do
            meeting_response = CreateMeeting.call(params).result
            expect(meeting_response.status).to eq(MISSED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(false)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end

          it 'should mark meeting as missed' do
            meeting_response = CreateMeeting.call(params_all_day).result
            expect(meeting_response.status).to eq(MISSED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(true)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end
        end

        context 'with status as conducted' do
          before do
            params['status'] = CONDUCTED
            params_all_day['status'] = CONDUCTED
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          end

          it 'should add conducted by and conducted at values' do
            meeting_response = CreateMeeting.call(params).result
            expect(meeting_response.status).to eq(CONDUCTED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(false)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(@user.id)
            expect(meeting_response.conducted_at.present?).to be(true)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end

          it 'should add conducted by and conducted at values' do
            meeting_response = CreateMeeting.call(params_all_day).result
            expect(meeting_response.status).to eq(CONDUCTED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(true)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(@user.id)
            expect(meeting_response.conducted_at.present?).to be(true)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end
        end

        context 'with status as cancelled' do
          before do
            params['status'] = CANCELLED
            params_all_day['status'] = CANCELLED
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          end

          it 'should add cancelled by and cancelled at values' do
            meeting_response = CreateMeeting.call(params).result
            expect(meeting_response.status).to eq(CANCELLED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(false)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.conducted_at.present?).to be(false)
            expect(meeting_response.cancelled_by_id).to eq(@user.id)
            expect(meeting_response.cancelled_at.present?).to be(true)
            expect(meeting_response.to.present?).to be (true)
            meeting_response.status = CONDUCTED
            expect(meeting_response.valid?).to be (false)
          end

          it 'should add cancelled by and cancelled at values' do
            meeting_response = CreateMeeting.call(params_all_day).result
            expect(meeting_response.status).to eq(CANCELLED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(true)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.conducted_at.present?).to be(false)
            expect(meeting_response.cancelled_by_id).to eq(@user.id)
            expect(meeting_response.cancelled_at.present?).to be(true)
            expect(meeting_response.to.present?).to be (true)
            meeting_response.status = CONDUCTED
            expect(meeting_response.valid?).to be (false)
          end
        end
      end

      context 'with future dates' do
        let(:params) { build(:meeting, owner: @user, from: DateTime.now + 2.days, to: DateTime.now + 2.days + 1.hour).as_json }
        let(:params_all_day) { build(:meeting, owner: @user, all_day: true, from: (DateTime.now + 2.days).beginning_of_day).as_json }

        before do
          timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          params['timezone'] = { id: timezone.entity_id, name: timezone.name }
          params_all_day['timezone'] = { id: timezone.entity_id, name: timezone.name }
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        context 'with status as scheduled' do
          before do
            params['status'] = SCHEDULED
            params_all_day['status'] = SCHEDULED
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          end

          it 'should mark meeting as scheduled' do
            meeting_response = CreateMeeting.call(params).result
            expect(meeting_response.status).to eq(SCHEDULED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(false)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end

          it 'should mark meeting as scheduled' do
            meeting_response = CreateMeeting.call(params_all_day).result
            expect(meeting_response.status).to eq(SCHEDULED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(true)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end
        end

        context 'with status as missed' do
          before do
            params['status'] = MISSED
            params_all_day['status'] = MISSED
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
            expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          end

          it 'should mark meeting as scheduled' do
            meeting_response = CreateMeeting.call(params).result
            expect(meeting_response.status).to eq(SCHEDULED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(false)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end

          it 'should mark meeting as scheduled' do
            meeting_response = CreateMeeting.call(params_all_day).result
            expect(meeting_response.status).to eq(SCHEDULED)
            expect(meeting_response.owner_id).to eq(@user.id)
            expect(meeting_response.all_day).to be(true)
            expect(meeting_response.created_by_id).to eq(@user.id)
            expect(meeting_response.updated_by_id).to eq(@user.id)
            expect(meeting_response.conducted_by_id).to eq(nil)
            expect(meeting_response.cancelled_by_id).to eq(nil)
            expect(meeting_response.to.present?).to be (true)
          end
        end
      end

      context 'with check in details' do
        before do
          timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input = build(:meeting_all_day).as_json(except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at, :time_zone_id])
          @input['timezone'] = { id: timezone.entity_id, name: timezone.name }
          meeting_attendance = build(:meeting_attendance)
          @checked_in_details = {latitude: meeting_attendance.checked_in_latitude, longitude: meeting_attendance.checked_in_longitude }.with_indifferent_access
          @input.merge!('checked_in_details': @checked_in_details)
        end

        it 'should create meeting attendance and check in' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).exactly(1).times
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingUpdated))
          expect(MeetingScheduledEventPublisher).to receive(:call)

          meeting = CreateMeeting.call(@input.with_indifferent_access).result
          expect(meeting.meeting_attendances.present?).to be(true)
          meeting_attendance = meeting.meeting_attendances.find_by(user_id: meeting.owner_id)
          expect(meeting_attendance.checked_in_latitude).to eq(@checked_in_details[:latitude])
          expect(meeting_attendance.checked_in_longitude).to eq(@checked_in_details[:longitude])
          expect(meeting_attendance.checked_out_latitude).to be(nil)
          expect(meeting_attendance.checked_out_longitude).to be(nil)
        end

        context 'when geofence config is not present' do
          it 'should create meeting attendance and check in' do
            expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingCheckedinBeyondGeofence))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
            @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).exactly(1).times
            expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingUpdated))
            expect(MeetingScheduledEventPublisher).to receive(:call)
  
            meeting = CreateMeeting.call(@input.with_indifferent_access).result
            expect(meeting.meeting_attendances.present?).to be(true)
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: meeting.owner_id)
            expect(meeting_attendance.checked_in_latitude).to eq(@checked_in_details[:latitude])
            expect(meeting_attendance.checked_in_longitude).to eq(@checked_in_details[:longitude])
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
            expect(meeting_attendance.checked_out_latitude).to be(nil)
            expect(meeting_attendance.checked_out_longitude).to be(nil)
          end
        end

        context 'when check in location is within geofence' do
          it 'should create meeting attendance and check in' do
            expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingCheckedinBeyondGeofence))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
            @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })

            @input['checked_in_details'] = { latitude: '18.5670563', longitude: '73.7684087' }
            @input['location_coordinate'] = { lat: '18.5670563', lon: '73.7684087' }

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).exactly(1).times
            expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingUpdated))
            expect(MeetingScheduledEventPublisher).to receive(:call)
  
            meeting = CreateMeeting.call(@input.with_indifferent_access).result
            expect(meeting.meeting_attendances.present?).to be(true)
            meeting_attendance = meeting.meeting_attendances.find_by(user_id: meeting.owner_id)
            expect(meeting_attendance.checked_in_latitude).to eq('18.5670563')
            expect(meeting_attendance.checked_in_longitude).to eq('73.7684087')
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
            expect(meeting_attendance.checked_out_latitude).to be(nil)
            expect(meeting_attendance.checked_out_longitude).to be(nil)
          end
        end

        context 'when check in location is outside geofence' do
          context 'when restrictCheckIn is true' do
            it 'should not create meeting and raise exception' do
              @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })

              expect(PublishEvent).not_to receive(:call)
              expect(MeetingScheduledEventPublisher).not_to receive(:call)

              @input['checked_in_details'] = { latitude: '18.5670563', longitude: '73.7684087' }
              @input['location_coordinate'] = { lat: '18.559658', lon: '73.779938' }

              expect{
                CreateMeeting.call(@input.with_indifferent_access)
              }.to raise_error(ExceptionHandler::InvalidDataError, "01503021||Checkin location is beyond set geofence")
            end
          end

          context 'when restricCheckIn is false' do
            it 'should create meeting attendance and check in and raise checkedInOutsideGeofence event' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

              @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: false }, fieldSalesEnabled: true })

              @input['checked_in_details'] = { latitude: '18.5670563', longitude: '73.7684087' }
              @input['location_coordinate'] = { lat: '18.559658', lon: '73.779938' }

              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).exactly(1).times
              expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MeetingUpdated))
              expect(MeetingScheduledEventPublisher).to receive(:call)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedinBeyondGeofence)).exactly(1).times

              meeting = CreateMeeting.call(@input.with_indifferent_access).result
              expect(meeting.meeting_attendances.present?).to be(true)
              meeting_attendance = meeting.meeting_attendances.find_by(user_id: meeting.owner_id)
              expect(meeting_attendance.checked_in_latitude).to eq('18.5670563')
              expect(meeting_attendance.checked_in_longitude).to eq('73.7684087')
              expect(meeting_attendance.is_checked_in_outside_geofence).to eq(true)
              expect(meeting_attendance.checked_out_latitude).to be(nil)
              expect(meeting_attendance.checked_out_longitude).to be(nil)
            end
          end
        end
      end

      context 'with custom field values' do
        before do
          @input = build(:meeting_with_associated_entities).as_json(
            except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
          ).with_indifferent_access
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
          @input[:customFieldValues] = { cfTextField: 'This is custom text field', cfPicklist: { id: 123, name: 'PN' } }
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
          @field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Text Field', field_type: 'TEXT_FIELD')
          @other_field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Picklist', field_type: 'PICK_LIST')
          @picklist = create(:picklist, tenant_id: @user.tenant_id, internal_name: @other_field.internal_name, field: @other_field)
          @pl_val_1 = create(:picklist_value, tenant_id: @user.tenant_id, internal_name: 'value_1', picklist: @picklist, id: 123)
        end

        it 'should create meeting with custom field values' do
          meeting = described_class.call(meeting_params(ActionController::Parameters.new(@input))).result

          expect(meeting.custom_field_values.present?).to be(true)
          expect(meeting.custom_field_values.keys).to match_array([@field.internal_name, @other_field.internal_name])
          expect(meeting.custom_field_values[@field.internal_name]).to eq('This is custom text field')
          expect(meeting.custom_field_values['cfAnotherTextField']).to be(nil)
        end
      end

      context 'with other user as owner' do
        before do
          @input = build(:meeting_with_associated_entities).as_json(
            except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
          ).with_indifferent_access
          @another_user = create(:user, tenant_id: @user.tenant_id, name: 'Another owner user')
          @user_participants = [{id: @user.id, entity: LOOKUP_USER, name: 'John Doe', email: '<EMAIL>'}]
          @lead_participants = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
          @participants = @lead_participants + @user_participants
          @input["participants"] = @participants
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: "Jane Doe"),
            build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: 'Another owner user')
          ])
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:lead_look_up, entity_id: 9, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          @input['owner'] = { id: @another_user.id, name: @another_user.name }
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
          expect(EntityMetadataPublisher).to receive(:call).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
        end

        it 'returns success' do
          command = CreateMeeting.call(@input)
          expect( command.success? ).to be true
        end

        context 'and imported_meeting argument is false' do
          it 'makes current user as organizer' do
            meeting = CreateMeeting.call(@input).result
            expect(meeting.organizer.entity).to eq("user_#{@user.id}")
          end
        end

        context 'and imported_meeting atgument is true' do
          it 'makes owner user as organizer' do
            @input['organizer'] = nil
            meeting = CreateMeeting.call(@input, true).result
            expect(meeting.organizer.entity).to eq("user_#{@another_user.id}")
          end
        end

        it 'creates a meeting object' do
          expect{
            CreateMeeting.call(@input)
          }.to change { Meeting.count}.by(1)
        end

        context 'assigns given user as owner' do
          before do
            @meeting = CreateMeeting.call(@input).result
          end
          it { expect(@meeting.owner).to be_eql(@another_user) }

          context 'and adds that owner in participants' do
            it {
              expect(@meeting.participants.where(name: 'Another owner user')).to be_present
            }
          end
        end
      end

      after do
        thread = Thread.current
        thread[:auth] = nil
      end
    end

    context 'with invalid input' do
      before do
        @input = build(:meeting).as_json(except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at])
      end

      context 'fails without security context' do
        it "raises Authentication error" do
          expect{
            CreateMeeting.call(@input)
          }.to raise_error( ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end

      end

      context 'with security context but invalid data' do
        before do
          Thread.current[:auth] = @auth_data
          @user_participants = [{id: 10, entity: LOOKUP_USER, name: "John Doe"}]
          #@lead_participants = [{id: 9, entity: LOOKUP_LEAD, name: "John Doe"}]
          #@deal_participants = [{id: 9, entity: LOOKUP_DEAL, name: "John Doe"}]
          #@contact_participants = [{id: 9, entity: LOOKUP_CONTACT, name: "John Doe"}]
          @participants = @user_participants #+ @lead_participants + @deal_participants + @contact_participants
          @input["participants"] = @participants
          allow(ValidateUsers).to receive(:call).and_raise(
            ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid data."
          )
        end

        it "raises InvalidDataError" do
          expect {
            CreateMeeting.call(@input.except("from"))
          }.to raise_error( ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid data." )
          expect(Meeting.count).to be == 0
        end
      end

      context 'with incomplete check in details or trying to check out' do
        before do
          Thread.current[:auth] = @auth_data
          meeting_attendance = build(:meeting_attendance)
          @checked_in_details = {'latitude': meeting_attendance.checked_in_latitude}
          @checked_out_details = {'latitude': meeting_attendance.checked_out_latitude}
          timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input['timezone'] = { id: timezone.entity_id, name: timezone.name }

          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
          ])
        end

        context 'with incomplete checkin details' do
          it 'should raise invalid data error' do
            expect{
              CreateMeeting.call(@input.merge('checked_in_details' => @checked_in_details))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check in without time or location details")
          end
        end

        context 'when trying to checkout on meeting create' do
          it 'should raise invalid data error' do
            expect{
              CreateMeeting.call(@input.merge('checked_in_details' => @checked_in_details, 'checked_out_details' => @checked_out_details))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid checked out details.")
          end
        end
      end

      context 'when one of the custom fields is invalid' do
        before do
          Thread.current[:auth] = @auth_data
          @input = build(:meeting_with_associated_entities).as_json(
            except: [:id, :tenant_id, :owner_id, :created_by_id, :updated_by_id, :created_at, :updated_at]
          ).with_indifferent_access
          @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
          @input[:timezone] = { id: @timezone.entity_id, name: @timezone.name }
          @input[:customFieldValues] = { cfTextField: 'This is custom text field', cfPicklist: true }
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: "Jane Doe")
          ])
          @active_field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Text Field', field_type: 'TEXT_FIELD')
          @inactive_field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Inactive Field', field_type: 'TEXT_FIELD', active: false)
          @other_field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Picklist', field_type: 'PICK_LIST')
          @picklist = create(:picklist, tenant_id: @user.tenant_id, internal_name: @other_field.internal_name, field: @other_field)
          @pl_val_1 = create(:picklist_value, tenant_id: @user.tenant_id, internal_name: 'value_1', picklist: @picklist, id: 1, disabled: true)
        end

        it 'should raise invalid data error' do
          expect do
            described_class.call(meeting_params(ActionController::Parameters.new(@input))).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid value true for custom field - PICK_LIST.")
        end

        it 'should raise invalid data error for disabled picklist values' do
          @input[:customFieldValues] = { cfPicklist: { id: 1, name: 'disabled' } }
          expect do
            described_class.call(meeting_params(ActionController::Parameters.new(@input))).result
          end.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Invalid value {"id"=>1, "name"=>"disabled"} for custom field - PICK_LIST.')
        end

        context 'when inactive field data is present' do
          it 'should raise invalid data error' do
            @input[:customFieldValues] = { cfTextField: 'active field', cfInactiveField: 'inactive field' }
            expect do
              described_class.call(meeting_params(ActionController::Parameters.new(@input))).result
            end.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Inactive fields - cfInactiveField')
          end
        end

        context 'when non existant field data is present' do
          it 'should raise invalid data error' do
            @input[:customFieldValues] = { other_field: 'value', another_field: 'value', cfTextField: 'active field' }
            expect do
              described_class.call(meeting_params(ActionController::Parameters.new(@input))).result
            end.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Invalid custom fields - other_field, another_field')
          end
        end
      end
    end

    context 'when tenant usage limit is reached' do
      let(:params) { build(:meeting, owner: @user).as_json }

      before do
        usage_response = {
          "records" => {
            "used" => 100,
            "total" => 100
          }
        }

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(status: 200, body: usage_response.to_json, headers: {})
      end

      it 'should raise usage limit exceeded error' do
        expect {
          CreateMeeting.call(params)
        }.to raise_error(ExceptionHandler::UsageLimitExceeded)
      end

      it 'should not create any meeting' do
        expect {
          begin
            CreateMeeting.call(params)
          rescue ExceptionHandler::UsageLimitExceeded
            # Expected error, continue with test
          end
        }.not_to change(Meeting, :count)
      end
    end

    context 'when tenant usage limit is not reached' do
      let(:params) { build(:meeting, owner: @user).as_json }

      before do
        usage_response = {
          "records" => {
            "used" => 50,
            "total" => 100
          }
        }

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(status: 200, body: usage_response.to_json, headers: {})

        # Mock other required services
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
        ])
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
      end

      it 'should create meeting successfully' do
        expect {
          CreateMeeting.call(params)
        }.to change(Meeting, :count).by(1)
      end
    end

    context 'when tenant has unlimited usage' do
      let(:params) { build(:meeting, owner: @user).as_json }

      before do
        usage_response = {
          "records" => {
            "used" => 1000,
            "total" => -1
          }
        }

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(status: 200, body: usage_response.to_json, headers: {})

        # Mock other required services
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
        ])
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
      end

      it 'should create meeting successfully' do
        expect {
          CreateMeeting.call(params)
        }.to change(Meeting, :count).by(1)
      end
    end
  end
end
