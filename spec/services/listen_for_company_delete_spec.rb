require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForCompanyDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @company_lookup = build(:company_look_up, tenant_id: @user.tenant_id)
      @channel = connection.start.channel
      @exchange = @channel.topic COMPANY_EXCHANGE
    end

    context 'valid input' do
      context 'for company deleted event' do
        let(:payload) {
          {
            metadata: {
              'entityId': @company_lookup.entity_id,
              'tenantId': @user.tenant_id,
              'userId': @user.id
            }
          }.to_json
        }

        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: COMPANY_DELETED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe).with(COMPANY_EXCHANGE, COMPANY_DELETED_EVENT, COMPANY_DELETED_QUEUE).and_yield(payload.to_s)
        end

        context 'Meeting has other lookup and contact association along with current company' do
          it "Shouldn't delete meeting and unrelate current company" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
            expect(Calendar::Base).not_to receive(:call)
            meeting = create(:meeting, owner: @user)
            contact_lookup = build(:contact_look_up, entity_id: 16, tenant_id: @user.tenant_id, name: "John Company")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
            meeting.participants << [invitee, contact_lookup]
            meeting.related_to << [@company_lookup, contact_lookup]

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(5)
            expect(LookUp.find_by(id: @company_lookup.id)).to eq(@company_lookup)

            ListenForCompanyDelete.call()

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(4)
            expect(LookUp.exists?(id: @company_lookup.id)).to be(false)
          end
        end
      end
    end
  end
end
