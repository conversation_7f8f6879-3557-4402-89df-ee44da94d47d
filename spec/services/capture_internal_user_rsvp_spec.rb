require 'rails_helper'

RSpec.describe CaptureInternalUserRsvp do
  describe '#call' do
    before do
      @valid_user = create(:user)
      @meeting = create(:meeting, tenant_id: @valid_user.tenant_id)
      @meeting.participants << LookUp.new(entity: "#{LOOKUP_USER}_#{@valid_user.id}", name: @valid_user.name, tenant_id: @valid_user.tenant_id)
    end
    context 'valid' do
      before do
        event = Event::MeetingUpdated.new(@meeting, MeetingSerializer.call(@meeting, nil, false, nil, true).result, @valid_user.id)
        expect(Event::MeetingUpdated)
          .to receive(:new)
          .with(instance_of(Meeting), instance_of(ActiveSupport::HashWithIndifferentAccess), instance_of(Integer), nil)
          .once
          .and_return(event)
        allow(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated))
      end

      context 'for RSVP YES from valid user who is participant' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            notifyOrganiser: false,
            rsvpMessage: nil,
            rsvpResponse: RSVP_YES
          }
          @command = CaptureInternalUserRsvp.call(@meeting.id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end

      context 'for RSVP NO' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            notifyOrganiser: false,
            rsvpMessage: nil,
            rsvpResponse: RSVP_NO
          }
          @command = CaptureInternalUserRsvp.call(@meeting.id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end

      context 'for RSVP MAY_BE' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            notifyOrganiser: false,
            rsvpMessage: nil,
            rsvpResponse: RSVP_MAYBE
          }
          @command = CaptureInternalUserRsvp.call(@meeting.id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end
    end

    context 'invalid' do
      context 'with RSVP YES from invalid user who is not participant' do
        before do
          @invalid_user = create(:user)
          @auth_data = build(:auth_data, user_id: @invalid_user.id, tenant_id: @invalid_user.tenant_id, username: @invalid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            notifyOrganiser: false,
            rsvpMessage: nil,
            rsvpResponse: RSVP_YES
          }
        end

        it 'raises error' do
          expect{
            CaptureInternalUserRsvp.call(@meeting.id, @rsvp_data)
          }.to raise_error( ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Meeting not found.")
        end
      end

      context ' with incorrect RSVP value from valid user who is participant' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            notifyOrganiser: false,
            rsvpMessage: nil,
            rsvpResponse: "TESTING"
          }
        end

        it 'raises error' do
          expect{
            CaptureInternalUserRsvp.call(@meeting.id, @rsvp_data)
          }.to raise_error( ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid participant." )
        end

      end
    end
  end
end
