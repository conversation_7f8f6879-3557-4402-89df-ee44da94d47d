require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForUsageLimitChanged do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      let(:payload) {
        {
          "userId": 159,
          "tenantId": 9999,
          "planId": "embark",
          "usageEntityLimits": [
            {
              "usageEntity": "CUSTOM_FIELDS",
              "limit": 3
            }
          ]
        }.to_json
      }

      before do
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: USAGE_LIMIT_CHANGED_EVENT
      end

      context 'for usage limit changed and plan downgraded to embark' do
        before do
          allow(RabbitmqConnection).to receive(:subscribe).with(USER_EXCHANGE, USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE).and_yield(payload.to_s)
        end

        it 'should call deactivate custom fields service if plan downgraded' do
          expect(Field::DeactivateCustomFields).to receive(:call).with(9999)
          described_class.call
        end
      end

      context 'for usage limit changed and plan downgraded to other than embark' do
        before do
          allow(RabbitmqConnection).to receive(:subscribe)
                                    .with(USER_EXCHANGE, USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE)
                                    .and_yield(JSON.parse(payload).merge('planId' => 'elevate').to_json)
        end

        it 'should call deactivate custom fields service if plan downgraded' do
          expect(Field::DeactivateCustomFields).not_to receive(:call).with(9999)
          described_class.call
        end
      end
    end
  end
end
