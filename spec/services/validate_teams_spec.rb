# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ValidateUsers do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @user = build(:user_look_up, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when team is invalid' do
      before do
        stub_request(:post, "http://localhost:8081/v1/teams/search").with(
          body: {
            fields: [
              'name',
              'id'
            ],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'long',
                  value: '243'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Authorization'=>"Bearer #{@token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('teams-response.json'), headers: {})
      end
      it 'raises invalid data error' do
        expect { ValidateTeams.new([Team.new(id: 243)]).call }.to raise_error(ExceptionHandler::InvalidDataError, '01503001||Invalid team.')
      end
    end

    context 'when team is valid' do
      before do
        stub_request(:post, "http://localhost:8081/v1/teams/search").with(
          body: {
            fields: [
              'name',
              'id'
            ],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'long',
                  value: '123'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Authorization'=>"Bearer #{@token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('teams-response.json'), headers: {})

      end
      it 'returns teams' do
        teams = ValidateTeams.new([Team.new(id: 123)]).call
        expect(teams.first.id).to eq(123)
        expect(teams.first.name).to eq('team 2')
      end
    end
  end
end
