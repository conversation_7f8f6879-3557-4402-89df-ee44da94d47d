# frozen_string_literal: true

require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe MeetingScheduledPublisherJob, type: :job do
  describe 'Sidekiq Worker' do
    before(:each) do
      Sidekiq::Worker.clear_all
      ActiveJob::Base.queue_adapter = :test
      @user = create(:user)
    end

    it 'responds to perform' do
      expect(MeetingScheduledPublisherJob.new).to respond_to(:perform)
    end

    it 'schedules a job in correct queue' do
      meeting = create(:meeting, owner: @user)
      expect {
        MeetingScheduledPublisherJob.perform_later([meeting.id])
      }.to have_enqueued_job.on_queue('reminder_queue')
    end

    context 'when job is performing' do
      it 'should publish event for only meetings that are scheduled and from time is between next 15 minutes' do
        # given
        create(:meeting, id: 1, status: CANCELLED, owner: @user, from: DateTime.now + 6.minutes)
        create(:meeting, id: 2, status: SCHEDULED, owner: @user, from: DateTime.now + 16.minutes)
        m3 = create(:meeting, id: 3, status: SCHEDULED, owner: @user, from: DateTime.now + 11.minutes)

        # then
        expect(MeetingsScheduledInMinutesPublisher).to receive(:call).with([m3])

        # when
        MeetingScheduledPublisherJob.new.perform([1, 2, 3])
      end
    end
  end
end
