require 'rails_helper'

RSpec.describe ValidateDeals do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @deal = build(:deal_look_up, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=9").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          }
        ).to_return(status: 200, body: [{ id: 9, name: '<PERSON>', ownerId: 123 }].to_json, headers: {})
    end

    context 'valid input' do
      it 'returns blank if no users are passed' do
        expect(described_class.call(nil).result).to eq([])
      end

      it 'returns array of updated user look ups' do
        command = described_class.call([@deal])
        expect(command.result.class).to be Array
        expect(command.result.count).to be == 1
        expect(command.result[0].class).to be == LookUp
      end

      it 'returns lookups with updated names and owner id' do
        command = described_class.call([@deal])
        expect(command.result.first.name).to eq('<PERSON>e')
        expect(command.result.first.owner_id).to eq(123)
      end
    end

    context 'invalid input' do
      before do
        @invalid_deal = build(:deal_look_up, entity_id: 10, tenant_id: 9)
        stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=9,10").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(body: '', status: 404)
      end

      it 'raises InvalidDataError for non-existent user' do
        expect { described_class.call([@deal, @invalid_deal]) }.to raise_error(
          ExceptionHandler::InvalidDataError,
          "#{ErrorCode.invalid}||Invalid deal summary response."
        )
      end
    end
  end
end
