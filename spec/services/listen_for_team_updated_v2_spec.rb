# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForTeamUpdatedV2 do
  describe '#call' do
    let(:team) { create(:team, user_ids: []) }
    let(:payload) do
      team_updated_v2 = JSON.parse(file_fixture('listeners/team-updated-v2.json').read)
      team_updated_v2['entity']['id'] = team_updated_v2['oldEntity']['id'] = team_updated_v2['metadata']['entityId'] = team.id
      team_updated_v2['metadata']['tenantId'] = team.tenant_id

      team_updated_v2
    end

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic 'ex.iam'

      allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.iam', 'team.updated.v2', 'q.team.updated.v2.meetings')
          .and_yield(payload.to_json)
    end

    context 'valid input' do
      it 'updates user ids on team and team name' do
        expect(Rails.logger).to receive(:info).with("Received message for team id #{team.id} for team.updated.v2")
        described_class.call

        expect(team.reload.name).to eq('Updated team')
        expect(team.user_ids).to match_array([1])
      end
    end
  end
end
