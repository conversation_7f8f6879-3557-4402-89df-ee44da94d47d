require 'rails_helper'

RSpec.describe Field::UpdateFieldStatus do
  describe '#call' do
    let(:user)                { create(:user) }
    let(:another_user)        { create(:user, tenant_id: user.tenant_id) }
    let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name ) }
    let(:auth_data)           { ParseToken.call(valid_auth_token.token).result }
    let(:another_auth_data)   { ParseToken.call(another_valid_token.token).result }

    context 'valid' do
      before do
        Thread.current[:auth] = auth_data
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage))
      end

      context 'activate' do
        before do
          @field = create(:field, is_standard: false, active: false, created_by: another_user, updated_by: another_user, tenant_id: user.tenant_id)
        end

        it 'should activate field' do
          command = Field::UpdateFieldStatus.call(@field.id, ACTIVATE)

          expect(command.success?).to be(true)
          expect(@field.reload.active).to be(true)
          expect(@field.updated_by_id).to be(user.id)
        end
      end

      context 'deactivate' do
        before do
          @field = create(:field, is_standard: false, active: true, created_by: another_user, updated_by: another_user, tenant_id: user.tenant_id)
        end

        it 'should deactivate field' do
          command = Field::UpdateFieldStatus.call(@field.id, DEACTIVATE)

          expect(command.success?).to be(true)
          expect(@field.reload.active).to be(false)
          expect(@field.updated_by_id).to be(user.id)
          expect(@field.is_filterable).to be(false)
          expect(@field.is_sortable).to be(false)
          expect(@field.is_required).to be(false)
        end
      end
    end

    context 'invalid' do
      context 'invalid token' do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: User context missing in meeting field activate")
          expect { Field::UpdateFieldStatus.call(1, ACTIVATE) }.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'with invalid permission' do
        before {Thread.current[:auth] = another_auth_data }

        it 'should raise forbidden error' do
          expect { Field::UpdateFieldStatus.call(1, ACTIVATE) }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to update custom field.")
        end
      end

      context 'when invalid field id or system field' do
        before { Thread.current[:auth] = auth_data }

        context 'with invalid field id' do
          it 'should raise invalid data error' do
            expect { Field::UpdateFieldStatus.call(1, ACTIVATE) }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid field.")
          end
        end

        context 'with system field' do
          before { @field = create(:field, is_standard: true, created_by: user, updated_by: user) }

          it 'should raise invalid data error' do
            expect { Field::UpdateFieldStatus.call(@field.id, ACTIVATE) }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid field.")
          end
        end
      end
    end
  end
end
