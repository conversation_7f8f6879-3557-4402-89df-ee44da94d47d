require 'rails_helper'

RSpec.describe Field::DeactivateCustomFields do
  describe '#call' do
    before do
      @tenant_id = rand(101..200)
    end

    context 'when active custom fields are present on tenant' do
      before do
        @custom_fields = create_list(:custom_field, 3, tenant_id: @tenant_id, is_filterable: true)
      end

      it 'should deactivate active custom fields' do
        expect(PublishEvent).to receive(:call).exactly(12).times
        expect { described_class.call(@tenant_id) }.to change { Field.where(tenant_id: @tenant_id, active: false, is_standard: false).count }.by(3)
      end

      it 'should publish events for all deactivated custom fields' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2)).exactly(3).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated)).exactly(3).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage)).exactly(3).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated)).exactly(3).times
        described_class.call(@tenant_id)

        @custom_fields.each do |custom_field|
          custom_field.reload
          expect(custom_field.active).to eq(false)
          expect(custom_field.is_filterable).to eq(false)
          expect(custom_field.is_sortable).to eq(false)
          expect(custom_field.is_required).to eq(false)
        end
      end
    end

    context 'when active custom fields are not present on tenant' do
      it 'should do nothing' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage)).exactly(0).times
        described_class.call(@tenant_id)

        expect { described_class.call(@tenant_id) }.not_to change { Field.where(tenant_id: @tenant_id, active: false, is_standard: false).count }
      end
    end
  end
end
