require 'rails_helper'

RSpec.describe Field::CreateField do
  describe '#call' do
    let(:user)                { create(:user) }
    let(:another_user)        { create(:user, tenant_id: user.tenant_id) }
    let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name ) }
    let(:auth_data)           { ParseToken.call(valid_auth_token.token).result }
    let(:another_auth_data)   { ParseToken.call(another_valid_token.token).result }
    let(:params) {
      {
        description: 'This is custom field',
        displayName: 'Custom Picklist Field',
        filterable: true,
        sortable: false,
        required: false,
        type: 'PICK_LIST',
        pickLists: [
          {
            id: nil,
            name: nil,
            displayName: 'Picklist Value 1'
          },
          {
            id: nil,
            name: nil,
            displayName: 'Picklist Value 2'
          }
        ]
      }
    }

    def create_params(params)
      params.permit(
        :displayName,
        :description,
        :filterable,
        :sortable,
        :required,
        :type,
        {
          pickLists: [
            :id,
            :name,
            :displayName
          ]
        }
      )
    end

    context 'valid' do
      before do
        Thread.current[:auth] = auth_data
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldCreated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldCreatedV2))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage))
      end

      it 'should create custom field correctly' do
        command = described_class.call(create_params(ActionController::Parameters.new(params)))
        field = command.result

        expect(command.success?).to be(true)
        expect(field.display_name).to eq(params[:displayName])
        expect(field.description).to eq(params[:description])
        expect(field.internal_name).to start_with('cf')
        expect(field.field_type).to eq('PICK_LIST')
        expect(field.picklist.present?).to be(true)
        expect(field.created_by_id).to be(auth_data.user_id)
        expect(field.updated_by_id).to be(auth_data.user_id)
        expect(field.tenant_id).to be(auth_data.tenant_id)
        expect(field.system_default?).to be(false)

        picklist = field.picklist
        expect(picklist.display_name).to end_with('Picklist')
        expect(picklist.internal_name).to start_with('cp')
        expect(picklist.picklist_values.count).to be(params[:pickLists].count)

        picklist.picklist_values.each do |picklist_value|
          expect(picklist_value.internal_name).to start_with('cpv')
          expect(picklist_value.disabled).to be(false)
        end
      end
    end

    context 'invalid' do
      context 'when duplicate picklist value' do
        before do
          Thread.current[:auth] = auth_data
          params[:pickLists] << params[:pickLists].first
        end

        it 'should raise invalid data with cannot create entity error code' do
          expect(PublishEvent).to receive(:call).exactly(0).times
          expect do
            described_class.call(create_params(ActionController::Parameters.new(params)))
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.cannot_create_entity}||Invalid field.")
        end
      end

      context 'unauthorised user' do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect(Rails.logger).to receive(:error).with('Unauthorised: User context missing in Create Field')
          expect do
            described_class.call(create_params(ActionController::Parameters.new(params)))
          end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'user without custom field write' do
        before { Thread.current[:auth] = another_auth_data }

        it 'should raise authentication error' do
          expect do
            described_class.call(create_params(ActionController::Parameters.new(params)))
          end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to create custom field.")
        end
      end

      context 'when invalid field' do
        before { Thread.current[:auth] = auth_data }

        it 'should raise invalid data error' do
          expect(Rails.logger).to receive(:error).with("Validation failed: Display name can't be blank, Internal name custom field name should start with cf or should have valid characters")
          expect do
            described_class.call(create_params(ActionController::Parameters.new(params.merge(displayName: ''))))
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.cannot_create_entity}||Invalid field.")
        end
      end
    end
  end
end
