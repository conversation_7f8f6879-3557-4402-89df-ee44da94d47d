require 'rails_helper'

RSpec.describe ParticipantAddedEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      allow(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata))
      allow(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
      @meeting = create(:meeting, owner: @user)
      @lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON> Lead")
      owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
      @meeting.participants = [owner_lookup, @lead_lookup]
      @meeting.save
      allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
    end

    def stub_related_events(lead: 0, contact: 0, deal: 0, company: 0)
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(lead).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(contact).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToDeal)).exactly(deal).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToCompany)).exactly(company).times
    end

    context "for meeting create with participants" do
      it 'should call PublishEvent for each participants with each user' do
        @meeting.participants << build(:user_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "User John")
        stub_related_events(lead: 1)
        ParticipantAddedEventPublisher.call(@meeting)
      end
    end

    context "for meeting create with related_to" do
      it 'should call PublishEvent for each participants with each user' do
        @meeting.participants << build(:user_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "User John")
        @meeting.related_to = [@lead_lookup]

        stub_related_events(lead: 1)
        ParticipantAddedEventPublisher.call(@meeting)
      end
    end

    context "for meeting update with participants" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @contact_lookup = build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "John Contact")
        @company_lookup = build(:company_look_up, entity_id: 14, tenant_id: @user.tenant_id, name: "John Company")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
      end

      context 'with new users added' do
        before do
          @meeting.participants << @deal_lookup
          @meeting.participants << @contact_lookup
          @meeting.participants << @company_lookup
          @meeting.save
        end
        it 'should call PublishEvent for each entities with new user' do
          stub_related_events(lead: 1, contact: 1, deal: 1, company: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@invitee])
        end
      end
      context 'with new entities added' do
        before do
          @meeting.participants << @invitee
          @meeting.save
        end
        it 'should call PublishEvent for each new entities with each user' do
          stub_related_events(contact: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@deal_lookup, @contact_lookup])
        end
      end

      context 'with one new entities added and one new user added' do
        it 'should call PublishEvent for new entity with each user and old entity with new user' do
          stub_related_events(lead: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@deal_lookup, @invitee])
        end
      end
    end

    context "for meeting update with related_to" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @contact_lookup = build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "John Contact")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
      end

      context 'with new entity added' do
        before do
          @meeting.participants << @invitee
          @meeting.save
        end

        it 'should call PublishEvent for each entities with new user' do
          stub_related_events(contact: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@contact_lookup])
        end
      end

      context 'with new entities added' do
        before do
          @meeting.participants << @invitee
          @meeting.save
        end

        it 'should call PublishEvent for each new entities with each user' do
          stub_related_events(contact: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@deal_lookup, @contact_lookup])
        end
      end

      context 'with one new entity added in related_to and one new user added in participants' do
        before do
          @meeting.participants << @invitee
          @meeting.related_to << @deal_lookup
          @meeting.save
        end

        it 'should publish event for new related entity with each user and old related entity with new user' do
          stub_related_events(lead: 1, contact: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@contact_lookup, @invitee])
        end
      end

      context 'with related to entity already present and tried to add user participant' do
        before do
          @meeting.related_to << @deal_lookup
          @meeting.save
        end

        it 'should publish events for existing non user entities for new user' do
          stub_related_events(lead: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@invitee])
        end
      end

      context 'with duplicate entity in participants and related to' do
        before do
          @meeting.related_to << @lead_lookup
          @meeting.related_to << @deal_lookup
          @meeting.save
        end

        it 'should publish events once per new user per old entity' do
          stub_related_events(lead: 1, deal: 1)
          ParticipantAddedEventPublisher.call(@meeting, [@invitee])
        end
      end
    end
  end
end
