#frozen_string_literal: true

require 'rails_helper'

RSpec.describe V2::SearchNotes do
  let(:user)                          { create(:user) }
  let(:another_user)                  { create(:user, tenant_id: user.tenant_id) }
  let(:valid_auth_token)              { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)                     { ParseToken.call(valid_auth_token.token).result }
  let(:invalid_auth_token)            { build(:auth_token, :without_note_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data)             { ParseToken.call(invalid_auth_token.token).result }

  describe '#call' do
    let(:params) { ActionController::Parameters.new({}).permit! }
    
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end
    
    context 'when authorised user' do
      let(:meeting) { create(:meeting, owner: user) }
      let!(:user_note) { create(:note, created_by: user, meeting_id: meeting.id, tenant_id: user.tenant_id) }
      let!(:other_note) { create(:note, created_by: another_user, meeting_id: meeting.id, tenant_id: user.tenant_id) }

        it 'returns notes' do
          search_response = described_class.call(params).result

          expect(search_response.map(&:id)).to match_array([user_note.id, other_note.id])
        end
    end

    context 'when user does not have read on notes' do
      before { Thread.current[:auth] = invalid_auth_data }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read notes")
        expect { described_class.call(params) }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end
  end
end
