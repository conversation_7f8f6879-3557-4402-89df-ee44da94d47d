# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeactivateConnectedAccount do
  describe '#call' do
    before do
      @user = create(:user)
      create(:connected_account, active: true, user_id: @user.id, tenant_id: @user.tenant_id)
      @ms_account = create(:connected_account, active: true, user_id: @user.id, tenant_id: @user.tenant_id, provider_name: MICROSOFT_TEAMS_PROVIDER, provider_subscription_resource_id: 'webhook-id')
    end

    context 'valid input' do
      context '#disconnect_account' do
        before do
          expect(PublishEvent).not_to receive(:call)
          @service = DeactivateConnectedAccount.call(@user.id, @user.tenant_id, GOOGLE_PROVIDER, 'USER', '<EMAIL>', '<EMAIL>')
        end

        it 'keeps the count of connected account same' do
          expect(ConnectedAccount.count).to eq(2)
        end

        it 'checks for data' do
          acct = ConnectedAccount.first
          expect(acct.tenant_id).to eq(@user.tenant_id)
          expect(acct.user_id).to eq(@user.id)
          expect(acct.provider_name).to eq(GOOGLE_PROVIDER)
          expect(acct.active).to eq(false)
        end

        it 'checks for service response' do
          expect(@service.success?).to eq(true)
          expect(@service.result).to eq(true)
        end

      end

      context 'when account is of Microsoft' do
        it 'unsubscribes webhook' do
          stub_request(:delete, "https://graph.microsoft.com/v1.0/subscriptions/webhook-id").
          with(
            headers: {
              'Authorization'=>"Bearer #{@ms_account.fetch_access_token}",
            }).
          to_return(status: 204, body: {}.to_json, headers: {})

          DeactivateConnectedAccount.call(@user.id, @user.tenant_id, MICROSOFT_TEAMS_PROVIDER, 'USER', '<EMAIL>', '<EMAIL>')

          expect(@ms_account.reload.active).to be_falsy
          expect(@ms_account.provider_subscription_resource_id).to be_nil
        end
      end

      context 'when account is auto disconnected' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::CalenderDisconnected)).exactly(1).times
          @service = DeactivateConnectedAccount.call(@user.id, @user.tenant_id, GOOGLE_PROVIDER, 'AUTO', '<EMAIL>', '<EMAIL>')
        end

        it 'publishes calender disconnect event and performs task' do
          expect(@service.success?).to eq(true)
          expect(@service.result).to eq(true)
        end
      end
    end

    context 'invalid input' do
      context 'when connected account is not found' do
        it 'does raise the connected account not found exception' do
          @user.connected_accounts.destroy_all
          expect do
            DeactivateConnectedAccount.call(@user.id, @user.tenant_id, GOOGLE_PROVIDER, 'USER', '<EMAIL>', '<EMAIL>')
          end.to raise_error(ExceptionHandler::ConnectedAccountNotFound, "#{ErrorCode.connected_account_not_found}||Connected Account not found.")
        end
      end
    end
  end
end
