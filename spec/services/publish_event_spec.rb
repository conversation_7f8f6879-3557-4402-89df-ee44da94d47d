require 'rails_helper'
require 'bunny-mock'
#BunnyMock::use_bunny_queue_pop_api = true

RSpec.describe PublishEvent do
  describe '#call' do
    before do
      connection = BunnyMock.new
      #allow(Bunny).to receive(:new).with("amqp://#{ENV['RABBITMQ_USER']}:#{ENV['RABBITMQ_PASSWORD']}@#{ENV['RABBITMQ_HOST']}:#{ENV['RABBITMQ_PORT']}").and_return(connection)
      @channel = connection.start.channel
      #allow(connection).to receive(:start).and_return( nil )
      #allow(connection).to receive(:create_channel).and_return( @channel )
      @exchange = @channel.topic MEETING_EXCHANGE
      BunnyMock.use_bunny_queue_pop_api = true
    end

    context 'valid input' do
      context 'meeting participant user added event' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, participant_user: true)
          @participant = @meeting.participants.first
          @event = Event::MeetingParticipantUserAdded.new(@meeting, @meeting.participants.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.added' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting participant user removed event' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, participant_user: true)
          @participant = @meeting.participants.first
          @event = Event::MeetingParticipantUserRemovedEvent.new(@meeting, @meeting.participants.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.removed' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting related to lead' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, related_lead: true, participant_user: true)
          @participant = @meeting.related_to.first
          @event = Event::MeetingRelatedToLead.new(@meeting, @meeting.participants.first, @meeting.related_to.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.removed' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting related to deal' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, related_deal: true, participant_user: true)
          @participant = @meeting.related_to.first
          @event = Event::MeetingRelatedToDeal.new(@meeting, @meeting.participants.first, @meeting.related_to.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.removed' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting related to contact' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, related_contact: true, participant_user: true)
          @participant = @meeting.related_to.first
          @event = Event::MeetingRelatedToContact.new(@meeting, @meeting.participants.first, @meeting.related_to.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.removed' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting related to company' do
        before do
          @user = create(:user)
          @meeting = build(:meeting_with_associated_entities, related_company: true, participant_user: true)
          @participant = @meeting.related_to.first
          @event = Event::MeetingRelatedToCompany.new(@meeting, @meeting.participants.first, @meeting.related_to.first)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.user.participant.removed' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end
      context 'meeting unrelated to lead' do
        pending "#TODO think how to trigger this event?"
      end

      context 'meeting scheduled' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner:@user)
          @participant = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @participant
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingScheduled.new(@meeting)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.scheduled' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting scheduled with participant' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner: @user)
          @owner = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @owner
          @participant = @meeting.participants.first
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingScheduledWithParticipant.new(@meeting, @participant)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.scheduled.with.participant' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting rescheduled' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner: @user)
          @participant = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @participant
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingReScheduled.new(@meeting)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.rescheduled' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting rescheduled with participant' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner: @user)
          @owner = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @owner
          @participant = @meeting.participants.first
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingReScheduledWithParticipant.new(@meeting, @participant)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.rescheduled.with.participant' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting participant rsvp updated' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner: @user)
          @owner = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @owner
          @participant = @meeting.participants.first
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingParticipantRsvpUpdated.new(@meeting, @participant)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.participant.rsvp.updated' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting cancelled' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner:@user)
          @participant = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @participant
          @meeting.status = CANCELLED
          @meeting.save!
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
          @event = Event::MeetingCancelled.new(@meeting)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.cancelled' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting cancelled with participant' do
        before do
          @user = create(:user)
          @meeting = create(:meeting_with_associated_entities, owner: @user)
          @owner = build(:user_look_up, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
          @meeting.participants << @owner
          @participant = @meeting.participants.first
          @meeting.status = CANCELLED
          @meeting.save!
          @event = Event::MeetingCancelledWithParticipant.new(@meeting, @participant)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.cancelled.participant' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting created v2' do
        before do
          user = create(:user)
          Thread.current[:auth] = build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
          @meeting = create(:meeting_with_associated_entities, owner:user)
          participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: user.id, name: user.name)
          @meeting.participants << participant

          @event = Event::MeetingCreated.new(@meeting, user.id)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.created.v2' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
        end
      end

      context 'meeting updated v2' do
        before do
          user = create(:user)
          another_user = create(:user, tenant_id: user.tenant_id)
          Thread.current[:auth] = build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
          @old_meeting = create(:meeting_with_associated_entities, owner:user)
          participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: user.id, name: user.name)
          @old_meeting.participants << participant
          @new_meeting = Meeting.find(@old_meeting.id)
          @new_meeting.update(status: MISSED)
          @new_meeting.participants << build(:user_look_up, tenant_id: another_user.tenant_id, entity_id: another_user.id, name: another_user.name)

          @old_meeting = MeetingSerializer.call(@old_meeting, nil, false, nil, true).result
          @event = Event::MeetingUpdated.new(@new_meeting, @old_meeting, user.id)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.updated.v2' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
          parsed_payload = JSON.parse(payload)
          expect(parsed_payload['entity']['participants'].count).to eq(3)
          expect(parsed_payload['entity']['status']).to eq(MISSED)
          expect(parsed_payload['oldEntity']['participants'].count).to eq(2)
          expect(parsed_payload['oldEntity']['status']).to eq(SCHEDULED)
        end
      end

      context 'meeting deleted v2' do
        before do
          user = create(:user)
          Thread.current[:auth] = build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
          meeting = create(:meeting_with_associated_entities, owner:user)
          participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: user.id, name: user.name)
          meeting.participants << participant
          @old_serialized_meeting = MeetingSerializer.call(meeting, nil, false, nil, true).result

          @event = Event::MeetingDeleted.new(@old_serialized_meeting, user.id)
          @queue = @channel.queue @event.routing_key
          @queue.bind @exchange, routing_key: @event.routing_key
          allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
          PublishEvent.call(@event)
        end

        it 'raises an event in the Meeting Exchange & routes it to queue meeting.deleted.v2' do
          expect(@queue.message_count).to eq(1)
        end
        it 'publishes the correct payload' do
          payload = @queue.pop.last
          expect(payload).to eq(@event.to_json)
          parsed_payload = JSON.parse(payload)
          expect(parsed_payload['entity']).to be(nil)
          expect(parsed_payload['oldEntity']).to eq(@old_serialized_meeting)
        end
      end
    end
  end
end
