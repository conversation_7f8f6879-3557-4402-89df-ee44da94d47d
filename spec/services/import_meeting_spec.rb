# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ImportMeeting do
  let(:meeting_to_import) do
    {
      "jobId": 1,
      "meeting": {
        "title": "check With PickList",
        "description": "With Custom PickList for Meeting",
        "allDay": false,
        "timezone": {
          "id": 372,
          "name": "Asia/Calcutta"
        },
        "location": "Pune",
        "medium": "OFFLINE",
        "customFieldValues": {
          "cfMeetingCustom": {
            "id": 325305,
            "name": "chcek2"
          }
        },
        "status": "scheduled",
        "organizer": "<EMAIL>",
        "relatedToLeads": [
          "<EMAIL>","<EMAIL>"],
          "relatedToContacts": [
            "<EMAIL>","<EMAIL>"],
            "relatedToCompanies": [
              "<EMAIL>","<EMAIL>"],
              "participants": [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
              ],
              "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
              "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
              "owner": "<EMAIL>"
      }
    }
  end

  def import_meeting_params(params)
    params.permit(
      :jobId,
      meeting: [
        :title,
        :description,
        :allDay,
        :organizer,
        {
          timezone: [
            :id,
            :name
          ]
        },
        :location,
        :medium,
        {
          customFieldValues: {}
        },
        :status,
        {
          relatedToLeads: []
        },
        {
          relatedToContacts: []
        },
        {
          relatedToCompanies: []
        },
        {
          participants: []
        },
        :from,
        :to,
        :owner
      ]
    )
  end

  def stub_entity_search_request(emails:, entity_type:, response:)
    rules =
      emails.map do |email|
        {
          id: 'multi_field',
          field: 'multi_field',
          type: 'multi_field',
          input: 'multi_field',
          operator: 'multi_field',
          value: email
        }
      end

    entity_request_body = {
      fields: entity_type == LOOKUP_COMPANY ? %w[id firstName lastName emails ownedBy] : %w[id firstName lastName emails ownerId],
      jsonRule: {
        rules: rules,
        condition: 'OR',
        valid: true
      }
    }

    stub_request(:post, "#{SERVICE_SEARCH}/v1/search/#{entity_type}?page=0&size=100&sort=updatedAt,desc").
      with(
        body: entity_request_body,
        headers: {
          'Authorization' => "Bearer #{@token}",
          'Content-Type' => 'application/json',
        }).
        to_return(status: 200, body: response.to_json, headers: {})
  end

  describe '#call' do
    context 'when token is invalid' do
      before do
        thread = Thread.current
        thread[:auth] = nil
        thread[:token] = nil
      end

      it 'raises AuthenticationError with unauthorized access error' do
        expect { described_class.call({}) }.to raise_error(ExceptionHandler::AuthenticationError, "01501005||Unauthorized access.")
      end
    end

    context 'with valid token' do
      before do
        @user = create(:user)
        @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
        other_field = create(:custom_field, tenant_id: @user.tenant_id, display_name: 'Meeting Custom', field_type: 'PICK_LIST')
        picklist = create(:picklist, tenant_id: @user.tenant_id, internal_name: other_field.internal_name, field: other_field)
        create(:picklist_value, tenant_id: @user.tenant_id, internal_name: 'chcek2', picklist: picklist, id: 325305)

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage").
          with(headers: { 'Authorization' => "Bearer #{@token}" }).
          to_return(status: 200, body: { records: { used: 50, total: 100 } }.to_json)
      end

      context 'when meeting medium is not offline' do
        it 'raises invalid meeting medium error' do
          meeting_to_import[:meeting][:medium] = GOOGLE_PROVIDER
          expect {
            described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting medium.")
        end
      end

      context 'when email is invalid' do
        context 'and that emails is from relatedToLeads' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:relatedToLeads] = %w[<EMAIL> john]
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john' in relatedToLeads.")
          end
        end

        context 'and that emails is from relatedToContacts' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:relatedToContacts] = %w[<EMAIL> john]
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john' in relatedToContacts.")
          end
        end

        context 'and that emails is from relatedToCompanies' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:relatedToCompanies] = %w[<EMAIL> john]
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john' in relatedToCompanies.")
          end
        end

        context 'and that emails is from participants' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:participants] = %w[<EMAIL> john]
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john' in participants.")
          end
        end

        context 'and that emails is from owner' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:owner] = 'john@lead'
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john@lead' in owner.")
          end
        end

        context 'and that emails is from organizer' do
          it 'raises InvalidDataError with error message' do
            meeting_to_import[:meeting][:organizer] = 'john'
            expect {
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid email - 'john' in organizer.")
          end
        end
      end

      context 'when lead with given emails is not found' do
        it 'raises invalid lead email error' do
          meeting_to_import[:meeting][:relatedToLeads] << '<EMAIL>'
          lead_request_body = {
            fields: %w[id firstName lastName emails ownerId],
            jsonRule: {
              rules: [
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                }
              ],
              condition: 'OR',
              valid: true
            }
          }

          lead_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'lead',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'lead',
                id: 2,
                ownerId: 234
              },
            ]
          }

          stub_request(:post, "#{SERVICE_SEARCH}/v1/search/lead?page=0&size=100&sort=updatedAt,desc").
            with(
              body: lead_request_body,
              headers: {
                'Authorization' => "Bearer #{@token}",
                'Content-Type' => 'application/json',
              }).
              to_return(status: 200, body: lead_response.to_json, headers: {})

              expect {
                described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
              }.to raise_error(ExceptionHandler::InvalidDataError, "01503001||lead with emails '<EMAIL>' not found.")
        end
      end

      context 'when contact with given emails is not found' do
        it 'raises invalid contact email error' do
          lead_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'lead',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'lead',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

          meeting_to_import[:meeting][:relatedToContacts] << '<EMAIL>'
          contact_request_body = {
            fields: %w[id firstName lastName emails ownerId],
            jsonRule: {
              rules: [
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                }
              ],
              condition: 'OR',
              valid: true
            }
          }

          contact_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'contact',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'contact',
                id: 2,
                ownerId: 234
              },
            ]
          }

          stub_request(:post, "#{SERVICE_SEARCH}/v1/search/contact?page=0&size=100&sort=updatedAt,desc").
            with(
              body: contact_request_body,
              headers: {
                'Authorization' => "Bearer #{@token}",
                'Content-Type' => 'application/json',
              }).
              to_return(status: 200, body: contact_response.to_json, headers: {})

              expect {
                described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
              }.to raise_error(ExceptionHandler::InvalidDataError, "01503001||contact with emails '<EMAIL>' not found.")
        end
      end

      context 'when company with given emails is not found' do
        it 'raises invalid company email error' do
          lead_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'lead',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'lead',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

          contact_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'contact',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'contact',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)
          meeting_to_import[:meeting][:relatedToCompanies] << '<EMAIL>'
          company_request_body = {
            fields: %w[id firstName lastName emails ownedBy],
            jsonRule: {
              rules: [
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                },
                {
                  id: 'multi_field',
                  field: 'multi_field',
                  type: 'multi_field',
                  input: 'multi_field',
                  operator: 'multi_field',
                  value: '<EMAIL>'
                }
              ],
              condition: 'OR',
              valid: true
            }
          }

          company_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'company',
                id: 1,
                ownerId: 123,
                ownedBy: {
                  id: 123,
                  name: 'User 123'
                }
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'company',
                id: 2,
                ownerId: 234,
                ownedBy: {
                  id: 234,
                  name: "User 234"
                }
              },
            ]
          }

          stub_request(:post, "#{SERVICE_SEARCH}/v1/search/company?page=0&size=100&sort=updatedAt,desc").
            with(
              body: company_request_body,
              headers: {
                'Authorization' => "Bearer #{@token}",
                'Content-Type' => 'application/json',
              }
            ).to_return(status: 200, body: company_response.to_json, headers: {})

          expect {
            described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
          }.to raise_error(ExceptionHandler::InvalidDataError, "01503001||company with emails '<EMAIL>' not found.")
        end
      end

      context 'when replacing participants' do
        before do
          lead_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'lead',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'lead',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

          contact_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'contact',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'contact',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

          company_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'company',
                id: 1,
                ownerId: 123
              },
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'tony',
                lastName: 'company',
                id: 2,
                ownerId: 234
              },
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_COMPANY, response: company_response)

          user_rule = {emailIds: ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']}
          user_response = [
              {
                emailId: '<EMAIL>',
                firstName: 'john',
                lastName: 'user',
                id: 1
              }
            ]
          
          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
            with(
              body: user_rule.to_json,
              headers: {
                'Authorization': "Bearer #{@token}",
                'Content-Type': 'application/json',
              }
            ).to_return(status: 200, body: user_response.to_json, headers: {})

          contact_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'contact',
                id: 1,
                ownerId: 123
              }
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

          lead_response = {
            content: [
              {
                emails: [
                  { type: 'OFFICE', value: '<EMAIL>', primary:true }
                ],
                firstName: 'john',
                lastName: 'lead',
                id: 1,
                ownerId: 123
              }
            ]
          }
          stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

          user_rule = {emailIds: ['<EMAIL>']}

          user_response = [
              {
                emailId: '<EMAIL>',
                firstName: 'john',
                lastName: 'user',
                id: 1
              }
          ]

          organizer_rule = {emailIds: ['<EMAIL>']}

          organizer_respose = [
              {
                emailId: '<EMAIL>',
                firstName: 'john',
                lastName: 'user',
                id: 1,
                ownerId: 123
              }
            ]

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
            with(
              body: user_rule.to_json,
              headers: {
                'Authorization': "Bearer #{@token}",
                'Content-Type': 'application/json',
              }
            ).to_return(status: 200, body: user_response.to_json, headers: {})


          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
            with(
              body: organizer_rule.to_json,
              headers: {
                'Authorization': "Bearer #{@token}",
                'Content-Type': 'application/json',
              }
            ).to_return(status: 200, body: organizer_respose.to_json, headers: {})

          stub_request(:get, "http://localhost:8081/v1/users/summary?id=1").
            with(
              headers: {
                'Authorization'=> "Bearer #{@token}"
              }
            ).to_return(status: 200, body: [{
                id: @user.id,
                name: "user owner",
                email: { primary: true, value: "<EMAIL>" }
              }].to_json, headers: {}
            )
        end

        it 'entities are searched in sequence of user, contact and lead and events are published' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(1).times
          stub_request(:get, "#{SERVICE_IAM}/v1/api-keys/meeting-rsvp").
            with(
              headers: {
                Authorization: "Bearer #{@token}"
              }
            ).to_return(status: 200, body: { apiKey: 'testtoken' }.to_json, headers: {})

          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
          described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
        end

        it 'creates meeting with given details' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(1).times
          stub_request(:get, "#{SERVICE_IAM}/v1/api-keys/meeting-rsvp").
            with(
              headers: {
                'Authorization'=> "Bearer #{@token}"
              }
            ).to_return(status: 200, body: { apiKey: 'testtoken' }.to_json, headers: {})

          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
          described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))

          expect(Meeting.count).to eq(1)
          meeting = Meeting.last
          expect(meeting.title).to eq('check With PickList')
          expect(meeting.medium).to eq('OFFLINE')
          expect(meeting.status).to eq('scheduled')
          expect(meeting.imported_by_id).to eq(@user.id)
          expect(meeting.organizer).not_to be_nil
          expect(meeting.organizer.entity).to eq 'user_1'
        end

        it 'creates participants and related to' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(1).times
          stub_request(:get, "#{SERVICE_IAM}/v1/api-keys/meeting-rsvp").
            with(
              headers: {
                'Authorization'=> "Bearer #{@token}"
              }
            ).to_return(status: 200, body: { apiKey: 'testtoken' }.to_json, headers: {})

          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(3).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
          described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))

          meeting = Meeting.last
          expect(meeting.participants.count).to eq(4)
          expect(meeting.participants.map(&:entity_type)).to match_array(%w[user lead contact external])
          expect(meeting.participants.map(&:owner_id)).to match_array([nil, nil, 123, 123])

          expect(meeting.related_to.count).to eq(6)
          expect(meeting.related_to.map(&:entity_type)).to match_array(%w[lead lead contact contact company company])
          expect(meeting.related_to.map(&:owner_id)).to match_array([123, 123, 123, 234, 234, 234])
        end

        context 'when validating owner' do
          context 'if user is not searched' do
            it 'raises invalid owner email error' do
              meeting_to_import[:meeting][:owner] = '<EMAIL>'
              user_rule = {emailIds: ['<EMAIL>']}
              user_response = []
              stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
                with(
                  body: user_rule.to_json,
                  headers: {
                    'Authorization': "Bearer #{@token}",
                    'Content-Type': 'application/json',
                  }
                ).to_return(status: 200, body: user_response.to_json, headers: {})

              expect {
                described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
              }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Owner user with emails '<EMAIL>' not found.")
            end
          end

          context 'when owner is empty' do
            it 'makes current user as owner' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(1).times
              stub_request(:get, "#{SERVICE_IAM}/v1/api-keys/meeting-rsvp").
                with(
                  headers: {
                    'Authorization'=> "Bearer #{@token}"
                  }
                ).to_return(status: 200, body: '{"apiKey": "testtoken"}', headers: {})

              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(4).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(2).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(2).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToCompany)).exactly(2).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(3).times
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

              meeting_to_import[:meeting][:owner] = nil
              described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
              expect(Meeting.last.owner_id).to eq(@user.id)
            end
          end
        end

        context 'when imported meeting does not have organizer and owner is present and that owner is not present in participants' do
          it 'adds that owner in participants' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(1).times
            stub_request(:get, "#{SERVICE_IAM}/v1/api-keys/meeting-rsvp").
              with(
                headers: {
                  'Authorization'=> "Bearer #{@token}"
                }
              ).to_return(status: 200, body: '{"apiKey": "testtoken"}', headers: {})

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(4).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToCompany)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(3).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times

            meeting_to_import[:meeting][:owner] = '<EMAIL>'
            meeting_to_import[:meeting][:organizer] = nil
            user_rule = [
              {
                id: 'email',
                field: 'email',
                type: 'string',
                operator: 'in',
                value: '<EMAIL>'
              }
            ]

            user_response = {
              content: [
                {
                  emails: [
                    { type: 'OFFICE', value: '<EMAIL>', primary:true }
                  ],
                  firstName: 'john',
                  lastName: 'user',
                  id: 111
                }
              ]
            }

            stub_request(:post, "#{SERVICE_IAM}/v1/users/search?page=0&size=100&sort=updatedAt,desc").with(
              body: {
                fields: %w[id firstName lastName email],
                jsonRule: {
                  rules: user_rule,
                  condition: 'AND',
                  valid: true
                }
              },
              headers: {
                'Authorization': "Bearer #{@token}",
                'Content-Type': 'application/json',
              }
            ).to_return(status: 200, body: user_response.to_json, headers: {})

            described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
            meeting = Meeting.last
            expect(meeting.participants.pluck(:entity).include?("user_#{meeting.owner_id}"))
          end
        end
      end
    end

    context 'when tenant usage limit is reached' do
      before do
        @user = create(:user)
        @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token

        usage_response = {
          "records" => {
            "used" => 100,
            "total" => 100
          }
        }

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(status: 200, body: usage_response.to_json, headers: {})
      end

      it 'should raise usage limit exceeded error' do
        expect {
          described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
        }.to raise_error(ExceptionHandler::UsageLimitExceeded)
      end

      it 'should not create any meeting' do
        expect {
          begin
            described_class.call(import_meeting_params(ActionController::Parameters.new(meeting_to_import)))
          rescue ExceptionHandler::UsageLimitExceeded
            # Expected error, continue with test
          end
        }.not_to change(Meeting, :count)
      end
    end
  end
end
