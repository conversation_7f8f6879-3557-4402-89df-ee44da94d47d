# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForDealReassign do
  describe '#call' do
    let(:payload) { JSON.parse(file_fixture('deal-reassigned-payload.json').read) }
    let(:lookup) { create(:deal_look_up, tenant_id: payload['deal']['tenantId'], entity_id: payload['id']) }

    context 'valid input' do
      context 'when payload is received for deal reassigned event' do
        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with('ex.deal', 'deal.reassigned', 'q.deal.reassigned.meetings')
            .and_yield(payload.to_json)
          lookup
        end

        it 'updates the deal lookup with new owner' do
          expect(Rails.logger).to receive(:info).with("Received message ex.deal for deal.reassigned")
          described_class.call

          expect(LookUp.where(entity: "deal_#{lookup.entity_id}").pluck(:owner_id)).to match([12])
        end
      end
    end
  end
end
