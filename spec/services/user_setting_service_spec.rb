# frozen_string_literal: true

require 'rails_helper'

RSpec.describe  UserSettingService do

  describe "#fetch" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "Success" do
      let(:user_data){ file_fixture('user-profile-response.json').read }

      context 'with no args' do
        before do
          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: user_data, headers: {})
        end

        it 'should return dateformat, preffered timezone and permissions of current user' do
          response = UserSettingService.new.fetch
          expect(response[:timezone]).to eq('Africa/Nairobi')
          expect(response[:permissions]).not_to be_nil
          expect(response[:date_format]).to eq('MMM D, YYYY [at] h:mm a')
        end
      end

      context 'with ars' do
        before do
          stub_request(:get, "#{SERVICE_IAM}/v1/users/123").with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: user_data, headers: {})
        end

        it 'gets details of given user' do
          response = UserSettingService.new.fetch(123)
          expect(response[:timezone]).to eq('Africa/Nairobi')
          expect(response[:permissions]).not_to be_nil
          expect(response[:date_format]).to eq('MMM D, YYYY [at] h:mm a')
        end
      end
    end
  end
end
