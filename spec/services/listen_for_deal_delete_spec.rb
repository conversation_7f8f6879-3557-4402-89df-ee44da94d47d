require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForDealDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @deal_lookup = create(:deal_look_up, tenant_id: @user.tenant_id)
      @channel = connection.start.channel
      @exchange = @channel.topic DEAL_EXCHANGE
    end

    context 'valid input' do
      context 'for deal deleted event' do
        let(:payload) { { "id" => @deal_lookup.entity_id, "tenantId" => @deal_lookup.tenant_id, "userId" => @user.id}.to_json }

        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: DEAL_DELETED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe).with(DEAL_EXCHANGE, DEAL_DELETED_EVENT, DEAL_DELETED_QUEUE).and_yield(payload.to_s)
        end

        context "Meeting has other lookup and contact association along with current deal" do
          it "Shouldn't delete meeting and unrelate current deal" do
            allow(PublishEvent).to receive(:call)

            meeting = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            contact_lookup = build(:deal_look_up, entity_id: 16, tenant_id: @user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
            meeting.participants << [invitee, contact_lookup]
            meeting.related_to << [@deal_lookup, contact_lookup]

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(5)
            expect(LookUp.find_by(id: @deal_lookup.id)).to eq(@deal_lookup)

            ListenForDealDelete.call()

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(4)
            expect(LookUp.find_by(id: @deal_lookup.id)).to be_nil
          end
        end
      end
    end
  end
end
