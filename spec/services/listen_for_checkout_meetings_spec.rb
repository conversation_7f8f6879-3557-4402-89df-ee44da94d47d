# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForCheckoutMeetings do
  describe '#call' do
    before do
      @user = create(:user)
      @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user, status: CONDUCTED)
      create(:meeting_attendance, meeting_id: @meeting.id, user_id: @user.id)
    end

    context 'valid input' do
      context 'for deal deleted event' do
        let(:payload) {
          {
            meetingIds: [@meeting.id],
            tenantId: @user.tenant_id,
            performedBy: @user.id,
            latitude: 18.8879,
            longitude: 19.983,
            markedAt: '2024-01-25T11:30:10.607Z'
          }.to_json
        }

        before do
          allow(RabbitmqConnection).to receive(:subscribe).with(FIELD_SALES_EXCHANGE, FIELD_SALES_CHECKOUT_MEETINGS_EVENT, FIELD_SALES_CHECKOUT_MEETINGS_QUEUE).and_yield(payload.to_s)
        end

        context "When event is consumed for given user and meeting ids" do
          it "does checkout for given meetings" do
            allow(PublishEvent).to receive(:call)
            ListenForCheckoutMeetings.call()
            expect(@meeting.meeting_attendances.find_by(user_id: @user.id).checked_out_latitude).to eq('18.8879')
            expect(@meeting.meeting_attendances.find_by(user_id: @user.id).checked_out_longitude).to eq('19.983')
          end
        end
      end
    end
  end
end
