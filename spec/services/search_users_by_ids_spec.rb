# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SearchUsersByIds do
  describe '#call' do
    let(:user) { create(:user) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name).token }

    context 'valid' do
      context 'when user ids are blank' do
        it 'returns blank hash' do
          expect(described_class.call([]).result).to eq({})
        end
      end

      context 'when no user matches search' do
        let(:response_content) {
          {
            content: [],
            totalPages: 1
          }
        }

        before do
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=0&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: {
                fields: %w[id firstName lastName],
                jsonRule: {
                  rules: [{ id: 'id', field: 'id', type: 'double', operator: 'in', value: '1,2,3' }],
                  condition: 'AND',
                  valid: true
                }
              }.to_json
            )
            .to_return(status: 200, body: response_content.to_json)
        end

        it 'returns blank hash' do
          expect(described_class.call([1, 2, 3], token).result).to eq({})
        end
      end

      context 'when matching users are less than 100' do
        let(:response_one) {
          {
            content: [{ id: 1, firstName: 'User', lastName: 'One' }, { id: 2, firstName: 'User', lastName: 'Two' }, { id: 3, firstName: 'User', lastName: 'Three' }],
            number: 0,
            totalPages: 1
          }
        }
        let(:request_body) {
          {
            fields: %w[id firstName lastName],
            jsonRule: {
              rules: [{ id: 'id', field: 'id', type: 'double', operator: 'in', value: '1,2,3' }],
              condition: 'AND',
              valid: true
            }
          }
        }

        before do
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=0&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: request_body.to_json
            )
            .to_return(status: 200, body: response_one.to_json)
          Thread.current[:token] = token
        end

        it 'returns hash of user id and name' do
          expect(described_class.call([1, 2, 3]).result).to eq({
            1 => 'User One',
            2 => 'User Two',
            3 => 'User Three'
          })
        end

        after { Thread.current[:token] = nil }
      end

      context 'when matching users are more than 100' do
        let(:response_one) {
          {
            content: [{ id: 1, firstName: 'User', lastName: 'One' }, { id: 2, firstName: 'User', lastName: 'Two' }, { id: 3, firstName: 'User', lastName: 'Three' }],
            number: 0,
            totalPages: 3
          }
        }
        let(:response_two) {
          {
            content: [{ id: 4, firstName: 'User', lastName: 'Four' }, { id: 5, firstName: 'User', lastName: 'Five' }, { id: 6, firstName: 'User', lastName: 'Six' }],
            number: 1,
            totalPages: 3
          }
        }
        let(:response_three) {
          {
            content: [{ id: 7, firstName: 'User', lastName: 'Seven' }, { id: 8, firstName: 'User', lastName: 'Eight' }, { id: 9, firstName: 'User', lastName: 'Nine' }],
            number: 2,
            totalPages: 3
          }
        }
        let(:request_body) {
          {
            fields: %w[id firstName lastName],
            jsonRule: {
              rules: [{ id: 'id', field: 'id', type: 'double', operator: 'in', value: '1,2,3,4,5,6,7,8,9' }],
              condition: 'AND',
              valid: true
            }
          }
        }

        before do
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=0&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: request_body.to_json
            )
            .to_return(status: 200, body: response_one.to_json)
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=1&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: request_body.to_json
            )
            .to_return(status: 200, body: response_two.to_json)
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=2&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: request_body.to_json
            )
            .to_return(status: 200, body: response_three.to_json)
        end

        it 'returns hash of user id and name' do
          expect(described_class.call((1..9).to_a, token).result).to eq({
            1 => 'User One',
            2 => 'User Two',
            3 => 'User Three',
            4 => 'User Four',
            5 => 'User Five',
            6 => 'User Six',
            7 => 'User Seven',
            8 => 'User Eight',
            9 => 'User Nine'
          })
        end
      end
    end

    context 'invalid' do
      context 'when token is not present' do
        it 'raises unauthorized error' do
          expect { described_class.call([1, 2, 3]).result }.to raise_error(ExceptionHandler::AuthenticationError, '01501005')
        end
      end

      context 'when search api returns error' do
        let(:request_body) {
          {
            fields: %w[id firstName lastName],
            jsonRule: {
              rules: [{ id: 'id', field: 'id', type: 'double', operator: 'in', value: '1,2,3' }],
              condition: 'AND',
              valid: true
            }
          }
        }

        before do
          stub_request(:post, "http://localhost:8081/v1/users/search?sort=updatedAt,desc&page=0&size=100")
            .with(
              headers: {
                Authorization: "Bearer #{token}",
                content_type: 'application/json',
                accept: 'application/json'
              },
              body: request_body.to_json
            )
            .to_return(status: 400, body: '')
          Thread.current[:token] = token
        end

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with('SearchUsersByIds 400')
          expect { described_class.call([1, 2, 3]).result }.to raise_error(ExceptionHandler::InvalidDataError, '01503001')
        end

        after { Thread.current[:token] = nil }
      end
    end
  end
end
