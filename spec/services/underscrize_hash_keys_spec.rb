require 'rails_helper'

RSpec.describe UnderscorizeHashKeys do
  describe '#call' do
    context 'valid input' do
      it 'accepts only hash' do
        expect(UnderscorizeHashKeys.new({})).to be_valid
      end
    end
    context 'invalid input' do
      it 'rejects any other input other than hash' do
        expect{UnderscorizeHashKeys.new([1,2,3])}.to raise_error(RuntimeError)
      end
    end
    it 'returns underscore keys' do
      input = {'testKey' => 303, 'test_keyAgain' => 333, 'test_key_my' => 'asdnafsdf'}
      expect(UnderscorizeHashKeys.call(input).result) == { 'test_key' => 303, 'test_key_again' => 333, 'test_key_my' => 'asdnafsdf'}
    end
    it 'updates nest hash as well' do
      input = {'testKey' => 303, 'test_keyAgain'=> {'testKey_lore' => 333, 'test_key_my' => 'asdnafsdf'}}
      expect(UnderscorizeHashKeys.call(input).result) == { 'testKey' => 303, 'test_key_again' => {'test_key_lore' => 333, 'test_key_my' => 'asdnafsdf'} }
    end
    it 'updates nest hash with array as well' do
      input = {'testKey' => 303, 'test_keyAgain'=> [{'testKey_lore' => 333, 'test_key_my' => 'asdnafsdf'},{'testKeylore' => 333, 'test_key' => 'asdnafsdf'}]}
      expect(UnderscorizeHashKeys.call(input).result) == { 'testKey' => 303, 'test_key_again' => [{'test_key_lore' => 333, 'test_key_my' => 'asdnafsdf'},{'test_keylore' => 333, 'test_key' => 'asdnafsdf'}] }
    end
  end
end