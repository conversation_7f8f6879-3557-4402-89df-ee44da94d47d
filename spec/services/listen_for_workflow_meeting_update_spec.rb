# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForWorkflowMeetingUpdate do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic('ex.workflow')
      @payload = {
        "entity": {
          "title": "updated meeting name",
          "description": "meeting desc updated from workflow",
          "from": {
            "allDay": false,
            "from": "2023-03-16T07:58:23.927Z",
            "to": "2023-03-16T08:28:23.927Z"
          },
          "allDay": {
            "allDay": true,
            "from": "2023-03-16T07:58:23.927Z",
            "to": "2023-03-16T08:28:23.927Z"
          },
          "timezone": {
            "id": 373,
            "name": "Asia/Kolkata"
          },
          "status": "scheduled",
          "participants": [
            {
              "id": 3,
              "entity": "USER",
              "name": "R<PERSON>",
              "email": "<EMAIL>",
              "rsvpResponse": nil,
              "rsvpMessage": nil
            },
            {
              "id": 4,
              "entity": "USER",
              "name": "RVJ",
              "email": "<EMAIL>",
              "rsvpResponse": nil,
              "rsvpMessage": nil
            }
          ],
          "organizer": {
            "id": 3,
            "entity": "USER",
            "name": "RJ",
            "email": "<EMAIL>"
          },
          "medium": "OFFLINE",
          "location": "Pune"
        },
        "metadata": {
          "eventId": 74745,
          "tenantId": 190,
          "userId": 114,
          "entityType": "MEETING",
          "workflowId": "WF_301",
          "executedWorkflows": [
            "WF_309",
            "WF_301"    ],
          "entityAction": "CREATED",
          "executeWorkflow": true,
          "entityId": 3763,
          "workflowName": "Workflow 1"
        }
      }.to_json
      @metadata = {
        headers: {
          replyToEvent: 'workflow.meeting.update.reply',
          replyToExchange: 'ex.workflow'
        }
      }
    end


    context 'with valid input' do
      it 'calls UpdateMeetingViaWorkflow service with given payload' do
        @queue = @channel.queue('')
        @queue.bind(@exchange, routing_key: 'q.workflow.meeting.update.meetings')
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.workflow', 'workflow.meeting.update', 'q.workflow.meeting.update.meetings')
          .and_yield(@payload.to_s, @metadata)

        event = nil

        event_details = nil
        expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
          event_details = args.first
        end

        ListenForWorkflowMeetingUpdate.call()

        expect(JSON.parse(event_details.to_json)).to eq({
          "eventId"=>74745,
          "status"=>"SUCCESS",
          "statusCode"=>200
        })
      end
    end
  end
end
