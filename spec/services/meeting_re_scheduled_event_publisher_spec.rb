require 'rails_helper'

RSpec.describe MeetingReScheduledEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      allow(PublishEvent).to receive(:call)
      @meeting = create(:meeting, owner: @user)
      lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      @meeting.participants << [lead_lookup]
      @meeting.save
      allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
    end

    context "for meeting update with participants" do
      before do
        @meeting.title = "New Title"
        @meeting.save
      end
      it 'should call PublishEvent for each participants with each user' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant))
        MeetingReScheduledEventPublisher.call(@meeting, @meeting.participants)
      end
    end
  end
end
