require 'rails_helper'

RSpec.describe SearchUsersByEmails do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns correct output" do
        data = [{"emailId":"<EMAIL>","firstName":"user1","lastName":"test","id":1},
                {"emailId":"<EMAIL>","firstName":"user2","lastName":"test","id":2},
                {"emailId":"<EMAIL>","firstName":"user3","lastName":"test","id":3},
                {"emailId":"<EMAIL>","firstName":"user4","lastName":"test","id":4}]

        payload = { emailIds: ['<EMAIL>', '<EMAIL>'] }

        stub_request(:post, SERVICE_IAM + "/v1/users/search-by-email").
          with(
            body: payload.to_json,
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: data.to_json, headers: {})

            emails = ['<EMAIL>', '<EMAIL>']

            command = SearchUsersByEmails.call(emails, 1)

            expect(command.result[:matched]).to eq([{:entity=>"user_1", :email=>"<EMAIL>", :name=>"user1 test", :tenant_id=>1}])
            expect(command.result[:unmatched]).to eq([{:entity=>"external", :email=>"<EMAIL>", :tenant_id=>1}])
      end
    end
  end
end
