require 'rails_helper'

RSpec.describe SearchCompanies do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input' do
      let(:emails) { ['<EMAIL>', '<EMAIL>'] }

      before do
        rules = []

        emails.each do |email|
          rules << {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: email
          }
        end
        payload = { fields: %w[id firstName lastName emails ownedBy], jsonRule: { rules: rules, condition: 'OR', valid: true } }

        stub_request(:post, "#{SERVICE_SEARCH}/v1/search/company?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }).
            to_return(status: 200, body: file_fixture('sample-company-search-response.json').read, headers: {})
      end

      it 'returns correct output' do
        command = described_class.call(emails, 1)

        expect(command.result[:matched]).to eq([{ entity: 'company_1', email: '<EMAIL>', name: 'Company1 test', tenant_id: 1, owner_id: 123 }])
        expect(command.result[:unmatched]).to eq([{ entity: 'external', email: '<EMAIL>', tenant_id: 1 }])
      end
    end
  end
end
