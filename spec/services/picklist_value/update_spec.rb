require 'rails_helper'

RSpec.describe PicklistValue::Update do
  describe '#call' do
    let(:tenant_id)           { 101 }
    let(:user)                { create(:user, tenant_id: tenant_id) }
    let(:another_user)        { create(:user, tenant_id: tenant_id) }
    let(:field)             { create(:custom_field, tenant_id: tenant_id, created_by: another_user, updated_by: another_user, field_type: 'PICK_LIST') }
    let(:picklist)            { create(:picklist, field: field, tenant_id: tenant_id) }
    let(:picklist_value)      { create(:picklist_value, picklist: picklist, tenant_id: tenant_id, display_name: "Single'O Quote") }
    let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:auth_data)           { ParseToken.call(valid_auth_token.token).result }
    let(:auth_data_without_field_update)   { ParseToken.call(another_valid_token.token).result }

    def escape_quotes(value)
      value.split("'").join("''")
    end

    context 'valid' do
      before do
        Thread.current[:auth] = auth_data
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::PicklistValueUpdated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))
        create_list(:meeting, 5, tenant_id: tenant_id, owner: user, custom_field_values: { field.internal_name => { id: picklist_value.id, name: picklist_value.display_name }, 'cfSomeOtherField' => 123 })
        create_list(:meeting, 5, tenant_id: tenant_id, owner: user)
      end

      it 'should update picklist value' do
        expect(Rails.logger).to receive(:info).with("Event::MeetingFieldUpdated V2 Updated Field ID #{field.id}")
        expect(Rails.logger).to receive(:info).with("PicklistValue::Update Meetings updated 5")
        expect(Rails.logger).to receive(:info).with("Event::MeetingLayoutUpdated for Tenant ID 101")
        expect(Rails.logger).to receive(:info).with(/Event::PicklistValueUpdated/)

        old_display_name = picklist_value.display_name
        command = described_class.call({ picklist_id: picklist.id, id: picklist_value.id, displayName: 'Updated Name' })

        expect(command.success?).to be(true)
        picklist_value_hash = command.result
        expect(picklist_value_hash['displayName']).not_to eq(old_display_name)
        expect(picklist_value_hash['displayName']).to eq('Updated Name')
        expect(field.reload.updated_by_id).to be(user.id)

        expect(
          Meeting.where(tenant_id: tenant_id)
                  .where("cast (custom_field_values ->> '#{field.internal_name}' as jsonb) = '#{Arel.sql(escape_quotes({ id: picklist_value.id, name: old_display_name }.to_json))}'").count
        ).to be(0)
        expect(
          Meeting.where(tenant_id: tenant_id)
                  .where("cast (custom_field_values ->> '#{field.internal_name}' as jsonb) = '#{Arel.sql(escape_quotes({ id: picklist_value.id, name: 'Updated Name' }.to_json))}'").count
        ).to be(5)
        expect(
          Meeting.where(tenant_id: tenant_id)
                  .where("cast (custom_field_values ->> 'cfSomeOtherField' as integer) = 123").count
        ).to be(5)
      end
    end

    context 'invalid' do
      context 'invalid id' do
        before { Thread.current[:auth] = auth_data }

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: -1, displayName: 'Updated Name' }).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist value.")
        end
      end

      context 'standard field' do
        before do
          Thread.current[:auth] = auth_data
          field.update!(is_standard: true)
        end

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_value.id, displayName: 'Updated Name' }).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist value.")
        end
      end

      context 'when display name is blank' do
        before { Thread.current[:auth] = auth_data }

        it 'should raise invalid data error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_value.id, displayName: '' }).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist value.")
        end
      end

      context 'when user does not have permission' do
        before { Thread.current[:auth] = auth_data_without_field_update }

        it 'should raise forbidden error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_value.id, displayName: 'Updated Name' }).result
          end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to update custom field.")
        end
      end

      context 'user without context' do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_value.id, displayName: 'Updated Name' }).result
          end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end
    end
  end
end
