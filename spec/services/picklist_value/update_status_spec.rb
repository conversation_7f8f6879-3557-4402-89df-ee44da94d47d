require 'rails_helper'

RSpec.describe PicklistValue::UpdateStatus do
  describe '#call' do
    let(:tenant_id) { 101 }
    let(:user) { create(:user, tenant_id: tenant_id) }
    let(:field) { create(:custom_field, tenant_id: tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
    let(:picklist) { create(:picklist, field: field, tenant_id: tenant_id) }
    let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: tenant_id) }
    let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:auth_data)           { ParseToken.call(valid_auth_token.token).result }
    let(:auth_data_without_field_update)   { ParseToken.call(another_valid_token.token).result }

    context 'valid' do
      before do
        Thread.current[:auth] = auth_data
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))
      end

      context 'enable' do
        before { PicklistValue.where(id: picklist_values.map(&:id)).update_all(disabled: true) }

        it 'should enable picklist value' do
          command = described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }, :enable)

          expect(command.success?).to be(true)
          expect(picklist_values.map(&:reload).map(&:disabled).count(false)).to be(1)
          expect(picklist_values.map(&:disabled).count(true)).to be(4)
          expect(field.reload.updated_by_id).to eq(user.id)
        end
      end

      context 'disable' do
        before { PicklistValue.where(id: picklist_values.map(&:id)).update_all(disabled: false) }

        it 'should enable picklist value' do
          command = described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }, :disable)

          expect(command.success?).to be(true)
          expect(picklist_values.map(&:reload).map(&:disabled).count(true)).to be(1)
          expect(picklist_values.map(&:disabled).count(false)).to be(4)
          expect(field.reload.updated_by_id).to eq(user.id)
        end
      end
    end

    context 'invalid' do
      context 'invalid id' do
        before { Thread.current[:auth] = auth_data }

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: -1 }, :enable).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist field.")
        end
      end

      context 'standard field' do
        before do
          Thread.current[:auth] = auth_data
          field.update!(is_standard: true)
        end

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }, :enable).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist field.")
        end
      end

      context 'when user does not have permission' do
        before { Thread.current[:auth] = auth_data_without_field_update }

        it 'should raise forbidden error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }, :enable).result
          end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to update custom field.")
        end
      end

      context 'user without context' do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }, :enable).result
          end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end
    end
  end
end
