require 'rails_helper'

RSpec.describe PicklistValue::Delete do
  describe '#call' do
    let(:tenant_id) { 101 }
    let(:user) { create(:user, tenant_id: tenant_id) }
    let(:field) { create(:custom_field, tenant_id: tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
    let(:picklist) { create(:picklist, field: field, tenant_id: tenant_id) }
    let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: tenant_id) }
    let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:auth_data)           { ParseToken.call(valid_auth_token.token).result }
    let(:auth_data_without_field_delete)   { ParseToken.call(another_valid_token.token).result }

    context 'valid' do
      before do
        Thread.current[:auth] = auth_data
        picklist_values.last(4).each do |picklist_value|
          create(
            :meeting,
            tenant_id: user.tenant_id,
            created_by: user,
            updated_by: user,
            custom_field_values: {
              field.internal_name => {
                id: picklist_value.id,
                name: picklist_value.display_name
              }
            }
          )
        end
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))
      end

      it 'should destroy picklist value' do
        picklist_value_to_be_deleted = picklist_values.first.id
        command = described_class.call({ picklist_id: picklist.id, id: picklist_value_to_be_deleted })

        expect(command.success?).to be(true)
        expect(picklist.picklist_values.count).to be(4)
        expect(PicklistValue.find_by(id: picklist_value_to_be_deleted)).to be_nil
      end
    end

    context 'invalid' do
      context 'invalid id' do
        before { Thread.current[:auth] = auth_data }

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: -1 }).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist field.")
        end
      end

      context 'standard field' do
        before do
          Thread.current[:auth] = auth_data
          field.update!(is_standard: true)
        end

        it 'should raise invalid error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }).result
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid picklist field.")
        end
      end

      context 'when meeting with picklist value is present' do
        before do
          Thread.current[:auth] = auth_data
          picklist_value = picklist_values.first
          create(
            :meeting,
            tenant_id: user.tenant_id,
            created_by: user,
            updated_by: user,
            custom_field_values: {
              field.internal_name => {
                id: picklist_value.id,
                name: picklist_value.display_name
              }
            }
          )
        end

        it 'should raise entity cannot be destroyed error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }).result
          end.to raise_error(ExceptionHandler::EntityCannotBeDestroyed, ErrorCode.picklist_value_cannot_be_deleted)
        end
      end

      context 'when user does not have permission' do
        before { Thread.current[:auth] = auth_data_without_field_delete }

        it 'should raise forbidden error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }).result
          end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete custom field.")
        end
      end

      context 'user without context' do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect do
            described_class.call({ picklist_id: picklist.id, id: picklist_values.first.id }).result
          end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end
    end
  end
end
