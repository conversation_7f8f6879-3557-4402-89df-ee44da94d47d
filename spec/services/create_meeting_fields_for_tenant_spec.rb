require 'rails_helper'

RSpec.describe CreateMeetingFieldsForTenant, type: :model do
  describe "#MeetingFieldsCreation" do
    before do
      @tenant_id = rand(100)
      @user = create(:user, tenant_id: @tenant_id)
      @user_id = @user.id
      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        build(:user_look_up, entity_id: @user_id, tenant_id: @tenant_id, name: "<PERSON>")
      ])
    end

    context 'when creating meeting fields for tenant' do
      it 'should not create duplicate fields for tenant' do
        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        expect(Field.where(tenant_id: @tenant_id).count).to eql(32)

        CreateMeetingFieldsForTenant.new(@tenant_id + 1, @user_id + 1).call
        expect(Field.where(tenant_id: @tenant_id + 1).count).to eql(32)

        # For exclusive tenants, total count should add up
        expect(Field.count).to eql(64)

        CreateMeetingFieldsForTenant.new(@tenant_id + 1, @user_id + 1).call
        expect(Field.where(tenant_id: @tenant_id + 1).count).to eql(32)
        expect(Field.count).to eql(64)
      end

      it 'should create 16 non internal & 16 internal fields' do
        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        expect(Field.where(is_internal: false).count).to eq(16)
        expect(Field.where(is_internal: true).count).to eq(16)
      end

      it 'should create remaining fields if few are created' do
        fields = YAML.load_file("#{Rails.root}/config/meeting_fields.yml").slice('id', 'tenant_id', 'title', 'allDay', 'from')
        fields.each do |_, field_data|
          Field.create(field_data.merge('tenant_id' => @tenant_id, 'created_by' => @user, 'updated_by' => @user))
        end
        expect(Field.where(tenant_id: @tenant_id).count).to eq(5)

        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        expect(Field.where(tenant_id: @tenant_id).count).to eq(32)

        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        expect(Field.where(tenant_id: @tenant_id).count).to eq(32)
        expect(Field.count).to eq(32)
      end

      it 'should create relatedTo field with four entity lookups' do
        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        field = Field.where(tenant_id: @tenant_id, internal_name: 'relatedTo').first

        expect(field.display_name).to eql('Related To')
        expect(field.field_type).to eq('ENTITY_LOOKUP')
        expect(field.picklist.display_name).to eql('Related To Look Up')
        expect(field.picklist.picklist_values.count).to eql(4)

        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'LEAD').first.display_name).to eql('LEAD')
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'DEAL').first.display_name).to eql('DEAL')
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'CONTACT').first.display_name).to eql('CONTACT')
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'COMPANY').first.display_name).to eql('COMPANY')
      end

      it 'should create timezone field with 435 timezones' do
        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        field = Field.where(tenant_id: @tenant_id, internal_name: 'timezone').first

        expect(field.display_name).to eql('Timezone')
        expect(field.field_type).to eq('PICK_LIST')
        expect(field.picklist.display_name).to eql('Timezone')
        expect(field.picklist.picklist_values.count).to eql(435)

        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'America/Tijuana').first.display_name).to eql('(GMT-08:00) Tijuana, Baja California')
      end

      it 'should create status field with 4 statuses' do
        CreateMeetingFieldsForTenant.new(@tenant_id, @user_id).call
        field = Field.where(tenant_id: @tenant_id, internal_name: 'status').first

        expect(field.display_name).to eql('Status')
        expect(field.field_type).to eq('ENTITY_PICKLIST')
        expect(field.picklist.display_name).to eql('Status Picklist')
        expect(field.picklist.picklist_values.count).to eql(4)

        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'scheduled').first.display_name).to eql(SCHEDULED.capitalize)
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'missed').first.display_name).to eql(MISSED.capitalize)
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'conducted').first.display_name).to eql(CONDUCTED.capitalize)
        expect(PicklistValue.where(tenant_id: @tenant_id, internal_name: 'cancelled').first.display_name).to eql(CANCELLED.capitalize)
      end
    end

    context 'when error is raised' do
      before do
        allow(GetUserDetails).to receive_message_chain(:call, :result).and_return(@user)
      end

      it 'should create rest of the fields and log error message' do
        expect(Rails.logger).to receive(:error).exactly(32).times
        CreateMeetingFieldsForTenant.new(nil, @user_id).call

        expect(Field.count).to be(0)
        expect(Picklist.count).to be(0)
        expect(PicklistValue.count).to be(0)
      end
    end
  end
end
