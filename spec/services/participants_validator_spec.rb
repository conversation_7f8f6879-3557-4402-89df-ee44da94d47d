require 'rails_helper'

RSpec.describe ParticipantsValidator do
  describe "#call" do
    before do
      @user = create(:user)
      @params = { user: @user }
      participants = []
      participants << {id: 10, entity: LOOKUP_USER, name: "<PERSON>"}
      participants << {id: 11, entity: LOOKUP_LEAD, name: "<PERSON>"}
      participants << {id: 12, entity: LOOKUP_DEAL, name: "<PERSON> Deal"}
      participants << {id: 13, entity: LOOKUP_CONTACT, name: "John Contact"}
      participants << {id: 14, entity: LOOKUP_COMPANY, name: "John <PERSON>"}
      @params[:participants] = participants

      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name),
        build(:user_look_up, entity_id: 10, tenant_id: @user.tenant_id, name: '<PERSON>')
      ])

      allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
        build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      ])

      allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
        build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "<PERSON>")
      ])

      allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
        build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "John Contact")
      ])

      allow(ValidateCompanies).to receive_message_chain(:call, :result).and_return([
        build(:contact_look_up, entity_id: 14, tenant_id: @user.tenant_id, name: "John Company")
      ])
    end

    context 'with uniq participants' do

      it 'should return validated participants' do
        response = ParticipantsValidator.call(@params).result
        expect(response.size).to eq(6)
      end

      it 'should always add current user in participants' do
        response = ParticipantsValidator.call(@params).result
        expect(response.size).to eq(6)
        expect(response.first.entity_id).to eq(@user.id)
        expect(response.first.name).to eq(@user.name)
      end
    end

    context "with duplicate participants" do
      it 'should remove duplicate' do
        @params[:participants] << {id: 10, entity: LOOKUP_USER, name: "John Doe"}
        response = ParticipantsValidator.call(@params).result
        expect(response.size).to eq(6)
        expect(response.select{|resp| resp.entity_id == 10}.count).to eq(1)
      end
    end
  end
end
