require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe DeleteMeeting do
  describe '#call' do
    context "#destroy" do
      context "With Valid Permission" do
        before do
          @user = create(:user)
          @another_user = create(:user, tenant_id: @user.tenant_id)
          auth_data = build(:auth_data, :meeting_with_delete, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
          @lead_lookup = build(:lead_look_up, tenant_id: @user.tenant_id, entity_id: 1)
          thread = Thread.current
          thread[:auth] = auth_data
          Sidekiq::Worker.clear_all
          ActiveJob::Base.queue_adapter = :test
        end

        context "When user have delete permission" do
          before do
            @meeting = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
            @meeting.participants << @lead_lookup
            @meeting.related_to << @lead_lookup
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(1).times
            #expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
          end

          it 'should delete the meeting' do
            command = DeleteMeeting.call(@meeting.id)

            expect( command.success? ).to be true
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
          end

          context 'when there are share rules for that meeting' do
            it 'schedules job to delete share rules and publish share rule deleted events' do
              create(:share_rule, meeting_id: @meeting.id, tenant_id: @meeting.tenant_id, created_by: @user)

              expect { DeleteMeeting.call(@meeting.id) }.to have_enqueued_job(DeleteAssociatedShareRulesJob)
            end
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: @user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)

            command = DeleteMeeting.call(@meeting.id)

            expect( command.success? ).to be true
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Note.find_by(id: @note1.id)).to eq(nil)
            expect(Note.find_by(id: @note2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
          end


          context 'when publish_usage parameter is false' do
            it 'does not publish tenant usage' do
              expect(PublishUsageJob).not_to receive(:perform_later).with(@user.tenant_id)

              DeleteMeeting.call(@meeting.id, false)
            end
          end

          context 'when publish_usage parameter is true' do
            it 'does publish tenant usage' do
              expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once

              DeleteMeeting.call(@meeting.id, true)
            end
          end
          context 'when deleting checked in meeting' do
            before do
              create(:meeting_checked_in, meeting: @meeting)
            end

            it 'should delete meeting' do
              expect(MeetingAttendance.count).to eq(1)
              command = DeleteMeeting.call(@meeting.id)

              expect( command.success? ).to be true
              expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
              expect(MeetingLookUp.count).to eql(0)
              expect(MeetingAttendance.count).to eq(0)
            end
          end
        end

        context 'when meeting medium is google' do
          before do
            expect(ParticipantRemovedEventPublisher).not_to receive(:call)
            expect(MeetingCancelledEventPublisher).not_to receive(:call)
            @meeting = create(:meeting, owner: @user, medium: GOOGLE_PROVIDER)
            @meeting.participants << @lead_lookup
            @meeting.related_to << @lead_lookup
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(1).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
          end

          context 'when meeting status is cancelled' do
            it 'does not invoke call method of calendar base class' do
              @meeting.update_column(:status, CANCELLED)
              expect(Calendar::Base).not_to receive(:call)
              DeleteMeeting.call(@meeting.id)
            end
          end
        end

        context 'when meeting medium is offline' do
          before do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(1).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once

            @meeting = create(:meeting, owner: @user, medium: OFFLINE)
          end

          it 'does not invoke call method of calendar base class' do
            expect(Calendar::Base).not_to receive(:call)
            DeleteMeeting.call(@meeting.id)
          end
        end

        context "When user have delete all permission" do
          before do
            @meeting = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(1).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
          end

          it 'should delete the meeting' do
            command = DeleteMeeting.call(@meeting.id)

            expect( command.success? ).to be true
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: @user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)

            command = DeleteMeeting.call(@meeting.id)

            expect( command.success? ).to be true
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Note.find_by(id: @note1.id)).to eq(nil)
            expect(Note.find_by(id: @note2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
          end
        end
      end

      context "With Invalid Permission" do
        before do
          @user = create(:user)
          @another_user = create(:user, tenant_id: @user.tenant_id)
          auth_data = build(:auth_data, :meeting_without_delete, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        context "When user dont have delete permission" do
          before do
            @meeting = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
          end

          it "shouldn't delete the meeting" do
            expect{
              DeleteMeeting.call(@meeting.id).result
            }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete meeting.")
          end
        end

        context "When user dont have delete all permission" do
          before do
            @meeting = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
          end

          it "shouldn't delete the meeting" do
            expect{
              DeleteMeeting.call(@meeting.id).result
            }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete meeting.")
          end
        end
      end
    end
  end
end
