# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetMeetingDetails do
  describe '#call' do
    let(:user) { create(:user) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:auth_data) { build(:auth_data, :meeting_without_read_all, user_id: user.id, username: user.name, tenant_id: user.tenant_id) }
    let(:auth_data_with_read_all) { build(:auth_data, user_id: user.id, username: user.name, tenant_id: user.tenant_id) }

    before { Thread.current[:auth] = auth_data }

    context 'when user has access to a meeting' do
      context 'when user has read all' do
        before do
          @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
          Thread.current[:auth] = auth_data_with_read_all
        end

        it 'returns meeting' do
          expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
        end

        after { Thread.current[:auth] = auth_data }
      end

      context 'when user is owner of meeting' do
        before do
          @meeting = create(:meeting, owner: user, tenant_id: user.tenant_id)
        end

        it 'returns meeting' do
          expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
        end
      end

      context 'when user is participant or organizer' do
        context 'when user is participant' do
          before do
            @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
            @meeting.participants << build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id)
          end

          it 'returns meeting' do
            expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
          end
        end

        context 'when user is organizer' do
          before do
            @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
            @meeting.organizer = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id)
          end

          it 'returns meeting' do
            expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
          end
        end
      end

      context 'when user is owner of related entity' do
        PARENT_ENTITY_TYPES.each do |entity_type|
          context "when related entity type is #{entity_type}" do
            context "when #{entity_type} is related to" do
              before do
                @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
                @meeting.related_to << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id, owner_id: user.id)
              end

              it 'returns meeting' do
                expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
              end
            end

            if [LOOKUP_LEAD, LOOKUP_CONTACT].include?(entity_type)
              context "when #{entity_type} is participant" do
                before do
                  @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
                  @meeting.participants << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id, owner_id: user.id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end

              context "when #{entity_type} is organizer" do
                before do
                  @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
                  @meeting.organizer = build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id, owner_id: user.id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end
            end
          end
        end
      end

      context 'when meeting is shared via share all rule' do
        let(:third_user) { create(:user, tenant_id: user.tenant_id) }

        context 'when shared from user is participant' do
          before do
            @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
            @meeting.participants << build(:user_look_up, entity_id: third_user.id, tenant_id: user.tenant_id)
            create(:share_rule, share_all_records: true, tenant_id: user.tenant_id, created_by: user, from_id: third_user.id, to_id: user.id)
          end

          it 'returns meeting' do
            expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
          end
        end

        context 'when shared from user is organizer' do
          before do
            @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
            @meeting.organizer = build(:user_look_up, entity_id: third_user.id, tenant_id: user.tenant_id)
            create(:share_rule, share_all_records: true, tenant_id: user.tenant_id, created_by: user, from_id: third_user.id, to_id: user.id)
          end

          it 'returns meeting' do
            expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
          end
        end

        context 'when shared from user is meeting owner' do
          before do
            @meeting = create(:meeting, owner: third_user, tenant_id: user.tenant_id)
            @meeting.organizer = build(:user_look_up, entity_id: third_user.id, tenant_id: user.tenant_id)
            create(:share_rule, share_all_records: true, tenant_id: user.tenant_id, created_by: user, from_id: third_user.id, to_id: user.id)
          end

          it 'returns meeting' do
            expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
          end
        end
      end

      context 'when meeting is shared via specific meeting rule' do
        before do
          @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
          create(:share_rule, meeting_id: @meeting.id, tenant_id: user.tenant_id, created_by: user, from_id: another_user.id, to_id: user.id)
        end

        it 'returns meeting' do
          expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
        end
      end

      PARENT_ENTITY_TYPES.each do |entity_type|
        context "when user has access to related #{entity_type}" do
          before { Thread.current[:token] = build(:auth_token, :without_meeting_read_all_and_update_all_permission, user_id: user.id, tenant_id: user.tenant_id).token }

          context "when all #{entity_type.pluralize} are shared with meeting" do
            before do
              @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
              PARENT_ENTITY_TYPES.each do |local_entity|
                stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{local_entity.upcase}/MEETING")
                .with(
                  headers: {
                    Authorization: "Bearer #{Thread.current[:token]}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: { another_user.id.to_s => { read: true, meeting: true } }, accessByRecords: {} }.to_json)
              end
            end

            context 'when related entity is related to' do
              before do
                @meeting.related_to << build("#{entity_type}_look_up".to_sym, owner_id: another_user.id, tenant_id: user.tenant_id)
              end

              it 'returns meeting' do
                expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
              end
            end

            if [LOOKUP_LEAD, LOOKUP_CONTACT].include?(entity_type)
              context 'when related entity is participant' do
                before do
                  @meeting.participants << build("#{entity_type}_look_up".to_sym, owner_id: another_user.id, tenant_id: user.tenant_id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end

              context 'when related entity is organizer' do
                before do
                  @meeting.organizer = build("#{entity_type}_look_up".to_sym, owner_id: another_user.id, tenant_id: user.tenant_id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end
            end
          end

          context "when #{entity_type} is shared with meeting" do
            before do
              @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id)
              PARENT_ENTITY_TYPES.each do |local_entity|
                stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{local_entity.upcase}/MEETING")
                .with(
                  headers: {
                    Authorization: "Bearer #{Thread.current[:token]}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: {}, accessByRecords: { '1001' => { read: true, meeting: true }} }.to_json)
              end
            end

            context 'when related entity is related to' do
              before do
                @meeting.related_to << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id)
              end

              it 'returns meeting' do
                expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
              end
            end

            if [LOOKUP_LEAD, LOOKUP_CONTACT].include?(entity_type)
              context 'when related entity is participant' do
                before do
                  @meeting.participants << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end

              context 'when related entity is organizer' do
                before do
                  @meeting.organizer = build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: user.tenant_id)
                end

                it 'returns meeting' do
                  expect(described_class.call(@meeting.id).result.id).to eq(@meeting.id)
                end
              end
            end
          end
        end
      end
    end

    context 'when meeting is not found' do
      it 'raises not found error' do
        expect { described_class.call(-1).result }.to raise_error(ExceptionHandler::NotFound, '01502001||Meeting not found.')
      end
    end

    context 'when user does not have access to a meeting' do
      before { @meeting = create(:meeting, owner: another_user, tenant_id: user.tenant_id) }

      it 'raises unauthorized error' do
        expect { described_class.call(@meeting.id).result }.to raise_error(ExceptionHandler::Forbidden, '01501005||You do not have access to this meeting.')
      end
    end
  end
end
