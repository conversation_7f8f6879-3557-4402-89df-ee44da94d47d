require 'rails_helper'

RSpec.describe GetField do
  describe '#call' do
    let!(:user)             { create(:user)}
    let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }

    context 'user is authorized' do
      before do
        thread = Thread.current
        thread[:auth] = auth_data
        @field = FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user)
      end

      context 'with valid id' do
        it 'should return field object' do
          expect(GetField.call(@field.id).result).to eql(@field)
        end
      end

      context 'for tenant_id field' do
        it 'should raise Not Found error with not found error code' do
          @field.update(internal_name: 'tenant_id')
          expect{GetField.call(@field.id).result}.to raise_error(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Field not found.")
        end
      end

      context 'with invalid id' do
        it 'should raise Not Found error with not found error code' do
          expect{GetField.call(@field.id + 1).result}.to raise_error(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Field not found.")
        end
      end
    end

    context 'user is unauthorized' do
      before do
        thread = Thread.current
        thread[:auth] = nil
      end

      it 'should raise Authentication Error with unauthorized error code' do
        field = FactoryBot.create(:field, created_by: user, updated_by: user)
        expect{GetField.call({'id' => field.id}).result}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end
