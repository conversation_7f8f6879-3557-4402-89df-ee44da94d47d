require 'rails_helper'

RSpec.describe ProcessEvent do
  describe "#call" do
    let(:data) { {
      'medium': 'GOOGLE',
      "kind": "calendar#event",
      "etag": "\"3230914771832000\"",
      "id": "0490f0u2gpp0bk3mnq581ri6h0",
      "status": "confirmed",
      "htmlLink": "https://www.google.com/calendar/event?eid=MDQ5MGYwdTJncHAwYmszbW5xNTgxcmk2aDAgc2h3ZXRhLmt5bGFzQG0",
      "created": "2021-03-11T10:09:45.000Z",
      "updated": "2021-03-11T10:09:45.916Z",
      "summary": "test",
      "creator": {
        "email": "<EMAIL>",
        "self": true
      },
      "organizer": {
        "email": "<EMAIL>",
        "self": true
      },
      "start": {
        "dateTime": "2021-03-11T16:30:00+05:30",
        "timeZone": "UTC"
      },
      "end": {
        "dateTime": "2021-03-11T17:30:00+05:30",
        "timeZone": "UTC"
      },
      "iCalUID": "<EMAIL>",
      "sequence": 0,
      "attendees": [
        {
          "email": "<EMAIL>",
          "organizer": true,
          "self": true,
          "responseStatus": "accepted"
        },
        {
          "email": "<EMAIL>",
          "responseStatus": "accepted"
        },
        {
          "email": "<EMAIL>",
          "responseStatus": "needsAction"
        }
      ],
      "hangoutLink": "https://meet.google.com/tst-zqho-bzw",
      "conferenceData": {
        "entryPoints": [
          {
            "entryPointType": "video",
            "uri": "https://meet.google.com/tst-zqho-bzw",
            "label": "meet.google.com/tst-zqho-bzw"
          }
        ],
        "conferenceSolution": {
          "key": {
            "type": "hangoutsMeet"
          },
          "name": "Google Meet",
          "iconUri": "https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png"
        },
        "conferenceId": "tst-zqho-bzw"
      },
      "reminders": {
        "useDefault": true
      },
      "eventType": "default"
    } }

    before do
      @connected_account = create(:connected_account, tenant_id: 10)
      @token = GenerateToken.call(@connected_account.user.id, @connected_account.user.tenant_id).result
    end

    context "with valid input - " do
      before do
        rules = []
        emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        emails.each do |email|
          rules << {
            id: "multi_field",
            field: "multi_field",
            type: "multi_field",
            input: "multi_field",
            operator: "multi_field",
            value: email
          }
        end
        payload = { fields: %w[id firstName lastName emails ownerId], jsonRule: { rules: rules, condition: 'OR', valid: true } }

        response_data = { "content": [{ emails:[{ type: 'OFFICE',value: '<EMAIL>', primary:true }],
                                      lastName: 'test', firstName: 'Contact3 test', id: 3, ownerId: 123 }] }

        lead_payload_rules = [
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          },
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          },
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          }
        ]

        lead_payload = { fields: %w[id firstName lastName emails ownerId], jsonRule: { rules: lead_payload_rules, condition: 'OR', valid: true } }

        stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=updatedAt,desc&page=0&size=100").
          with(
            body: lead_payload.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: { "content": [{ emails: [{ type: 'OFFICE', value: '<EMAIL>', primary: true }],
            lastName: 'test', firstName: 'Lead name', id: 1 }] }.to_json, headers: {})

        user_payload_rules = {emailIds: ['<EMAIL>']}

        stub_request(:post, SERVICE_IAM + "/v1/users/search-by-email").
          with(
            body: user_payload_rules.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: [{ emailId: '<EMAIL>', id: 1, name: 'sk' }].to_json, headers: {})

        user_payload_rules = {emailIds: ['<EMAIL>','<EMAIL>']}

        stub_request(:post, SERVICE_IAM + "/v1/users/search-by-email").
          with(
            body: user_payload_rules.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: [{"emailId": "<EMAIL>", "id":1, "name": "sk"}].to_json, headers: {})


        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: response_data.to_json, headers: {})

        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }).
          to_return(status: 200, body: response_data.to_json, headers: {})
      end

      it "returns correct output" do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice
        ProcessEvent.call(@connected_account, data)
        expect(Meeting.count).to eq 1
        meeting = Meeting.last
        expect(meeting.owner).to eq @connected_account.user
        expect(meeting.created_by).to eq @connected_account.user
        expect(meeting.tenant_id).to eq @connected_account.tenant_id
      end

      it 'validates participants' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice
        ProcessEvent.call(@connected_account, data)
        meeting = Meeting.last
        expect(meeting.participants.count).to eq 3
      end

      it 'sets rsvp response' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice
        ProcessEvent.call(@connected_account, data)
        look_up = LookUp.where(email: '<EMAIL>').last
        participant_look_up = MeetingLookUp.where(look_up_id: look_up.id).last
        expect(participant_look_up.rsvp_response).to eq RSVP_YES
      end

      it 'sets organizer' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice
        ProcessEvent.call(@connected_account, data)
        meeting = Meeting.last
        participant_look_ups = meeting.participant_look_ups.where(organizer: true)
        organizer = participant_look_ups.first.look_up
        look_up = LookUp.where(tenant_id: 10, email: '<EMAIL>').where("entity like ?", "#{LOOKUP_USER.downcase}_%").first
        expect(organizer.entity_type).to eq look_up.entity_type
        expect(organizer.email).to eq look_up.email
        expect(organizer.id).to eq look_up.id
      end

      context 'when existing meeting is updated' do
        it '' do
          create(:meeting, provider_meeting_id: '0490f0u2gpp0bk3mnq581ri6h0', tenant_id: 10, status: SCHEDULED, medium: GOOGLE_PROVIDER)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice
          ProcessEvent.call(@connected_account, data)
        end
      end

      context 'when event is for allday' do
        it 'creates allday meeting' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).twice

          data['start'] = {
            'date': Date.today.strftime('%Y-%m-%d')
          }
          data['end'] = {
            'date': Date.today.strftime('%Y-%m-%d')
          }
          ProcessEvent.call(@connected_account, data.with_indifferent_access)
          expect(Meeting.last.all_day).to be_truthy
        end
      end
    end

    context 'when payload is for cancelled event' do
      before do
        create(:meeting, provider_meeting_id: '0490f0u2gpp0bk3mnq581ri6h0', tenant_id: 10, status: SCHEDULED)
        data = {
          id: '0490f0u2gpp0bk3mnq581ri6h0',
          status: 'cancelled'
        }
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        ProcessEvent.call(@connected_account, data)
      end

      it 'only updates status of existing meeting to cancelled' do
        expect(Meeting.last.status).to eq('cancelled')
      end
    end
  end
end
