require 'rails_helper'

RSpec.describe DeleteBulkMeetings do
  describe '#call' do
    context "#destroy" do
      context "With Valid Permission" do
        before do
          @user = create(:user)
          @another_user = create(:user, tenant_id: @user.tenant_id)
          auth_data = build(:auth_data, :meeting_with_delete, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          @lead_lookup = build(:lead_look_up, tenant_id: @user.tenant_id, entity_id: 1)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        context "When user have delete permission" do
          before do
            @meeting = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
            @meeting.participants << @lead_lookup
            @meeting.related_to << @lead_lookup
            @meeting_2 = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting_2.participants << owner_lookup
            @meeting_2.participants << @lead_lookup
            @meeting_2.related_to << @lead_lookup
          end

          it 'should delete the meeting' do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(2).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(2).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
            expect(command.result).to match ({
              successCount: 2,
              errorCount: 0,
              response: match_array([
                {entityId: @meeting.id, ownerId: @user.id, result: "success", tenantId: @user.tenant_id},
                {entityId: @meeting_2.id, ownerId: @user.id, result: "success", tenantId: @user.tenant_id}
              ])
            })
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: @user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)

            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(2).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(2).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once

            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(Note.find_by(id: @note1.id)).to eq(nil)
            expect(Note.find_by(id: @note2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
            expect(command.result).to match ({
              response: match_array([
                {entityId: @meeting.id, ownerId: @user.id, result: "success", tenantId: @user.tenant_id},
                {entityId: @meeting_2.id, ownerId: @user.id, result: "success", tenantId: @user.tenant_id}
              ]),
              successCount: 2,
              errorCount: 0
            })
          end

          context 'when meeting medium is GOOGLE' do
            let(:meeting) { create(:meeting_with_associated_entities, medium: GOOGLE_PROVIDER) }

            def stub_delete_google_meeting_request(status, response_body)
              headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Accept': 'application/json' }
              stub_request(:delete, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(meeting.provider_meeting_id)}?sendUpdates=all")
                .with(headers: headers)
                .to_return(status: status, body: response_body)
            end

            before do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(1).times
              expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
              @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                              provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
            end

            it 'does delete meeting at GOOGLE side' do
              meeting.participants << [build(:user_look_up, name: 'Test', email: '<EMAIL>'), @lead_lookup]
              expect(Calendar::Base).to receive(:call).and_call_original
              expect(Calendar::Google::DeleteEvent).to receive(:call).and_call_original
              meeting.update(provider_meeting_id: 1, tenant_id: @user.tenant_id)
              stub_delete_google_meeting_request(204, {}.to_json)
              command = DeleteBulkMeetings.call([meeting.reload.id])
              expect(command.success?).to eq(true)
              expect(Meeting.find_by(id: meeting.id)).to eq(nil)
            end
          end
        end

        context "When user have delete all permission" do
          before do
            @meeting = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup
            @meeting.participants << @lead_lookup
            @meeting.related_to << @lead_lookup

            @meeting_2 = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting_2.participants << owner_lookup
          end

          it 'should delete the meeting' do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(2).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(1).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
            expect(command.result).to match ({
              response: match_array([
                {entityId: @meeting.id, ownerId: @another_user.id, result: "success", tenantId: @user.tenant_id},
                {entityId: @meeting_2.id, ownerId: @another_user.id, result: "success", tenantId: @user.tenant_id}
              ]),
              successCount: 2,
              errorCount: 0
            })
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: @user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)

            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(2).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(1).times
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once

            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(Note.find_by(id: @note1.id)).to eq(nil)
            expect(Note.find_by(id: @note2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eql(0)
            expect(command.result).to match ({
              response: match_array([
                {entityId: @meeting.id, ownerId: @another_user.id, result: "success", tenantId: @user.tenant_id},
                {entityId: @meeting_2.id, ownerId: @another_user.id, result: "success", tenantId: @user.tenant_id}
              ]),
              successCount: 2,
              errorCount: 0
            })
          end
        end
      end

      context "With Invalid Permission" do
        before do
          @user = create(:user)
          @another_user = create(:user, tenant_id: @user.tenant_id)
          auth_data = build(:auth_data, :meeting_without_delete, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        context "When user dont have delete permission" do
          before do
            @meeting = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup

            @meeting_2 = create(:meeting, owner: @user)
            owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
            @meeting_2.participants << owner_lookup
          end

          it "shouldn't delete the meeting" do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(Integer)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
            expect(TenantUsagePublisher).to receive(:call).with(instance_of(Integer)).exactly(0).times
            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(@meeting)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(@meeting_2)
            expect(command.result).to match ({
              response: match_array([
                { entityId: @meeting.id, ownerId: @user.id, result: "error", tenantId: @user.tenant_id },
                { entityId: @meeting_2.id, ownerId: @user.id, result: "error", tenantId: @user.tenant_id }
              ]),
              successCount: 0,
              errorCount: 2
            })
          end
        end

        context "When user dont have delete all permission" do
          before do
            @meeting = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting.participants << owner_lookup

            @meeting_2 = create(:meeting, owner: @another_user)
            owner_lookup = build(:user_look_up, entity_id: @another_user.id, tenant_id: @another_user.tenant_id, name: @another_user.name, email:"<EMAIL>")
            @meeting_2.participants << owner_lookup
          end

          it "shouldn't delete the meeting" do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting), instance_of(NilClass)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
            expect(TenantUsagePublisher).to receive(:call).with(instance_of(Integer)).exactly(0).times
            command = DeleteBulkMeetings.call(Meeting.pluck(:id))

            expect( command.success? ).to be true
            expect(Calendar::Base).not_to receive(:call)
            expect(Meeting.find_by(id: @meeting.id)).to eq(@meeting)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(@meeting_2)
            expect(command.result[:response]).to match_array([
              {entityId: @meeting.id, ownerId: @another_user.id, result: "error", tenantId: @user.tenant_id},
              {entityId: @meeting_2.id, ownerId: @another_user.id, result: "error", tenantId: @user.tenant_id}
            ])
            expect(command.result[:successCount]).to eq(0)
            expect(command.result[:errorCount]).to eq(2)
          end
        end
      end
    end
  end
end
