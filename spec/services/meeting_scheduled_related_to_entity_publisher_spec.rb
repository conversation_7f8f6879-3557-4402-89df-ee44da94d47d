require 'rails_helper'
require 'bunny-mock'

RSpec.describe MeetingScheduledRelatedToEntityPublisher do
  describe '#call' do
    before do
      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MEETING_EXCHANGE
      @queue = @channel.queue MEETING_SCHEDULED_RELATED_TO_ENTITY_EVENT
      @queue.bind @exchange, routing_key: MEETING_SCHEDULED_RELATED_TO_ENTITY_EVENT
      allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
    end

    context 'when MeetingScheduledRelatedToEntityPublisher is called' do
      before do
        @meeting = create(:meeting, owner: @user, tenant_id: @user.tenant_id)
        lead_look_up = build(:lead_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
        user_look_up = build(:user_look_up, tenant_id: @user.tenant_id)

        @meeting.participants = [user_look_up, lead_look_up]
        @meeting.related_to = [lead_look_up]

        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data

        data = MeetingSerializer.call(@meeting).result.except("recordActions")
        @event_data = Event::MeetingScheduledRelatedToEntity.new(data).to_json

        MeetingScheduledRelatedToEntityPublisher.call(@meeting)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
        expect(@queue.all.first[:options][:routing_key]).to eq(MEETING_SCHEDULED_RELATED_TO_ENTITY_EVENT)
      end

      it 'publishes the correct payload' do
        expect(@queue.all.first[:message]).to eq(@event_data)
      end
    end
  end
end
