require 'rails_helper'

RSpec.describe AuthorizeApiRequest do
  let(:auth_data) { build(:auth_data) }
  let(:header) { { 'Authorization' => token_generator(auth_data) } }
  subject(:invalid_request_obj) { described_class.new({}) }
  subject(:request_obj) { described_class.new(header) }

  describe '#call' do
    # returns user object when request is valid
    context 'when valid request' do
      it 'returns auth data object' do
        result = request_obj.call.result
        expect(result) == auth_data
      end
    end

    # returns error message when invalid request
    context 'when invalid request' do
      context 'when missing token' do
        it 'raises a MissingToken error' do
          expect { invalid_request_obj.call }
            .to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'when invalid token' do
        subject(:invalid_request_obj) do
          described_class.new('Authorization' => invalid_token_generator(auth_data))
        end

        it 'raises an InvalidToken error' do
          expect { invalid_request_obj.call }
            .to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'when token is expired' do
        let(:header) { { 'Authorization' => expired_token_generator(auth_data) } }
        subject(:request_obj) { described_class.new(header) }

        it 'raises ExceptionHandler::ExpiredSignature error' do
          pending "Token expiry is not checked as validation is removed from the Auth::Data class..Need to implement it"
          expect { request_obj.call }
            .to raise_error(
              ExceptionHandler::InvalidToken,
              'Invalid token.'
            )
        end
      end

      context 'fake token' do
        let(:header) { { 'Authorization' => 'foobar' } }
        subject(:invalid_request_obj) { described_class.new(header) }

        it 'handles JWT::DecodeError' do
          expect { invalid_request_obj.call }
            .to raise_error(
              ExceptionHandler::Forbidden,
              "#{ErrorCode.unauthorized}||Unauthorized access."
            )
        end
      end
    end
  end
end
