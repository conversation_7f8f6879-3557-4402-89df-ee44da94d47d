require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForTenantCreation do
  describe '#call' do
    before do
      @tenant_id = rand(100)
      @user_id = rand(100)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
      @payload = {
        "userId" => @user_id,
        "tenantId" => @tenant_id,
        "firstName" => nil,
        "lastName" => "last"
      }.to_json
      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        build(:user_look_up, entity_id: @user_id, tenant_id: @tenant_id, name: "<PERSON>")
      ])
    end

    context 'valid input' do
      context 'for tenant created event' do
        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: TENANT_CREATED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(USER_EXCHANGE, TENANT_CREATED_EVENT, TENANT_CREATED_QUEUE)
            .and_yield(@payload.to_s)

          ListenForTenantCreation.call()
        end

        it 'should create required meeting fields for given tenant id' do
          expect(Field.where(tenant_id: @tenant_id).count).to eql(32)
        end
      end
    end
  end
end
