require 'rails_helper'

RSpec.describe LookUpListener do
  describe '#call' do
    context 'Listen to Entity Events' do
      it 'should call for the listeners' do
        expect(ListenForUserUpdate).to receive(:call)
        expect(ListenForLeadUpdate).to receive(:call)
        expect(ListenForContactUpdate).to receive(:call)
        expect(ListenForDealUpdate).to receive(:call)
        expect(ListenForCompanyUpdate).to receive(:call)
        expect(ListenForDailySchedulerEvent).to receive(:call)
        expect(ListenForUsageLimitChanged).to receive(:call)
        expect(ListenForWorkflowMeetingUpdate).to receive(:call)
        expect(ListenForCompanyReassign).to receive(:call)
        expect(ListenForDealReassign).to receive(:call)
        expect(ListenForTeamUpdatedV2).to receive(:call)
        expect(Listeners::CreateAllShareRulesForHierarchy).to receive(:call)
        expect(Listeners::DeleteAllShareRulesForHierarchy).to receive(:call)
        expect(ListenForCheckoutMeetings).to receive(:call)
        described_class.call
      end
    end
  end
end
