require 'rails_helper'

RSpec.describe CancelMeeting do
  describe '#call' do
    context 'with owner trying to cancel' do
      before do
        @user = create(:user)
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
      end

      context 'scheduled meeting' do
        let(:calendar_base) { instance_double(Calendar::Base) }

        before do
          @meeting = create(:meeting, owner: @user)
        end

        it 'should cancel it' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

          command = CancelMeeting.call(@meeting.id)
          expect( command.success? ).to be true
          expect(@meeting.reload.status).to eq(CANCELLED)
          expect(@meeting.cancelled_by.id).to eq(@user.id)
          expect(@meeting.cancelled_by.name).to eq(@user.name)
          expect(@meeting.cancelled_at.present?).to be(true)
          expect(@meeting.updated_by.id).to eq(@user.id)
        end

        context 'when meeting medium is google' do
          before do
            expect(ParticipantRemovedEventPublisher).not_to receive(:call)
            expect(MeetingCancelledEventPublisher).not_to receive(:call)
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

            @meeting.update(medium: GOOGLE_PROVIDER)
            allow(Calendar::Base).to receive(:call).and_return(calendar_base)
            allow(calendar_base).to receive(:success?).and_return(true)
            allow(calendar_base).to receive(:result).and_return(
              { provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7d7sjg' }
            )
            @command = CancelMeeting.call(@meeting.id)
          end

          it 'does invoke call method of calendar base class' do
            expect(Calendar::Base).to have_received(:call).once
          end

          it 'get command result for calendar base class' do
            expect(calendar_base).to have_received(:result).once
          end

          it 'checks for command success of cancel meeting' do
            expect(@command).to be_success
          end
        end

        context 'when meeting medium is offline' do
          before do
            expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
            @meeting.update(medium: OFFLINE)
            @command = CancelMeeting.call(@meeting.id)
          end

          it 'does not invoke call method of calendar base class' do
            expect(Calendar::Base).not_to receive(:call)
          end

          it 'checks for command success of cancel meeting' do
            expect(@command).to be_success
          end
        end
      end

      context 'conducted meeting' do
        before do
          @meeting = create(:meeting, owner: @user)
          @meeting.update(status: CONDUCTED, conducted_at: DateTime.now.utc, conducted_by: @user, updated_by: @user)
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        end

        it 'should cancel it' do
          expect(@meeting.conducted_by_id).to eq(@user.id)
          expect(@meeting.conducted_at.present?).to be(true)
          expect(@meeting.cancelled_by_id).to eq(nil)
          expect(@meeting.cancelled_at.present?).to be(false)
          expect(@meeting.updated_by_id).to eq(@user.id)
          expect(@meeting.status).to eq(CONDUCTED)

          command = CancelMeeting.call(@meeting.id)
          expect( command.success? ).to be true
          expect(@meeting.reload.status).to eq(CANCELLED)
          expect(@meeting.cancelled_by.id).to eq(@user.id)
          expect(@meeting.cancelled_by.name).to eq(@user.name)
          expect(@meeting.cancelled_at.present?).to be(true)
          expect(@meeting.updated_by.id).to eq(@user.id)
          expect(@meeting.conducted_at.present?).to be(false)
          expect(@meeting.conducted_by_id).to eq(nil)
        end
      end

      context 'missed meeting' do
        before do
          @meeting = create(:meeting, owner: @user)
          @meeting.update(status: MISSED, from: (@meeting.from - 1.month), to: (@meeting.to - 1.month))
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        end

        it 'should cancel it' do
          expect(@meeting.conducted_by_id).to eq(nil)
          expect(@meeting.conducted_at.present?).to be(false)
          expect(@meeting.cancelled_by_id).to eq(nil)
          expect(@meeting.cancelled_at.present?).to be(false)
          expect(@meeting.updated_by_id).to eq(@user.id)
          expect(@meeting.status).to eq(MISSED)
          expect(@meeting.missed?).to be(true)

          command = CancelMeeting.call(@meeting.id)
          expect( command.success? ).to be true
          expect(@meeting.reload.status).to eq(CANCELLED)
          expect(@meeting.cancelled_by.id).to eq(@user.id)
          expect(@meeting.cancelled_by.name).to eq(@user.name)
          expect(@meeting.cancelled_at.present?).to be(true)
          expect(@meeting.updated_by.id).to eq(@user.id)
          expect(@meeting.conducted_at.present?).to be(false)
          expect(@meeting.conducted_by_id).to eq(nil)
          expect(@meeting.missed?).to be(false)
        end
      end

      context 'Cancelled meeting' do
        before do
          expect(ParticipantRemovedEventPublisher).not_to receive(:call)
          expect(MeetingCancelledEventPublisher).not_to receive(:call)

          @meeting = create(:meeting, owner: @user)
          @meeting.update(status: CANCELLED)
        end

        it 'should not cancel it' do
          expect do
            CancelMeeting.call(@meeting.id)
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting record for cancelling meeting.")
        end

        context 'when meeting medium is google' do
          it 'does not invoke call method of calendar base class' do
            @meeting.update(medium: GOOGLE_PROVIDER)
            expect(Calendar::Base).not_to receive(:call)
            expect do
              CancelMeeting.call(@meeting.id)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting record for cancelling meeting.")
          end
        end
      end
    end

    context 'when user is not owner or participant and has read all access' do
      before do
        @user = create(:user)
        @another_user = create(:user, tenant_id: @user.tenant_id)
        @meeting = create(:meeting, owner: @another_user)
      end

      context 'and update all access' do
        before do
          auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        end

        it 'should cancel meeting' do
          command = CancelMeeting.call(@meeting.id)
          expect(command.success?).to be true
          expect(@meeting.reload.status).to eq(CANCELLED)
        end
      end

      context 'but not update all access' do
        before do
          auth_data = build(:auth_data, :meeting_without_update_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        it 'should raise unauthorized error' do
          expect{CancelMeeting.call(@meeting.id)}.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to update meeting.")
        end
      end
    end

    context 'when security context is missing' do
      let(:get_security_context_base) { instance_double(GetSecurityContext) }

      before do
        allow(GetSecurityContext).to receive(:call).and_return(get_security_context_base)
        allow(get_security_context_base).to receive(:success?).and_return(false)
        @meeting = create(:meeting, owner: create(:user))
      end

      it 'does raise authentication exception' do
        expect(Rails.logger).to receive(:error).once
        expect do
          CancelMeeting.call(@meeting.id)
        end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end
