# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe MarkMeetingAttendance do
  let(:update_google_meeting_payload) do
    {
      summary: @meeting.title,
      location: @params[:location],
      status: @meeting.status == CANCELLED ? 'cancelled' : 'confirmed',
      description: @meeting.description,
      start: { dateTime: @meeting.from.in_time_zone(@meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: @meeting.to.in_time_zone(@meeting.time_zone.name).strftime('%FT%T%:z') },
      attendees: @meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end
    }
  end

  let(:update_google_meeting_success_response) do
    {
      kind: 'calendar#event',
      etag: '3308646057738000',
      id: 'fej8f0ateki87e6lj7dub7sj8g',
      summary: @meeting.title,
      location: @params[:location],
      status: @meeting.status,
      description: @meeting.description,
      start: { dateTime: @meeting.from.in_time_zone(@meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: @meeting.to.in_time_zone(@meeting.time_zone.name).strftime('%FT%T%:z') },
      source: {
        url: APP_KYLAS_HOST,
        title: KYLAS
      },
      attendees: @meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end,
      hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
      conferenceData: {
        createRequest: {
          requestId: 'abcd',
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          },
          status: {
            statusCode: 'success'
          }
        },
        entryPoints: [
          {
            entryPointType: 'video',
            uri: 'https://meet.google.com/qzz-qpfe-ppc',
            label: 'meet.google.com/qzz-qpfe-ppc'
          },
          {
            regionCode: 'US',
            entryPointType: 'phone',
            uri: 'tel:******-301-8549',
            label: '******-301-8549',
            pin: '247728753'
          }
        ],
        conferenceSolution: {
          key: {
            type: 'hangoutsMeet'
          },
          name: 'Google Meet',
          iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
        },
        conferenceId: 'qzz-qpfe-ppc'
      },
      reminders: {
        useDefault: true
      },
      conferenceSolution: {
        key: {
          type: 'hangoutsMeet'
        },
        name: 'Google Meet',
        iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
      },
      eventType: 'default'
    }
  end

  describe '#call' do
    def checkin_checkout_params(params)
      params = ActionController::Parameters.new(params)
      params.permit(
        :location,
        {
          checkedInDetails: [
            :latitude,
            :longitude
          ]
        },
        {
          checkedOutDetails: [
            :latitude,
            :longitude
          ]
        }
      )
    end

    def stub_update_google_meeting_request(status, request_body, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:patch, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@meeting.provider_meeting_id)}?conferenceDataVersion=1&sendUpdates=all")
        .with(body: request_body, headers: headers)
        .to_return(status: status, body: response_body)
    end

    before do
      @user = create(:user)
      @meeting = create(:meeting, owner: @user)
      @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @participant_user = create(:user, tenant_id: @user.tenant_id)
      participant = build(:user_look_up, tenant_id: @participant_user.tenant_id, entity_id: @participant_user.id, name: @participant_user.name)
      @meeting.participants << participant
      @meeting.save!
    end

    context 'check-in' do
      before do
        @params_hash = {
          location: 'Baner, Pune',
          checkedInDetails: {
            latitude: '18.559658',
            longitude: '73.779938'
          }
        }
      end

      context 'valid' do
        before do
          @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
          Thread.current[:auth] = @auth_data
          @params = checkin_checkout_params(@params_hash)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).once
        end

        context 'when checking in for the first time' do
          it 'should check in meeting' do
            meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :in, @params).result
            expect(meeting_attendance.meeting_id).to eq(@meeting.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(@params_hash.dig(:checkedInDetails, :latitude))
            expect(meeting_attendance.checked_in_longitude).to eq(@params_hash.dig(:checkedInDetails, :longitude))
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(false)
            expect(meeting_attendance.meeting.location).to eq(@meeting.location)
          end
        end

        context 'when checking in a checked out meeting' do
          before { create(:meeting_attendance, meeting: @meeting) }

          it 'should check-in meeting and remove check out details' do
            meeting_attendance = @meeting.meeting_attendances.find_by(user_id: @user.id)
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(true)

            meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :in, @params).result
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(false)
            expect(meeting_attendance.checked_in_latitude).to eq(@params_hash.dig(:checkedInDetails, :latitude))
            expect(meeting_attendance.checked_in_longitude).to eq(@params_hash.dig(:checkedInDetails, :longitude))
            expect(@meeting.reload.location).to eq(@meeting.location)
          end
        end

        context 'when any participant tries to check in' do
          it 'checks in' do
            @participant_user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
            Thread.current[:auth] = build(:auth_data, user_id: @participant_user.id, tenant_id: @participant_user.tenant_id, username: @participant_user.name)

            MarkMeetingAttendance.call(@meeting.id, :in, @params).result
            meeting_attendance = @meeting.meeting_attendances.find_by(user_id: @participant_user.id)
            expect(meeting_attendance).to be_present
            expect(meeting_attendance.checked_in?).to be_truthy
          end
        end
      end

      context 'invalid' do
        context 'when checking in an already checked in meeting' do
          before do
            Thread.current[:auth] = @auth_data
            @params = checkin_checkout_params(@params_hash)
            create(:meeting_checked_in, meeting: @meeting)
          end

          it 'should raise invalid data error' do
            expect(Calendar::Base).not_to receive(:call)
            expect{
              MarkMeetingAttendance.call(@meeting.id, :in, @params)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting action.")
          end
        end

        context 'with unauthorised user' do
          before do
            another_user = create(:user, tenant_id: @user.tenant_id)
            Thread.current[:auth] = build(:auth_data, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name)
            @params = checkin_checkout_params(@params_hash)
          end

          it 'should raise forbidden error' do
            expect(Calendar::Base).not_to receive(:call)
            expect do
              MarkMeetingAttendance.call(@meeting.id, :in, @params)
            end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have access to check in or check out meeting.")
          end
        end

        context 'with invalid latitude longitude' do
          before do
            Thread.current[:auth] = @auth_data
            params_hash_dup = @params_hash.deep_dup
            params_hash_dup[:checkedInDetails][:latitude] = nil
            @params = checkin_checkout_params(params_hash_dup)
          end

          it 'should raise invalid data error' do
            expect(Calendar::Base).not_to receive(:call)
            expect do
              MarkMeetingAttendance.call(@meeting.id, :in, @params)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check in without time or location details")
          end
        end
      end

      context 'validate_check_in_location' do
        let(:meeting) { create(:meeting, owner: @user, location_latitude: 18.559658, location_longitude: 73.779938) }
        let(:params) do
          {
            location: 'Baner, Pune',
            checkedInDetails: {
              latitude: '18.559658',
              longitude: '73.779938'
            }
          }
        end

        before do
          @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
          @auth_data = ParseToken.call(@token).result
          thread = Thread.current
          thread[:auth] = @auth_data
          thread[:token] = @token
          @user.update(geofence_config: nil)
          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{@user.id}/geofence")
          .to_return(status: 200, body: { fieldSalesEnabled: true, "meetingCheckInCheckOut": { restrictCheckIn: true, radius: 500 } }.to_json, headers: { 'Content-Type' => 'application/json' })
        end

        context 'if check in location is outside geofence' do
          context 'if restrictCheckIn is true' do
            context 'if fieldSalesEnabled is true' do
              it 'raises an error' do
                params[:checkedInDetails][:latitude] = '18.5670563'
                params[:checkedInDetails][:longitude] = '73.7684087'
      
                expect { MarkMeetingAttendance.call(meeting.id, :in, params).result }.to raise_error(ExceptionHandler::InvalidDataError, "01503021||Checkin location is beyond set geofence")
              end
            end

            context 'if fieldSalesEnabled is false' do
              before do
                stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{@user.id}/geofence")
                .to_return(status: 200, body: { fieldSalesEnabled: false, "meetingCheckInCheckOut": { restrictCheckIn: true, radius: 500 } }.to_json, headers: { 'Content-Type' => 'application/json' })
              end

              it 'checks in user' do
                params[:checkedInDetails][:latitude] = '18.5670563'
                params[:checkedInDetails][:longitude] = '73.7684087'
      
                meeting_attendance = MarkMeetingAttendance.call(meeting.id, :in, params).result
                expect(meeting_attendance.meeting_id).to eq(meeting.id)
              end
            end
          end

          context 'if restrictCheckIn is false' do
            before do
              stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{@user.id}/geofence")
              .to_return(status: 200, body: { fieldSalesEnabled: true, "meetingCheckInCheckOut": { restrictCheckIn: false, radius: 500 } }.to_json, headers: { 'Content-Type' => 'application/json' })
            end

            it 'allows checkin and raises event' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedinBeyondGeofence)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once

              
              params[:checkedInDetails][:latitude] = '18.5670563'
              params[:checkedInDetails][:longitude] = '73.7684087'
    
              meeting_attendance = MarkMeetingAttendance.call(meeting.id, :in, params).result
              expect(meeting_attendance.meeting_id).to eq(meeting.id)
              expect(meeting_attendance.checked_in_at.present?).to be(true)
              expect(meeting_attendance.checked_in_latitude).to eq(params.dig(:checkedInDetails, :latitude))
              expect(meeting_attendance.checked_in_longitude).to eq(params.dig(:checkedInDetails, :longitude))
              expect(meeting_attendance.checked_in?).to be(true)
              expect(meeting_attendance.checked_out?).to be(false)
              expect(meeting_attendance.meeting.location).to eq(meeting.location)
              expect(meeting_attendance.is_checked_in_outside_geofence).to eq(true)
            end
          end

          it 'checks in user if geofence configuration is not present' do
            @user.update(geofence_config: { "meetingCheckInCheckOut": nil })
            params[:checkedInDetails][:latitude] = '18.5670563'
            params[:checkedInDetails][:longitude] = '73.7684087'

            meeting_attendance = MarkMeetingAttendance.call(meeting.id, :in, params).result
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(params.dig(:checkedInDetails, :latitude))
            expect(meeting_attendance.checked_in_longitude).to eq(params.dig(:checkedInDetails, :longitude))
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(false)
            expect(meeting_attendance.meeting.location).to eq(meeting.location)
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end

          it 'checks in user if meeting lat long are not present' do
            meeting.update(location_latitude: nil, location_longitude: nil)

            params[:checkedInDetails][:latitude] = '18.5670563'
            params[:checkedInDetails][:longitude] = '73.7684087'

            meeting_attendance = MarkMeetingAttendance.call(meeting.id, :in, params).result
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(params.dig(:checkedInDetails, :latitude))
            expect(meeting_attendance.checked_in_longitude).to eq(params.dig(:checkedInDetails, :longitude))
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(false)
            expect(meeting_attendance.meeting.location).to eq(meeting.location)
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end
        end

        context 'if check in location is within the geofence' do
          it 'checks in user' do
            meeting_attendance = MarkMeetingAttendance.call(meeting.id, :in, params).result
            expect(meeting_attendance.meeting_id).to eq(meeting.id)
            expect(meeting_attendance.checked_in_at.present?).to be(true)
            expect(meeting_attendance.checked_in_latitude).to eq(params.dig(:checkedInDetails, :latitude))
            expect(meeting_attendance.checked_in_longitude).to eq(params.dig(:checkedInDetails, :longitude))
            expect(meeting_attendance.checked_in?).to be(true)
            expect(meeting_attendance.checked_out?).to be(false)
            expect(meeting_attendance.meeting.location).to eq(meeting.location)
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end
        end
      end
    end

    context 'check-out' do
      before do
        @params_hash = {
          checkedOutDetails: {
            latitude: '18.559658',
            longitude: '73.779938'
          }
        }
      end

      context 'valid' do
        before do
          @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
          Thread.current[:auth] = @auth_data
          @params_hash[:location] = 'Balewadi, Pune'
          @params = checkin_checkout_params(@params_hash)
          create(:meeting_checked_in, meeting: @meeting)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedOut)).once
        end

        it 'should check out meeting' do
          meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :out, @params).result
          expect(meeting_attendance.checked_out_at.present?).to be(true)
          expect(meeting_attendance.checked_out_latitude).to eq(@params_hash.dig(:checkedOutDetails, :latitude))
          expect(meeting_attendance.checked_out_longitude).to eq(@params_hash.dig(:checkedOutDetails, :longitude))
          expect(meeting_attendance.checked_in?).to be(true)
          expect(meeting_attendance.checked_out?).to be(true)
          expect(meeting_attendance.meeting.reload.location).not_to eq(@params_hash[:location])
        end

        it 'should not invoke call method of calendar base class' do
          expect(Calendar::Base).not_to receive(:call)
          MarkMeetingAttendance.call(@meeting.id, :out, @params).result
        end

        context 'when any participant tries to check out' do
          it 'checks out' do
            @participant_user.update(geofence_config: { "meetingCheckInCheckOut" => nil })

            a = create(:meeting_checked_in, meeting: @meeting, user_id: @participant_user.id)
            Thread.current[:auth] = build(:auth_data, user_id: @participant_user.id, tenant_id: @participant_user.tenant_id, username: @participant_user.name)

            MarkMeetingAttendance.call(@meeting.id, :out, @params).result
            meeting_attendance = @meeting.meeting_attendances.find_by(user_id: @participant_user.id)
            expect(meeting_attendance).to be_present
            expect(meeting_attendance.checked_in?).to be_truthy
          end
        end

        context 'validate_check_out_location' do
          before do
            @meeting.update!(location_latitude: 18.559658, location_longitude: 73.779938)
          end

          context 'if check out location is outside geofence' do
            before do
              @params[:checkedOutDetails][:latitude] = '18.5670563'
              @params[:checkedOutDetails][:longitude] = '73.7684087'
            end

            it 'sets is_checked_out_outside_geofence to false if geofence configuration is not present' do  
              meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :out, @params).result
              expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
            end
  
            it 'sets is_checked_out_outside_geofence to false if meeting lat long are not present' do
              @meeting.update(location_latitude: nil, location_longitude: nil)
              @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
  
              meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :out, @params).result
              expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
            end

            it 'sets is_checked_out_outside_geofence to true' do
              @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })

              meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :out, @params).result
              expect(meeting_attendance.is_checked_out_outside_geofence).to eq(true)
            end
          end
  
          context 'if check out location is within the geofence' do
            it 'checks out user' do
              @params[:checkedOutDetails][:latitude] = '18.559658'
              @params[:checkedOutDetails][:longitude] = '73.779938'
              @user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })

              meeting_attendance = MarkMeetingAttendance.call(@meeting.id, :out, @params).result
              expect(meeting_attendance.meeting_id).to eq(@meeting.id)
              expect(meeting_attendance.checked_out_at.present?).to be(true)
              expect(meeting_attendance.checked_out_latitude).to eq(@params.dig(:checkedOutDetails, :latitude))
              expect(meeting_attendance.checked_out_longitude).to eq(@params.dig(:checkedOutDetails, :longitude))
              expect(meeting_attendance.checked_out?).to be(true)
              expect(meeting_attendance.meeting.location).to eq(@meeting.location)
              expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
            end
          end
        end
      end

      context 'invalid' do
        context 'when checking out an already checked out meeting' do
          before do
            Thread.current[:auth] = @auth_data
            @params = checkin_checkout_params(@params_hash)
            create(:meeting_attendance, meeting: @meeting)
          end

          it 'should raise invalid data error' do
            expect(Calendar::Base).not_to receive(:call)
            expect do
              MarkMeetingAttendance.call(@meeting.id, :out, @params)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid meeting action.")
          end
        end

        context 'with unauthorised user' do
          before do
            another_user = create(:user, tenant_id: @user.tenant_id)
            Thread.current[:auth] = build(:auth_data, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name)
            @params = checkin_checkout_params(@params_hash)
            create(:meeting_checked_in, meeting: @meeting)
          end

          it 'should raise forbidden error' do
            expect(Calendar::Base).not_to receive(:call)
            expect do
              MarkMeetingAttendance.call(@meeting.id, :out, @params)
            end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have access to check in or check out meeting.")
          end
        end

        context 'with invalid latitude longitude' do
          before do
            Thread.current[:auth] = @auth_data
            params_hash_dup = @params_hash.deep_dup
            params_hash_dup[:checkedOutDetails][:latitude] = nil
            @params = checkin_checkout_params(params_hash_dup)
            create(:meeting_checked_in, meeting: @meeting)
          end

          it 'should raise invalid data error' do
            expect(Calendar::Base).not_to receive(:call)
            expect {
              MarkMeetingAttendance.call(@meeting.id, :out, @params)
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check out without time or location details")
          end
        end

        context 'with checking out without checking in' do
          before do
            @user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
            Thread.current[:auth] = @auth_data
            @params = checkin_checkout_params(@params_hash)
            create(:meeting_checked_in, meeting: @meeting, checked_in_at: nil, checked_in_latitude: nil, checked_in_longitude: nil)
          end

          it 'should raise invalid data error' do
            expect(Calendar::Base).not_to receive(:call)
            expect do
              MarkMeetingAttendance.call(@meeting.id, :out, @params)
            end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot check out without checking in")
          end
        end
      end
    end
  end
end