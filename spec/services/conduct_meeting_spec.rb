require 'rails_helper'
require 'bunny-mock'

RSpec.describe ConductMeeting do
  describe "#call" do
    let(:user) { create(:user) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:meeting) { create(:meeting, owner: user) }
    let(:auth_data) { build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data_with_update_all_access) {
      build(
        :auth_data,
        user_id: another_user.id,
        tenant_id: another_user.tenant_id,
        username: another_user.name,
        permissions: build_list(
          :auth_permission,
          1,
          name: 'meeting',
          action: build(
            :auth_permission_action,
            read: true,
            read_all: true,
            update: true,
            update_all: true
          )
        )
      ) }

    context 'with valid request' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
      end

      context 'when user is meeting owner' do
        it 'should conduct meeting' do
          thread = Thread.current
          thread[:auth] = auth_data

          meeting_response = ConductMeeting.call(meeting.id).result
          expect(meeting_response.id).to eq(meeting.id)
          expect(meeting_response.status).to eq(CONDUCTED)
          expect(meeting_response.conducted_at.present?).to eq(true)
          expect(meeting_response.conducted_by_id).to eq(user.id)
          expect(meeting_response.created_by_id).to eq(user.id)
          expect(meeting_response.updated_by_id).to eq(user.id)
        end
      end

      context 'when user is not meeting owner but has read all access' do
        context 'and update all access' do
          it 'should conduct meeting' do
            thread = Thread.current
            thread[:auth] = auth_data_with_update_all_access

            meeting_response = ConductMeeting.call(meeting.id).result
            expect(meeting_response.id).to eq(meeting.id)
            expect(meeting_response.status).to eq(CONDUCTED)
            expect(meeting_response.conducted_at.present?).to eq(true)
            expect(meeting_response.conducted_by_id).to eq(another_user.id)
            expect(meeting_response.created_by_id).to eq(user.id)
          expect(meeting_response.updated_by_id).to eq(another_user.id)
          end
        end
      end
    end

    context 'with invalid request' do
      context 'with valid user but incorrect meeting id' do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
        end

        it 'should raise Record Not Found Error' do
          expect{ConductMeeting.call(meeting.id + 1)}.to raise_error(ExceptionHandler::NotFound)
        end
      end

      context 'when conducting a cancelled meeting' do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
          meeting.update(status: CANCELLED)
        end

        it 'should raise Invalid Data Error' do
          expect{ConductMeeting.call(meeting.id)}.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||cannot update cancelled meeting")
        end
      end

      context 'when user is not meeting owner but has read all access' do
        context 'and does not have update all access' do
          let(:auth_data_without_update_all_access) {
            build(
              :auth_data,
              user_id: another_user.id,
              tenant_id: another_user.tenant_id,
              username: another_user.name,
              permissions: build_list(
                :auth_permission,
                1,
                name: 'meeting',
                action: build(
                  :auth_permission_action,
                  read: true,
                  read_all: true,
                  update: true,
                  update_all: false
                )
              )
            )
          }
          it 'should raise unauthorized error' do
            thread = Thread.current
            thread[:auth] = auth_data_without_update_all_access

            expect{ConductMeeting.call(meeting.id).result}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
          end
        end
      end

      context 'with invalid user' do
        before do
          thread = Thread.current
          thread[:auth] = nil
        end

        it 'should raise Authentication error' do
          expect{ConductMeeting.call(meeting.id)}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end
    end
  end
end
