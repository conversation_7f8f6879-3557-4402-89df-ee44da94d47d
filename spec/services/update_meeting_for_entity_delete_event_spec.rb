# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateMeetingForEntityDeleteEvent do
  let(:user) { create(:user) }
  let(:lead_look_up) { create(:lead_look_up, tenant_id: user.tenant_id, entity_id: 1) }
  let(:offline_meeting) { create(:meeting) }

  describe '#call' do
    context 'when no meetings are present on lookup' do
      it 'just deletes the lookup' do
        UpdateMeetingForEntityDeleteEvent.call(lead_look_up.entity_id, user.tenant_id, user.id, lead_look_up.entity_type)
        expect(LookUp.find_by(id: lead_look_up.id)).to eq(nil)
      end
    end

    context 'when meeting is OFFLINE' do
      context 'when meetings are present on lookup' do
        context 'when lead lookup is present on meeting and lead entity is organizer' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
          end

          it 'should removes lead lookup and make lead organizer to external' do
            lead_look_up = create(:lead_look_up, tenant_id: user.tenant_id, entity_id: 1)
            deal_look_up = create(:deal_look_up, tenant_id: user.tenant_id, entity_id: 2)
            offline_meeting.participants << lead_look_up
            offline_meeting.participants << deal_look_up
            offline_meeting.organizer = lead_look_up
            UpdateMeetingForEntityDeleteEvent.call(lead_look_up.entity_id, user.tenant_id, user.id, lead_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: offline_meeting.id, look_up_id: lead_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: lead_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: deal_look_up.id)).to eq(deal_look_up)
            offline_meeting.reload
            expect(offline_meeting.organizer.entity).to match(/#{LOOKUP_EXTERNAL}_/)
            expect(offline_meeting.organizer.email).to eq(lead_look_up.email)
          end
        end

        context 'when contact lookup is present on meeting and contact entity is organizer' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
          end

          it 'should removes contact lookup and make contact organizer to external' do
            contact_look_up = create(:contact_look_up, tenant_id: user.tenant_id, entity_id: 1)
            deal_look_up = create(:deal_look_up, tenant_id: user.tenant_id, entity_id: 2)
            offline_meeting.participants << contact_look_up
            offline_meeting.participants << deal_look_up
            offline_meeting.organizer = contact_look_up
            UpdateMeetingForEntityDeleteEvent.call(contact_look_up.entity_id, user.tenant_id, user.id, contact_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: offline_meeting.id, look_up_id: contact_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: contact_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: deal_look_up.id)).to eq(deal_look_up)
            offline_meeting.reload
            expect(offline_meeting.organizer.entity).to match(/#{LOOKUP_EXTERNAL}_/)
            expect(offline_meeting.organizer.email).to eq(contact_look_up.email)
          end
        end

        context 'when deal lookup is present on meeting' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
          end

          it 'just removes the deal lookup' do
            contact_look_up = create(:contact_look_up, tenant_id: user.tenant_id, entity_id: 1)
            deal_look_up = create(:deal_look_up, tenant_id: user.tenant_id, entity_id: 2)
            offline_meeting.participants << contact_look_up
            offline_meeting.participants << deal_look_up
            UpdateMeetingForEntityDeleteEvent.call(deal_look_up.entity_id, user.tenant_id, user.id, deal_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: offline_meeting.id, look_up_id: deal_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: deal_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: contact_look_up.id)).to eq(contact_look_up)
          end
        end

        context 'when company lookup is present on meeting' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
          end

          it 'just removes the company lookup' do
            contact_look_up = create(:contact_look_up, tenant_id: user.tenant_id, entity_id: 1)
            company_look_up = create(:company_look_up, tenant_id: user.tenant_id, entity_id: 2)
            offline_meeting.participants << contact_look_up
            offline_meeting.participants << company_look_up
            UpdateMeetingForEntityDeleteEvent.call(company_look_up.entity_id, user.tenant_id, user.id, company_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: offline_meeting.id, look_up_id: company_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: company_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: contact_look_up.id)).to eq(contact_look_up)
          end
        end
      end
    end

    context 'when meeting medium is GOOGLE' do
      let(:update_google_meeting_payload) do
        attendees = @google_meeting.participants.reject do |attendee|
          attendee.entity.start_with?('deal') || attendee.entity.start_with?('company')
        end
        {
          summary: @google_meeting.title,
          location: @google_meeting.location,
          status: @google_meeting.status == CANCELLED ? 'cancelled' : 'confirmed',
          description: @google_meeting.description,
          start: { dateTime: @google_meeting.from.in_time_zone(@google_meeting.time_zone.name).strftime('%FT%T%:z') },
          end: { dateTime: @google_meeting.to.in_time_zone(@google_meeting.time_zone.name).strftime('%FT%T%:z') },
          attendees: attendees.map do |participant|
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          end
        }
      end

      let(:update_google_meeting_success_response) do
        attendees = @google_meeting.participants.reject do |attendee|
          attendee.entity.start_with?('deal') || attendee.entity.start_with?('company')
        end
        {
          kind: 'calendar#event',
          etag: '3308646057738000',
          id: 'fej8f0ateki87e6lj7dub7sj8g',
          summary: @google_meeting.title,
          location: @google_meeting.location,
          status: @google_meeting.status,
          description: @google_meeting.description,
          start: { dateTime: @google_meeting.from.in_time_zone(@google_meeting.time_zone.name).strftime('%FT%T%:z') },
          end: { dateTime: @google_meeting.to.in_time_zone(@google_meeting.time_zone.name).strftime('%FT%T%:z') },
          source: {
            url: APP_KYLAS_HOST,
            title: KYLAS
          },
          attendees: attendees.map do |participant|
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          end,
          hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
          conferenceData: {
            createRequest: {
              requestId: 'abcd',
              conferenceSolutionKey: {
                type: 'hangoutsMeet'
              },
              status: {
                statusCode: 'success'
              }
            },
            entryPoints: [
              {
                entryPointType: 'video',
                uri: 'https://meet.google.com/qzz-qpfe-ppc',
                label: 'meet.google.com/qzz-qpfe-ppc'
              },
              {
                regionCode: 'US',
                entryPointType: 'phone',
                uri: 'tel:******-301-8549',
                label: '******-301-8549',
                pin: '247728753'
              }
            ],
            conferenceSolution: {
              key: {
                type: 'hangoutsMeet'
              },
              name: 'Google Meet',
              iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
            },
            conferenceId: 'qzz-qpfe-ppc'
          },
          reminders: {
            useDefault: true
          },
          conferenceSolution: {
            key: {
              type: 'hangoutsMeet'
            },
            name: 'Google Meet',
            iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
          },
          eventType: 'default'
        }
      end

      def stub_update_google_meeting_request(status, request_body, response_body)
        headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
        stub_request(:patch, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@google_meeting.provider_meeting_id)}?conferenceDataVersion=1&sendUpdates=all")
          .with(body: request_body, headers: headers)
          .to_return(status: status, body: response_body)
      end

      before do
        @user = create(:user)
        @google_meeting = create(:meeting, medium: GOOGLE_PROVIDER, owner_id: @user.id, provider_meeting_id: 1, location: 'Baner, Pune')
        @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                        provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
      end

      context 'when meetings are present on lookup' do
        context 'when deal lookup is present on meeting' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
            expect(Calendar::Base).not_to receive(:call)
          end

          it 'just removes the deal lookup and updates meeting on google side' do
            contact_look_up = create(:contact_look_up, tenant_id: @user.tenant_id, entity_id: 1)
            deal_look_up = create(:deal_look_up, tenant_id: @user.tenant_id, entity_id: 2)
            @google_meeting.participants << contact_look_up
            @google_meeting.participants << deal_look_up
            stub_update_google_meeting_request(200, update_google_meeting_payload.to_json, update_google_meeting_success_response.to_json)
            UpdateMeetingForEntityDeleteEvent.call(deal_look_up.entity_id, @user.tenant_id, @user.id, deal_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: @google_meeting.id, look_up_id: deal_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: deal_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: contact_look_up.id)).to eq(contact_look_up)
          end
        end

        context 'when company lookup is present on meeting' do
          before do
            expect(Calendar::Base).not_to receive(:call)
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingEntityDisassociated)).once
          end

          it 'just removes the company lookup' do
            contact_look_up = create(:contact_look_up, tenant_id: @user.tenant_id, entity_id: 1)
            company_look_up = create(:company_look_up, tenant_id: @user.tenant_id, entity_id: 2)
            @google_meeting.participants << contact_look_up
            @google_meeting.participants << company_look_up
            stub_update_google_meeting_request(200, update_google_meeting_payload.to_json, update_google_meeting_success_response.to_json)
            UpdateMeetingForEntityDeleteEvent.call(company_look_up.entity_id, @user.tenant_id, @user.id, company_look_up.entity_type)
            expect(MeetingLookUp.find_by(meeting_id: @google_meeting.id, look_up_id: company_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: company_look_up.id)).to eq(nil)
            expect(LookUp.find_by(id: contact_look_up.id)).to eq(contact_look_up)
          end
        end
      end

      context 'when only owner is present on meeting' do
        def stub_delete_google_meeting_request(status, response_body)
          headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Accept': 'application/json' }
          stub_request(:delete, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@google_meeting.provider_meeting_id)}?sendUpdates=all")
            .with(headers: headers)
            .to_return(status: status, body: response_body)
        end

        before do
          expect(ParticipantRemovedEventPublisher).not_to receive(:call)
          expect(MeetingCancelledEventPublisher).not_to receive(:call)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(1).times
          expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
        end

        it 'destroys the meeting' do
          stub_delete_google_meeting_request(204, {}.to_json)
          user_look_up = create(:user_look_up, entity_id: 1, tenant_id: @user.tenant_id)
          user_look_up.meetings << @google_meeting
          UpdateMeetingForEntityDeleteEvent.call(user_look_up.entity_id, @user.tenant_id, @user.id, user_look_up.entity_type)
          expect(Meeting.find_by(id: @google_meeting.id)).to eq(nil)
          expect(LookUp.find_by(id: user_look_up.id)).to eq(nil)
        end
      end
    end
  end
end
