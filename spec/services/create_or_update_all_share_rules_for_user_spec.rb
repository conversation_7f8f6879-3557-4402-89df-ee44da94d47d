# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateOrUpdateAllShareRulesForUser do
  describe '#call' do
    let(:user) { create(:user) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:params) do
      data = JSON.parse(file_fixture('listeners/create-all-meeting-share-rules-payload.json').read)
      data['tenantId'] = user.tenant_id
      data['ownerId'] = user.id
      data
    end

    context 'valid' do
      context 'when existing share rule is present' do
        let(:to_user) { create(:user, tenant_id: user.tenant_id, id: 100001) }

        before do
          @share_rule = create(:share_rule, tenant_id: user.tenant_id, from_id: user.id, to_id: to_user.id, share_all_records: true, system_default: true, created_by: another_user, updated_by: another_user)
        end

        context 'when user exists' do
          it 'updates share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2))
            expect { described_class.call(params).result }.to change(ShareRule, :count).by(0).and change(User, :count).by(0)
            expect(@share_rule.reload.name).to eq('Via_Manager')
            expect(@share_rule.actions).to eq({ 'read' => true, 'update' => true, 'write' => true })
            expect(@share_rule.created_by_id).to eq(another_user.id)
            expect(@share_rule.updated_by_id).to eq(user.id)
          end
        end
      end

      context 'when existing share rule is not present' do
        context 'when user exists' do
          before { create(:user, tenant_id: user.tenant_id, id: 100001) }

          it 'creates share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2))
            expect { described_class.call(params).result }.to change(ShareRule, :count).by(1).and change(User, :count).by(0)
            share_rule = ShareRule.last
            expect(share_rule.from_id).to eq(user.id)
            expect(share_rule.to_id).to eq(100001)
            expect(share_rule.tenant_id).to eq(user.tenant_id)
            expect(share_rule.share_all_records).to be_truthy
            expect(share_rule.system_default).to be_truthy
            expect(share_rule.name).to eq('Via_Manager')
            expect(share_rule.actions).to eq({ 'read' => true, 'update' => true, 'write' => true })
            expect(share_rule.created_by_id).to eq(user.id)
            expect(share_rule.updated_by_id).to eq(user.id)
          end
        end

        context 'when user does not exists' do
          let(:token) { GenerateToken.call(100001, user.tenant_id).result }

          before do
            params['ownerId'] = 100001
            params['userActions'].first['id'] = user.id
          end

          it 'creates share rule and user' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2))
            expect { described_class.call(params).result }.to change(ShareRule, :count).by(1).and change(User, :count).by(1)

            share_rule = ShareRule.last
            expect(share_rule.from_id).to eq(100001)
            expect(share_rule.to_id).to eq(user.id)
            expect(share_rule.tenant_id).to eq(user.tenant_id)
            expect(share_rule.share_all_records).to be_truthy
            expect(share_rule.system_default).to be_truthy
            expect(share_rule.name).to eq('Via_Manager')
            expect(share_rule.actions).to eq({ 'read' => true, 'update' => true, 'write' => true })
            expect(share_rule.created_by_id).to eq(100001)
            expect(share_rule.updated_by_id).to eq(100001)

            new_user = User.last
            expect(new_user.id).to eq(100001)
            expect(new_user.tenant_id).to eq(user.tenant_id)
            expect(new_user.name).to eq('From User')
          end
        end
      end
    end

    context 'invalid' do
      context 'when one of the to user is not found' do
        let(:token) { GenerateToken.call(user.id, user.tenant_id).result }

        before do
          params['userActions'] << { "id" => 124, "action" => { "read" => true, "update" => false } }
        end

        it 'skips create or update of that share rule' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2))
          expect(Rails.logger).to receive(:error).with("GetUserDetails user errors [\"Name can't be blank\"]")
          expect(Rails.logger).to receive(:error).with('Listeners::CreateAllShareRulesForHierarchy to user 124 missing 01502001||Unauthorized access.')
          expect { described_class.call(params).result }.to change(ShareRule, :count).by(1).and change(User, :count).by(1)

          share_rule = ShareRule.last
          expect(share_rule.from_id).to eq(user.id)
          expect(share_rule.to_id).to eq(100001)
          expect(share_rule.tenant_id).to eq(user.tenant_id)
          expect(share_rule.share_all_records).to be_truthy
          expect(share_rule.system_default).to be_truthy
          expect(share_rule.name).to eq('Via_Manager')
          expect(share_rule.actions).to eq({ 'read' => true, 'update' => true, 'write' => true })
          expect(share_rule.created_by_id).to eq(user.id)
          expect(share_rule.updated_by_id).to eq(user.id)

          new_user = User.last
          expect(new_user.id).to eq(100001)
          expect(new_user.tenant_id).to eq(user.tenant_id)
          expect(new_user.name).to eq('To User')
        end
      end
    end
  end
end
