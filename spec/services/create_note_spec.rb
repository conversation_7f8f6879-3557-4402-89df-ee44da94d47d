require 'rails_helper'

RSpec.describe CreateNote do
  describe '#call' do
    context 'with valid input' do
      before do
        @user = create(:user)
        Thread.current[:auth] = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

        @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user)
        participant = build(:user_look_up)
        participant.name = @user.name
        participant.tenant_id = @user.tenant_id
        participant.entity = "user_#{@user.id}"
        @meeting.participants << participant

        @params = { description: "Test description" }
      end

      it "associates note with meeting" do
        note = CreateNote.call(@params, @meeting.id).result

        expect(@meeting.notes.count).to eq(1)
        expect(@meeting.notes.first.description).to eq(note.description)
        expect(@meeting.notes.first.created_by_id).to eq(note.created_by_id)
        expect(@meeting.notes.first.tenant_id).to eq(@user.tenant_id)
      end

      after do
        thread = Thread.current
        thread[:auth] = nil
      end
    end

    context 'with invalid input - ' do
      before do
        @user = create(:user)

        @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user)
        participant = build(:user_look_up)
        participant.name = @user.name
        participant.tenant_id = @user.tenant_id
        participant.entity = "user_#{@user.id}"
        @meeting.participants << participant

        @params = { description: "Test description" }
      end

      context 'without security context' do
        it "raises Authentication error" do
          expect{
            CreateNote.call(@params, @meeting.id).result
          }.to raise_error( ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context "when note description is not provided" do
        before do
          Thread.current[:auth] = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          @params = {}
        end

        it "raises invalid data error" do
          expect{
            CreateNote.call(@params, @meeting.id).result
          }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid note.")
        end
      end

      context 'when meeting not found' do
        before do
          Thread.current[:auth] = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        end

        it "raises not found error" do
          expect{
            CreateNote.call(@params, -1).result
          }.to raise_error(ExceptionHandler::NotFound, "01502001||Meeting not found.")
        end
      end

      context "fails when user is not owner or invitee for the meeting" do
        before do
          Thread.current[:auth] = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          @meeting.update(owner: create(:user, tenant_id: @user.tenant_id))
          @meeting.participants = []
          @params = { description: "Test description" }
        end

        it "raises note creation not allowed error" do
          expect{
            CreateNote.call(@params, @meeting.id).result
          }.to raise_error(ExceptionHandler::NoteCreateNotAllowed, "#{ErrorCode.note_create_not_allowed}||Note creation is not allowed.")
        end
      end

      context "when user doesn't have create permission on note" do
        before do
          Thread.current[:auth] = build(:auth_data, :note_without_write, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          @params = { description: "Test description" }
        end

        it "raises note creation not allowed error" do
          expect{
            CreateNote.call(@params, @meeting.id).result
          }.to raise_error(ExceptionHandler::NoteCreateNotAllowed, "#{ErrorCode.note_create_not_allowed}||Note creation is not allowed.")
        end
      end
    end
  end
end