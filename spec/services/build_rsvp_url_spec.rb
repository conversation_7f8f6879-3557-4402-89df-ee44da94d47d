require 'rails_helper'

RSpec.describe BuildRsvpUrl do
  describe '#call' do
    before do
      @meeting = build(:meeting_with_associated_entities, participant_user: true)
      @participant = @meeting.participants.first
      @rsvp_token = "test"
    end

    context 'RSVP Yes' do
      before do
        @rsvp_status = RSVP_YES
        @command = BuildRsvpUrl.call(@meeting, @participant, @rsvp_token, @rsvp_status)
      end

      it 'is successful ' do
        expect(@command.success?).to be true
      end

      it 'returns valid URL ' do
        url = @command.result
        uri = URI.parse(url)
        expect(uri).to be_truthy
        expect(uri.is_a?(URI::HTTP)).to be true
        expect(uri.scheme + "://" + uri.host).to be == APP_KYLAS_HOST
        expect(uri.path).to be == "/meetings/rsvp"
      end

      context 'data params' do
        it 'returns url with data params ' do
          url = @command.result
          uri = URI.parse(url)
          data_param, data_value = URI.decode_www_form(uri.query)[0]
          expect(data_param).to be == "data"

          decoded_value = Base64.decode64(data_value)
          params = URI.decode_www_form(decoded_value)
          expected_params = [["mid", @meeting.public_id], ["pid", @participant.public_id], ["token", @rsvp_token], ["rsvp", @rsvp_status]]
          expect(params).to be  == expected_params
        end
      end
    end

    context 'RSVP NO' do
      before do
        @meeting.from = 5.minutes.ago
        @rsvp_status = RSVP_NO
      end

      it 'should be successful' do
        command = BuildRsvpUrl.call(@meeting, @participant, @rsvp_token, @rsvp_status)
        expect(command.success?).to be(true)
      end

      it 'should return valid URL ' do
        command = BuildRsvpUrl.call(@meeting, @participant, @rsvp_token, @rsvp_status)
        url = command.result
        uri = URI.parse(url)
        expect(uri).to be_truthy
        expect(uri.is_a?(URI::HTTP)).to be true
        expect(uri.scheme + "://" + uri.host).to be == APP_KYLAS_HOST
        expect(uri.path).to be == "/meetings/rsvp"
      end

      it 'should return url with data params ' do
        command = BuildRsvpUrl.call(@meeting, @participant, @rsvp_token, @rsvp_status)
        url = command.result
        uri = URI.parse(url)
        data_param, data_value = URI.decode_www_form(uri.query)[0]
        expect(data_param).to be == "data"

        decoded_value = Base64.decode64(data_value)
        params = URI.decode_www_form(decoded_value)
        expected_params = [["mid", @meeting.public_id], ["pid", @participant.public_id], ["token", @rsvp_token], ["rsvp", @rsvp_status]]
        expect(params).to be  == expected_params
      end
    end
  end
end
