require 'rails_helper'

RSpec.describe UpdateField do
  describe '#call' do
    let!(:user)             { create(:user)}
    let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }

    context 'with authorized user' do
      let(:params) {
        {
          "pickLists": nil,
          "displayName": "Time(From)",
          "description": "Updated description",
          "filterable": true,
          "sortable": false,
          "standard": true,
          "required": false,
          "type": "TEXT_FIELD"
        }.with_indifferent_access
      }

      before do
        thread = Thread.current
        thread[:auth] = auth_data
        @field = FactoryBot.create(:field, field_type: 'TEXT_FIELD', tenant_id: auth_data.tenant_id, created_by: user, updated_by: user)
        params.merge!('id' => @field.id)
      end

      context 'with standard field' do
        it 'should update field with valid params' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated)).once

          updated_field = described_class.call(@field.id, params).result
          expect(updated_field.display_name).to eq(params['displayName'])
          expect(updated_field.description).to eq(params['description'])

          # Currently, we are updating only above two fields, rest of the fiels should remain same
          expect(updated_field.internal_name).to eq(@field.internal_name)
          expect(updated_field.is_required).to eq(@field.is_required)
          expect(updated_field.is_sortable).to eq(@field.is_sortable)
          expect(updated_field.is_filterable).to eq(@field.is_filterable)
          expect(updated_field.is_standard).to eq(@field.is_standard)
          expect(updated_field.updated_by).to eq(user)
        end
      end

      context 'with custom field' do
        context 'with non-picklist field' do
          before { @field.update!(is_standard: false, internal_name: "cf#{@field.internal_name}") }

          it 'should update field with valid params' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))

            updated_field = described_class.call(@field.id, params).result
            expect(updated_field.display_name).to eq(params['displayName'])
            expect(updated_field.description).to eq(params['description'])

            expect(updated_field.internal_name).to eq(@field.internal_name)
            expect(updated_field.is_required).to eq(params['required'])
            expect(updated_field.is_sortable).to eq(params['sortable'])
            expect(updated_field.is_filterable).to eq(params['filterable'])
            expect(updated_field.is_standard).to eq(false)
            expect(updated_field.updated_by).to eq(user)
          end
        end

        context 'with picklist field' do
          before do
            @field.update!(is_standard: false, internal_name: "cf#{@field.internal_name}", field_type: 'PICK_LIST', is_sortable: false)
            picklist = create(:picklist, field: @field, tenant_id: @field.tenant_id)
            @picklist_value = create(:picklist_value, picklist: picklist, tenant_id: @field.tenant_id)
            params[:pickLists] = [{ id: @picklist_value.id, name: @picklist_value.internal_name, display_name: @picklist_value.display_name },
                                  { id: nil, name: nil, display_name: 'Picklist Value 2' }]
          end

          it 'should create new picklist values if updated' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdatedV2))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldUpdated))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated))

            updated_field = described_class.call(@field.id, params).result
            expect(updated_field.display_name).to eq(params['displayName'])
            expect(updated_field.description).to eq(params['description'])
            expect(updated_field.updated_by).to eq(user)

            expect(updated_field.reload.picklist.picklist_values.size).to be(2)
            expect(updated_field.picklist.picklist_values.map(&:display_name)).to match_array([@picklist_value.display_name, 'Picklist Value 2'])
          end

          context 'when updating with duplicate picklist value' do
            before { params[:pickLists] << params[:pickLists].last }

            it 'should raise invalid data error if duplicate picklist value' do
              expect(PublishEvent).to receive(:call).exactly(0).times
              expect do
                described_class.call(@field.id, params).result
              end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid field.")
            end
          end
        end
      end

      it 'should raise NotFound error with not_found error code if field is tenant_id' do
        @field.update(internal_name: 'tenant_id')
        expect{described_class.call(@field.id, params).result}.to raise_error(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Field not found.")
      end

      it 'should raise Invalid Data Error with invalid error code' do
        expect {
          described_class.call(@field.id, params.merge('displayName' => '')).result
        }
        .to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid field.")
      end
    end

    context 'with unauthorized user' do
      before do
        thread = Thread.current
        thread[:auth] = nil
      end
      it 'should raise Authentication Error with unauthorized error code' do
        expect{described_class.call(1, {}).result}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end
