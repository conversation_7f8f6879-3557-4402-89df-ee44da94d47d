require 'rails_helper'

RSpec.describe MeetingCancelledEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      allow(PublishEvent).to receive(:call)
      @meeting = create(:meeting, owner: @user)
      lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      @meeting.participants << [lead_lookup]
      @meeting.save
    end

    context "for meeting cancelled with participants" do
      it 'should call PublishEvent for each participants with each user' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
        MeetingCancelledEventPublisher.call(@meeting)
      end
    end

    context "for meeting destroyed with participants" do
      it 'should call PublishEvent for each participants with each user' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
        MeetingCancelledEventPublisher.call(@meeting)
      end
    end
  end
end
