require 'rails_helper'

 #TODO Sometimes test cases failes randomly
RSpec.describe DeleteNote do
  describe '#call' do
    context "#destroy" do
      before(:each) do
        @user = create(:user)
        @another_user = create(:user, tenant_id: @user.tenant_id)
      end

      context "With Valid Permission" do
        context "When user have delete permission" do
          before(:each) do
            auth_data = build(:auth_data, :note_with_delete_true, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data

            @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user, title: 'Test meeting to check if user have delete permission')
            @note = create(:note, created_by_id: @user.id, meeting_id: @meeting.id, created_at: 1.minutes.ago, description: 'Note created by logged in user' )
            participant = build(:user_look_up)
            participant.name = @user.name
            participant.tenant_id = @user.tenant_id
            participant.entity = "user_#{@user.id}"
            @meeting.participants << participant
          end

          it 'should delete the note created by him' do
            command = DeleteNote.call(@note.id, @meeting.id)

            expect( command.success? ).to be true
            expect(Note.find_by(id: @note.id)).to eq(nil)
          end

          it 'should delete the note which is created by another user and current user is organizer of meeting' do
            @note1 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id, description: 'Note created by another user')

            command = DeleteNote.call(@note1.id, @meeting.id)

            expect( command.success? ).to be true
            expect(Note.find_by(id: @note1.id)).to eq(nil)
          end
        end

        context "When user have delete all permission" do
          before(:each) do
            auth_data = build(:auth_data, :note_with_delete_true, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data


            @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user, title: 'Test meeting to check if user have delete all permission')
            @note = create(:note, created_by_id: @user.id, meeting_id: @meeting.id, created_at: 1.minutes.ago, description: 'Note created by logged in user' )
            participant = build(:user_look_up)
            participant.name = @user.name
            participant.tenant_id = @user.tenant_id
            participant.entity = "user_#{@user.id}"
            @meeting.participants << participant
          end

          it 'should delete the note which he created' do
            command = DeleteNote.call(@note.id, @meeting.id)

            expect( command.success? ).to be true
            expect(Note.find_by(id: @note.id)).to eq(nil)
          end

          it 'should delete the note created by another user' do
            note1 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id, description: 'Note created by another user')

            command = DeleteNote.call(note1.id, @meeting.id)

            expect( command.success? ).to be true
            expect(Note.find_by(id: note1.id)).to eq(nil)
          end
        end

        context "When user is not organizer not creater of note" do
          it "shouldn't delete the note which are not created by him and he is not organizer" do
            @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @another_user)
            @note = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)
            participant = build(:user_look_up)
            participant.name = @user.name
            participant.tenant_id = @user.tenant_id
            participant.entity = "user_#{@user.id}"
            @meeting.participants << participant

            auth_data = build(:auth_data, :note_without_delete_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data

            expect{
              DeleteNote.call(@note.id, @meeting.id).result
            }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete note.")
          end
        end

        context "When publish_usage parameter is present" do
          before(:each) do
            auth_data = build(:auth_data, :note_with_delete_true, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
            thread = Thread.current
            thread[:auth] = auth_data

            @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user, title: 'Test meeting to check if user have delete permission')
            @note = create(:note, created_by_id: @user.id, meeting_id: @meeting.id, created_at: 1.minutes.ago, description: 'Note created by logged in user' )
          end

          context 'when publish_usage is true' do
            it 'should publish usage after deletion of note' do
              expect(TenantUsagePublisher).to receive(:call).with(@meeting.tenant_id).exactly(1).times

              DeleteNote.call(@note.id, @meeting.id, true)
              expect(Note.find_by(id: @note.id)).to eq(nil)
            end
          end

          context 'when publish_usage is false' do
            it 'should not publish usage after deletion of note' do
              expect(TenantUsagePublisher).not_to receive(:call)

              DeleteNote.call(@note.id, @meeting.id, false)
              expect(Note.find_by(id: @note.id)).to eq(nil)
            end
          end
        end
      end

      context "With Invalid Permission" do
        before do
          @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @another_user)
          @note = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)
          participant = build(:user_look_up)
          participant.name = @user.name
          participant.tenant_id = @user.tenant_id
          participant.entity = "user_#{@user.id}"
          @meeting.participants << participant

          auth_data = build(:auth_data, :note_without_delete, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
        end

        context "When user dont have delete permission" do
          it "shouldn't delete the note" do
            expect{
              DeleteNote.call(@note.id, @meeting.id).result
            }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete note.")
          end
        end

        context "When user dont have delete all permission" do
          it "shouldn't delete the note" do
            note1 = create(:note, created_by_id: @another_user.id, meeting_id: @meeting.id)

            expect{
              DeleteNote.call(note1.id, @meeting.id).result
            }.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||You do not have permission to delete note.")
          end
        end
      end
    end
  end
end
