# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules::Update do
  describe '#call' do
    before do
      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)
      @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      @token_without_share_rule_create = @auth_data
      @auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      permission_without_share_rule_create = build(:auth_data, :share_rule_without_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      @token_without_share_rule_create.permissions += permission_without_share_rule_create
    end

    context 'when user context is not present' do
      before { Thread.current[:auth] = nil }

      it 'returns unauthorized error' do
        expect(PublishEvent).not_to receive(:call)
        expect {
          described_class.new(update_share_meeting_params(12)).call
        }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end

    context 'when user is context is present' do
      before do
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
      end

      context 'and share rule is not present' do
        it 'raises error' do
          expect(PublishEvent).not_to receive(:call)
          expect {
            described_class.new(update_share_meeting_params(12)).call
          }.to raise_error(ExceptionHandler::NotFound, '01503019||Share rule not found.')
        end
      end

      context 'when share rule is present' do
        context 'and current user is not creator of that share rule' do
          before do
            @share_rule = create(:share_rule, tenant_id: @user.tenant_id)
          end

          it 'raises error' do
            expect(PublishEvent).not_to receive(:call)
            expect{
              described_class.new(update_share_meeting_params(@share_rule.id)).call
            }.to raise_error(ExceptionHandler::Forbidden, '01501005||Unauthorized access.')
          end
        end

        context 'and share rule is system default' do
          before do
            @share_rule = create(:share_rule, tenant_id: @user.tenant_id, system_default: true,created_by: @user)
          end

          it 'raises error' do
            expect(PublishEvent).not_to receive(:call)
            expect{
              described_class.new(update_share_meeting_params(@share_rule.id)).call
            }.to raise_error(ExceptionHandler::Forbidden, '01501005||Unauthorized access.')
          end
        end

        context 'when user can update share rule' do
          before do
            @share_rule = create(:share_rule, created_by: @user)
          end

          context 'and actions other than allowed are passed' do
            it 'raises invalid actions error' do
              expect(PublishEvent).not_to receive(:call)
              expect {
                described_class.new(update_share_meeting_params(@share_rule.id, { actions: { email: true } })).call
              }.to raise_error(ExceptionHandler::InvalidDataError, '01503014||Actions are invalid')
            end
          end

          context 'To and from users are same' do
            it 'raises error' do
              expect(PublishEvent).not_to receive(:call)
              expect {
                described_class.new(update_share_meeting_params(@share_rule.id, { from_id: @user.id, to_id: @user.id })).call
              }.to raise_error(ExceptionHandler::InvalidDataError, '01503018||From and To users cannot be same.')
            end
          end

          context 'when meeting id is present' do
            context 'and meeting is not found' do
              it 'raises not found error' do
                expect(PublishEvent).not_to receive(:call)
                expect {
                  described_class.new(update_share_meeting_params(@share_rule.id, { id: 12 })).call
                }.to raise_error(ExceptionHandler::NotFound, '01502001||Meeting not found.')
              end
            end

            context 'and meeting is found' do
              context 'and target user already owns meeting' do
                before do
                  @meeting = create(:meeting, owner: @user, created_by: create(:user, tenant_id: @user.tenant_id), time_zone: nil, tenant_id: @user.tenant_id)
                  stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
                    headers: {
                    'Authorization'=>'Bearer '+ @token
                     }
                  ).to_return(
                    status: 200,
                    body: [
                      {
                        "id": 213,
                        "name": "Jane Doe",
                        "email": {"primary": true,"value": "<EMAIL>"}
                      }
                    ].to_json, headers: {}
                  )

                  stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
                    headers: {
                    'Authorization'=>'Bearer '+ @token
                     }
                  ).to_return(
                    status: 200,
                    body: [
                      {
                        "id": 123,
                        "name": "Jane Doe",
                        "email": {"primary": true,"value": "<EMAIL>"}
                      }
                    ].to_json, headers: {}
                  )
                end

                it 'raises error' do
                  expect(PublishEvent).not_to receive(:call)
                  expect {
                    described_class.new(update_share_meeting_params(@share_rule.id, { id: @meeting.id, from_id: @meeting.owner_id, to_id: @meeting.created_by_id })).call
                  }.to raise_error(ExceptionHandler::InvalidDataError, '01503015||You cannot share Entity to Owner of that Entity.')
                end
              end

              context 'and target user already owns meeting' do
                before do
                  @meeting = create(:meeting, owner: @user, time_zone: nil, tenant_id: @user.tenant_id)
                  stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
                    headers: {
                    'Authorization'=>'Bearer '+ @token
                     }
                  ).to_return(
                    status: 200,
                    body: [
                      {
                        "id": 213,
                        "name": "Jane Doe",
                        "email": {"primary": true,"value": "<EMAIL>"}
                      }
                    ].to_json, headers: {}
                  )

                  stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
                    headers: {
                    'Authorization'=>'Bearer '+ @token
                     }
                  ).to_return(
                    status: 200,
                    body: [
                      {
                        "id": 123,
                        "name": "Jane Doe",
                        "email": {"primary": true,"value": "<EMAIL>"}
                      }
                    ].to_json, headers: {}
                  )
                end

                it 'raises error' do
                  expect(PublishEvent).not_to receive(:call)
                  expect {
                    described_class.new(update_share_meeting_params(@share_rule.id, { id: @meeting.id, from_id: 213, to_id: @share_rule.to_id })).call
                  }.to raise_error(ExceptionHandler::InvalidDataError, '01503020||Share rule record owner is not meeting owner.')
                end
              end

              context 'but user does not have permission on meeting' do
                before do
                  another_user = create(:user, tenant_id: @user.tenant_id)
                  @meeting = create(:meeting, owner: another_user, time_zone: nil, tenant_id: @user.tenant_id)
                  token = FactoryBot.build(:auth_token, :without_meeting_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id).token
                  token_without_update_all = build(:auth_data, :meeting_without_update_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
                  token_without_update_all.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions

                  Thread.current[:auth] = token_without_update_all
                  Thread.current[:token] = token
                end

                it 'raises forbidden error' do
                  expect(PublishEvent).not_to receive(:call)
                  expect {
                    described_class.new(update_share_meeting_params(@share_rule.id, { id: @meeting.id, from_id: @meeting.owner_id })).call
                  }.to raise_error(ExceptionHandler::Forbidden, '01501005||You do not have access to this meeting.')
                end
              end

              context 'and user have permission on meeting' do
                before do
                  @meeting = create(:meeting, owner: @user, time_zone: nil, tenant_id: @user.tenant_id)
                end

                context 'but share rule for same meeting and users exists' do
                  before do
                    @existing_share_rule = create(:share_rule, from_id: @user.id, to_id: @another_user.id, tenant_id: @user.tenant_id, meeting_id: @meeting.id)
                  end

                  it 'raises error' do
                    expect(PublishEvent).not_to receive(:call)
                    expect {
                      described_class.new(update_share_meeting_params(@share_rule.id, { id: @meeting.id })).call
                    }.to raise_error(ExceptionHandler::InvalidDataError, "01503016||This meeting is already shared with users/team with selected permissions||#{@existing_share_rule.id}")
                  end
                end

                context 'and share rule with same config is not present' do
                  it 'creates share rule' do
                    expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2)).exactly(1).times
                    described_class.new(update_share_meeting_params(@share_rule.id, { id: @meeting.id })).call
                    expect(@share_rule.reload.actions).to eq({
                      "read" => true
                    })
                    expect(@share_rule.name).to eq('sharing meeting updated')
                    expect(@share_rule.meeting_id).to eq(@meeting.id)
                    expect(@share_rule.from_id).to eq(@user.id)
                    expect(@share_rule.from_type).to eq('USER')
                    expect(@share_rule.to_id).to eq(@another_user.id)
                    expect(@share_rule.to_type).to eq('USER')
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  private

  def update_share_meeting_params(share_rule_id, params = {})
    {
      "id": params[:id],
      "share_rule_id": share_rule_id,
      "actions": params[:actions] || {
        "read": true,
        "update": false,
        "delete": false,
        "email": false,
        "call": false,
        "note": false,
        "sms": false,
        "task": false
      },
      "name": "sharing meeting updated",
      "description": "sharing meeting updated",
      "from": {
        "id": params[:from_id] || @user.id,
        "type": params[:from_type] || "USER"
      },
      "to": {
        "id": params[:to_id] || @another_user.id,
        "type": params[:to_type] || "USER"
      }
    }
  end
end
