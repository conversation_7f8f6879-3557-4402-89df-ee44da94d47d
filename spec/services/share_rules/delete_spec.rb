# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules::Delete do
  describe '#call' do
    before do
      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)
      @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @token_without_share_rule_create = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      permission_without_share_rule_create = build(:auth_data, :share_rule_without_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      @token_without_share_rule_create.permissions += permission_without_share_rule_create
    end

    context 'when user context is not present' do
      before { Thread.current[:auth] = nil }

      it 'returns unauthorized error' do
        expect {
          described_class.new({ id: 12 }).call
        }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end

    context 'when user context is present' do
      before do
        thread = Thread.current
        thread[:auth] = @auth_data
      end

      context 'and share rule is not present' do
        it 'raises error' do
          expect(PublishEvent).not_to receive(:call)
          expect {
            described_class.new(12).call
          }.to raise_error(ExceptionHandler::NotFound, '01503019||Share rule not found.')
        end
      end

      context 'when share rule is present' do
        context 'and current user is not creator of that share rule' do
          before do
            @share_rule = create(:share_rule, tenant_id: @user.tenant_id)
          end

          it 'raises error' do
            expect(PublishEvent).not_to receive(:call)
            Thread.current[:auth] = @token_without_share_rule_create
            expect{
              described_class.new(@share_rule.id).call
            }.to raise_error(ExceptionHandler::Forbidden, '01501005||Unauthorized access.')
          end
        end

        context 'and share rule is system default' do
          before do
            @share_rule = create(:share_rule, tenant_id: @user.tenant_id, system_default: true, created_by: @user)
          end

          it 'raises error' do
            expect(PublishEvent).not_to receive(:call)
            expect{
              described_class.new(@share_rule.id).call
            }.to raise_error(ExceptionHandler::Forbidden, '01501005||Unauthorized access.')
          end
        end

        context 'when user have delete_all share rule' do
          before do
            @share_rule = create(:share_rule, created_by: @user)
          end

          it 'deletes share rule when user is owner of share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2)).exactly(1).times
            described_class.new(@share_rule.id).call
            expect(ShareRule.count).to eq(0)
          end

          it 'deletes share rule when user is not owner of share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2)).exactly(1).times
            @share_rule.update!(created_by: @another_user)

            described_class.new(@share_rule.id).call
            expect(ShareRule.count).to eq(0)
          end
        end
      end
    end
  end
end
