# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules::Read do
  describe '#call' do
    let(:user) { create(:user) }
    let(:share_rule) { create(:share_rule, created_by: user, tenant_id: user.tenant_id) }
    let(:auth_data) do
      data =  build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
      data.permissions += build(:auth_data, :share_rule_with_create).permissions
      data
    end

    before { Thread.current[:auth] = auth_data }

    context 'when share rule is present and user can read' do
      context 'when user has read all' do
        before do
          allow_any_instance_of(Auth::Data).to receive(:can_access?).with('shareRule', 'read_all').and_return(true)
          share_rule.update(created_by: create(:user, tenant_id: user.tenant_id))
        end

        it 'returns share rule' do
          expect(described_class.new(share_rule.id).call.id).to eq(share_rule.id)
        end
      end

      context 'when user has read' do
        it 'returns share rule' do
          expect(described_class.new(share_rule.id).call.id).to eq(share_rule.id)
        end
      end
    end

    context 'when share rule is not present' do
      it 'raises not found error' do
        expect do
          described_class.new(-1).call
        end.to raise_error(ExceptionHandler::NotFound, '01503019||Share rule not found.')
      end
    end

    context 'when user cannot read share rule' do
      context 'when user does not have read all and is not share rule creator' do
        before do
          share_rule.update(created_by: create(:user, tenant_id: user.tenant_id))
        end

        it 'raises forbidden error' do
          expect do
            described_class.new(share_rule.id).call
          end.to raise_error(ExceptionHandler::Forbidden, '01501005||Unauthorized access.')
        end
      end
    end

    context 'when user context is not present' do
      before { Thread.current[:auth] = nil }

      it 'raises authentication error' do
        expect(Rails.logger).to receive(:error).with('Unauthorised: User context missing in ShareRules::Read')
        expect do
          described_class.new(share_rule.id).call
        end.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end
  end
end
