# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules::Create do
  describe '#call' do
    before do
      @user = create(:user)
      @auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      @token_without_share_rule_create = @auth_data
      @auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      permission_without_share_rule_create = build(:auth_data, :share_rule_without_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
      @token_without_share_rule_create.permissions += permission_without_share_rule_create
    end

    def stub_user_summary_request(bearer_token)
      stub_request(:get, "http://localhost:8081/v1/users/summary?id=123")
        .with(
          headers: {
            Authorization: "Bearer #{bearer_token}"
          }
        ).to_return(
          status: 200,
          body: [
            {
              id: 123,
              name: '<PERSON>',
              email: { primary: true, value: '<EMAIL>'}
            }
          ].to_json,
          headers: {}
        )
    end

    context 'when user context is not present' do
      before { Thread.current[:auth] = nil }

      it 'returns unauthorized error' do
        expect(PublishEvent).not_to receive(:call)

        expect {
          described_class.new(share_meeting_params(213, 123)).call
        }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end

    context 'when user is context is present' do
      before do
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
      end

      context 'and actions other than allowed are passed' do
        it 'raises invalid actions error' do
          expect(PublishEvent).not_to receive(:call)

          expect {
            described_class.new(share_meeting_params(213, 123, {actions: { email: true } })).call
          }.to raise_error(ExceptionHandler::InvalidDataError, '01503014||Actions are invalid')
        end
      end

      context 'To and from users are same' do
        before { @meeting = create(:meeting, owner: @user, time_zone: nil, tenant_id: @user.tenant_id) }

        it 'raises error' do
          expect(PublishEvent).not_to receive(:call)
          expect {
            described_class.new(share_meeting_params(123, 123, { id: @meeting.id })).call
          }.to raise_error(ExceptionHandler::InvalidDataError, '01503018||From and To users cannot be same.')
        end
      end

      context 'when meeting id is present' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=213")
            .with(
              headers: {
                Authorization: "Bearer #{@token}"
              }
            ).to_return(
              status: 200,
              body: [
                {
                  id: 213,
                  name: 'Jane Doe',
                  email: { primary: true, value: '<EMAIL>' }
                }
              ].to_json,
              headers: {}
            )
          stub_user_summary_request(@token)
        end

        context 'and meeting is not found' do
          it 'raises not found error' do
            expect(PublishEvent).not_to receive(:call)

            expect {
              described_class.new(share_meeting_params(213, 123, { id: 12 })).call
            }.to raise_error(ExceptionHandler::NotFound, '01502001||Meeting not found.')
          end
        end

        context 'and meeting is found' do
          context 'when from user is not meeting owner' do
            before do
              @meeting = create(:meeting, owner: @user, created_by: create(:user, tenant_id: @user.tenant_id), time_zone: nil, tenant_id: @user.tenant_id)
            end

            it 'raises error' do
              expect(PublishEvent).not_to receive(:call)

              expect {
                described_class.new(share_meeting_params(213, 123, { id: @meeting.id })).call
              }.to raise_error(ExceptionHandler::InvalidDataError, '01503020||Share rule record owner is not meeting owner.')
            end
          end

          context 'and target user already owns meeting' do
            before do
              @meeting = create(:meeting, owner: @user, created_by: create(:user, tenant_id: @user.tenant_id), time_zone: nil, tenant_id: @user.tenant_id)
            end

            it 'raises error' do
              expect(PublishEvent).not_to receive(:call)

              expect {
                described_class.new(share_meeting_params(@meeting.owner_id, @meeting.created_by_id, { id: @meeting.id })).call
              }.to raise_error(ExceptionHandler::InvalidDataError, '01503015||You cannot share Entity to Owner of that Entity.')
            end
          end

          context 'but user does not have permission on meeting' do
            before do
              another_user = create(:user, tenant_id: @user.tenant_id)
              @meeting = create(:meeting, owner: another_user, time_zone: nil, tenant_id: @user.tenant_id)
              token = FactoryBot.build(:auth_token, :without_meeting_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id).token
              token_without_update_all = build(:auth_data, :meeting_without_update_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
              token_without_update_all.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions

              stub_request(:get, "http://localhost:8081/v1/users/summary?id=213")
                .with(
                  headers: {
                    Authorization: "Bearer #{token}"
                  }
                ).to_return(
                  status: 200,
                  body: [
                    {
                      id: 213,
                      name: 'Jane Doe',
                      email: { primary: true, value: '<EMAIL>' }
                    }
                  ].to_json,
                  headers: {}
                )
              stub_user_summary_request(token)


              Thread.current[:auth] = token_without_update_all
              Thread.current[:token] = token
            end

            it 'raises forbidden error' do
              expect(PublishEvent).not_to receive(:call)

              expect {
                described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
              }.to raise_error(ExceptionHandler::Forbidden, '01501005||You do not have access to this meeting.')
            end
          end

          context 'and user have permission on meeting' do
            before do
              @meeting = create(:meeting, owner: @user, time_zone: nil, tenant_id: @user.tenant_id)
            end

            context 'but share rule for same meeting and users exists' do
              before do
                @existing_share_rule = create(:share_rule, from_id: @meeting.owner_id, to_id: 123, tenant_id: @user.tenant_id, meeting_id: @meeting.id)
              end

              it 'raises error' do
                expect(PublishEvent).not_to receive(:call)
                expect {
                  described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                }.to raise_error(ExceptionHandler::InvalidDataError, "01503016||This meeting is already shared with users/team with selected permissions||#{@existing_share_rule.id}")
              end
            end

            context 'and share rule with same config is not present' do
              let(:another_user) { create(:user, tenant_id: @user.tenant_id) }
              let(:auth_data_without_read_all) { build(:auth_data, :meeting_without_read_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }
              let(:token_without_read_all) { build(:auth_token, :without_meeting_read_all_and_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id).token }

              before do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times
                auth_data_without_read_all.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
                Thread.current[:auth] = auth_data_without_read_all
                Thread.current[:token] = token_without_read_all
                stub_user_summary_request(token_without_read_all)
              end

              context 'when user  has update all' do
                before do
                  Thread.current[:auth] = @auth_data
                  stub_user_summary_request(@token)
                  @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                end

                it 'creates share rule' do
                  expect do
                    described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                  end.to change(ShareRule, :count).by(1)

                  share_rule = ShareRule.last

                  expect(share_rule.actions).to eq({
                    "read" => true
                  })
                  expect(share_rule.meeting_id).to eq(@meeting.id)
                  expect(share_rule.from_id).to eq(@meeting.owner_id)
                  expect(share_rule.from_type).to eq('USER')
                  expect(share_rule.to_id).to eq(123)
                  expect(share_rule.to_type).to eq('USER')
                end

                after { Thread.current[:auth] = auth_data_without_read_all }
              end

              context 'when user has read all and reshare' do
                before do
                  allow_any_instance_of(Auth::Data).to receive(:can_access?).with('shareRule', 'write').and_return(true)
                  allow_any_instance_of(Auth::Data).to receive(:can_access?).with('meeting', 'update_all').and_return(false)
                  allow_any_instance_of(Auth::Data).to receive(:can_access?).with('meeting', 'read_all').and_return(true)
                  allow_any_instance_of(Auth::Data).to receive(:can_access?).with('meeting', 'reshare').and_return(true)
                  @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                end

                it 'creates share rule' do
                  expect do
                    described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                  end.to change(ShareRule, :count).by(1)
                end
              end

              context 'when user is owner or creator' do
                context 'when user is owner' do
                  before { @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user) }

                  it 'creates share rule' do
                    expect do
                      described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                    end.to change(ShareRule, :count).by(1)
                  end
                end

                context 'when user is creator' do
                  before { @meeting = create(:meeting, tenant_id: @user.tenant_id, created_by: @user, owner: another_user) }

                  it 'creates share rule' do
                    expect do
                      described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                    end.to change(ShareRule, :count).by(1)
                  end
                end
              end

              context 'when user is organizer' do
                before do
                  @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                  @meeting.organizer = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id)
                end

                it 'creates share rule' do
                  expect do
                    described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                  end.to change(ShareRule, :count).by(1)
                end
              end

              context 'when user has reshare permission' do
                context 'when user is participant' do
                  before do
                    @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                    @meeting.participants << build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id)
                  end

                  it 'creates share rule' do
                    expect do
                      described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                    end.to change(ShareRule, :count).by(1)
                  end
                end

                context 'when user is owner of associated entity' do
                  PARENT_ENTITY_TYPES.each do |entity_type|
                    context "when associated entity is #{entity_type}" do
                      before do
                        @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                        @meeting.related_to << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: @user.tenant_id, owner_id: @user.id)
                      end

                      it 'creates share rule' do
                        expect do
                          described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                        end.to change(ShareRule, :count).by(1)
                      end
                    end
                  end
                end

                context 'when from user of share all meetings rule is owner or participant or organizer' do
                  context 'when from user is meeting owner' do
                    before do
                      @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                      create(:share_rule, tenant_id: @user.tenant_id, from_id: another_user.id, to_id: @user.id, share_all_records: true)
                    end

                    it 'creates share rule' do
                      expect do
                        described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                      end.to change(ShareRule, :count).by(1)
                    end
                  end

                  context 'when from user is meeting participant' do
                    before do
                      @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: create(:user, tenant_id: another_user.tenant_id))
                      @meeting.participants << build(:user_look_up, entity_id: another_user.id, tenant_id: @user.tenant_id)
                      create(:share_rule, tenant_id: @user.tenant_id, from_id: another_user.id, to_id: @user.id, share_all_records: true)
                    end

                    it 'creates share rule' do
                      expect do
                        described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                      end.to change(ShareRule, :count).by(1)
                    end
                  end

                  context 'when from user is meeting organizer' do
                    before do
                      @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: create(:user, tenant_id: another_user.tenant_id))
                      @meeting.organizer = build(:user_look_up, entity_id: another_user.id, tenant_id: @user.tenant_id)
                      create(:share_rule, tenant_id: @user.tenant_id, from_id: another_user.id, to_id: @user.id, share_all_records: true)
                    end

                    it 'creates share rule' do
                      expect do
                        described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                      end.to change(ShareRule, :count).by(1)
                    end
                  end
                end

                context 'when meeting is shared' do
                  before do
                    @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                    create(:share_rule, tenant_id: @user.tenant_id, from_id: another_user.id, to_id: @user.id, meeting_id: @meeting.id)
                  end

                  it 'creates share rule' do
                    expect do
                      described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                    end.to change(ShareRule, :count).by(1)
                  end
                end

                context 'when user has access to associated entity' do
                  PARENT_ENTITY_TYPES.each do |entity_type|
                    context "when associated entity is #{entity_type}" do
                      before do
                        @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: another_user)
                        @meeting.related_to << build("#{entity_type}_look_up".to_sym, entity_id: 1001, tenant_id: @user.tenant_id, owner_id: another_user.id)
                        PARENT_ENTITY_TYPES.each do |local_entity|
                          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{local_entity.upcase}/MEETING")
                          .with(
                            headers: {
                              Authorization: "Bearer #{Thread.current[:token]}"
                            }
                          )
                          .to_return(status: 200, body: { accessByOwners: { another_user.id.to_s => { read: true, meeting: true } }, accessByRecords: {} }.to_json)
                        end
                      end

                      it 'creates share rule' do
                        expect do
                          described_class.new(share_meeting_params(@meeting.owner_id, 123, { id: @meeting.id })).call
                        end.to change(ShareRule, :count).by(1)
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end

      context 'when meeting id is not present' do
        context 'but user does not have update_all permission on meetings' do
          context 'and from_id is not current user' do
            before do
              another_user = create(:user, tenant_id: @user.tenant_id)
              token = FactoryBot.build(:auth_token, :without_meeting_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id).token
              token_without_update_all = build(:auth_data, :meeting_without_update_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
              token_without_update_all.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions

              Thread.current[:auth] = token_without_update_all
              Thread.current[:token] = token

              stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
                headers: {
                'Authorization'=>'Bearer '+ token
                 }
              ).to_return(
                status: 200,
                body: [
                  {
                    "id": 213,
                    "name": "Jane Doe",
                    "email": {"primary": true,"value": "<EMAIL>"}
                  }
                ].to_json, headers: {}
              )

              stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
                headers: {
                'Authorization'=>'Bearer '+ token
                 }
              ).to_return(
                status: 200,
                body: [
                  {
                    "id": 123,
                    "name": "Jane Doe",
                    "email": {"primary": true,"value": "<EMAIL>"}
                  }
                ].to_json, headers: {}
              )
            end

            it 'raises forbidden error' do
              expect(PublishEvent).not_to receive(:call)
              expect {
                described_class.new(share_meeting_params(213, 123)).call
              }.to raise_error(ExceptionHandler::Forbidden, '01501005||You do not have access to this meeting.')
            end
          end

          context 'and from_id is current user' do
            before do
              another_user = create(:user, tenant_id: @user.tenant_id)
              token = FactoryBot.build(:auth_token, :without_meeting_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id).token
              token_without_update_all = build(:auth_data, :meeting_without_update_all, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
              token_without_update_all.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions

              Thread.current[:auth] = token_without_update_all
              Thread.current[:token] = token

              stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
                headers: {
                'Authorization'=>'Bearer '+ token
                 }
              ).to_return(
                status: 200,
                body: [
                  {
                    "id": 213,
                    "name": "Jane Doe",
                    "email": {"primary": true,"value": "<EMAIL>"}
                  }
                ].to_json, headers: {}
              )

              stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
                headers: {
                'Authorization'=>'Bearer '+ token
                 }
              ).to_return(
                status: 200,
                body: [
                  {
                    "id": 123,
                    "name": "Jane Doe",
                    "email": {"primary": true,"value": "<EMAIL>"}
                  }
                ].to_json, headers: {}
              )
            end

            it 'creates share rule' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times

              described_class.new(share_meeting_params(@user.id, 123)).call
              expect(ShareRule.count).to eq(1)
              expect(ShareRule.last.share_all_records).to eq(true)
              expect(ShareRule.last.from_id).to eq(@user.id)
            end
          end
        end

        context 'and user have update_all permission on meeting' do
          before do
            stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
              headers: {
              'Authorization'=>'Bearer '+ @token
               }
            ).to_return(
              status: 200,
              body: [
                {
                  "id": 213,
                  "name": "Jane Doe",
                  "email": {"primary": true,"value": "<EMAIL>"}
                }
              ].to_json, headers: {}
            )

            stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
              headers: {
              'Authorization'=>'Bearer '+ @token
               }
            ).to_return(
              status: 200,
              body: [
                {
                  "id": 123,
                  "name": "Jane Doe",
                  "email": {"primary": true,"value": "<EMAIL>"}
                }
              ].to_json, headers: {}
            )
          end

          context 'but share rule for same users exists' do
            before do
              @existing_share_rule = create(:share_rule, from_id: 213, to_id: 123, tenant_id: @user.tenant_id, share_all_records: true)
            end

            it 'raises error' do
              expect(PublishEvent).not_to receive(:call)
              expect {
                described_class.new(share_meeting_params(213, 123)).call
              }.to raise_error(ExceptionHandler::InvalidDataError, "01503016||This meeting is already shared with users/team with selected permissions||#{@existing_share_rule.id}")
            end
          end

          context 'and share rule with same config does not exist' do
            it 'creates share rule' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times

              described_class.new(share_meeting_params(213, 123)).call
              expect(ShareRule.count).to eq(1)
              share_rule = ShareRule.last

              expect(share_rule.actions).to eq({
                "read" => true
              })
              expect(share_rule.meeting_id).to eq(nil)
              expect(share_rule.from_id).to eq(213)
              expect(share_rule.from_type).to eq('USER')
              expect(share_rule.to_id).to eq(123)
              expect(share_rule.to_type).to eq('USER')
              expect(share_rule.share_all_records).to eq(true)
            end
          end
        end
      end
    end
  end

  private

  def share_meeting_params(from_id, to_id, params = {})
    {
      "id": params[:id],
      "actions": params[:actions] || {
        "read": true,
        "update": false,
        "delete": false,
        "email": false,
        "call": false,
        "note": false,
        "sms": false,
        "task": false
      },
      "name": "sharing meeting",
      "description": "sharing meeting",
      "from": {
        "id": from_id,
        "type": params[:from_type] || "USER"
      },
      "to": {
        "id": to_id,
        "type": params[:to_type] || "USER"
      }
    }
  end

  def user_payload(ids = [])
    {
      fields: [
        'name',
        'id'
      ],
      jsonRule: {
        rules: [
          {
            operator: 'in',
            id: 'id',
            field: 'id',
            type: 'long',
            value: ids.join(',')
          }
        ],
        condition: 'AND',
        valid: true
      }
    }.to_json
  end
end
