# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules::Search, type: :service do
  describe '#call' do
    let(:user) { create(:user) }
    let(:auth_data) { build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }

    context 'when authorized user' do
      before do
        Thread.current[:auth] = auth_data
        @share_rule = create(:share_rule, tenant_id: user.tenant_id, created_by: user, updated_by: user)
      end

      it 'returns pagindated response' do
        search_response = described_class.new({}).call

        expect(search_response.class.to_s).to eq('ShareRule::ActiveRecord_Relation')
        expect(search_response.count).to eq(1)
        expect(search_response.first.id).to eq(@share_rule.id)
      end
    end

    context 'when unauthorized user' do
      before { Thread.current[:auth] = nil }

      it 'raises error' do
        expect { described_class.new({}).call }.to raise_error(ExceptionHandler::AuthenticationError, '01501005||Unauthorized access.')
      end
    end
  end
end
