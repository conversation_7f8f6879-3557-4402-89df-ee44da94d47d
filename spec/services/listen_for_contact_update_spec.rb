require 'rails_helper'

RSpec.describe ListenForContactUpdate do
  describe '#call' do
    let(:lookup) { create(:contact_look_up) }

    context 'valid input' do
      context 'for contact updated event' do
        let(:payload) {
          {
            id: lookup.entity_id,
            tenantId: lookup.tenant_id,
            firstName: '<PERSON>',
            lastName: 'Doe',
            ownerId: 102,
            emails: [{ type: 'OFFICE', value: '<EMAIL>', primary: true }, { type: 'PERSONAL', value: '<EMAIL>', primary: false }]
          }.to_json
        }

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(CONTACT_EXCHANGE, CONTACT_UPDATED_EVENT, CONTACT_UPDATED_QUEUE)
            .and_yield(payload.to_s)
          described_class.call
        end

        it 'updates the contact lookup with new name, owner and email' do
          look_ups = LookUp.where(tenant_id: lookup.tenant_id, entity: "contact_#{lookup.entity_id}")

          expect(look_ups.count).to eq(1)
          expect(look_ups.first.name).to eq('<PERSON>')
          expect(look_ups.first.email).to eq('<EMAIL>')
          expect(look_ups.first.owner_id).to eq(102)
        end

        context "when contact email is not available" do
          let(:payload) {
            {
              id: lookup.entity_id,
              tenantId: lookup.tenant_id,
              firstName: 'John',
              lastName: 'Doe',
              ownerId: 102,
              emails: []
            }.to_json
          }

          it 'updates the contact lookup correctly' do
            look_ups = LookUp.where(tenant_id: lookup.tenant_id, entity: "contact_#{lookup.entity_id}")

            expect(look_ups.count).to eq(1)
            expect(look_ups.first.name).to eq('John Doe')
            expect(look_ups.first.email).to be_nil
            expect(look_ups.first.owner_id).to eq(102)
          end
        end
      end
    end
  end
end
