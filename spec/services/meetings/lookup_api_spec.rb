# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Meetings::LookupApi, type: :service do
  describe '#call' do
    let!(:user) { create(:user)}
    let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
    let!(:valid_auth_token) { build(:auth_token, :without_meeting_read_all_and_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let!(:auth_data) { build(:auth_data, :meeting_without_read_all, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

    before do
      create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true)
      create(:field, internal_name: 'title', tenant_id: user.tenant_id, active: true, is_filterable: true)
    end

    context 'when user is authorized' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end
      end

      context 'when query is present' do
        context 'when default view' do
          before do
            create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
            @meetings = create_list(:meeting, 5, title: "ABCDEFGHIJ", tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)
          end

          it 'returns first 5 meetings matching query' do
            meeting_response = described_class.call(ActionController::Parameters.new({ q: 'defg' }).permit!).result

            expect(meeting_response.map(&:id)).to match_array(@meetings.map(&:id))
            expect(meeting_response.map(&:title)).to match_array(@meetings.map(&:title))
          end
        end

        context 'when share view' do
          before do
            create(:team, tenant_id: user.tenant_id, user_ids: [user.id])
            @shared_meeting = create(:meeting, title: "ABCDEFGHIJ", tenant_id: another_user.tenant_id, owner: another_user, from: 20.minutes.ago)
            create_list(:meeting, 5, title: "ABCDEFGHIJ", tenant_id: another_user.tenant_id, owner: another_user, from: 20.minutes.ago)
            create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
            @meetings = create_list(:meeting, 4, title: "ABCDEFGHIJ", tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)

            create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: user.id, meeting_id: @shared_meeting.id, actions: { read: true, update: true })
          end

          it 'returns first 5 meetings matching query which user can update' do
            meeting_response = described_class.call(ActionController::Parameters.new({ q: 'defg', view: 'share' }).permit!).result

            expect(meeting_response.count).to eq(5)
            expect(meeting_response.map(&:id)).to match_array([@shared_meeting.id] + @meetings.map(&:id))
            expect(meeting_response.map(&:title)).to match_array([@shared_meeting.title] + @meetings.map(&:title))
          end
        end

        context 'when checkin view' do
          before do
            create(:meeting, owner_id: user.id, title: 'Meeting user dint checkin', tenant_id: user.tenant_id)
            create(:meeting, owner_id: another_user.id, title: 'Meeting user not invited', tenant_id: another_user.id)

            @meeting_checked_in_by_user = create(:meeting, owner_id: user.id, title: 'Checked out By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_user.id, user_id: user.id)

            @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
            participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
            @meeting_checked_in_by_other_user.participants << participant
            @meeting_checked_in_by_other_user.save!
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)
          end

          context 'when timezone is not passed' do
            before do
              stub_request(:get, "#{SERVICE_IAM}/v1/users/#{user.id}").with(
                headers: {
                  "Authorization" => "Bearer #{valid_auth_token}",
                  'Accept'=>'application/json',
                  'Content-Type'=>'application/json'
                }
              ).to_return(status: 200, body: file_fixture('user-profile-response.json').read, headers: {})
            end

            it 'returns meetings eligible for checkin' do
              meeting_response = described_class.call(ActionController::Parameters.new({ q: 'user', view: 'checkin' }).permit!).result
              expect(meeting_response.map(&:title)).to match_array(['Checked In By Another User', 'Meeting user dint checkin', 'Checked out By User'])
            end
          end

          context 'when timezone is passed' do
            context 'and timezone is invalid' do
              before do
                stub_request(:get, "#{SERVICE_IAM}/v1/users/#{user.id}").with(
                  headers: {
                    "Authorization" => "Bearer #{valid_auth_token}",
                    'Accept'=>'application/json',
                    'Content-Type'=>'application/json'
                  }
                ).to_return(status: 200, body: file_fixture('user-profile-response.json').read, headers: {})
              end

              it 'fetches user timezone and returns meetings eligible for meeting' do
                meeting_response = described_class.call(ActionController::Parameters.new({ q: 'user', view: 'checkin', timezone: 'invalid' }).permit!).result
                expect(meeting_response.map(&:title)).to match_array(['Checked In By Another User', 'Meeting user dint checkin', 'Checked out By User'])
              end
            end

            context 'and timezone is valid' do
              it 'returns meetings eligible for checkin' do
                meeting_response = described_class.call(ActionController::Parameters.new({ q: 'user', view: 'checkin', timezone: 'Asia/Calcutta' }).permit!).result
                expect(meeting_response.map(&:title)).to match_array(['Checked In By Another User', 'Meeting user dint checkin', 'Checked out By User'])
              end
            end
          end
        end
      end

      context 'when query is not present' do
        context 'when default view' do
          before do
            @meetings = create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
            create_list(:meeting, 5, title: "ABCDEFGHIJ", tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)
          end

          it 'returns first 5 meetings accessible to user' do
            meeting_response = described_class.call(ActionController::Parameters.new({ q: nil }).permit!).result

            expect(meeting_response.map(&:id)).to match_array(@meetings.map(&:id))
            expect(meeting_response.map(&:title)).to match_array(@meetings.map(&:title))
          end
        end

        context 'when share view' do
          before do
            create_list(:meeting, 5, title: "ABCDEFGHIJ", tenant_id: another_user.tenant_id, owner: another_user, from: 20.minutes.ago)
            @meetings = create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
            create_list(:meeting, 5, title: "ABCDEFGHIJ", tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)
          end

          it 'returns first 5 meetings which user can update' do
            meeting_response = described_class.call(ActionController::Parameters.new({ q: nil, view: 'share' }).permit!).result

            expect(meeting_response.count).to eq(10)
            expect(meeting_response.map(&:id)).to match_array(@meetings.map(&:id))
            expect(meeting_response.map(&:title)).to match_array(@meetings.map(&:title))
          end
        end

        context 'when checkin view' do
          before do
            create(:meeting, owner_id: user.id, title: 'Meeting user dint checkin', tenant_id: user.tenant_id)
            create(:meeting, owner_id: another_user.id, title: 'Meeting user not invited', tenant_id: another_user.id)

            @meeting_checked_in_and_out_by_user = create(:meeting, owner_id: user.id, title: 'Checked in and out By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_checked_in_and_out_by_user.id, user_id: user.id)

            @meeting_not_checked_out_by_user = create(:meeting, owner_id: user.id, title: 'Checked in and out By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_not_checked_out_by_user.id, user_id: user.id, checked_out_at: nil, checked_out_latitude: nil, checked_out_longitude: nil)

            @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
            participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
            @meeting_checked_in_by_other_user.participants << participant
            @meeting_checked_in_by_other_user.save!
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)
          end

          it 'returns meetings that ' do
            meeting_response = described_class.call(ActionController::Parameters.new({ view: 'checkin', timezone: 'Asia/Calcutta' }).permit!).result
            expect(meeting_response.map(&:title)).to match_array(['Checked In By Another User', 'Meeting user dint checkin', 'Checked in and out By User'])
          end
        end
      end
    end

    context 'when user is unauthorized' do
      before do
        Thread.current[:auth] = nil
      end

      it 'raises error' do
        expect{ described_class.call(ActionController::Parameters.new({ q: 'abcd' }).permit!).result }.to raise_error(ExceptionHandler::AuthenticationError, "01501005||Unauthorized access.")
      end
    end
  end
end
