# frozen_string_literal: true


require 'rails_helper'

RSpec.describe ProcessMicrosoftWebhook do
  describe "#call" do
    before do
      @connected_account = create(:connected_account, tenant_id: 10, provider_subscription_resource_id: 'fa1104bf-4b37-4315-8026-3ac7202b4409')
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    let(:create_event_params) { JSON.parse(file_fixture('microsoft/create_event_webhook_payload.json').read) }
    let(:processed_event) {
      {
        "id"=>"AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=",
        "attendees"=>[{:email=>"<EMAIL>", :responseStatus=>"tentative"}, {:email=>"<EMAIL>", :responseStatus=>"accepted"}],
        "organizer"=>{:email=>"<EMAIL>"},
        "htmlLink"=>
         "https://teams.microsoft.com/l/meetup-join/19%3ameeting_MThlMmYxMzYtNWJjMy00YWVlLTkyODItZDQyY2FlZTM4OTdi%40thread.v2/0?context=%7b%22Tid%22%3a%2246ecb5f0-1227-4755-a101-64d39a05e1c7%22%2c%22Oid%22%3a%22f678077d-f779-4e40-a094-3f3da8959984%22%7d",
        "start"=>{"dateTime"=>"2023-02-27T14:30:00.0000000", "timeZone"=>"UTC"},
        "end"=>{"dateTime"=>"2023-02-27T15:00:00.0000000", "timeZone"=>"UTC"},
        "summary"=>"Test meeting created",
        "description"=>"<html>\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\r\n</head>\r\n<body>\r\n<br>\r\n<div style=\"width:100%\"><span style=\"white-space:nowrap; color:#5F5F5F; opacity:.36\">________________________________________________________________________________</span>\r\n</div>\r\n<div class=\"me-email-text\" lang=\"en-GB\" style=\"color:#252424; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">\r\n<div style=\"margin-top:24px; margin-bottom:20px\"><span style=\"font-size:24px; color:#252424\">Microsoft Teams meeting</span>\r\n</div>\r\n<div style=\"margin-bottom:20px\">\r\n<div style=\"margin-top:0px; margin-bottom:0px; font-weight:bold\"><span style=\"font-size:14px; color:#252424\">Join on your computer, mobile app or room device</span>\r\n</div>\r\n<a href=\"https://teams.microsoft.com/l/meetup-join/19%3ameeting_MThlMmYxMzYtNWJjMy00YWVlLTkyODItZDQyY2FlZTM4OTdi%40thread.v2/0?context=%7b%22Tid%22%3a%2246ecb5f0-1227-4755-a101-64d39a05e1c7%22%2c%22Oid%22%3a%22f678077d-f779-4e40-a094-3f3da8959984%22%7d\" class=\"me-email-headline\" style=\"font-size:14px; font-family:'Segoe UI Semibold','Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif; text-decoration:underline; color:#6264a7\">Click\r\n here to join the meeting</a> </div>\r\n<div style=\"margin-bottom:20px; margin-top:20px\">\r\n<div style=\"margin-bottom:4px\"><span data-tid=\"meeting-code\" style=\"font-size:14px; color:#252424\">Meeting ID:\r\n<span style=\"font-size:16px; color:#252424\">441 224 713 017</span> </span><br>\r\n<span style=\"font-size:14px; color:#252424\">Passcode: </span><span style=\"font-size:16px; color:#252424\">dm3DFb\r\n</span>\r\n<div style=\"font-size:14px\"><a href=\"https://www.microsoft.com/en-us/microsoft-teams/download-app\" class=\"me-email-link\" style=\"font-size:14px; text-decoration:underline; color:#6264a7; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">Download\r\n Teams</a> | <a href=\"https://www.microsoft.com/microsoft-teams/join-a-meeting\" class=\"me-email-link\" style=\"font-size:14px; text-decoration:underline; color:#6264a7; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">\r\nJoin on the web</a></div>\r\n</div>\r\n</div>\r\n<div style=\"margin-bottom:24px; margin-top:20px\"><a href=\"https://aka.ms/JoinTeamsMeeting\" class=\"me-email-link\" style=\"font-size:14px; text-decoration:underline; color:#6264a7; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">Learn more</a>\r\n | <a href=\"https://teams.microsoft.com/meetingOptions/?organizerId=f678077d-f779-4e40-a094-3f3da8959984&amp;tenantId=46ecb5f0-1227-4755-a101-64d39a05e1c7&amp;threadId=19_meeting_MThlMmYxMzYtNWJjMy00YWVlLTkyODItZDQyY2FlZTM4OTdi@thread.v2&amp;messageId=0&amp;language=en-GB\" class=\"me-email-link\" style=\"font-size:14px; text-decoration:underline; color:#6264a7; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">\r\nMeeting options</a> </div>\r\n</div>\r\n<div style=\"font-size:14px; margin-bottom:4px; font-family:'Segoe UI','Helvetica Neue',Helvetica,Arial,sans-serif\">\r\n</div>\r\n<div style=\"font-size:12px\"></div>\r\n<div></div>\r\n<div style=\"width:100%\"><span style=\"white-space:nowrap; color:#5F5F5F; opacity:.36\">________________________________________________________________________________</span>\r\n</div>\r\n</body>\r\n</html>\r\n",
        "medium" => "MICROSOFT",
        "location"=>"",
        "all_day" => false
      }
    }

    context 'for event created webhook' do
      it 'it processes event successfully and adds changelog' do
        stub_request(:get, "https://graph.microsoft.com/v1.0/me/events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=").
         with(
           headers: {
            'Authorization'=>"Bearer #{@connected_account.access_token}",
            'Content-Type'=>'application/json',
           }).
         to_return(status: 200, body: file_fixture('microsoft/get_event_by_id.json').read, headers: {})

        expect(ProcessEvent).to receive(:call).with(@connected_account, processed_event)
        create_event_params['value'].first['changeType'] = 'created'
        ProcessMicrosoftWebhook.call(create_event_params['value'].first)
        change_log = OutlookChangeLog.last
        expect(change_log.resource_id).to eq('AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=')
        expect(change_log.change_key).to eq('klVXtc94vUGmhLpZaJymJAABgBxitg==')
      end

      context 'when change log is present for given change' do
        it 'does not process event' do
          stub_request(:get, "https://graph.microsoft.com/v1.0/me/events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=").
          with(
            headers: {
             'Authorization'=>"Bearer #{@connected_account.access_token}",
             'Content-Type'=>'application/json',
            }).
          to_return(status: 200, body: file_fixture('microsoft/get_event_by_id.json').read, headers: {})
          OutlookChangeLog.create(change_key: 'klVXtc94vUGmhLpZaJymJAABgBxitg==', resource_id: 'AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=')

          expect(ProcessEvent).not_to receive(:call)
          create_event_params['value'].first['changeType'] = 'created'
          ProcessMicrosoftWebhook.call(create_event_params['value'].first)
        end
      end
    end

    context 'when event is not found for given id' do
      it 'does not process event' do
        stub_request(:get, "https://graph.microsoft.com/v1.0/me/events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=").
        with(
          headers: {
           'Authorization'=>"Bearer #{@connected_account.access_token}",
           'Content-Type'=>'application/json',
          }).
        to_return(status: 401, body: {}.to_json, headers: {})

        expect(ProcessEvent).not_to receive(:call)
        ProcessMicrosoftWebhook.call(create_event_params['value'].first)
      end
    end

    context 'when webhook is for deleted event' do
      it 'does not fetch event and processes with cancelled status' do
        create(:meeting, provider_meeting_id: 'AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=')
        deleted_event_params = create_event_params['value'].first
        deleted_event_params['changeType'] = 'deleted'

        expect(ProcessEvent).to receive(:call).with(
          @connected_account,
          { id: 'AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=',
          status: 'cancelled'
          })

        ProcessMicrosoftWebhook.call(deleted_event_params)
      end
    end
  end
end
