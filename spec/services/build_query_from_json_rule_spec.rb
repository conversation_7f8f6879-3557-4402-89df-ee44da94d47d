require 'rails_helper'

RSpec.describe BuildQueryFromJsonRule do
  describe '#call' do
    context 'when standard fields' do
      context 'string fields' do
        it 'works for equal operator' do
          valid_rule = build(:json_rule_string_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" ~* '#{valid_rule.value}'")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_string_not_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" !~* '#{valid_rule.value}'")
        end

        it 'works for contains operator' do
          valid_rule = build(:json_rule_string_contains)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" ILIKE '%#{valid_rule.value}%'")
        end

        it 'works for not_contains operator' do
          valid_rule = build(:json_rule_string_not_contains)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("NOT (\"meetings\".\"#{valid_rule.field}\" ILIKE '%#{valid_rule.value}%')")
        end

        it 'works for begins_with operator' do
          valid_rule = build(:json_rule_string_begins_with)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" ILIKE '#{valid_rule.value}%'")
        end

        it 'works for is_empty operator' do
          valid_rule = build(:json_rule_string_is_empty)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("(\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL)")
        end

        it 'works for is_not_empty operator' do
          valid_rule = build(:json_rule_string_is_not_empty)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("NOT ((\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL))")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_string_in)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (\"meetings\".\"#{valid_rule.field}\" ILIKE '#{valid_rule.value.split(',')[0]}' OR \"meetings\".\"#{valid_rule.field}\" ILIKE '#{valid_rule.value.split(',')[1]}')")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_string_not_in)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE NOT ((\"meetings\".\"#{valid_rule.field}\" ILIKE '#{valid_rule.value.split(',')[0]}' OR \"meetings\".\"#{valid_rule.field}\" ILIKE '#{valid_rule.value.split(',')[1]}'))")
        end

        it 'works for is_null operator' do
          valid_rule = JsonRule.new(build(:json_rule_string_is_null).as_json)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("(\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL)")
        end

        it 'works for is_not_null operator' do
          valid_rule = JsonRule.new(build(:json_rule_string_is_not_null).as_json)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("NOT ((\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL))")
        end
      end

      context 'long fields' do
        it 'works for equal operator' do
          valid_rule = build(:json_rule_long_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" = #{valid_rule.value}")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_long_not_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" != #{valid_rule.value}")
        end

        it 'works for is null operator' do
          valid_rule = build(:json_rule_long_is_null)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" IS NULL")
        end

        it 'works for is not null operator' do
          valid_rule = build(:json_rule_long_is_not_null)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" IS NOT NULL")
        end

        it 'works for greater operator' do
          valid_rule = build(:json_rule_long_greater)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" > #{valid_rule.value}")
        end

        it 'works for greater_or_equal operator' do
          valid_rule = build(:json_rule_long_greater_or_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" >= #{valid_rule.value}")
        end

        it 'works for less operator' do
          valid_rule = build(:json_rule_long_less)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" < #{valid_rule.value}")
        end

        it 'works for less_or_equal operator' do
          valid_rule = build(:json_rule_long_less_or_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" <= #{valid_rule.value}")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_long_in, value: '123, 124')
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" IN (#{valid_rule.value})")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_long_not_in, value: '123, 124')
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" NOT IN (#{valid_rule.value})")
        end

        it 'works for between operator' do
          valid_rule = build(:json_rule_long_between, value: [122, 124])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"#{valid_rule.field}\" >= #{valid_rule.value.first} AND \"meetings\".\"#{valid_rule.field}\" <= #{valid_rule.value.last}")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_long_not_between, value: [122, 124])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE NOT (\"meetings\".\"#{valid_rule.field}\" >= #{valid_rule.value.first} AND \"meetings\".\"#{valid_rule.field}\" <= #{valid_rule.value.last})")
        end
      end

      context 'date fields' do
        let(:date_value) { "2020-09-03 10:30:00" }
        let(:start_date) { (DateTime.current - 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }
        let(:end_date) { (DateTime.current + 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }

        it 'works for greater operator' do
          valid_rule = build(:json_rule_date_greater, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" > '#{valid_rule.value}'")
        end

        it 'works for equal operator' do
          valid_rule = build(:json_rule_date_equal, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" = '#{valid_rule.value}'")
        end

        it 'works for less operator' do
          valid_rule = build(:json_rule_date_less, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" < '#{valid_rule.value}'")
        end

        it 'works for greater_or_equal operator' do
          valid_rule = build(:json_rule_date_greater_or_equal, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" >= '#{valid_rule.value}'")
        end

        it 'works for less_or_equal operator' do
          valid_rule = build(:json_rule_date_less_or_equal, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" <= '#{valid_rule.value}'")
        end

        it 'works for between operator' do
          valid_rule = build(:json_rule_date_between, value: [start_date, end_date])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" BETWEEN '#{valid_rule.value[0]}' AND '#{valid_rule.value[1]}'")
        end

        it 'works for not_between operator' do
          valid_rule = build(:json_rule_date_not_between, value: [start_date, end_date])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"#{valid_rule.field}\" < '#{valid_rule.value[0]}' OR \"meetings\".\"#{valid_rule.field}\" > '#{valid_rule.value[1]}'")
        end

        it 'works for is_null operator' do
          valid_rule = build(:json_rule_date_is_null, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"#{valid_rule.field}\" IS NULL")
        end

        it 'works for is_not_null operator' do
          valid_rule = build(:json_rule_date_is_not_null, value: date_value)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"#{valid_rule.field}\" IS NOT NULL")
        end
      end

      context 'meeting_attendance_fields' do
        let(:start_date) { (DateTime.current - 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }
        let(:end_date) { (DateTime.current + 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }

        it 'works for equal operator' do
          valid_rule = build(:json_rule_meeting_attendance_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" = '#{valid_rule.value}'")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_meeting_attendance_not_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE (\"meeting_attendances\".\"#{valid_rule.field}\" != '#{valid_rule.value}' OR \"meeting_attendances\".\"#{valid_rule.field}\" IS NULL)")
        end

        it 'works for greater operator' do
          valid_rule = build(:json_rule_meeting_attendance_greater)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" > '#{valid_rule.value}'")
        end

        it 'works for greater_or_equal operator' do
          valid_rule = build(:json_rule_meeting_attendance_greater_or_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" >= '#{valid_rule.value}'")
        end

        it 'works for less operator' do
          valid_rule = build(:json_rule_meeting_attendance_less)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" < '#{valid_rule.value}'")
        end

        it 'works for less_or_equal operator' do
          valid_rule = build(:json_rule_meeting_attendance_less_or_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" <= '#{valid_rule.value}'")
        end

        it 'works for between operator' do
          valid_rule = build(:json_rule_meeting_attendance_between, value: [start_date, end_date])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" BETWEEN '#{valid_rule.value[0]}' AND '#{valid_rule.value[1]}'")
        end

        it 'works for not_between operator' do
          valid_rule = build(:json_rule_meeting_attendance_not_between, value: [start_date, end_date])
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE (\"meeting_attendances\".\"#{valid_rule.field}\" < '#{valid_rule.value[0]}' OR \"meeting_attendances\".\"#{valid_rule.field}\" > '#{valid_rule.value[1]}')")
        end

        it 'works for is_null operator' do
          valid_rule = build(:json_rule_meeting_attendance_is_null)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" IS NULL")
        end

        it 'works for is_not_null operator' do
          valid_rule = build(:json_rule_meeting_attendance_is_not_null)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"#{valid_rule.field}\" IS NOT NULL")
        end
      end

      context 'checked_in_out_by' do
        it 'works for equal operator' do
          valid_rule = build(:checked_in_out_by_equal, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"user_id\" = 1")
        end

        it 'works for not equal operator' do
          valid_rule = build(:checked_in_out_by_not_equal, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE (\"meeting_attendances\".\"user_id\" != 1 OR \"meeting_attendances\".\"user_id\" IS NULL)")
        end

        it 'works for in operator' do
          valid_rule = build(:checked_in_out_by_in, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"user_id\" IN (1)")
        end

        it 'works for not in operator' do
          valid_rule = build(:checked_in_out_by_not_in, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE (\"meeting_attendances\".\"user_id\" NOT IN (1) OR \"meeting_attendances\".\"user_id\" IS NULL)")
        end

        it 'works for is_null operator' do
          valid_rule = build(:checked_in_out_by_is_null, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"user_id\" IS NULL")
        end

        it 'works for is_not_null operator' do
          valid_rule = build(:checked_in_out_by_is_not_null, type: 'meeting_attendance', field: 'user_id', value: 1)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("LEFT OUTER JOIN \"meeting_attendances\" ON \"meeting_attendances\".\"meeting_id\" = \"meetings\".\"id\" WHERE \"meeting_attendances\".\"user_id\" IS NOT NULL")
        end
      end

      context 'boolean fields' do
        it 'works for equal operator' do
          valid_rule = build(:json_rule_boolean_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"#{valid_rule.field}\" = TRUE")
        end

        it 'works for not equal operator' do
          valid_rule = build(:json_rule_boolean_not_equal)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"#{valid_rule.field}\" != TRUE")
        end
      end

      context 'lookup field' do
        let(:tenant_id) { 2 }
        let(:user_id) { 1 }
        let(:entity_type) { 'user' }
        let(:entity_id) { 3 }

        it 'works for equal operator' do
          valid_rule = build(:json_rule_participants_lookup_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.participant = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"lu1\".\"entity\" = 'user_#{entity_id}'")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_participants_lookup_not_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE \"meetings\".\"id\" NOT IN (SELECT \"meetings\".\"id\" FROM \"meetings\" INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.participant = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"meetings\".\"tenant_id\" = #{tenant_id} AND (lu1.entity = 'user_#{entity_id}'))")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_participants_lookup_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.participant = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE (lu1.entity IN ('user_#{entity_id}','user_4'))")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_participants_lookup_not_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"id\" NOT IN (SELECT \"meetings\".\"id\" FROM \"meetings\" INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.participant = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"meetings\".\"tenant_id\" = #{tenant_id} AND (lu1.entity IN ('user_#{entity_id}','user_4')))")
        end
      end

      context 'organizer field' do
        let(:tenant_id) { 2 }
        let(:user_id) { 1 }
        let(:entity_type) { 'user' }
        let(:entity_id) { 3 }

        it 'works for equal operator' do
          valid_rule = build(:json_rule_organizer_lookup_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.organizer = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"lu1\".\"entity\" = 'user_3'")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_organizer_lookup_not_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("SELECT \"meetings\".* FROM \"meetings\" INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.organizer = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"lu1\".\"entity\" != 'user_3'")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_organizer_lookup_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.organizer = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"lu1\".\"entity\" IN ('user_3', 'user_4')")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_organizer_lookup_not_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id AND mlu1.organizer = TRUE INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"lu1\".\"entity\" NOT IN ('user_3', 'user_4')")
        end
      end

      context 'related lookup field' do
        let(:tenant_id) { 2 }
        let(:user_id) { 1 }
        let(:entity_type) { 'lead' }
        let(:entity_id) { 3 }

        it 'works for equal operator' do
          valid_rule = build(:json_rule_related_lookup_equal, value: { "entity" => entity_type, "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE (lu1.entity = '#{entity_type}_#{entity_id}')")
        end
      end

      context 'associated lookup field' do
        let(:tenant_id) { 2 }
        let(:user_id) { 1 }
        let(:entity_type) { 'user' }
        let(:entity_id) { 3 }

        it 'works for equal operator' do
          valid_rule = build(:json_rule_associated_lookup_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE (lu1.entity = 'user_3')")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_associated_lookup_not_equal, value: { "entity" => "user", "id" => entity_id })
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"id\" NOT IN (SELECT \"meetings\".\"id\" FROM \"meetings\" INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"meetings\".\"tenant_id\" = #{tenant_id} AND (lu1.entity = 'user_3'))")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_associated_lookup_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE (lu1.entity IN ('user_3','user_4'))")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_associated_lookup_not_in, value: [{ "entity" => "user", "id" => entity_id }, { "entity" => "user", "id" => "4" }])
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("\"meetings\".\"id\" NOT IN (SELECT \"meetings\".\"id\" FROM \"meetings\" INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE \"meetings\".\"tenant_id\" = #{tenant_id} AND (lu1.entity IN ('user_3','user_4')))")
        end

        it 'works for is_not_null operator' do
          valid_rule = build(:json_rule_associated_lookup_is_not_null, value: 'deal')
          command = described_class.call(Meeting, [valid_rule], tenant_id, user_id)
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("INNER JOIN meeting_look_ups mlu1 ON mlu1.meeting_id = meetings.id INNER JOIN look_ups lu1 ON mlu1.look_up_id = lu1.id WHERE (lu1.entity LIKE 'deal%')")
        end
      end
    end

    context 'when custom fields' do
      context 'string fields' do
        it 'works for equal operator' do
          valid_rule = build(:json_rule_string_equal, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as text) ~* '#{valid_rule.value}')")
        end

        it 'works for not_equal operator' do
          valid_rule = build(:json_rule_string_not_equal, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (NOT (cast(custom_field_values ->> '#{valid_rule.field}' as text) ~* '#{valid_rule.value}') OR (cast(custom_field_values ->> '#{valid_rule.field}' as text) IS NULL))")
        end

        it 'works for contains operator' do
          valid_rule = build(:json_rule_string_contains, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE '%#{valid_rule.value}%')")
        end

        it 'works for not_contains operator' do
          valid_rule = build(:json_rule_string_not_contains, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (NOT (cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE '%#{valid_rule.value}%') OR (cast(custom_field_values ->> '#{valid_rule.field}' as text) IS NULL))")
        end

        it 'works for begins_with operator' do
          valid_rule = build(:json_rule_string_begins_with, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE '#{valid_rule.value}%')")
        end

        it 'works for is_empty operator' do
          valid_rule = build(:json_rule_string_is_empty, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as text) IS NULL)")
        end

        it 'works for is_not_empty operator' do
          valid_rule = build(:json_rule_string_is_not_empty, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as text) IS NOT NULL)")
        end

        it 'works for in operator' do
          valid_rule = build(:json_rule_string_in, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE ((cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE 'value1') OR (cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE 'value2'))")
        end

        it 'works for not_in operator' do
          valid_rule = build(:json_rule_string_not_in, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (NOT ((cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE 'value1') OR (cast(custom_field_values ->> '#{valid_rule.field}' as text) ILIKE 'value2')) OR (cast(custom_field_values ->> '#{valid_rule.field}' as text) IS NULL))")
        end

        it 'works for is_null operator' do
          valid_rule = JsonRule.new(build(:json_rule_string_is_null, is_custom_field: true).as_json)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("(\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL)")
        end

        it 'works for is_not_null operator' do
          valid_rule = JsonRule.new(build(:json_rule_string_is_not_null, is_custom_field: true).as_json)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("NOT ((\"meetings\".\"#{valid_rule.field}\" = '' OR \"meetings\".\"#{valid_rule.field}\" IS NULL))")
        end
      end

      context 'boolean fields' do
        it 'works for equal operator' do
          valid_rule = build(:json_rule_boolean_equal, is_custom_field: true, value: false)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as boolean) = #{valid_rule.value.to_s.upcase} OR cast(custom_field_values ->> '#{valid_rule.field}' as boolean) IS NULL)")
        end

        it 'works for not equal operator' do
          valid_rule = build(:json_rule_boolean_not_equal, is_custom_field: true, value: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (NOT cast(custom_field_values ->> '#{valid_rule.field}' as boolean) = #{valid_rule.value.to_s.upcase} OR cast(custom_field_values ->> '#{valid_rule.field}' as boolean) IS NULL)")
        end
      end

      context 'date fields' do
        let(:date_value) { "2020-09-03 10:30:00" }
        let(:start_date) { (DateTime.current - 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }
        let(:end_date) { (DateTime.current + 5.minutes).strftime("%Y-%m-%d %H:%M:%S") }

        it 'works for greater operator' do
          valid_rule = build(:json_rule_date_greater, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) > '#{valid_rule.value}')")
        end

        it 'works for equal operator' do
          valid_rule = build(:json_rule_date_equal, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) = '#{valid_rule.value}')")
        end

        it 'works for less operator' do
          valid_rule = build(:json_rule_date_less, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) < '#{valid_rule.value}')")
        end

        it 'works for greater_or_equal operator' do
          valid_rule = build(:json_rule_date_greater_or_equal, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) >= '#{valid_rule.value}')")
        end

        it 'works for less_or_equal operator' do
          valid_rule = build(:json_rule_date_less_or_equal, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) <= '#{valid_rule.value}')")
        end

        it 'works for between operator' do
          valid_rule = build(:json_rule_date_between, value: [start_date, end_date], is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) BETWEEN '#{valid_rule.value.first}' AND '#{valid_rule.value.last}')")
        end

        it 'works for not_between operator' do
          valid_rule = build(:json_rule_date_not_between, value: [start_date, end_date], is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE ((cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) < '#{start_date}' OR cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) > '#{end_date}'))")
        end

        it 'works for is_null operator' do
          valid_rule = build(:json_rule_date_is_null, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) IS NULL)")
        end

        it 'works for is_not_null operator' do
          valid_rule = build(:json_rule_date_is_not_null, value: date_value, is_custom_field: true)
          command = described_class.call(Meeting, [valid_rule])
          result, _join_index_count = command.result
          expect(command.success?).to be true
          expect(result.to_sql).to include("WHERE (cast(custom_field_values ->> '#{valid_rule.field}' as timestamp) IS NOT NULL)")
        end
      end

      %w[long picklist].each do |type|
        let(:converted_data) { { long: 'double precision', picklist: 'integer' }.with_indifferent_access }

        def column_extension(type)
          type.to_s == 'picklist' ? " ->> 'id' " : ' '
        end

        def operator(type)
          type.to_s == 'picklist' ? '->' : '->>'
        end

        context 'long fields' do
          it 'works for equal operator' do
            valid_rule = build(:json_rule_long_equal, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) = #{valid_rule.value})")
          end

          it 'works for not_equal operator' do
            valid_rule = build(:json_rule_long_not_equal, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (NOT cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) = #{valid_rule.value} OR cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) IS NULL)")
          end

          it 'works for is null operator' do
            valid_rule = build(:json_rule_long_is_null, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) IS NULL")
          end

          it 'works for is not null operator' do
            valid_rule = build(:json_rule_long_is_not_null, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) IS NOT NULL")
          end

          it 'works for greater operator' do
            valid_rule = build(:json_rule_long_greater, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) > #{valid_rule.value})")
          end

          it 'works for greater_or_equal operator' do
            valid_rule = build(:json_rule_long_greater_or_equal, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) >= #{valid_rule.value})")
          end

          it 'works for less operator' do
            valid_rule = build(:json_rule_long_less, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) < #{valid_rule.value})")
          end

          it 'works for less_or_equal operator' do
            valid_rule = build(:json_rule_long_less_or_equal, is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) <= #{valid_rule.value})")
          end

          it 'works for in operator' do
            valid_rule = build(:json_rule_long_in, value: '123, 124', is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) IN (#{valid_rule.value}))")
          end

          it 'works for not_in operator' do
            valid_rule = build(:json_rule_long_not_in, value: '123, 124', is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE (cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) NOT IN (#{valid_rule.value}) OR cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) IS NULL)")
          end

          it 'works for between operator' do
            valid_rule = build(:json_rule_long_between, value: [122, 124], is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE ((cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) >= #{valid_rule.value.first} AND cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) <= #{valid_rule.value.last}))")
          end

          it 'works for not_between operator' do
            valid_rule = build(:json_rule_long_not_between, value: [122, 124], is_custom_field: true, type: type)
            command = described_class.call(Meeting, [valid_rule])
            result, _join_index_count = command.result
            expect(command.success?).to be true
            expect(result.to_sql).to include("WHERE ((cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) < #{valid_rule.value.first} OR cast(custom_field_values #{operator(type)} '#{valid_rule.field}'#{column_extension(type)}as #{converted_data[type]}) > #{valid_rule.value.last}))")
          end
        end
      end
    end
  end
end
