require 'rails_helper'

RSpec.describe UpdateMeetingFieldsForTenant, type: :model do
  describe "#MeetingFieldsUpdate" do
    before do
      @tenant_id = rand(100)
      @from_tenant_id = 1
      @to_tenant_id = 100
      @user = create(:user, tenant_id: @tenant_id)
      Thread.current[:auth] = build(:auth_data, user_id: @user.id, tenant_id: @tenant_id, username: @user.name)
    end

    context 'when updating MICROSOFT as picklist value display name' do
      before do
        (@from_tenant_id..@to_tenant_id).each do |tenant_id|
          field = create(:field, internal_name: 'medium', display_name: 'Medium', field_type: 'ENTITY_PICKLIST',
                                 tenant_id: tenant_id, created_by_id: @user.id, updated_by_id: @user.id)
          picklist = create(:picklist, display_name: 'Medium Picklist', internal_name: 'medium_picklist',
                                       tenant_id: tenant_id, field_id: field.id)
          picklist.picklist_values.create(internal_name: 'MICROSOFT', display_name: 'MS Teams',
                                          tenant_id: tenant_id, picklist_id: picklist.id)
        end
        @tenant_count = (@from_tenant_id..@to_tenant_id).count
      end

      context 'when picklist value is not present on tenant' do
        it 'does create new picklist value' do
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'MS Teams').count).to eq(@tenant_count)
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'Outlook Calendar').count).to eq(0)
          expect(Rails.logger).to receive(:info).with("UpdateMeetingFieldsForTenant medium picklist value microsoft updated for Tenants #{@from_tenant_id} to #{@to_tenant_id} count #{@tenant_count}")

          UpdateMeetingFieldsForTenant.call(@from_tenant_id, @to_tenant_id)
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'MS Teams').count).to eq(0)
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'Outlook Calendar').count).to eq(@tenant_count)
        end
      end
    end
  end
end
