require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForCompanyUpdate do
  describe '#call' do
    before do
      @lookup = create(:company_look_up)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic COMPANY_EXCHANGE
      @payload = {
        "companyId" => @lookup.entity_id,
        "tenantId" => @lookup.tenant_id,
        "companyName" => "Acme Corp Ltd"
    }.to_json
    end

    context 'valid input' do
      context 'for company name updated event' do 
        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: COMPANY_NAME_UPDATED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(COMPANY_EXCHANGE, COMPANY_NAME_UPDATED_EVENT, COMPANY_NAME_UPDATED_QUEUE)
            .and_yield(@payload.to_s)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          ListenForCompanyUpdate.call()
        end

        it 'updates the company lookup with new name' do
          look_ups = LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_COMPANY + "_" + @lookup.entity_id.to_s)
          expect(look_ups.count).to be == 1
          LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_COMPANY + "_" + @lookup.entity_id.to_s).each do |company|
            expect(company.name).to be == "Acme Corp Ltd"
          end
        end
      end
    end
  end
end
