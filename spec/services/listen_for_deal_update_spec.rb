require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForDealUpdate do
  describe '#call' do
    before do
      @lookup = create(:deal_look_up)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic DEAL_EXCHANGE
      @payload = {
        "id" => @lookup.entity_id,
        "tenantId" => @lookup.tenant_id,
        "name" => "John Doe"
    }.to_json
    end

    context 'valid input' do
      context 'for deal updated event' do 
        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: DEAL_NAME_UPDATED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(DEAL_EXCHANGE, DEAL_NAME_UPDATED_EVENT, DEAL_NAME_UPDATED_QUEUE)
            .and_yield(@payload.to_s)
          #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
          ListenForDealUpdate.call()
        end

        it 'updates the deal lookup with new name' do
          look_ups = LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_DEAL + "_" + @lookup.entity_id.to_s)
          expect(look_ups.count).to be == 1
          LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_DEAL + "_" + @lookup.entity_id.to_s).each do |deal|
            expect(deal.name).to be == "John Doe"
          end
        end
      end
    end
  end
end
