# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SearchLeads do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input' do
      let(:emails) { ['<EMAIL>', '<EMAIL>'] }

      before do
        rules = []
        emails.each do |email|
          rules << {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: email
          }
        end
        payload = { fields: %w[id firstName lastName emails ownerId], jsonRule: { rules: rules, condition: 'OR', valid: true } }

        stub_request(:post, "#{SERVICE_SEARCH}/v1/search/lead?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              Authorization: "Bearer #{@token}"
            }
          ).to_return(status: 200, body: file_fixture('sample-lead-search-response.json').read, headers: {})
      end

      it 'returns correct output' do
        command = described_class.call(emails, 10)

        expect(command.result[:matched]).to eq([{ entity: 'lead_1', email: '<EMAIL>', name: 'lead1 test', tenant_id: 10, owner_id: 123 }])
        expect(command.result[:unmatched]).to eq([{ entity: 'external', email: '<EMAIL>', tenant_id: 10 }])
      end
    end
  end
end
