require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForLeadDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @lead_lookup = create(:lead_look_up, tenant_id: @user.tenant_id)
      @channel = connection.start.channel
      @exchange = @channel.topic LEAD_EXCHANGE
    end

    context 'valid input' do
      context 'for lead deleted event' do
        let(:payload) { { "id" => @lead_lookup.entity_id, "tenantId" => @lead_lookup.tenant_id, "userId" => @user.id}.to_json }

        before do
          @queue = @channel.queue ""
          @queue.bind @exchange, routing_key: LEAD_DELETED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe).with(LEAD_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE).and_yield(payload.to_s)
        end

        context "Meeting has only one lookup association which is current lead" do
          it "Should delete meeting and call dependant events" do
            meeting = create(:meeting, owner: @user)
            invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
            allow_any_instance_of(MeetingLookUp).to receive(:publish_entity_metadata)
            meeting.participants << [invitee]
            meeting.related_to << @lead_lookup

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToLead)).once
            #expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
            #expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
            expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once

            ListenForLeadDelete.call()

            expect(Meeting.count).to eq(0)
            expect(MeetingLookUp.count).to eql(0)
          end
        end

        context "Meeting has other lookup association along with current lead" do
          it "Shouldn't delete meeting and unrelate current lead" do
            meeting = create(:meeting, owner: @user)
            @deal_lookup = build(:deal_look_up, entity_id: 16, tenant_id: @user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
            allow(PublishEvent).to receive(:call)
            meeting.participants << [invitee, @deal_lookup, @lead_lookup]
            meeting.related_to << [@lead_lookup, @deal_lookup]

            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).once

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(6)
            expect(LookUp.find_by(id: @lead_lookup.id)).to eq(@lead_lookup)

            ListenForLeadDelete.call()

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(4)
            expect(LookUp.find_by(id: @lead_lookup.id)).to be_nil
          end
        end
      end
    end
  end
end
