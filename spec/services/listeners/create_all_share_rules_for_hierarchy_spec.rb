# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::CreateAllShareRulesForHierarchy do
  describe '#call' do
    let(:user) { create(:user) }
    let(:payload) do
      data = JSON.parse(file_fixture('listeners/create-all-meeting-share-rules-payload.json').read)
      data['tenantId'] = user.tenant_id
      data['ownerId'] = user.id
      data
    end

    before do
      expect(Rails.logger).to receive(:info).with("Received message for iam.create.all.meetings.shareRules for tenant id #{user.tenant_id} for user id #{user.id}")
    end

    context 'when user actions are present' do
      before do
        allow(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'iam.create.all.meetings.shareRules', 'q.iam.create.all.meetings.shareRules.meetings').and_yield(payload.to_json)
      end

      it 'calls create or update share rule service with payload' do
        expect(CreateOrUpdateAllShareRulesForUser).to receive(:call).with(payload)
        described_class.call
      end
    end

    context 'when user actions are not present' do
      before do
        payload['userActions'] = []
        allow(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'iam.create.all.meetings.shareRules', 'q.iam.create.all.meetings.shareRules.meetings').and_yield(payload.to_json)
      end

      it 'returns without calling any service' do
        expect(CreateOrUpdateAllShareRulesForUser).not_to receive(:call).with(payload)
        described_class.call
      end
    end
  end
end
