# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::DeleteAllShareRulesForHierarchy do
  describe '#call' do
    let(:user) { create(:user) }
    let(:payload) do
      data = JSON.parse(file_fixture('listeners/delete-all-meeting-share-rules-payload.json').read)
      data['tenantId'] = user.tenant_id
      data['ownerId'] = user.id
      data
    end

    before do
      expect(Rails.logger).to receive(:info).with("Received message for iam.delete.all.meetings.shareRules for tenant id #{user.tenant_id} for user id #{user.id}")
    end

    context 'when user actions are present' do
      before do
        allow(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'iam.delete.all.meetings.shareRules', 'q.iam.delete.all.meetings.shareRules.meetings').and_yield(payload.to_json)
      end

      it 'calls delete share rule service with payload' do
        expect(DeleteAllShareRulesForUser).to receive(:call).with(payload)
        described_class.call
      end
    end

    context 'when user actions are not present' do
      before do
        payload['userIds'] = []
        allow(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'iam.delete.all.meetings.shareRules', 'q.iam.delete.all.meetings.shareRules.meetings').and_yield(payload.to_json)
      end

      it 'returns without calling any service' do
        expect(DeleteAllShareRulesForUser).not_to receive(:call).with(payload)
        described_class.call
      end
    end
  end
end
