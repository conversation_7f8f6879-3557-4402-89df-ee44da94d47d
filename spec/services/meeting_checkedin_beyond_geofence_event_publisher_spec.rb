# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MeetingCheckedinBeyondGeofenceEventPublisher do
  describe "#call" do
    let(:tenant_id) { 123 }
    let(:user) { create(:user) }
    let(:meeting) { create(:meeting, owner: user) }

    before do
      allow(PublishEvent).to receive(:call)
    end

    it 'should call PublishEvent with the correct event object' do
      expect(PublishEvent).to receive(:call).with(
        instance_of(Event::MeetingCheckedinBeyondGeofence)
      ).once

      described_class.call(meeting, user, tenant_id)
    end

    it 'creates an event with correct data' do
      event = nil
      allow(PublishEvent).to receive(:call) { |e| event = e }

      described_class.call(meeting, user, tenant_id)

      expect(event.routing_key).to eq('meeting.checkedin.beyond.geofence')
      expect(event.as_json).to eq(
        {
          tenantId: tenant_id,
          user: {
            id: user.id,
            name: user.name
          },
          meeting: {
            id: meeting.id,
            title: meeting.title
          }
        }
      )
    end
  end
end
