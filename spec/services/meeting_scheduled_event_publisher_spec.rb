require 'rails_helper'

RSpec.describe MeetingScheduledEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      allow(PublishEvent).to receive(:call)
      @meeting = create(:meeting, owner: @user)
      lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      @meeting.participants << [lead_lookup]
      @meeting.save
      allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return ("test token")
    end

    context "for meeting create with participants" do
      it 'should call PublishEvent for each participants with each user' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant))
        MeetingScheduledEventPublisher.call(@meeting, @meeting.participants)
      end
    end

    context "for meeting update with participants" do

      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "<PERSON>")
        @contact_lookup = build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "<PERSON>")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
      end

      context 'with new users added' do
        before do
          @meeting.participants << @deal_lookup
          @meeting.participants << @contact_lookup
          @meeting.save
        end
        it 'should call PublishEvent for each entities with new user' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).twice
          MeetingScheduledEventPublisher.call(@meeting, [@invitee, @deal_lookup, @contact_lookup])
        end
      end
      context 'with new entities added' do
        before do
          @meeting.participants << @invitee
          @meeting.save
        end
        it 'should call PublishEvent for each new entities with each user' do
          # Once for owner and second for new invitee
          # Once for owner and second for new invitee
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).once
          MeetingScheduledEventPublisher.call(@meeting, [@invitee])
        end
      end

      context 'with one new entities added and one new user added' do
        it 'should call PublishEvent for new entity with each user and old entity with new user' do
          # Once new invitee
          # Once for owner and second for new invitee
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).once
          MeetingScheduledEventPublisher.call(@meeting, [@deal_lookup, @invitee])
        end
      end
    end

  end
end
