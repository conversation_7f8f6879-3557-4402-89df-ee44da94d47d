require 'rails_helper'

RSpec.describe GetLayoutView do
  describe '#call' do
    let!(:user)             { create(:user)}
    let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }

    context 'user is authorized' do
      context 'tenant has fields created' do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
          CreateMeetingFieldsForTenant.call(auth_data.tenant_id, user.id)
          create_list(:field, 3, tenant_id: auth_data.tenant_id, active: true, is_standard: false, is_internal: false)
        end

        context 'with create view' do
          it 'should return create view layout' do
            params = ActionController::Parameters.new({ 'view': 'create' }).permit!
            layout = GetLayoutView.call(params).result

            expect(layout.keys).to eql(['layoutItems'])
            expect(layout['layoutItems'].map { |section| section['item']['heading'] }).to eql(['Basic Info', 'Other Details'])

            layout_items = layout['layoutItems'].first['layoutItems']
            expect(layout_items.count).to be >= 10
            related_entities = layout_items.select { |item| item['item']['type'] == 'ENTITY_LOOKUP' }.first['item']['pickLists']
            expect(layout_items.find{ |item| item['item']['internalName'] == 'owner' }['item']['lookupUrl']).to eq('/users/meeting-invitee/lookup?q=')
            expect(related_entities.count).to eql(4)
            expect(related_entities.map { |item| item['displayName'] }).to match_array(["LEAD", "DEAL", "CONTACT", "COMPANY"])
            expect(related_entities.map { |item| item['lookupUrl'] }).to match_array([RELATED_LEAD_LOOKUP_URL, RELATED_DEAL_LOOKUP_URL, RELATED_CONTACT_LOOKUP_URL, RELATED_COMPANY_LOOKUP_URL])

            status_field = layout_items.find { |item| item['item']['internalName'] == 'status' }['item']
            expect(status_field['type']).to eq('PICK_LIST')
            expect(status_field['internal']).to be(true)

            timezones = layout_items.select { |item| item['item']['internalName'] == 'timezone' }.first['item']['pickLists']
            expect(timezones.count).to be >= 435
            expect(timezones.map { |timezone| timezone['systemDefault'] == true }.count).to eql(435)

            internal_names = layout_items.map { |item| item['item']['internalName'] }
            expect(EXCLUDED_MEETING_LAYOUT_FIELDS & internal_names).to eql([])

            layout_items = layout['layoutItems'].last['layoutItems']
            expect(layout_items.count).to be(3)
            expect(layout_items.first['row']).to be(1)
            expect(layout_items.first['column']).to be(1)
            expect(layout_items.second['row']).to be(1)
            expect(layout_items.second['column']).to be(2)
            expect(layout_items.last['row']).to be(2)
            expect(layout_items.last['column']).to be(1)
          end
        end

        context 'with edit view' do
          it 'should return edit view layout' do
            params = ActionController::Parameters.new({ 'view': 'edit' }).permit!
            layout = GetLayoutView.call(params).result

            expect(layout['layoutItems'].map { |section| section['item']['heading'] }).to match_array(['Basic Info', 'Other Details', 'Internals', 'Check In & Check Out Internals'])
            basic_info_layout_items = layout['layoutItems'].first['layoutItems']
            # Check if the medium field is readOnly or not
            read_only_fields = basic_info_layout_items.select { |field| %w[medium providerLink organizer].include?(field.dig('item', 'internalName')) }
            expect(read_only_fields.all? { |f| f.dig('item', 'readOnly') == true }).to eql(true)

            internals_layout_items = layout['layoutItems'].last(2).first['layoutItems']
            expect(internals_layout_items.count).to eql(9)

            expect(internals_layout_items.map { |item| item['item']['internalName'] }).to eql(["createdBy", "createdAt", "updatedBy", "updatedAt", "conductedBy", "conductedAt", "cancelledBy", "cancelledAt", "importedBy"])
            expect(internals_layout_items.map { |item| item['item']['type'] }).to eql(["LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP"])

            checkin_checkout_internals = layout['layoutItems'].last['layoutItems']
            expect(checkin_checkout_internals.count).to eql(6)
            expect(checkin_checkout_internals.map { |item| item['item']['internalName'] }).to eql(["checkedInAt", "checkedInLatitude", "checkedInLongitude", "checkedOutAt", "checkedOutLatitude", "checkedOutLongitude"])
            expect(checkin_checkout_internals.map { |item| item['item']['type'] }).to eql(["DATETIME_PICKER", "TEXT_FIELD", "TEXT_FIELD", "DATETIME_PICKER", "TEXT_FIELD", "TEXT_FIELD"])

            internal_names = (basic_info_layout_items + internals_layout_items).map { |item| item['item']['internalName'] }
            expect(EXCLUDED_MEETING_LAYOUT_FIELDS & internal_names).to eql([])
          end
        end
      end

      context 'tenant has none of the fields created' do
        it 'should raise Not Found error with not found error code' do
          params = ActionController::Parameters.new({ 'view': 'create' }).permit!
          expect{GetLayoutView.call(params).result}.to raise_error(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Field not found.")
        end
      end
    end

    context 'user is unauthorized' do
      before do
        thread = Thread.current
        thread[:auth] = nil
      end

      it 'should raise Authentication Error with unauthorized error code' do
        params = ActionController::Parameters.new({ 'view': 'create' }).permit!
        expect{GetLayoutView.call(params).result}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end
