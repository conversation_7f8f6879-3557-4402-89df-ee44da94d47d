require 'rails_helper'

RSpec.describe CaptureExternalUserRsvp do
  describe '#call' do
    before do
      @valid_user = create(:user)
      @meeting = create(:meeting, tenant_id: @valid_user.tenant_id, owner: @valid_user)
      @participant = LookUp.new(entity: "#{LOOKUP_USER}_#{@valid_user.id}", name: @valid_user.name, tenant_id: @valid_user.tenant_id)
      @meeting.participants << @participant
    end
    context 'valid' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).exactly(1).times
        event = Event::MeetingUpdated.new(@meeting, MeetingSerializer.call(@meeting, nil, false).result, @valid_user.id)
        expect(Event::MeetingUpdated)
          .to receive(:new)
          .with(instance_of(Meeting), instance_of(ActiveSupport::HashWithIndifferentAccess), instance_of(Integer), nil)
          .once
          .and_return(event)
        allow(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated))
      end

      context 'for RSVP YES from valid user who is participant' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: RSVP_YES
          }
          @command = CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end
      context 'for RSVP NO' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: RSVP_NO
          }
          @command = CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end

      context 'for RSVP MAY_BE' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: RSVP_MAYBE
          }
          @command = CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
        end

        it 'returns success' do
          expect(@command.success?).to be == true
        end

        it 'update rsvpResponse for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_response).to eq(@rsvp_data[:rsvpResponse])
        end

        it 'update rsvpMessage for the correct participant' do
          meeting_lookup = @command.result
          expect(meeting_lookup.rsvp_message).to eq(@rsvp_data[:rsvpMessage])
        end
      end
    end

    context 'invalid' do
      context 'with RSVP YES from invalid user who is not participant' do
        before do
          @invalid_user = create(:user)
          @auth_data = build(:auth_data, user_id: @invalid_user.id, tenant_id: @valid_user.tenant_id, username: @invalid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: 11111,
            rsvpMessage: nil,
            rsvpResponse: RSVP_YES
          }
        end

        it 'raises ParticipantNotFound error' do
          expect{
            CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
          }.to raise_error( ExceptionHandler::ParticipantNotFound, "#{ErrorCode.participant_not_found}||Participant not found." )
        end
      end

      context ' with incorrect RSVP value from valid user who is participant' do
        before do
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: "TESTING"
          }
        end

        it 'raises error' do
          expect{
            CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
          }.to raise_error( ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid RSVP response." )
        end

      end

      context 'for expired meeting' do
        before do
          @meeting.from = Time.now - 10.days
          @meeting.save(validate:false)
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: "YES"
          }
        end

        it 'raises MeetingExpired error' do
          expect{
            CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
          }.to raise_error( ExceptionHandler::MeetingExpired, "#{ErrorCode.meeting_expired}||Meeting expired." )
        end

      end
      context 'for cancelled meeting' do
        before do
          @meeting.status = CANCELLED
          @meeting.save(validate:false)
          @auth_data = build(:auth_data, user_id: @valid_user.id, tenant_id: @valid_user.tenant_id, username: @valid_user.name)
          thread = Thread.current
          thread[:auth] = @auth_data
          @rsvp_data = {
            pid: @participant.public_id,
            rsvpMessage: nil,
            rsvpResponse: "YES"
          }
        end

        it 'raises MeetingCancelled error' do
          expect{
            CaptureExternalUserRsvp.call(@meeting.public_id, @rsvp_data)
          }.to raise_error(ExceptionHandler::MeetingCancelled, "#{ErrorCode.meeting_cancelled}||Meeting cancelled.")
        end

      end
    end
  end
end
