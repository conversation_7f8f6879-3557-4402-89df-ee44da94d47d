require 'rails_helper'
require 'bunny-mock'

RSpec.describe MarkMeetingAsMissed do
  describe '#call' do
    before do
      @user = create(:user)
      @time_zone =  build(:timezone_look_up, name: 'UTC')
    end

    context "when mark meeting as missed service called" do
      before do
        start_time = (DateTime.now.utc - 3.day)
        create_list(:meeting, 25, from: start_time, to: (start_time + 1.hour), time_zone: @time_zone)

        start_time += 12.hours
        create_list(:meeting_all_day, 25, from: start_time, time_zone: @time_zone)
        create_list(:meeting_all_day, 25, from: start_time, status: CONDUCTED, conducted_at: DateTime.now.utc, conducted_by: @user, time_zone: @time_zone)

        start_time +=  1.day + 12.hours
        create_list(:meeting, 25, from: start_time, to: (start_time + 1.hour), time_zone: @time_zone)

        start_time = DateTime.now.utc - 15.hours
        create_list(:meeting_all_day, 25, from: start_time, time_zone: @time_zone)
        create_list(:meeting_all_day, 25, from: start_time, status: CANCELLED, cancelled_at: DateTime.now.utc, cancelled_by: @user, time_zone: @time_zone)

        start_time += 18.hours + 1.hour
        create_list(:meeting, 25, from: start_time, to: (start_time + 1.hour), time_zone: @time_zone)
      end

      it "should mark all past scheduled meetings in the last 24 hours to missed" do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(50).times
        described_class.call

        expect(Meeting.pluck(:status).count(SCHEDULED)).to eq(75)
        expect(Meeting.pluck(:status).count(MISSED)).to eq(50)
        expect(Meeting.pluck(:status).count(CANCELLED)).to eq(25)
        expect(Meeting.pluck(:status).count(CONDUCTED)).to eq(25)
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].lt(DateTime.now.utc)).count).to eq(150)
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].lt(DateTime.now.utc)).pluck(:status).uniq).to match_array([SCHEDULED, MISSED, CONDUCTED, CANCELLED])
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].gt(DateTime.now.utc)).count).to eq(25)
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].gt(DateTime.now.utc)).pluck(:status).uniq).to match_array([SCHEDULED])
        expect(Meeting.where(Meeting.arel_table['from'.to_sym].lt(DateTime.now.utc - 24.hours)).count).to eq(100)
        expect(Meeting.where(Meeting.arel_table['from'.to_sym].lt(DateTime.now.utc - 24.hours)).pluck(:status).count(MISSED)).to eq(25)
        expect(Meeting.where(Meeting.arel_table['from'.to_sym].lt(DateTime.now.utc - 24.hours)).pluck(:status).count(SCHEDULED)).to eq(50)
        expect(Meeting.where(Meeting.arel_table['from'.to_sym].lt(DateTime.now.utc - 24.hours)).pluck(:status).count(CONDUCTED)).to eq(25)

        end_time = DateTime.now.utc
        start_time = (end_time - 24.hours)
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].between(start_time..end_time)).count).to eq(75)
        expect(Meeting.where(Meeting.arel_table['to'.to_sym].between(start_time..end_time)).pluck(:status).uniq).to match_array([MISSED, CANCELLED])
      end
    end

    context 'when an error occurs when marking one of the meetings as missed' do
      before do
        start_time = (DateTime.now.utc - 12.hours)
        @meeting = create(:meeting, from: start_time, to: (start_time + 1.hour), time_zone: @time_zone)
        expect_any_instance_of(Meeting).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)
      end

      it 'should log error for the meeting and not publish any events' do
        expect(Rails.logger).to receive(:error).with("MarkMeetingAsMissed Meeting #{@meeting.id} not marked as missed. Message Record invalid")
        described_class.call
      end
    end
  end
end
