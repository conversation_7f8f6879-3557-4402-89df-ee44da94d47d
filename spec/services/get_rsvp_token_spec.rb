require 'rails_helper'

RSpec.describe GetRsvpToken do
  describe '#call' do
    before do
      @token = build(:auth_token, user_id: 9, tenant_id: 10).token
      @user = build(:user_look_up, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end
    context 'valid input' do
      before do
        stub_request(:get, "http://localhost:8081/v1/api-keys/meeting-rsvp").
         with(
           headers: {
          'Authorization'=>'Bearer '+ @token
           }).
         to_return(status: 200, body: {"apiKey": "a71ac5be-2d39-47e7-b1d3-8cc130a7fb3f"}.to_json, headers: {})
      end
      it ' returns if no users are passed' do
        expect(GetRsvpToken.call().success?).to be true
      end

      it 'returns array of updated user look ups' do
        command = GetRsvpToken.call()
        expect(command.result.class).to be String
        expect(command.result).to be == "a71ac5be-2d39-47e7-b1d3-8cc130a7fb3f"
      end

    end
  end
end
