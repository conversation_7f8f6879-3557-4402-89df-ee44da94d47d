require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForContactDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @contact_lookup = create(:contact_look_up, tenant_id: @user.tenant_id)
      @channel = connection.start.channel
      @exchange = @channel.topic CONTACT_EXCHANGE
    end

    context 'valid input' do
      context 'for contact deleted event' do
        let(:payload) { { 'id' => @contact_lookup.entity_id, 'tenantId' => @contact_lookup.tenant_id, 'userId' => @user.id }.to_json }

        before do
          @queue = @channel.queue ''
          @queue.bind @exchange, routing_key: CONTACT_DELETED_EVENT
          allow(RabbitmqConnection).to receive(:subscribe).with(CONTACT_EXCHANGE, CONTACT_DELETED_EVENT, CONTACT_DELETED_QUEUE).and_yield(payload.to_s)
        end

        context 'Meeting has only one lookup association which is current contact' do
          it 'Should delete meeting and call dependant events' do
            another_user = create(:user, tenant_id: @user.tenant_id)
            participant = build(:user_look_up, tenant_id: another_user.tenant_id, entity: "user_#{another_user.id}")
            meeting = create(:meeting, owner: @user)
            meeting.related_to << @contact_lookup
            meeting.participants << participant
            meeting.save

            # TODO: fix this
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
            #expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
            #expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
            expect(PublishUsageJob).to receive(:perform_later).with(another_user.tenant_id).once

            ListenForContactDelete.call()

            expect(Meeting.count).to eq(0)
            expect(MeetingLookUp.count).to eql(0)
          end
        end

        context 'when meeting medium is GOOGLE' do
          context 'when meeting has only one lookup of user' do
            def stub_delete_google_meeting_request(status, response_body)
              headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Accept': 'application/json' }
              stub_request(:delete, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(@google_meeting.provider_meeting_id)}?sendUpdates=all")
                .with(headers: headers)
                .to_return(status: status, body: response_body)
            end

            before do
              @google_meeting = create(:meeting, medium: GOOGLE_PROVIDER, owner_id: @user.id, provider_meeting_id: 1)
              @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                              provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)

              expect(ParticipantRemovedEventPublisher).not_to receive(:call)
              expect(MeetingCancelledEventPublisher).not_to receive(:call)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
              expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id).once
            end

            it 'destroys the meeting' do
              stub_delete_google_meeting_request(204, {}.to_json)
              @contact_lookup.meetings << @google_meeting
              ListenForContactDelete.call
              expect(Meeting.find_by(id: @google_meeting.id)).to eq(nil)
              expect(LookUp.find_by(id: @contact_lookup.id)).to eq(nil)
            end
          end
        end

        context 'Meeting has other lookup association along with current contact' do
          it 'Shouldn\'t delete meeting and unrelate current contact' do
            allow(PublishEvent).to receive(:call)

            meeting = create(:meeting, owner: @user)
            @deal_lookup = build(:deal_look_up, entity_id: 16, tenant_id: @user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
            meeting.participants << [invitee, @deal_lookup, @contact_lookup]
            meeting.related_to << [@contact_lookup, @deal_lookup]

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(6)
            expect(LookUp.find_by(id: @contact_lookup.id)).to eq(@contact_lookup)

            ListenForContactDelete.call()

            expect(Meeting.count).to eq(1)
            expect(MeetingLookUp.count).to eql(4)
            expect(LookUp.find_by(id: @contact_lookup.id)).to be_nil
          end
        end
      end
    end
  end
end
