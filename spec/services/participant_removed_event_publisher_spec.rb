require 'rails_helper'

RSpec.describe ParticipantRemovedEventPublisher do
  describe "#call" do
    before do
      @user = create(:user)
      allow(PublishEvent).to receive(:call)
      @meeting = create(:meeting, owner: @user)
      @lead_lookup = build(:lead_look_up, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      owner_lookup = build(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
      @meeting.participants = [owner_lookup, @lead_lookup]
      @meeting.save
    end

    def stub_unrelated_events(lead: 0, contact: 0, deal: 0, company: 0)
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToLead)).exactly(lead).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).exactly(contact).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).exactly(deal).times
      expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToCompany)).exactly(company).times
    end

    context "for meeting cancel" do
      it 'should call PublishEvent for each participants with each user' do
        @meeting.participants << build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: "invitee")
        stub_unrelated_events(lead: 1)
        ParticipantRemovedEventPublisher.call(@meeting)
      end
    end

    context "for meeting update with external participants removed" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @contact_lookup = build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "John Contact")
        @external_lookup = build(:external_look_up, entity_id: 14, tenant_id: @user.tenant_id, name: "John external")
        @meeting.participants << build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
      end

      it 'should call PublishEvent for each participants with each user' do
        stub_unrelated_events(contact: 1, deal: 1)
        ParticipantRemovedEventPublisher.call(@meeting, [@deal_lookup, @contact_lookup, @external_lookup])
      end
    end

    context "for meeting update with invitee removed" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @contact_lookup = build(:contact_look_up, entity_id: 13, tenant_id: @user.tenant_id, name: "John Contact")
        @company_lookup = build(:company_look_up, entity_id: 14, tenant_id: @user.tenant_id, name: "John Company")
        @meeting.participants = @meeting.participants + [@deal_lookup, @contact_lookup, @company_lookup]
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
      end

      it 'should call PublishEvent for each participants with each user' do
        stub_unrelated_events(lead: 1, contact: 1, deal: 1, company: 1)
        ParticipantRemovedEventPublisher.call(@meeting, [@invitee])
      end
    end

    context "for meeting update with related entity removed" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
        @meeting.participants = @meeting.participants + [@invitee]
        @meeting.related_to = [@deal_lookup]
      end

      it 'should call PublishEvent correctly' do
        stub_unrelated_events(deal: 1)
        ParticipantRemovedEventPublisher.call(@meeting, [@deal_lookup])
      end
    end

    context "for meeting update with user entity removed" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
        @meeting.participants = @meeting.participants + [@invitee]
        @meeting.related_to = [@deal_lookup]
      end

      it 'should call PublishEvent correctly' do
        stub_unrelated_events(lead: 1, deal: 1)
        ParticipantRemovedEventPublisher.call(@meeting, [@invitee])
      end
    end

    context "for meeting delete with related entity and participant" do
      before do
        @deal_lookup = build(:deal_look_up, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
        @invitee = build(:user_look_up, entity_id: 15, tenant_id: @user.tenant_id, name: 'invitee')
        @meeting.participants = @meeting.participants + [@invitee]
        @meeting.related_to = [@deal_lookup]
      end

      it 'should call PublishEvent correctly' do
        stub_unrelated_events(lead: 1, deal: 1)
        ParticipantRemovedEventPublisher.call(@meeting)
      end
    end
  end
end
