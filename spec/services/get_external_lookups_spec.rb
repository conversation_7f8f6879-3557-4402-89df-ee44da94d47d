require 'rails_helper'

RSpec.describe GetExternalLookups do
  describe '#call' do
    before do
      @user = create(:user)
      auth_data = build(:auth_data, :note, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      @external_matching_look_ups = create_list(:look_up, 10, entity: LOOKUP_EXTERNAL, tenant_id: @user.tenant_id)
      @entity_with_matching_email = @external_matching_look_ups.first
      @entity_with_matching_name = @external_matching_look_ups.last
      @entity_with_matching_name.update(name: 'test')
      @entity_with_matching_email.update(email: '<EMAIL>')
      @other_tenant_data = create(:look_up, entity: LOOKUP_EXTERNAL, tenant_id: 1111111111111, email: '<EMAIL>')
    end

    context 'with search string' do
      before do
        @look_ups = GetExternalLookups.call('test').result
      end

      it 'returns records matched by name and email of current tenant' do
        expect(@look_ups.count).to eq(2)
        expect(@look_ups.collect(&:id)).to eq([@entity_with_matching_email.id, @entity_with_matching_name.id])
      end

      it 'will not return data of other tenant' do
        expect(@look_ups.count).to eq(2)
        expect(@look_ups.collect(&:id)).not_to include(@other_tenant_data.id)
      end
    end

    context 'without search string' do
      it 'returns 5 records' do
        look_ups = GetExternalLookups.call.result
        expect(look_ups.count).to eq(5)
      end
    end
  end
end
