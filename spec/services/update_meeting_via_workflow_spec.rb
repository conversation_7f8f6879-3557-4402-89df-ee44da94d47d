# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateMeetingViaWorkflow do
  let(:payload) { JSON.parse(file_fixture('meeting_workflow_payload.json').read) }

  describe '#call' do
    context 'when meeting for given payload is not found' do
      it 'does nothing and returns' do
        expect(described_class.call(payload.with_indifferent_access).result).to be_nil
      end
    end

    context 'when meeting is present' do
      before do
        company_look_up = build(:company_look_up, entity_id: 10, name: 'Some other company')
        meeting = create(:meeting, id: 3763, tenant_id: 190, all_day: false)
        meeting.related_to << company_look_up
        @user = create(:user, id: 114, tenant_id: 190)
        @timezone = create(:timezone_look_up, tenant_id: @user.tenant_id)
        custom_field_1 = create(
          :custom_field,
          field_type: 'TEXT_FIELD',
          display_name: 'Text Field',
          tenant_id: @user.tenant_id
        )
        custom_field_2 = create(
          :custom_field,
          field_type: 'NUMBER',
          display_name: 'Number Field',
          tenant_id: @user.tenant_id
        )
        custom_field_3 = create(
          :custom_field,
          field_type: 'PICK_LIST',
          display_name: 'Picklist Field',
          tenant_id: @user.tenant_id
        )
        picklist = create(
          :picklist,

          tenant_id: @user.tenant_id,
          internal_name: custom_field_3.internal_name,
          field: custom_field_3
        )
        pl_val_1 = create(
          :picklist_value,
          id: 849_440_483,
          tenant_id: @user.tenant_id, internal_name: 'value_1', picklist: picklist
        )
        pl_val_2 = create(
          :picklist_value,
          id: 849_440_484,
          tenant_id: @user.tenant_id, internal_name: 'value_2', picklist: picklist,
          display_name: 'pl value 2'
        )
        meeting.custom_field_values[custom_field_1.internal_name] = 'existing name'
        meeting.custom_field_values[custom_field_3.internal_name] =
          { id: pl_val_1.id, value: 'value_1' }.with_indifferent_access
        meeting.save
        @user1 = create(:user, tenant_id: @user.tenant_id)
        @user2 = create(:user, tenant_id: @user.tenant_id)
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return(
          [
            build(
              :user_look_up,
              entity_id: @user1.id,
              tenant_id: @user.tenant_id,
              name: 'RJ'
            ),
            build(
              :user_look_up,
              entity_id: @user2.id,
              tenant_id: @user.tenant_id,
              name: 'RVJ'
            )
          ]
        )
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
      end

      context 'when meeting data is for allDay' do
        it 'updates meeting for given details' do
          described_class.call(payload.with_indifferent_access)
          meeting = Meeting.last
          expect(meeting.title).to eq('updated meeting name')
          expect(meeting.description).to eq('meeting desc updated from workflow')
          expect(meeting.all_day?).to eq(true)
          expect(meeting.custom_field_values).to eq(
            {
              'cfNumberField' => 123_123_123,
              'cfPicklistField' => { 'id' => 849_440_484, 'name' => 'pl value 2' },
              'cfTextField' => 'existing name'
            }
          )
        end
      end

      context 'when meeting data is not for allDay' do
        it 'updates data according to given details' do
          payload['entity']['from'] = {
            "allDay": false,
            "from": '2023-03-16T07:58:23.927Z',
            "to": '2023-03-16T08:28:23.927Z'
          }.with_indifferent_access
          payload['entity'].delete('allDay')
          described_class.call(payload.with_indifferent_access)
          meeting = Meeting.last
          expect(meeting.title).to eq('updated meeting name')
          expect(meeting.description).to eq('meeting desc updated from workflow')
          expect(meeting.all_day?).to eq(false)
        end
      end
    end
  end
end
