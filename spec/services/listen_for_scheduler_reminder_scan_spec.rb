require 'rails_helper'
require 'bunny-mock'
require 'sidekiq/testing'

RSpec.describe ListenForSchedulerReminderScan do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MEETING_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue MEETING_SCHEDULED_IN_MINUTES_EVENT
        @queue.bind @exchange, routing_key: MEETING_SCHEDULED_IN_MINUTES_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE).and_return(@queue)
        # allow(RabbitmqConnection).to receive(:get_channel).and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(SCHEDULER_EXCHANGE, SCHEDULER_REMINDER_SCAN_EVENT, SCHEDULER_REMINDER_SCAN_QUEUE)
          .and_yield(payload.to_s)

        @time_after_13_minutes = DateTime.now + 13.minutes
        @meeting = create(:meeting, owner: @user, from: @time_after_13_minutes)
        @meeting.participants << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 11, name: "John User")
        @meeting.participants << create(:user_look_up, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name, email:"<EMAIL>")
      end

      context 'with valid values' do
        let(:triggered_at) { DateTime.now }
        let(:payload) { { "message": "reminder-scan", "triggeredAt": (triggered_at).strftime }.to_json }

        before do
          Sidekiq::Worker.clear_all
          ActiveJob::Base.queue_adapter = :test
        end

        it 'should enqueue only required jobs' do
          start_time = triggered_at + 14.minutes
          17.times do |t|
            create_list(:meeting, 2, owner: @user, from: start_time + t.minutes)
          end

          expect do
            ListenForSchedulerReminderScan.call
          end.to have_enqueued_job.on_queue('reminder_queue').exactly(15).times
        end

        it 'should not enqueue job for meetings that are out of range of next to next 15 minutes' do
          create_list(:meeting, 2, owner: @user, from: triggered_at + 14.minutes)
          create_list(:meeting, 2, owner: @user, from: triggered_at + 31.minutes)
          expect do
            ListenForSchedulerReminderScan.call
          end.not_to have_enqueued_job.on_queue('reminder_queue')
        end
      end
    end
  end
end
