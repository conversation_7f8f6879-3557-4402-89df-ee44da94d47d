# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForCompanyReassign do
  describe '#call' do
    let(:payload) { JSON.parse(file_fixture('company-reassigned-payload.json').read) }
    let(:lookup) { create(:company_look_up, tenant_id: payload['metadata']['tenantId'], entity_id: payload['entity']['id']) }

    context 'valid input' do
      context 'when payload is received for company reassigned event' do
        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with('ex.company', 'company.reassigned', 'q.company.reassigned.meetings')
            .and_yield(payload.to_json)
          lookup
        end

        it 'updates the company lookup with new owner' do
          expect(Rails.logger).to receive(:info).with("Received message ex.company for company.reassigned")
          described_class.call

          expect(LookUp.where(entity: "company_#{lookup.entity_id}").pluck(:owner_id)).to match([99])
        end
      end
    end
  end
end
