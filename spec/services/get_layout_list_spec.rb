require 'rails_helper'

RSpec.describe GetLayoutList do
  describe '#call' do
    let!(:user)             { create(:user)}
    let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }

    context 'user is authorized' do
      context 'tenant has fields created' do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
          CreateMeetingFieldsForTenant.call(auth_data.tenant_id, user.id)
          Field.find_by(internal_name: 'createdBy').update(display_name: '<PERSON>')
          Field.find_by(internal_name: 'conductedBy').update(display_name: 'Conductor')
        end

        it 'should return layout list' do
          list_layout = GetLayoutList.call.result

          expect(list_layout.keys).to eq(%w[leftNav pageConfig defaultConfig])
          expect(list_layout['defaultConfig']['fields']).to eq(%w[title allDay from to timezone status
                                                                  participants organizer relatedTo medium location
                                                                  description recordActions])
          expect(list_layout['pageConfig']['actionConfig'].keys).to eq(%w[search filter create importItems
                                                                          columnSelector])

          table_config = list_layout['pageConfig']['tableConfig']
          expect(table_config.keys).to eq(%w[fetchURL searchService recordClickAction columns clickActionUrl])
          expect(table_config['fetchURL']).to eq('/search/meetings')
          expect(table_config['searchService']).to eq('search')
          expect(table_config['recordClickAction']).to eq('VIEW')
          expect(table_config['columns'].count).to be >= 22
          expect(table_config['columns'].select { |field| field['id'] == 'tenant_id' }.any?).to eq(false)
          expect(table_config['columns'][-13..-10].map { |field| field['header'] }).to match_array(['Associated Leads', 'Associated Contacts', 'Associated Deals', 'Associated Companies'])
          expect(table_config['columns'][-9..-2].map { |field| field['header'] }).to match_array(['Malik Fields', 'Updated By Fields', 'Conductor Fields', 'Cancelled By Fields', 'Organizer Fields', 'Owner Fields', 'Invitees Fields', 'Checked In/Out By Fields'])
          expect(table_config['columns'].last['header']).to eq('Checked In/Out By')
          status_field = table_config['columns'].find { |field| field['id'] == 'status' }
          expect(status_field['fieldType']).to eq('PICK_LIST')
          expect(status_field['isInternal']).to be(true)

          fixed_fields = %w(id status title allDay from to participants medium location description createdBy createdAt updatedBy updatedAt owner timezone relatedTo)
          expect(fixed_fields & table_config['columns'].map { |field| field['id'] }).to eq(fixed_fields)
          expect(table_config['columns'].first.keys).to eq(%w(id header fieldType isStandard isSortable isFilterable isInternal isRequired active showDefaultOptions picklist primaryField))
        end
      end

      context 'tenant has none of the fields created' do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
        end

        it 'should return Not Found error with not found error code' do
          expect{GetLayoutList.call.result}.to raise_error(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||Field not found.")
        end
      end
    end

    context 'user is unauthorized' do
      before do
        thread = Thread.current
        thread[:auth] = nil
      end

      it 'should raise Authentication Error with unauthorized error code' do
        expect{GetLayoutList.call.result}.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end
