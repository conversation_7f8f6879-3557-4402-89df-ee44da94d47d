# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CheckMeetingsAccess do
  let!(:user) { create(:user)}
  let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:auth_data) { ParseToken.call(valid_auth_token.token).result }
  let!(:headers) { valid_headers(valid_auth_token) }

  before do
    Thread.current[:token] = valid_auth_token
    Thread.current[:auth] = auth_data

    create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true)
    [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
      stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
        .with(
          headers: {
            Authorization: "Bearer #{valid_auth_token}"
          }
        )
        .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
    end
  end

  context 'with valid token' do
    let(:meetings) { create_list(:meeting, 3, tenant_id: user.tenant_id, owner: another_user) }

    before do
      participant = build(:user_look_up)
      participant.name = user.name
      participant.tenant_id = user.tenant_id
      participant.entity = "user_#{user.id}"

      meetings.each_with_index do |m, index|
        m.participants << participant
        m.save!
      end
    end

    context 'when meeting ids are provided' do
      it "returns all accessible meeting ids" do
        meetings_ids = CheckMeetingsAccess.new({ meeting_ids: meetings.map(&:id) }).check_meetings_access

        expect(meetings_ids.count).to eq(3)
        expect(meetings_ids.first).to eq(meetings.first.id)
        expect(meetings_ids.last).to eq(meetings.last.id)
      end
    end

    context 'when related entity_id and entity are provided' do
      let(:related_meeting_ids){ meetings.first(2).map(&:id) }

      before do
        lead_lookup = build(:lead_look_up, tenant_id: user.tenant_id, entity_id: 234, entity_type: "lead")
        meetings.first(2).each do |m|
          m.related_to << lead_lookup
          m.save!
        end
      end

      it "returns all accessible meeting ids" do
        meetings_ids = CheckMeetingsAccess.new({ relatedToEntityId: 234, relatedToEntityType: "lead" }).get_meetings_related_to_entity

        expect(meetings_ids).to match_array(related_meeting_ids)
      end
    end
  end

  context 'with invalid token' do
    before do
      Thread.current[:token] = 'invalid_auth_token'
      Thread.current[:auth] = {}
    end

    context 'when related entity_id and entity are provided' do
      it "returns empty array" do
        meetings_ids = CheckMeetingsAccess.new({ relatedToEntityId: 234, relatedToEntityType: "lead" }).get_meetings_related_to_entity

        expect(meetings_ids).to match_array([])
      end
    end
  end
end
