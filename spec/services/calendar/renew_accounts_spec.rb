# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::RenewAccounts' do
  describe '#call' do
    context 'when service is called' do
      context 'when there are renew for microsoft' do
        before(:each) do
          @acc_with_future_date = create(:connected_account, subscription_renewal_date: Date.today + 1.day, provider_name: MICROSOFT_TEAMS_PROVIDER, sync_type: { 'calendar_to_kylas': true })
          @acc_with_past_date = create(:connected_account, subscription_renewal_date: Date.today - 1.day, provider_name: MICROSOFT_TEAMS_PROVIDER, sync_type: { 'calendar_to_kylas': true })
          @acc_with_todays_date = create(:connected_account, subscription_renewal_date: Date.today, provider_name: MICROSOFT_TEAMS_PROVIDER, sync_type: { 'calendar_to_kylas': true })

          create(:connected_account, subscription_renewal_date: Date.today + 1.day, provider_name: MICROSOFT_TEAMS_PROVIDER, sync_type: { 'calendar_to_kylas': false })
          create(:connected_account, subscription_renewal_date: Date.today, provider_name: GOOGLE_PROVIDER, sync_type: { 'calendar_to_kylas': true })
        end

        it 'updates account subscription_renewal_date for eligible accounts' do
          expect(Calendar::Microsoft::RenewSubscription).to receive(:call).and_return(true).exactly(2).times
          expect(Calendar::Google::RenewSubscription).to receive(:call).and_return(true).exactly(1).times
          Calendar::RenewAccounts.call()
          expect(@acc_with_past_date.reload.subscription_renewal_date).to eq(Date.today + MS_RENEWAL_DURATION.days)
          expect(@acc_with_todays_date.reload.subscription_renewal_date).to eq(Date.today + MS_RENEWAL_DURATION.days)
        end
      end
    end
  end
end
