# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Google::Base' do
  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id, provider_name: GOOGLE_PROVIDER)
    @meeting = create(:meeting, medium: GOOGLE_PROVIDER)
  end

  describe '#call' do
    context 'when event class is not found' do
      it 'does raise the exception of invalid data' do
        @meeting.update(medium: OFFLINE)
        expect do
          Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_CREATE_EVENT, @user)
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid calender event.")
      end
    end

    context 'when event class is found' do
      context 'when connected account is not found' do
        it 'does raise the exception of connected account not found' do
          @connected_account.destroy
          expect do
            Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_CREATE_EVENT, @user)
          end.to raise_error(ExceptionHandler::ConnectedAccountNotFound, "#{ErrorCode.connected_account_not_found}||Connected Account not found.")
        end
      end

      context 'when connected account is found' do
        let(:calendar_base_instance) { instance_double(Calendar::Google::CreateEvent) }

        it 'does call method of respective provider for creating meeting' do
          expect(Calendar::Google::CreateEvent).to receive(:call)
          Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_CREATE_EVENT, @user)
        end

        it 'does return meeting link and provider meeting id' do
          allow(Calendar::Google::CreateEvent).to receive(:call).and_return(calendar_base_instance)
          allow(calendar_base_instance).to receive(:result).and_return(
            { provider_link: 'https://meet.google.com/abc-defg-msg', provider_meeting_id: 10 }
          )

          command = Calendar::Base.call(@auth_data, @meeting, ONLINE_MEETING_CREATE_EVENT, @user)
          expect(Calendar::Google::CreateEvent).to have_received(:call).once
          expect(calendar_base_instance).to have_received(:result).once
          expect(command.result).to eq({ provider_link: 'https://meet.google.com/abc-defg-msg', provider_meeting_id: 10 })
        end
      end
    end
  end
end
