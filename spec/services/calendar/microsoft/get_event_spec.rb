# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::GetEvent' do
  let(:event_id) {
    'AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA='
  }

  def stub_get_event_request(status: 200)
    stub_request(:get, "https://graph.microsoft.com/v1.0/me/events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=").
    with(
      headers: {
        'Authorization'=>"Bearer #{@connected_account.access_token}",
        'Content-Type'=>'application/json',
      }).
    to_return(status: status, body: file_fixture('microsoft/get_event_by_id.json').read, headers: {})
  end

  describe '#call' do
    before do
      @user = create(:user)
      @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      @connected_account = create(:connected_account, provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: Faker::Internet.email, user_id: @user.id, tenant_id: @user.tenant_id)
    end

    it 'gets event with given id' do
      stub_get_event_request
      response = Calendar::Microsoft::GetEvent.call(@connected_account, event_id).result

      expect(response[:data]).to be_present
    end

    context 'when api returns 400' do
      it 'raises error for invalid data' do
        stub_get_event_request(status: 400)
        expect(Calendar::Microsoft::GetEvent.call(@connected_account, event_id).result).to eq({})
      end
    end

    context 'when api returns 401' do
      it 'raises error for invalid data' do
        stub_get_event_request(status: 401)
        expect(Calendar::Microsoft::GetEvent.call(@connected_account, event_id).result).to eq({})
      end
    end
  end
end
