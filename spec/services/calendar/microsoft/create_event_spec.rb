# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::CreateEvent' do
  let(:meeting) { build(:meeting_with_associated_entities, all_day: false, participant_user: true, status: SCHEDULED, location: 'Pune') }

  let(:create_microsoft_teams_meeting_payload) do
    {
      subject: meeting.title,
      body: {
        contentType: 'HTML',
        content: meeting.description
      },
      start: {
        timeZone: meeting.time_zone.name,
        dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T')
      },
      end: {
        timeZone: meeting.time_zone.name,
        dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T')
      },
      location: {
        displayName: meeting.location
      },
      attendees: meeting.participants.map do |participant|
        {
          emailAddress: {
            address: participant.email,
            name: participant.name
          },
          type: 'required'
        }
      end,
      allowNewTimeProposals: true,
      transactionId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
      isOnlineMeeting: true,
      onlineMeetingProvider: 'teamsForBusiness',
      isOrganizer: true,
      isAllDay: meeting.all_day?,
      isReminderOn: true
    }
  end

  let(:create_microsoft_teams_meeting_success_response) do
    {
      '@odata.context': 'https://graph.microsoft.com/v1.0/',
      '@odata.etag': 'W/"8QKxOJgiwEydrBrfsj65fAAA24IUYA=="',
      id: 'AAMkAGNhMWE2',
      createdDateTime: '2022-06-21T19:22:30.6543808Z',
      lastModifiedDateTime: '2022-06-21T19:22:35.7477456Z',
      changeKey: '8QKxOJgiwEydrBrfsj65fAAA24IUYA==',
      categories: [],
      transactionId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
      originalStartTimeZone: meeting.time_zone.name,
      originalEndTimeZone: meeting.time_zone.name,
      iCalUId: '040000008200E00074C',
      reminderMinutesBeforeStart: 15,
      isReminderOn: true,
      hasAttachments: false,
      subject: 'Let\'s go for lunch',
      bodyPreview: 'Does',
      importance: 'normal',
      sensitivity: 'normal',
      isAllDay: true,
      isCancelled: false,
      isOrganizer: true,
      responseRequested: true,
      seriesMasterId: nil,
      showAs: 'busy',
      type: 'singleInstance',
      webLink: 'https://outlook.office365.com/owa/',
      onlineMeetingUrl: nil,
      isOnlineMeeting: true,
      onlineMeetingProvider: 'teamsForBusiness',
      allowNewTimeProposals: true,
      isDraft: false,
      hideAttendees: false,
      recurrence: nil,
      responseStatus: {
        response: 'organizer',
        time: '0001-01-01T00:00:00Z'
      },
      body: {
        contentType: 'html',
        content: '<html></html>'
      },
      start: {
        timeZone: meeting.time_zone.name,
        dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T')
      },
      end: {
        timeZone: meeting.time_zone.name,
        dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T')
      },
      location: {
        displayName: 'Harry\'s Bar',
        locationType: 'default',
        uniqueId: 'Harry\'s Bar',
        uniqueIdType: 'private'
      },
      locations: [
        {
          displayName: 'Harry\'s Bar',
          locationType: 'default',
          uniqueId: 'Harry\'s Bar',
          uniqueIdType: 'private'
        }
      ],
      attendees: [
        {
          type: 'required',
          status: {
            response: 'none',
            time: '0001-01-01T00:00:00Z'
          },
          emailAddress: {
            name: 'Shyam Pandav',
            address: '<EMAIL>'
          }
        }
      ],
      organizer: {
        emailAddress: {
          name: 'Shyam Pandav',
          address: '<EMAIL>'
        }
      },
      onlineMeeting: {
        joinUrl: 'https://teams.microsoft.com/l/meetup-join/19%3ameeting'
      }
    }
  end

  let(:create_microsoft_teams_meeting_forbidden_response) do
    {
      "error": {
        "code": 'Forbidden',
        "message": "Your request can't be completed. You do not have access",
        "innerError": {
          "date": '2022-06-24T06:32:53',
          "request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2',
          "client-request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2'
        }
      }
    }
  end

  let(:create_microsoft_teams_meeting_invalid_data_response) do
    {
      "error": {
        "code": 'ErrorInvalidRequest',
        "message": "Your request can't be completed. Read-only calendars can't be modified.",
        "innerError": {
          "date": '2022-06-24T06:32:53',
          "request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2',
          "client-request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2'
        }
      }
    }
  end

  let(:create_microsoft_teams_meeting_unauthorized_response) do
    {
      "error": {
        "code": 'InvalidAuthenticationToken',
        "message": 'Access token has expired or is not yet valid.',
        "innerError": {
          "date": '2022-06-23T19:26:05',
          "request-id": '2b68242f-1de9-4f94-81a9-4a00fde8ff90',
          "client-request-id": '2b68242f-1de9-4f94-81a9-4a00fde8ff90'
        }
      }
    }
  end

  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    allow(SecureRandom).to receive(:uuid).and_return('1e486426-b5dd-494d-9754-b77cdaa7f3f7')
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                    provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: Faker::Internet.email)
    meeting.time_zone.name = 'Asia/Kolkata'
  end

  describe '#call' do
    def stub_create_microsoft_teams_meeting_request(status, request_body, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:post, "https://graph.microsoft.com/v1.0/me/calendars/#{CGI::escape(@connected_account.calendar_id)}/events")
        .with(body: request_body, headers: headers)
        .to_return(status: status, body: response_body)
    end

    context 'when microsoft_teams returns 201 status code' do
      it 'does create an online meeting and returns meeting link and meeting id' do
        stub_create_microsoft_teams_meeting_request(
          201,
          create_microsoft_teams_meeting_payload.to_json,
          create_microsoft_teams_meeting_success_response.to_json
        )
        expect(
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants }).result
        ).to eq({ provider_link: 'https://teams.microsoft.com/l/meetup-join/19%3ameeting', provider_meeting_id: 'AAMkAGNhMWE2' })
      end
    end

    context 'when microsoft_teams returns 400 status code' do
      it 'does not create an online meeting and raise invalid data error exception' do
        stub_create_microsoft_teams_meeting_request(
          400,
          create_microsoft_teams_meeting_payload.to_json,
          create_microsoft_teams_meeting_success_response.to_json
        )
        expect do
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when microsoft_teams returns 401 status code' do
      it 'does not create an online meeting and raise provider unauthorized error exception' do
        stub_create_microsoft_teams_meeting_request(
          401,
          create_microsoft_teams_meeting_payload.to_json,
          create_microsoft_teams_meeting_success_response.to_json
        )
        expect do
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when microsoft_teams returns 403 status code' do
      it 'does not create an online meeting and raise forbidden error exception' do
        stub_create_microsoft_teams_meeting_request(
          403,
          create_microsoft_teams_meeting_payload.to_json,
          create_microsoft_teams_meeting_success_response.to_json
        )
        expect do
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when microsoft_teams returns 500 status code' do
      it 'does not create an online meeting and raise internal server error exception' do
        stub_create_microsoft_teams_meeting_request(500, create_microsoft_teams_meeting_payload.to_json, {}.to_json)
        expect do
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Microsoft::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end
  end
end
