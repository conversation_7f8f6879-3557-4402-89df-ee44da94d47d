# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::UnsubscribeWebhook' do
  let(:unauthorized_response) {
    {
      "error": {
        "code": "InvalidAuthenticationToken",
        "message": "Access token has expired or is not yet valid.",
        "innerError": {
          "date": "2023-02-23T16:52:04",
          "request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e",
          "client-request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e"
        }
      }
    }
  }
  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: 'primary', provider_subscription_resource_id: 'webhook-id')
  end

  describe '#call' do
    context 'when microsoft returns 201 status code' do
      it 'does create an online meeting and returns meeting link and meeting id' do
        stub_request(:delete, "https://graph.microsoft.com/v1.0/subscriptions/webhook-id").
        with(
          headers: {
            'Authorization'=>"Bearer #{@connected_account.fetch_access_token}",
          }).
        to_return(status: 204, body: {}.to_json, headers: {})

        Calendar::Microsoft::UnsubscribeWebhook.call(@connected_account)
      end
    end
  end
end
