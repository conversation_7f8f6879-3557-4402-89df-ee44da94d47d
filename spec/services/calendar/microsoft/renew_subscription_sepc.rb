# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::RenewSubscription' do
  let(:unauthorized_response) {
    {
      "error": {
        "code": "InvalidAuthenticationToken",
        "message": "Access token has expired or is not yet valid.",
        "innerError": {
          "date": "2023-02-23T16:52:04",
          "request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e",
          "client-request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e"
        }
      }
    }
  }
  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: 'primary')
  end

  describe '#call' do
    context 'when microsoft returns 200 status code' do
      it 'updates expiration date and logs info' do
        stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{@connected_account.provider_subscription_resource_id}").
        with(
          body: JSON.dump({
            "expirationDateTime": (DateTime.now + MS_RENEWAL_DURATION.days).strftime('%Y-%m-%dT%H:%MZ'),
          }),
          headers: {
            'Authorization'=>"Bearer #{@connected_account.fetch_access_token}",
            'Content-Type'=>'application/json'
          }).
        to_return(status: 200, body: file_fixture('microsoft/webhook-response.json').read, headers: {})

        expect(Rails.logger).to receive(:info).with("Renewed subscription for connected account #{@connected_account.id} | Subscription Id - #{@connected_account.provider_subscription_resource_id}")
        Calendar::Microsoft::RenewSubscription.call(@connected_account).result
      end
    end

     context 'when microsoft returns 401 status code' do
      it 'does not update expiration date' do
        stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{@connected_account.provider_subscription_resource_id}").
        with(
          body: JSON.dump({
            "expirationDateTime": (DateTime.now + MS_RENEWAL_DURATION.days).strftime('%Y-%m-%dT%H:%MZ'),
          }),
          headers: {
            'Authorization'=>"Bearer #{@connected_account.fetch_access_token}",
            'Content-Type'=>'application/json'
          }).
        to_return(status: 401, body: unauthorized_response.to_json, headers: {})

        expect(Rails.logger).to receive(:error).with("Calendar::Microsoft::RenewSubscription exception for #{@connected_account.id} | #{@connected_account.provider_subscription_resource_id} | 401")
        expect(Rails.logger).to receive(:info).with("Subscription renewal failed for connected account #{@connected_account.id} | Subscription Id - #{@connected_account.provider_subscription_resource_id}")
        Calendar::Microsoft::RenewSubscription.call(@connected_account).result
      end
    end
  end
end
