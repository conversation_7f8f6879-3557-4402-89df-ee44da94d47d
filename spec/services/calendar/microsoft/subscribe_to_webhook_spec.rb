# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::SubscribeToWebhook' do
  let(:unauthorized_response) {
    {
      "error": {
        "code": "InvalidAuthenticationToken",
        "message": "Access token has expired or is not yet valid.",
        "innerError": {
          "date": "2023-02-23T16:52:04",
          "request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e",
          "client-request-id": "5c7770fb-e6fd-45a4-91a3-96558a5e519e"
        }
      }
    }
  }
  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: 'primary')
  end

  describe '#call' do
    context 'when microsoft returns 201 status code' do
      it 'does create an online meeting and returns meeting link and meeting id' do
        stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions").
        with(
          body: JSON.dump({
            "changeType": "created,updated,deleted",
            "notificationUrl": "http://localhost/v1/meetings/webhooks/microsoft",
            "resource": "/me/events",
            "expirationDateTime": (DateTime.now + MS_RENEWAL_DURATION.days),
            "clientState": "#{@connected_account.id}#{@user.id}#{@user.tenant_id}_secret"
          }),
          headers: {
            'Authorization'=>"Bearer #{@connected_account.fetch_access_token}",
            'Content-Type'=>'application/json'
          }).
        to_return(status: 201, body: file_fixture('microsoft/webhook-response.json').read, headers: {})

        expect(
          Calendar::Microsoft::SubscribeToWebhook.call(@connected_account).result
        ).to eq({ resource_id: '233c7c8d-6d5f-4e7c-82a6-************' })
      end
    end

     context 'when microsoft returns 401 status code' do
      it 'does not create an online meeting and raise provider unauthorized error exception' do
        stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions").
        with(
          body: JSON.dump({
            "changeType": "created,updated,deleted",
            "notificationUrl": "http://localhost/v1/meetings/webhooks/microsoft",
            "resource": "/me/events",
            "expirationDateTime": (DateTime.now + MS_RENEWAL_DURATION.days),
            "clientState": "#{@connected_account.id}#{@user.id}#{@user.tenant_id}_secret"
          }),
          headers: {
            'Authorization'=>"Bearer #{@connected_account.fetch_access_token}",
            'Content-Type'=>'application/json'
          }).
        to_return(status: 401, body: unauthorized_response.to_json, headers: {})

        expect(Calendar::Microsoft::SubscribeToWebhook.call(@connected_account).result).to eq({})
      end
    end
  end
end
