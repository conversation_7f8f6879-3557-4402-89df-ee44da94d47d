# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Microsoft::CancelEvent' do
  let(:meeting) { build(:meeting_with_associated_entities, all_day: true, participant_user: true, provider_meeting_id: 1) }

  let(:cancel_microsoft_meeting_forbidden_response) do
    {
      "error": {
        "code": 'Forbidden',
        "message": "Your request can't be completed. You do not have access",
        "innerError": {
          "date": '2022-06-24T06:32:53',
          "request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2',
          "client-request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2'
        }
      }
    }
  end

  let(:cancel_microsoft_meeting_invalid_data_response) do
    {
      "error": {
        "code": 'ErrorInvalidRequest',
        "message": "Your request can't be completed. Read-only calendars can't be modified.",
        "innerError": {
          "date": '2022-06-24T06:32:53',
          "request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2',
          "client-request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2'
        }
      }
    }
  end

  let(:cancel_microsoft_meeting_unauthorized_response) do
    {
      "error": {
        "code": 'InvalidAuthenticationToken',
        "message": 'Access token has expired or is not yet valid.',
        "innerError": {
          "date": '2022-06-23T19:26:05',
          "request-id": '2b68242f-1de9-4f94-81a9-4a00fde8ff90',
          "client-request-id": '2b68242f-1de9-4f94-81a9-4a00fde8ff90'
        }
      }
    }
  end

  let(:cancel_microsoft_meeting_not_found_response) do
    {
      "error": {
        "code": 'ErrorInvalidRequest',
        "message": "Your request can't be completed. Invalid meeting id",
        "innerError": {
          "date": '2022-06-24T06:32:53',
          "request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2',
          "client-request-id": '03efa02a-b93c-42de-ab57-c8041f6665f2'
        }
      }
    }
  end

  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    organizer_look_up = create(
      :user_look_up,
      email: '<EMAIL>',
      tenant_id: @user.tenant_id,
      entity: "user_#{@user.id}"
    )
    meeting.organizer = organizer_look_up
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                    provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: Faker::Internet.email)
    meeting.update(
      provider_link: 'https://teams.microsoft.com/l/meetup-join/19%3ameeting',
      provider_meeting_id: 'AAMkAGNhMWE2'
    )
    @connected_account.update(calendar_id: SecureRandom.uuid)
  end

  describe '#call' do
    def stub_cancel_microsoft_meeting_request(status, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:post, "https://graph.microsoft.com/v1.0/me/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(meeting.provider_meeting_id)}/cancel")
        .with(headers: headers)
        .to_return(status: status, body: response_body)
    end

    context 'when microsoft returns 202 status code' do
      it 'does cancel an online meeting and returns old meeting link and meeting id' do
        stub_cancel_microsoft_meeting_request(202, {}.to_json)
        expect(
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account).result
        ).to eq({ provider_link: 'https://teams.microsoft.com/l/meetup-join/19%3ameeting', provider_meeting_id: 'AAMkAGNhMWE2' })
      end
    end

    context 'when microsoft returns 400 status code' do
      it 'does not cancel an online meeting and raise the invalid data error exception' do
        stub_cancel_microsoft_meeting_request(400, cancel_microsoft_meeting_invalid_data_response.to_json)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when microsoft returns 401 status code' do
      it 'does not cancel an online meeting and raise the provider_unauthorized error exception' do
        stub_cancel_microsoft_meeting_request(401, cancel_microsoft_meeting_unauthorized_response.to_json)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when microsoft returns 403 status code' do
      it 'does not cancel an online meeting and raise the forbidden error exception' do
        stub_cancel_microsoft_meeting_request(403, cancel_microsoft_meeting_forbidden_response.to_json)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when microsoft returns 404 status code' do
      it 'does not cancel an online meeting and raise the not found exception' do
        stub_cancel_microsoft_meeting_request(404, cancel_microsoft_meeting_not_found_response.to_json)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderNotFound, "#{ErrorCode.provider_not_found}||Provider not found.")
      end
    end

    context 'when microsoft returns 500 status code' do
      it 'does not cancel an online meeting and raise the internal server error exception' do
        stub_cancel_microsoft_meeting_request(500, {}.to_json)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when provider meeting id is not found' do
      it 'does raise the exception of not found' do
        meeting.update(provider_meeting_id: nil)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid provider meeting id.")
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Microsoft::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end
  end
end
