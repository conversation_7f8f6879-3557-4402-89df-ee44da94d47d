# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Google::RenewSubscription' do
  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: MICROSOFT_TEAMS_PROVIDER, calendar_id: 'primary')
  end

  describe '#call' do
    context 'when requested to renew subscription' do
      it 'calls watch event service and updates renewal details' do
        expect(Calendar::Google::WatchEvent).to receive_message_chain(:call, :result).and_return({ resource_id: 'updated-id' })
        Calendar::Google::RenewSubscription.call(@connected_account)
        expect(@connected_account.reload.provider_subscription_resource_id).to eq('updated-id')
      end
    end
  end
end
