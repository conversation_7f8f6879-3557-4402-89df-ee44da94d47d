# frozen_string_literal: true

require 'rails_helper'
include GoogleRequestHelper

RSpec.describe 'Calendar::Google::FetchEvents' do

  let(:unauthorized_response) do
    {
      "error": {
        "code": 401,
        "message": 'Re<PERSON> had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.',
        "errors": [
          {
            "message": 'Invalid Credentials',
            "domain": 'global',
            "reason": 'authError',
            "location": 'Authorization',
            "locationType": 'header'
          }
        ],
        "status": 'UNAUTHENTICATED'
      }
    }
  end

  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
  end

  describe '#call' do
    context 'when google returns 200 status code' do
      it 'return events returned by google' do
        events_response = JSON.parse(file_fixture('google/events.json').read)
        data = {
          next_sync_token: events_response['nextSyncToken'],
          items: events_response['items']
        }
        stub_fetch_events_request(200, events_response.to_json)
        expect(
          Calendar::Google::FetchEvents.call(@connected_account).result
        ).to eq(data)
      end
    end

     context 'when google returns 401 status code' do
      it 'raise provider unauthorized error exception' do
        stub_fetch_events_request(401, unauthorized_response.to_json)
        expect(Calendar::Google::FetchEvents.call(@connected_account).result).to eq({})
      end
    end
  end
end
