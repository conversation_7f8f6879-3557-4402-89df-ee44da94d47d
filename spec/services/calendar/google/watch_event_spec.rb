# frozen_string_literal: true

require 'rails_helper'
include GoogleRequestHelper

RSpec.describe 'Calendar::Google::WatchEvent' do

  let(:payload) { {id: 1234, type: 'webhook', address: 'http://localhost/v1/meetings/webhooks/google'}.to_json }
  let(:unauthorized_response) do
    {
      "error": {
        "code": 401,
        "message": 'Re<PERSON> had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.',
        "errors": [
          {
            "message": 'Invalid Credentials',
            "domain": 'global',
            "reason": 'authError',
            "location": 'Authorization',
            "locationType": 'header'
          }
        ],
        "status": 'UNAUTHENTICATED'
      }
    }
  end

  before do
    @user = create(:user)
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                provider_name: GOOGLE_PROVIDER, calendar_id: 'primary')
  end

  describe '#call' do
    context 'when google returns 200 status code' do
      it 'does create an online meeting and returns meeting link and meeting id' do
        stub_watch_google_meeting_request(200, payload, { id: 1234 }.to_json)
        expect(
          Calendar::Google::WatchEvent.call(@connected_account, 1234).result
        ).to eq({ resource_id: 1234 })
      end
    end

     context 'when google returns 401 status code' do
      it 'does not create an online meeting and raise provider unauthorized error exception' do
        stub_watch_google_meeting_request(401, payload, unauthorized_response.to_json)
        expect(Calendar::Google::WatchEvent.call(@connected_account, 1234).result).to eq({})
      end
    end
  end
end
