# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Google::CreateEvent' do
  let(:meeting) { build(:meeting_with_associated_entities, all_day: false, participant_user: true, status: SCHEDULED, location: 'Pune') }
  let(:create_google_meeting_payload) do
    {
      kind: 'calendar#event',
      summary: meeting.title,
      location: meeting.location,
      status: meeting.status == CANCELLED ? 'cancelled' : 'confirmed',
      description: meeting.description,
      start: { dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      source: {
        url: APP_KYLAS_HOST,
        title: KYLAS
      },
      attendees: meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end,
      conferenceData: {
        createRequest: {
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          },
          requestId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
          status: {
            statusCode: 'success'
          }
        }
      },
      reminders: {
        useDefault: true
      }
    }
  end

  let(:create_google_meeting_success_response) do
    {
      kind: 'calendar#event',
      etag: '3308646057738000',
      id: 'fej8f0ateki87e6lj7dub7sj8g',
      summary: meeting.title,
      location: meeting.location,
      status: 'confirmed',
      description: meeting.description,
      start: { dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      source: {
        url: APP_KYLAS_HOST,
        title: KYLAS
      },
      attendees: meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end,
      hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
      conferenceData: {
        createRequest: {
          requestId: '1e486426-b5dd-494d-9754-b77cdaa7f3f7',
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          },
          status: {
            statusCode: 'success'
          }
        },
        entryPoints: [
          {
            entryPointType: 'video',
            uri: 'https://meet.google.com/qzz-qpfe-ppc',
            label: 'meet.google.com/qzz-qpfe-ppc'
          },
          {
            regionCode: 'US',
            entryPointType: 'phone',
            uri: 'tel:******-301-8549',
            label: '******-301-8549',
            pin: '247728753'
          }
        ],
        conferenceSolution: {
          key: {
            type: 'hangoutsMeet'
          },
          name: 'Google Meet',
          iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
        },
        conferenceId: 'qzz-qpfe-ppc'
      },
      reminders: {
        useDefault: true
      },
      conferenceSolution: {
        key: {
          type: 'hangoutsMeet'
        },
        name: 'Google Meet',
        iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
      },
      eventType: 'default'
    }
  end

  let(:create_google_meeting_forbidden_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'calendar',
            "reason": 'requiredAccessLevel',
            "message": 'You need to have writer access to this calendar.'
          }
        ],
        "code": 403,
        "message": 'You need to have writer access to this calendar.'
      }
    }
  end

  let(:create_google_meeting_invalid_data_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'global',
            "reason": 'invalid',
            "message": 'Invalid resource id value.'
          }
        ],
        "code": 400,
        "message": 'Invalid resource id value.'
      }
    }
  end

  let(:create_google_meeting_unauthorized_response) do
    {
      "error": {
        "code": 401,
        "message": 'Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.',
        "errors": [
          {
            "message": 'Invalid Credentials',
            "domain": 'global',
            "reason": 'authError',
            "location": 'Authorization',
            "locationType": 'header'
          }
        ],
        "status": 'UNAUTHENTICATED'
      }
    }
  end

  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    allow(SecureRandom).to receive(:uuid).and_return('1e486426-b5dd-494d-9754-b77cdaa7f3f7')
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                    provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
  end

  describe '#call' do
    before do
      @connected_account.update(calendar_id: SecureRandom.uuid)
    end

    def stub_create_google_meeting_request(status, request_body, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:post, "https://www.googleapis.com/calendar/v3/calendars/#{@connected_account.calendar_id}/events?conferenceDataVersion=1&sendUpdates=all")
        .with(body: request_body, headers: headers)
        .to_return(status: status, body: response_body)
    end

    context 'when google returns 200 status code' do
      it 'does create an online meeting and returns meeting link and meeting id' do
        stub_create_google_meeting_request(200, create_google_meeting_payload.to_json, create_google_meeting_success_response.to_json)
        expect(
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants }).result
        ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
      end

      context 'when some participants do not have email' do
        it 'neglects such type of participants' do
          meeting.participants = [
            create(:user_look_up, name: 'Test 1', email: '<EMAIL>'),
            create(:user_look_up, name: 'Test 2', email: nil)
          ]
          participant = meeting.participants.first
          create_google_meeting_payload[:attendees] = [
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          ]
          create_google_meeting_success_response[:attendees] = [
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          ]
          stub_create_google_meeting_request(200, create_google_meeting_payload.to_json, create_google_meeting_success_response.to_json)
          expect(
            Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants }).result
          ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
        end
      end
    end

    context 'when google returns 400 status code' do
      it 'does not create an online meeting and raise invalid data error exception' do
        stub_create_google_meeting_request(400, create_google_meeting_payload.to_json, create_google_meeting_invalid_data_response.to_json)
        expect do
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when google returns 401 status code' do
      it 'does not create an online meeting and raise provider unauthorized error exception' do
        stub_create_google_meeting_request(401, create_google_meeting_payload.to_json, create_google_meeting_unauthorized_response.to_json)
        expect do
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when google returns 403 status code' do
      it 'does not create an online meeting and raise forbidden error exception' do
        stub_create_google_meeting_request(403, create_google_meeting_payload.to_json, create_google_meeting_forbidden_response.to_json)
        expect do
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when google returns 500 status code' do
      it 'does not create an online meeting and raise internal server error exception' do
        stub_create_google_meeting_request(500, create_google_meeting_payload.to_json, {}.to_json)
        expect do
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when cancelled meeting is created' do
      it 'does create an online meeting at google side' do
        meeting.update(status: CANCELLED)
        stub_create_google_meeting_request(200, create_google_meeting_payload.to_json, create_google_meeting_success_response.to_json)
        expect(
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants }).result
        ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Google::CreateEvent.call(@auth_data, meeting, @connected_account, { participants: meeting.participants })
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end
  end
end
