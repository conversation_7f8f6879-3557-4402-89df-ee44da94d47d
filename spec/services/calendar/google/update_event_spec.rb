# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Google::UpdateEvent' do
  let(:meeting) { create(:meeting, all_day: false, participant_user: true, provider_meeting_id: 1, status: SCHEDULED, location: 'Pune') }
  let(:update_google_meeting_payload) do
    {
      summary: meeting.title,
      location: meeting.location,
      status: meeting.status == CANCELLED ? 'cancelled' : 'confirmed',
      description: meeting.description,
      start: { dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      attendees: meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end
    }
  end

  let(:update_google_meeting_success_response) do
    {
      kind: 'calendar#event',
      etag: '3308646057738000',
      id: 'fej8f0ateki87e6lj7dub7sj8g',
      summary: meeting.title,
      location: meeting.location,
      status: meeting.status,
      description: meeting.description,
      start: { dateTime: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      end: { dateTime: meeting.to.in_time_zone(meeting.time_zone.name).strftime('%FT%T%:z') },
      source: {
        url: APP_KYLAS_HOST,
        title: KYLAS
      },
      attendees: meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end,
      hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
      conferenceData: {
        createRequest: {
          requestId: 'abcd',
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          },
          status: {
            statusCode: 'success'
          }
        },
        entryPoints: [
          {
            entryPointType: 'video',
            uri: 'https://meet.google.com/qzz-qpfe-ppc',
            label: 'meet.google.com/qzz-qpfe-ppc'
          },
          {
            regionCode: 'US',
            entryPointType: 'phone',
            uri: 'tel:******-301-8549',
            label: '******-301-8549',
            pin: '247728753'
          }
        ],
        conferenceSolution: {
          key: {
            type: 'hangoutsMeet'
          },
          name: 'Google Meet',
          iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
        },
        conferenceId: 'qzz-qpfe-ppc'
      },
      reminders: {
        useDefault: true
      },
      conferenceSolution: {
        key: {
          type: 'hangoutsMeet'
        },
        name: 'Google Meet',
        iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
      },
      eventType: 'default'
    }
  end

  let(:update_google_meeting_forbidden_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'calendar',
            "reason": 'requiredAccessLevel',
            "message": 'You need to have writer access to this calendar.'
          }
        ],
        "code": 403,
        "message": 'You need to have writer access to this calendar.'
      }
    }
  end

  let(:update_google_meeting_invalid_data_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'global',
            "reason": 'invalid',
            "message": 'Invalid resource id value.'
          }
        ],
        "code": 400,
        "message": 'Invalid resource id value.'
      }
    }
  end

  let(:update_google_meeting_unauthorized_response) do
    {
      "error": {
        "code": 401,
        "message": 'Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.',
        "errors": [
          {
            "message": 'Invalid Credentials',
            "domain": 'global',
            "reason": 'authError',
            "location": 'Authorization',
            "locationType": 'header'
          }
        ],
        "status": 'UNAUTHENTICATED'
      }
    }
  end

  let(:update_google_meeting_not_found_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'global',
            "reason": 'notFound',
            "message": 'Not Found'
          }
        ],
        "code": 404,
        "message": 'Not Found'
      }
    }
  end

  describe '#call' do
    def stub_update_google_meeting_request(status, request_body, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:patch, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(meeting.provider_meeting_id)}?conferenceDataVersion=1&sendUpdates=all")
        .with(body: request_body, headers: headers)
        .to_return(status: status, body: response_body)
    end

    before do
      @user = create(:user)
      @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      meeting.participants = [build(:user_look_up)]
      organizer_look_up = create(
        :user_look_up,
        email: '<EMAIL>',
        tenant_id: @user.tenant_id,
        entity: "user_#{@user.id}"
      )
      meeting.organizer = organizer_look_up
      @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                      provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
    end

    context 'when google returns 200 status code' do
      it 'does update an online meeting and returns meeting link and meeting id' do
        stub_update_google_meeting_request(200, update_google_meeting_payload.to_json, update_google_meeting_success_response.to_json)
        expect(
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account).result
        ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
      end

      context 'when some participants do not have email' do
        it 'neglects such type of participants' do
          meeting.participants = [
            create(:user_look_up, name: 'Test 1', email: '<EMAIL>'),
            create(:user_look_up, name: 'Test 2', email: nil)
          ]
          participant = meeting.participants.first
          update_google_meeting_payload[:attendees] = [
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          ]
          update_google_meeting_success_response[:attendees] = [
            { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
          ]
          stub_update_google_meeting_request(200, update_google_meeting_payload.to_json, update_google_meeting_success_response.to_json)
          expect(
            Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account).result
          ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
        end
      end
    end

    context 'when google returns 400 status code' do
      it 'does not update an online meeting and raise invalid data error exception' do
        stub_update_google_meeting_request(400, update_google_meeting_payload.to_json, update_google_meeting_invalid_data_response.to_json)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when google returns 401 status code' do
      it 'does not update an online meeting and raise provider unauthorized error exception' do
        stub_update_google_meeting_request(401, update_google_meeting_payload.to_json, update_google_meeting_unauthorized_response.to_json)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when google returns 403 status code' do
      it 'does not update an online meeting and raise forbidden error exception' do
        stub_update_google_meeting_request(403, update_google_meeting_payload.to_json, update_google_meeting_forbidden_response.to_json)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when google returns 404 status code' do
      it 'does not update an online meeting and raise not found error exception' do
        stub_update_google_meeting_request(404, update_google_meeting_payload.to_json, update_google_meeting_not_found_response.to_json)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderNotFound, "#{ErrorCode.provider_not_found}||Provider not found.")
      end
    end

    context 'when google returns 500 status code' do
      it 'does not update an online meeting and raise internal server error exception' do
        stub_update_google_meeting_request(500, update_google_meeting_payload.to_json, {}.to_json)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when provider meeting id is not found' do
      it 'does raise invalid data exception' do
        meeting.provider_meeting_id = nil
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid provider meeting id.")
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when meeting status is cancel' do
      it 'does update an online meeting and returns meeting link and meeting id' do
        meeting.status = CANCELLED
        stub_update_google_meeting_request(200, update_google_meeting_payload.to_json, update_google_meeting_success_response.to_json)
        expect(
          Calendar::Google::UpdateEvent.call(@auth_data, meeting, @connected_account).result
        ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
      end
    end
  end
end
