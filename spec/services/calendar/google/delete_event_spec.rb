# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Calendar::Google::DeleteEvent' do
  let(:meeting) { build(:meeting_with_associated_entities, participant_user: true) }

  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    organizer_look_up = create(
      :user_look_up,
      email: '<EMAIL>',
      tenant_id: @user.tenant_id,
      entity: "user_#{@user.id}"
    )
    meeting.organizer = organizer_look_up
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                    provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
  end

  describe '#call' do
    def stub_delete_google_meeting_request(status, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Accept': 'application/json' }
      stub_request(:delete, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(meeting.provider_meeting_id)}?sendUpdates=all")
        .with(headers: headers)
        .to_return(status: status, body: response_body)
    end

    context 'when google returns 204 status code' do
      it 'does destroy an online meeting and returns empty body' do
        meeting.update(provider_meeting_id: 1)
        stub_delete_google_meeting_request(204, {}.to_json)
        expect(
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account).result
        ).to eq(nil)
      end
    end

    context 'when google returns 400 status code' do
      it 'does not destroy an online meeting and returns invalid data exception' do
        meeting.update(provider_meeting_id: 2)
        stub_delete_google_meeting_request(400, {}.to_json)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when google returns 401 status code' do
      it 'does not destroy an online meeting and returns unauthorized exception' do
        meeting.update(provider_meeting_id: 3)
        stub_delete_google_meeting_request(401, {}.to_json)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when google returns 403 status code' do
      it 'does not destroy an online meeting and returns forbidden exception' do
        meeting.update(provider_meeting_id: 4)
        stub_delete_google_meeting_request(403, {}.to_json)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when google returns 404 status code' do
      it 'does not destroy an online meeting and raise the not found error exception' do
        meeting.update(provider_meeting_id: 5)
        stub_delete_google_meeting_request(404, {}.to_json)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderNotFound, "#{ErrorCode.provider_not_found}||Provider not found.")
      end
    end

    context 'when google returns 500 status code' do
      it 'does not destroy an online meeting and raise the internal server exception' do
        meeting.update(provider_meeting_id: 6)
        stub_delete_google_meeting_request(500, {}.to_json)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        meeting.update(provider_meeting_id: 7)
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when provider meeting id is not present' do
      it 'does raise invalid data error exception' do
        meeting.update(provider_meeting_id: nil)
        expect do
          Calendar::Google::DeleteEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid provider meeting id.")
      end
    end
  end
end
