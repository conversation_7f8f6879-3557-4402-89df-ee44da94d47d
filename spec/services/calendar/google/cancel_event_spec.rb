# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Google::Google::CancelEvent' do
  let(:meeting) { build(:meeting_with_associated_entities, all_day: true, participant_user: true, provider_meeting_id: 1) }
  let(:cancel_google_meeting_payload) do
    {
      status: 'cancelled'
    }
  end

  let(:cancel_google_meeting_success_response) do
    {
      kind: 'calendar#event',
      etag: '3308646057738000',
      id: 'fej8f0ateki87e6lj7dub7sj8g',
      summary: meeting.title,
      location: meeting.location,
      status: 'confirmed',
      description: meeting.description,
      start: { date: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%Y-%m-%d') },
      end: { date: meeting.from.in_time_zone(meeting.time_zone.name).strftime('%Y-%m-%d') },
      source: {
        url: APP_KYLAS_HOST,
        title: KYLAS
      },
      attendees: meeting.participants.map do |participant|
        { displayName: participant.name, email: participant.email, responseStatus: 'needsAction' }
      end,
      hangoutLink: 'https://meet.google.com/qzz-qpfe-ppc',
      conferenceData: {
        createRequest: {
          requestId: 'abcd',
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          },
          status: {
            statusCode: 'success'
          }
        },
        entryPoints: [
          {
            entryPointType: 'video',
            uri: 'https://meet.google.com/qzz-qpfe-ppc',
            label: 'meet.google.com/qzz-qpfe-ppc'
          },
          {
            regionCode: 'US',
            entryPointType: 'phone',
            uri: 'tel:******-301-8549',
            label: '******-301-8549',
            pin: '247728753'
          }
        ],
        conferenceSolution: {
          key: {
            type: 'hangoutsMeet'
          },
          name: 'Google Meet',
          iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
        },
        conferenceId: 'qzz-qpfe-ppc'
      },
      reminders: {
        useDefault: true
      },
      conferenceSolution: {
        key: {
          type: 'hangoutsMeet'
        },
        name: 'Google Meet',
        iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
      },
      eventType: 'default'
    }
  end

  let(:cancel_google_meeting_forbidden_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'calendar',
            "reason": 'requiredAccessLevel',
            "message": 'You need to have writer access to this calendar.'
          }
        ],
        "code": 403,
        "message": 'You need to have writer access to this calendar.'
      }
    }
  end

  let(:cancel_google_meeting_invalid_data_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'global',
            "reason": 'invalid',
            "message": 'Invalid resource id value.'
          }
        ],
        "code": 400,
        "message": 'Invalid resource id value.'
      }
    }
  end

  let(:cancel_google_meeting_unauthorized_response) do
    {
      "error": {
        "code": 401,
        "message": 'Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.',
        "errors": [
          {
            "message": 'Invalid Credentials',
            "domain": 'global',
            "reason": 'authError',
            "location": 'Authorization',
            "locationType": 'header'
          }
        ],
        "status": 'UNAUTHENTICATED'
      }
    }
  end

  let(:cancel_google_meeting_not_found_response) do
    {
      "error": {
        "errors": [
          {
            "domain": 'global',
            "reason": 'notFound',
            "message": 'Not Found'
          }
        ],
        "code": 404,
        "message": 'Not Found'
      }
    }
  end

  before do
    @user = create(:user)
    @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
    @auth_data = ParseToken.call(@token).result
    thread = Thread.current
    thread[:auth] = @auth_data
    thread[:token] = @token
    organizer_look_up = create(
      :user_look_up,
      email: '<EMAIL>',
      tenant_id: @user.tenant_id,
      entity: "user_#{@user.id}"
    )
    meeting.organizer = organizer_look_up
    @connected_account = create(:connected_account, user_id: @user.id, tenant_id: @user.tenant_id,
                                                    provider_name: GOOGLE_PROVIDER, calendar_id: Faker::Internet.email)
  end

  describe '#call' do
    def stub_cancel_google_meeting_request(status, request_body, response_body)
      headers = { Authorization: "Bearer #{@connected_account.access_token}", 'Content-Type': 'application/json' }
      stub_request(:patch, "https://www.googleapis.com/calendar/v3/calendars/#{CGI::escape(@connected_account.calendar_id)}/events/#{CGI::escape(meeting.provider_meeting_id)}?conferenceDataVersion=1&sendUpdates=all")
        .with(body: request_body, headers: headers)
        .to_return(status: status, body: response_body)
    end

    context 'when google returns 200 status code' do
      it 'does cancel an online meeting and returns meeting link and meeting id' do
        stub_cancel_google_meeting_request(200, cancel_google_meeting_payload.to_json, cancel_google_meeting_success_response.to_json)
        expect(
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account).result
        ).to eq({ provider_link: 'https://meet.google.com/qzz-qpfe-ppc', provider_meeting_id: 'fej8f0ateki87e6lj7dub7sj8g' })
      end
    end

    context 'when google returns 400 status code' do
      it 'does not cancel an online meeting and raise the invalid data error exception' do
        meeting.update(provider_meeting_id: 2)
        stub_cancel_google_meeting_request(400, cancel_google_meeting_payload.to_json, cancel_google_meeting_invalid_data_response.to_json)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInvalidDataError, "#{ErrorCode.provider_invalid}||Invalid provider.")
      end
    end

    context 'when google returns 401 status code' do
      it 'does not cancel an online meeting and raise the provider_unauthorized error exception' do
        meeting.update(provider_meeting_id: 3)
        stub_cancel_google_meeting_request(401, cancel_google_meeting_payload.to_json, cancel_google_meeting_unauthorized_response.to_json)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderUnauthorized, "#{ErrorCode.provider_unauthorized}||Unauthorized provider.")
      end
    end

    context 'when google returns 403 status code' do
      it 'does not cancel an online meeting and raise the forbidden error exception' do
        meeting.update(provider_meeting_id: 4)
        stub_cancel_google_meeting_request(403, cancel_google_meeting_payload.to_json, cancel_google_meeting_forbidden_response.to_json)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderForbidden, "#{ErrorCode.provider_forbidden}||Invalid provider.")
      end
    end

    context 'when google returns 404 status code' do
      it 'does not cancel an online meeting and raise the not found exception' do
        meeting.update(provider_meeting_id: 5)
        stub_cancel_google_meeting_request(404, cancel_google_meeting_payload.to_json, cancel_google_meeting_not_found_response.to_json)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderNotFound, "#{ErrorCode.provider_not_found}||Provider not found.")
      end
    end

    context 'when google returns 500 status code' do
      it 'does not cancel an online meeting and raise the internal server error exception' do
        meeting.update(provider_meeting_id: 6)
        stub_cancel_google_meeting_request(500, cancel_google_meeting_payload.to_json, {}.to_json)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end

    context 'when provider meeting id is not found' do
      it 'does raise the exception of not found' do
        meeting.update(provider_meeting_id: nil)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid provider meeting id.")
      end
    end

    context 'when read timeout exception is raised by Net::Http' do
      it 'does raise provider internal server error exception' do
        meeting.update(provider_meeting_id: 7)
        allow_any_instance_of(Net::HTTP).to receive(:request).and_raise(Net::ReadTimeout)
        expect do
          Calendar::Google::CancelEvent.call(@auth_data, meeting, @connected_account)
        end.to raise_error(ExceptionHandler::ProviderInternalServerError, "#{ErrorCode.provider_internal_server}||Provider internal server error.")
      end
    end
  end
end
