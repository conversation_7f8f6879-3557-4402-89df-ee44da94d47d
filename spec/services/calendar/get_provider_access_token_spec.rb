# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'GetProviderAccessToken' do
  describe '#call' do
    context 'when provider is blank' do
      it 'does raise invalid data error exception' do
        expect do
          Calendar::GetProviderAccessToken.call('')
        end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid provider name.")
      end
    end

    context 'when provider is present' do
      def stub_fetch_provider_access_token_request(token, status, response_body)
        stub_request(:get, %r{/v1/calendar-oauth/})
          .with(headers: { Authorization: "Bearer #{token}", Accept: 'application/json' })
          .to_return(status: status, body: response_body.to_json)
      end

      before do
        @user = create(:user)
        @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        @auth_data = ParseToken.call(@token).result
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
      end

      context 'when no security context is present' do
        it 'does raise authentication error exception' do
          thread = Thread.current
          thread[:token] = nil
          expect do
            Calendar::GetProviderAccessToken.call(GOOGLE_PROVIDER)
          end.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'when iam service returns status code 200' do
        it 'returns new access token and expires at' do
          response_body = { 'accessToken' => SecureRandom.uuid, 'expiresAt' => '2020-11-10T11:30:10.607Z' }
          stub_fetch_provider_access_token_request(@token, 200, response_body)
          expect(Calendar::GetProviderAccessToken.call(GOOGLE_PROVIDER).result).to eq(response_body)
        end
      end

      context 'when iam service returns status code 400' do
        it 'does raise invalid data error exception' do
          stub_fetch_provider_access_token_request(@token, 400, {})
          expect do
            Calendar::GetProviderAccessToken.call(GOOGLE_PROVIDER)
          end.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||Invalid data for getting provider access token.")
        end
      end

      context 'when iam service returns status code 403' do
        it 'does raise forbidden error exception' do
          stub_fetch_provider_access_token_request(@token, 403, {})
          expect do
            Calendar::GetProviderAccessToken.call(GOOGLE_PROVIDER)
          end.to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Unauthorized access.")
        end
      end

      context 'when iam service returns status code 500' do
        it 'does raise internal server error exception' do
          stub_fetch_provider_access_token_request(@token, 500, {})
          expect do
            Calendar::GetProviderAccessToken.call(GOOGLE_PROVIDER)
          end.to raise_error(ExceptionHandler::InternalServerError, "#{ErrorCode.internal_error}||Something went wrong while validating provider access token.")
        end
      end
    end
  end
end
