require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForUserUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      @lookup = create(:look_up, entity_type: LOOKUP_USER, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do

        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, USER_NAME_UPDATED_EVENT, USER_NAME_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, USER_EMAIL_UPDATED_EVENT, USER_EMAIL_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        #allow(@channel).to receive(:direct).with(MEETING_EXCHANGE).and_return( @queue )
        ListenForUserUpdate.call()
      end

      context 'meeting participant user name updated event' do
        let(:routing_key) { USER_NAME_UPDATED_EVENT }
        let(:payload)     { { "userId" => @user.id, "tenantId" => @user.tenant_id, "firstName" => "John", "lastName" => "Doe" }.to_json }

        it 'updates the User with new name' do
          user = User.find(@user.id)
          expect(user.name).to be == "John Doe"
        end

        it 'updates the user lookups with new name' do
          look_ups = LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_USER + "_" + @lookup.entity_id.to_s)
          expect(look_ups.count).to be == 1

          LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_USER + "_" + @lookup.entity_id.to_s).each do |user|
            expect(user.name).to be == "John Doe"
          end
        end
      end

      context 'meeting participant user email updated event' do
        let(:routing_key) { USER_EMAIL_UPDATED_EVENT }
        let(:payload)     { { "userId" => @user.id, "tenantId" => @user.tenant_id, "email": "<EMAIL>" }.to_json }

        it 'updates the user lookups with new email' do
          look_ups = LookUp.where(tenant_id: @lookup.tenant_id, entity: LOOKUP_USER + "_" + @lookup.entity_id.to_s)

          expect(look_ups.count).to be == 1
          expect(look_ups.first.email).to be == "<EMAIL>"
        end
      end
    end
  end
end
