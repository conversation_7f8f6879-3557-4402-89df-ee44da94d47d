require 'rails_helper'

RSpec.describe ListenForLeadUpdate do
  describe '#call' do
    let(:lookup) { create(:lead_look_up) }

    context 'valid input' do
      context 'for lead updated event' do
        let(:payload) {
          {
            id: lookup.entity_id,
            tenantId: lookup.tenant_id,
            firstName: '<PERSON>',
            lastName: 'Doe',
            ownerId: 101,
            emails: [{ type: 'OFFICE', value: '<EMAIL>', primary: true }, { type: 'PERSONAL', value: '<EMAIL>', primary: false }]
          }.to_json
        }

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(LEAD_EXCHANGE, LEAD_UPDATED_EVENT, LEAD_UPDATED_QUEUE)
            .and_yield(payload.to_s)
          described_class.call
        end

        it 'updates the lead lookup with new name, owner and email' do
          look_ups = LookUp.where(tenant_id: lookup.tenant_id, entity: "lead_#{lookup.entity_id}")
          expect(look_ups.count).to eq(1)
          expect(look_ups.pluck(:name, :email)).to match([['<PERSON>', '<EMAIL>']])
          expect(look_ups.pluck(:owner_id)).to match([101])
        end

        context "when lead email is not available" do
          let(:payload) {
            {
              id: lookup.entity_id,
              tenantId: lookup.tenant_id,
              firstName: 'John',
              lastName: 'Doe',
              ownerId: 101,
              emails: []
            }.to_json
          }

          it 'updates the contact lookup correctly' do
            look_ups = LookUp.where(tenant_id: lookup.tenant_id, entity: "lead_#{lookup.entity_id}")

            expect(look_ups.count).to eq(1)
            expect(look_ups.first.name).to eq('John Doe')
            expect(look_ups.first.email).to be_nil
            expect(look_ups.first.owner_id).to eq(101)
          end
        end
      end
    end
  end
end
