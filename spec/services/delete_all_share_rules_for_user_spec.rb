# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeleteAllShareRulesForUser do
  describe '#call' do
    let(:user) { create(:user) }
    let(:user_one) { create(:user, tenant_id: user.tenant_id) }
    let(:user_two) { create(:user, tenant_id: user.tenant_id) }
    let(:user_three) { create(:user, tenant_id: user.tenant_id) }
    let(:params) do
      data = JSON.parse(file_fixture('listeners/delete-all-meeting-share-rules-payload.json').read)
      data['tenantId'] = user.tenant_id
      data['ownerId'] = user.id
      data
    end

    before do
      create(:share_rule, tenant_id: user.tenant_id, from_id: user.id, to_id: user_one.id, share_all_records: true, system_default: true, name: 'Via_Manager')
      create(:share_rule, tenant_id: user.tenant_id, from_id: user.id, to_id: user_two.id, share_all_records: true, system_default: true, name: '<PERSON>_Manager')
      create(:share_rule, tenant_id: user.tenant_id, from_id: user.id, to_id: user_three.id, share_all_records: true, system_default: true, name: 'Via_Manager')
    end

    context 'when user ids is blank' do
      before { params['userIds'] = [] }

      it 'returns immediately' do
        expect(PublishEvent).not_to receive(:call).with(instance_of(Event::ShareRuleDeletedV2))
        expect { described_class.call(params) }.to change(ShareRule, :count).by(0)
      end
    end

    context 'when one user id is present' do
      before { params['userIds'] = [user_one.id] }

      it 'destroys share rule and publishes event' do
        expect(Rails.logger).to receive(:info).with(/Event::ShareRuleDeleted V2/)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2))
        expect { described_class.call(params) }.to change(ShareRule, :count).by(-1)
      end
    end

    context 'when two user ids are present' do
      before { params['userIds'] = [user_one.id, user_two.id] }

      it 'destroys two share rules and publishes two events' do
        expect(Rails.logger).to receive(:info).with(/Event::ShareRuleDeleted V2/).twice
        expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2)).twice
        expect { described_class.call(params) }.to change(ShareRule, :count).by(-2)
      end
    end
  end
end
