# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CheckoutMeetings do
  describe "#call" do
    context 'when meetings are valid' do
      before do
        @user = create(:user)
        @meeting = create(:meeting, tenant_id: @user.tenant_id, owner: @user, status: CONDUCTED)
        create(:meeting_attendance, meeting_id: @meeting.id, user_id: @user.id)
      end

      it 'marks all meeting as checked out' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        described_class.call({
          meetingIds: [@meeting.id],
          tenantId: @user.tenant_id,
          performedBy: @user.id,
          latitude: 18.8879,
          longitude: 19.983,
          markedAt: '2024-01-25T11:30:10.607Z'
        })

        expect(@meeting.meeting_attendances.find_by(user_id: @user.id).checked_out_latitude).to eq('18.8879')
        expect(@meeting.meeting_attendances.find_by(user_id: @user.id).checked_out_longitude).to eq('19.983')
      end
    end
  end
end
