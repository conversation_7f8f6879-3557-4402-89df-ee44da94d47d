require 'rails_helper'

RSpec.describe TenantUsagePublisher do
  describe '#call' do
    before do
      [1, 10].each do |tenant_id|
        meetings = create_list(:meeting, 5, tenant_id: tenant_id)
        meetings.each do |meeting|
          create_list(:note, 2, meeting_id: meeting.id, tenant_id: tenant_id)
        end
      end
      create_list(:field, 2, tenant_id: 1, is_standard: false, active: true)
      create_list(:field, 3, tenant_id: 10, is_standard: false, active: true)
    end

    it 'should publish event with correct data for single tenant' do
      expect(Rails.logger).to receive(:info).with('Tenant Usage publisher called')
      expect(Rails.logger).to receive(:info).with(/Event::TenantUsagePublisher -> Tenant ID:/)

      expect(Event::TenantUsage).to receive(:new).with([{:tenantId=>1, :count=>5, :usageEntity=>'MEETING'}, {:tenantId=>1, :count=>2, :usageEntity=>'MEETING_CUSTOM_FIELD'}]).and_return(Event::TenantUsage.new({}))
      expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage))

      command = described_class.call(1)
      expect(command.success?).to be(true)
    end

    it 'should publish event with correct data for all tenants' do
      expect(Rails.logger).to receive(:info).with('Tenant Usage publisher called')
      expect(Rails.logger).to receive(:info).with(/Event::TenantUsagePublisher -> Tenant ID:/)

      expect(Event::TenantUsage).to receive(:new).with(match_array([{:tenantId=>10, :count=>5, :usageEntity=>"MEETING"}, {:tenantId=>1, :count=>5, :usageEntity=>"MEETING"}, {:tenantId=>10, :count=>3, :usageEntity=>"MEETING_CUSTOM_FIELD"}, {:tenantId=>1, :count=>2, :usageEntity=>"MEETING_CUSTOM_FIELD"}])).and_return(Event::TenantUsage.new({}))
      expect(PublishEvent).to receive(:call).with(instance_of(Event::TenantUsage))

      command = described_class.call
      expect(command.success?).to be(true)
    end
  end
end
