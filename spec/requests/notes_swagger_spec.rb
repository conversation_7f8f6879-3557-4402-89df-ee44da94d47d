require 'swagger_helper'

RSpec.describe 'Meeting API', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }
  let(:meeting)          { create(:meeting, id: 12, created_by: user, tenant_id: user.tenant_id) }
  let(:note)             { create(:note, created_by_id: user.id, meeting_id: meeting.id, created_at: 1.minutes.ago )}

  path '/v1/meetings/{meeting_id}/notes' do
    get 'Get Notes for Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :meeting_id, in: :path, type: :string
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '200', 'Meeting Notes Searched' do
        before do
          invitee = create(:user, id: 999, tenant_id: user.tenant_id)
          meeting.participants << build(:user_look_up, entity_id: invitee.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          meeting.participants << build(:user_look_up, entity_id: user.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          note
        end
        let(:meeting_id) { meeting.id }
        let(:page) { '1' }
        let(:size) { '2' }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:meeting_id) { '12' }
        let(:page)          { '1' }
        let(:size)          { '2' }
        run_test!
      end
    end
  end

  path '/v1/meetings/{meeting_id}/notes' do
    post 'Create Notes for Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :meeting_id, in: :path, type: :string
      parameter name: :note_params, in: :body, schema: {
          type: :object,
          properties: {
              description: { type: :string }
          },
          required: [ 'description']
      }

      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '200', 'Meeting Notes created' do
        before do
          invitee = create(:user, id: 999, tenant_id: user.tenant_id)
          meeting.participants << build(:user_look_up, entity_id: invitee.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          meeting.participants << build(:user_look_up, entity_id: user.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          note
        end
        let(:meeting_id) { meeting.id }
        let(:note_params) { { description: 'My Note' } }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:meeting_id) { meeting.id }
        let(:note_params) { { description: 'My Note' } }
        run_test!
      end
    end
  end

  path '/v1/meetings/{meeting_id}/notes/{id}' do
    delete 'Delete Notes of Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :meeting_id, in: :path, type: :string
      parameter name: :id, in: :path, type: :string

      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      before do
        @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        @note = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
      end

      response '200', 'Meeting Notes deleted' do
        let(:meeting_id) { @meeting.id }
        let(:id) { @note.id }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:meeting_id) { @meeting.id }
        let(:id) { @note.id }
        run_test!
      end
    end
  end
end
