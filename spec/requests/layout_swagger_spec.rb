require 'swagger_helper'

RSpec.describe 'Layout API', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }

  path '/v1/meetings/layout' do
    get 'Meeting Layout' do
      tags 'Meeting Create and Edit Layout'
      consumes 'application/json'
      parameter name: :'view', in: :query, type: :string, required: true, description: 'create or edit layout'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:'view') { 'create' }
      response '200', 'Create or Edit Layout' do
        before { CreateMeetingFieldsForTenant.call(auth_data.tenant_id, user.id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Not Found' do
        run_test!
      end
    end
  end

  path '/v1/meetings/layout/list' do
    get 'Meeting Fields List' do
      tags 'Meeting Fields Listing API'
      consumes 'application/json'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meeting Fields List' do
        before { CreateMeetingFieldsForTenant.call(auth_data.tenant_id, user.id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Not Found' do
        run_test!
      end
    end
  end
end
