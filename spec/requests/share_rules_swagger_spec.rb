# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Share Rules API', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }

  path '/v1/meetings/share-rules/{id}' do
    get 'Get Share Rule' do
      tags 'Meeting Share Rules'
      consumes 'application/json'

      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:id) do
        @share_rule = create(:share_rule, created_by: user)
        @share_rule.id
      end

      response '200', 'Get Share Rule' do
        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Share rule does not exist' do
        let(:id) { 123 }

        run_test!
      end
    end
  end

  path '/v1/meetings/share-rules/search' do
    post 'Share Rules Search' do
      tags 'Meeting Share Rules'
      consumes 'application/json'

      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :sort, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      response '200', 'Share Rule Search Response' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:sort) { 'updatedAt,desc' }
        let(:jsonRule) { { jsonRule: {} }}

        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:page)          { '1' }
        let(:size)          { '2' }
        let(:sort)          { 'updatedAt,asc' }
        let(:jsonRule) { { jsonRule: {} }}

        run_test!
      end

      response '422', 'Invalid request' do
        let(:page)          { '1' }
        let(:size)          { '2' }
        let(:sort)          { 'createdBy,asc' }
        let(:jsonRule) { { jsonRule: {} }}

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/share' do
    post 'Create share rule for single meeting' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter(
        name: :share_rule,
        in: :body,
        schema: {
        type: :object,
        properties: {
          name: { type: :string },
          description: { type: :string },
          from: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          to: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          actions: { type: :object }
        }
      })

      let(:share_rule) {
        {
          "actions": {
            "read": true,
            "update": false,
            "delete": false,
            "email": false,
            "call": false,
            "note": false,
            "sms": false,
            "task": false
          },
          "name": "sharing meeting",
          "description": "sharing meeting",
          "from": {
            "id": user.id,
            "type": "USER"
          },
          "to": {
            "id": 123,
            "type": "USER"
          }
        }
      }

      let(:id) do
        @meeting = create(:meeting, owner: user, time_zone: nil, tenant_id: user.tenant_id)
        @meeting.id
      end

      parameter({
                  in: :header,
                  type: :string,
                  name: :Authorization,
                  required: true,
                  description: 'Client token'
                })

      response '200', 'share rule created' do
        before do
          create(:user, id: 123, tenant_id: user.tenant_id)

          stub_request(:get, "#{SERVICE_IAM}/v1/users/123").with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(
            status: 200,
            body: file_fixture('user-profile-response.json'),
            headers: {}
          )
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times
        end

        run_test!
      end
    end
  end

  path '/v1/meetings/share' do
    post 'Create share rule for all meetings' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter(
        name: :share_rule,
        in: :body,
        schema: {
        type: :object,
        properties: {
          name: { type: :string },
          description: { type: :string },
          from: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          to: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          actions: { type: :object }
        }
      })

      let(:share_rule) {
        {
          "actions": {
            "read": true,
            "update": false,
            "delete": false,
            "email": false,
            "call": false,
            "note": false,
            "sms": false,
            "task": false
          },
          "name": "sharing meeting",
          "description": "sharing meeting",
          "from": {
            "id": 213,
            "type": "USER"
          },
          "to": {
            "id": 123,
            "type": "USER"
          }
        }
      }

      parameter({
                  in: :header,
                  type: :string,
                  name: :Authorization,
                  required: true,
                  description: 'Client token'
                })

      response '200', 'share rule created' do
        before do
          create(:user, id: 213, tenant_id: user.tenant_id)
          create(:user, id: 123, tenant_id: user.tenant_id)

          stub_request(:get, "#{SERVICE_IAM}/v1/users/123").with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(
            status: 200,
            body: file_fixture('user-profile-response.json'),
            headers: {}
          )

          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times
        end

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/share/{share_rule_id}' do
    put 'Updates share rule for single meeting' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :share_rule_id, in: :path, type: :string
      parameter(
        name: :share_rule,
        in: :body,
        schema: {
        type: :object,
        properties: {
          name: { type: :string },
          description: { type: :string },
          from: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          to: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          actions: { type: :object }
        }
      })

      let(:share_rule) {
        {
          "actions": {
            "read": true,
            "update": false,
            "delete": false,
            "email": false,
            "call": false,
            "note": false,
            "sms": false,
            "task": false
          },
          "name": "sharing meeting",
          "description": "sharing meeting",
          "from": {
            "id": user.id,
            "type": "USER"
          },
          "to": {
            "id": 123,
            "type": "USER"
          }
        }
      }

      let(:id) do
        @meeting = create(:meeting, owner: user, time_zone: nil, tenant_id: user.tenant_id)
        @meeting.id
      end

      let(:share_rule_id) do
        @share_rule = create(:share_rule, created_by: user)
        @share_rule.id
      end

      parameter({
                  in: :header,
                  type: :string,
                  name: :Authorization,
                  required: true,
                  description: 'Client token'
                })

      response '200', 'share rule updated' do
        before do
          create(:user, id: 123, tenant_id: user.tenant_id)

          stub_request(:get, "#{SERVICE_IAM}/v1/users/123").with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(
            status: 200,
            body: file_fixture('user-profile-response.json'),
            headers: {}
          )
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2)).exactly(1).times
        end

        run_test!
      end
    end
  end

  path '/v1/meetings/share/{share_rule_id}' do
    put 'Updates share rule for all meetings' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :share_rule_id, in: :path, type: :string
      parameter(
        name: :share_rule,
        in: :body,
        schema: {
        type: :object,
        properties: {
          name: { type: :string },
          description: { type: :string },
          from: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          to: {
            type: :object,
            properties: {
              id: { type: :integer },
              type: { type: :string }
            }
          },
          actions: { type: :object }
        }
      })

      let(:share_rule) {
        {
          "actions": {
            "read": true,
            "update": false,
            "delete": false,
            "email": false,
            "call": false,
            "note": false,
            "sms": false,
            "task": false
          },
          "name": "sharing meeting",
          "description": "sharing meeting",
          "from": {
            "id": 213,
            "type": "USER"
          },
          "to": {
            "id": 123,
            "type": "USER"
          }
        }
      }

      let(:share_rule_id) do
        @share_rule = create(:share_rule, created_by: user)
        @share_rule.id
      end

      parameter({
                  in: :header,
                  type: :string,
                  name: :Authorization,
                  required: true,
                  description: 'Client token'
                })

      response '200', 'share rule updated' do
        before do
          create(:user, id: 213, tenant_id: user.tenant_id)
          create(:user, id: 123, tenant_id: user.tenant_id)

          stub_request(:get, "#{SERVICE_IAM}/v1/users/123").with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }
          ).to_return(
            status: 200,
            body: file_fixture('user-profile-response.json'),
            headers: {}
          )
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2)).exactly(1).times
        end

        run_test!
      end
    end
  end

  path '/v1/meetings/share-rules/{id}' do
    delete 'Delete Share Rule' do
      tags 'Meeting Share Rules'
      consumes 'application/json'

      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:id) do
        @share_rule = create(:share_rule, created_by: user)
        @share_rule.id
      end

      response '200', 'Share deleted successfully' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2)).exactly(1).times
        end
        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Share rule does not exist' do
        let(:id) { 123 }

        run_test!
      end
    end
  end
end
