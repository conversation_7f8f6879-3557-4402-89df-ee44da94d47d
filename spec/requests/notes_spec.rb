require 'rails_helper'
RSpec.describe "/notes", type: :request do
  let!(:user) { create(:user)}
  let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
  let(:valid_attributes) { { 'description': 'This is test description' } }
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_without_note_create_permission) { build(:auth_token, :without_note_write_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_without_note_read_permission) { build(:auth_token, :without_note_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_without_note_delete_permission) { build(:auth_token, :without_note_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_without_note_delete_all_permission) { build(:auth_token, :without_note_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_2) { build(:auth_token,:without_meeting_read_all_and_update_all_permission, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name ) }
  let(:headers_without_note_create_permission) { valid_headers(valid_auth_token_without_note_create_permission) }
  let(:headers_without_note_read_permission) { valid_headers(valid_auth_token_without_note_read_permission) }
  let(:headers_without_note_delete_permission) { valid_headers(valid_auth_token_without_note_delete_permission) }
  let(:headers_without_note_delete_all_permission) { valid_headers(valid_auth_token_without_note_delete_all_permission) }
  let(:headers) { valid_headers(valid_auth_token) }
  let(:headers_for_another_user) { valid_headers(valid_auth_token_2) }
  let(:header_with_invalid_token) { invalid_headers }

  describe "GET /index" do
    context "with valid input" do
      before do
        @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        @note = create(:note, created_by_id: user.id, meeting_id: @meeting.id, created_at: 1.minutes.ago )
        participant = build(:user_look_up)
        participant.name = user.name
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        @meeting.participants << participant
      end

      context "when the user is participant in meeting" do
        before { get meeting_notes_url(@meeting.id), headers: headers, as: :json }

        it "renders a successful response" do
          expect(response).to be_successful
        end

        it "returns notes associated with meeting" do
          content = json['content']
          expect(content.count).to eq(1)
          expect(content.first['id']).to eq(@note.id)
          expect(content.first['createdBy']['id']).to eq(user.id)
        end

        it "returns correct fields in the response" do
          content = json['content']
          expect(content.first.keys).to eq(['id', 'description', 'createdAt', 'createdBy'])
        end
      end

      context "Default order - latest first for notes" do
        before do
          @meeting.notes = []
          @notes = create_list(:note, 3, created_by_id: user.id, meeting_id: @meeting.id) do |note, i|
            note.created_at = i.minutes.from_now
            note.save!
          end
          get meeting_notes_url(@meeting.id, page: 1, size: 10), headers: headers, as: :json
        end

        it "returns note in the correct sequence" do
          expect(json['content'].first['id']).to eq(@notes.third.id)
          expect(json['content'].second['id']).to eq(@notes.second.id)
          expect(json['content'].third['id']).to eq(@notes.first.id)
        end
      end

      context "when pagination is applied" do
        before do
          @meeting.notes = []
          @notes = create_list(:note, 3, created_by_id: user.id, meeting_id: @meeting.id) do |note, i|
            note.created_at = i.minutes.from_now
            note.save!
          end
        end

        context "and page number is 1" do
          before do
            get meeting_notes_url(@meeting.id, page: 1, size: 2), headers: headers, as: :json
          end

          it "returns correct notes in the response" do
            expect(json['content'].count).to eq(2)
            expect(json['content'].map{ |el| el['id']}).to match_array([@notes.second.id, @notes.third.id])
          end

          it "returns correct pagination parameters" do
            expect(json['page']['no']).to eq(1)
            expect(json['page']['size']).to eq(2)

            expect(json['totalElements']).to eq(3)
            expect(json['totalPages']).to eq(2)
            expect(json['first']).to eq(true)
            expect(json['last']).to eq(false)
          end
        end

        context "and page number is other than 1" do
          before do
            get meeting_notes_url(@meeting.id, page: 2, size: 2), headers: headers, as: :json
          end

          it "returns correct notes in the response" do
            expect(json['content'].count).to eq(1)
            expect(json['content'].map{ |el| el['id']}).to match_array([@notes.first.id])
          end

          it "returns correct pagination parameters" do
            expect(json['page']['no']).to eq(2)
            expect(json['page']['size']).to eq(2)

            expect(json['totalElements']).to eq(3)
            expect(json['totalPages']).to eq(2)
            expect(json['first']).to eq(false)
            expect(json['last']).to eq(true)
          end
        end
      end
    end

    context "with invalid input" do
      before do
        @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        @note = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
        participant = build(:user_look_up)
        participant.name = user.name
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        @meeting.participants << participant
      end

      context "when the user is not the participant or owner for the meeting" do
        before do
          @meeting.participants = []
          get meeting_notes_url(@meeting.id), headers: headers, as: :json
        end

        context 'but the user has read all permission' do
          it "returns the notes" do
            expect(json['totalElements']).to eq(1)
            expect(json['first']).to be(true)
            expect(json['last']).to be(true)
          end
        end
      end

      context "when the user does not have Read/ReadAll permission for the notes" do
        context "and user is not owner of meeting" do
          before do
            @meeting.update(owner: another_user)
            get meeting_notes_url(@meeting.id), headers: headers_without_note_read_permission, as: :json
          end

          it "returns the correct error code" do
            expect(json['errorCode']).to match(ErrorCode.note_access_not_allowed)
          end
        end

        context "and user is owner of meeting" do
          before do
            @meeting.update(owner: @user)
            get meeting_notes_url(@meeting.reload.id), headers: headers, as: :json
          end

          it "returns the notes" do
            expect(json['content'].count).to eq(1)
          end
        end
      end

      context "when the request is invalid" do
        before { get meeting_notes_url(@meeting.id), headers: header_with_invalid_token, as: :json }

        it 'returns correct error code' do
          expect(json['errorCode']).to match(ErrorCode.unauthorized)
        end
      end
    end
  end

  describe "POST /create" do
    before do
      @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
      participant = build(:user_look_up)
      participant.name = user.name
      participant.tenant_id = user.tenant_id
      participant.entity = "user_#{user.id}"
      @meeting.participants << participant
    end

    context "with valid parameters" do
      context "when the user has permission to create note" do
        context "and the user is owner for the meeting" do
          before { post meeting_notes_url(@meeting.id), params: valid_attributes , headers: headers, as: :json }

          it "renders a JSON response with the new note" do
            expect(json['description']).to eq('This is test description')
            expect(json['createdBy']['id']).to eq(user.id)
          end
        end

        context "and the user is participant for the meeting" do
          before do
            participant = build(:user_look_up)
            participant.name = another_user.name
            participant.tenant_id = another_user.tenant_id
            participant.entity = "user_#{another_user.id}"
            @meeting.participants << participant

            post meeting_notes_url(@meeting.id), params: valid_attributes , headers: headers_for_another_user, as: :json
          end

          it "renders a JSON response with the new note" do
            expect(json['description']).to eq('This is test description')
            expect(json['createdBy']['id']).to eq(another_user.id)
          end
        end

        context "and the user is neither participant nor owner for the meeting" do
          before { post meeting_notes_url(@meeting.id), params: valid_attributes , headers: headers_for_another_user , as: :json }

          it "returns error code" do
            expect(json['errorCode']).to match('01503004')
          end
        end
      end

      context "when the user doesn't have permission to create note on the meeting" do
        before { post meeting_notes_url(@meeting.id), params: valid_attributes , headers: headers_without_note_create_permission, as: :json }

        it "returns correct error code" do
          expect(json['errorCode']).to match(ErrorCode.note_create_not_allowed)
        end
      end
    end

    context "with invalid parameters" do
      context "when description is not provided" do
        before { post meeting_notes_url(@meeting.id), params: {} , headers: headers, as: :json }

        it "returns correct error code" do
          expect(json['errorCode']).to match(ErrorCode.invalid)
        end
      end

      context "when meeting id is invalid" do
        before { post meeting_notes_url(Meeting.last.id + 1), params: valid_attributes , headers: headers, as: :json }

        it "returns correct error code" do
          expect(json['errorCode']).to match('01502001')
        end
      end
    end

    context 'with invalid request' do
      before { post meeting_notes_url(@meeting.id), params: {} , headers: header_with_invalid_token, as: :json }

      it 'returns correct error code' do
        expect(json['errorCode']).to match(ErrorCode.unauthorized)
      end
    end
  end

  context "#destroy" do
    before do
      @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
      @note = create(:note, created_by_id: user.id, meeting_id: @meeting.id, created_at: 1.minutes.ago )
      participant = build(:user_look_up)
      participant.name = user.name
      participant.tenant_id = user.tenant_id
      participant.entity = "user_#{user.id}"
      @meeting.participants << participant
    end

    context "With Valid Permission" do
      context "When user have delete all permission on note" do
        it 'should delete the note which he created' do
          delete "/v1/meetings/#{@meeting.id}/notes/#{@note.id}", headers: headers

          expect(Note.find_by(id: @note.id)).to eq(nil)
        end

        it 'should delete the note created by another user' do
          @note1 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

          delete "/v1/meetings/#{@meeting.id}/notes/#{@note1.id}", headers: headers

          expect(Note.find_by(id: @note1.id)).to eq(nil)
        end
      end

      context "When user have delete permission on note" do
        it 'should delete the note created by him' do
          delete "/v1/meetings/#{@meeting.id}/notes/#{@note.id}", headers: headers

          expect(Note.find_by(id: @note.id)).to eq(nil)
        end

        it 'should delete the note which is created by another user and current user is organizer of meeting' do
          @note1 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

          delete "/v1/meetings/#{@meeting.id}/notes/#{@note1.id}", headers: headers

          expect(Note.find_by(id: @note1.id)).to eq(nil)
        end

        it "shouldn't delete the meeting which are not created by him and he is not organizer" do
          @meeting = create(:meeting, tenant_id: user.tenant_id, owner: another_user)
          @note = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)
          participant = build(:user_look_up)
          participant.name = user.name
          participant.tenant_id = user.tenant_id
          participant.entity = "user_#{user.id}"
          @meeting.participants << participant

          delete "/v1/meetings/#{@meeting.id}/notes/#{@note.id}", headers: headers_without_note_delete_all_permission

          expect(Note.find_by(id: @note.id)).to eq(@note)
        end
      end
    end

    context "With Invalid Permission" do
      context "When user dont have delete permission" do
        it "shouldn't delete the meeting" do
          delete "/v1/meetings/#{@meeting.id}/notes/#{@note.id}", headers: headers_without_note_delete_permission

          expect(Note.find_by(id: @note.id)).to eq(@note)
        end
      end

      context "When user dont have delete all permission" do
        it "shouldn't delete the meeting" do
          @note1 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

          delete "/v1/meetings/#{@meeting.id}/notes/#{@note1.id}", headers: headers_without_note_delete_permission

          expect(Note.find_by(id: @note1.id)).to eq(@note1)
        end
      end
    end
  end

  describe '#search' do
    context 'when valid json rule is passed' do
      let(:valid_rule)     { { 'type': 'string', 'field': 'entityId', 'id': 'id', 'operator': 'in', 'value': @meetings.map(&:id).first(2).join(',')}}
      let(:request_params) { { "jsonRule": { "condition": 'AND', "rules": [valid_rule] }} }

      before do
        allow(PublishEvent).to receive(:call)
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        @meetings = create_list(:meeting, 3, tenant_id: user.tenant_id, owner: user)
        @meetings.each do |m|
          m.related_to = [@look_up]
          m.save!
          create(:note, description: "Note on meeting: #{m.id}", meeting: m, created_by: user)
        end
        create(:note, description: "Another note on meeting: #{@meetings.first.id}", meeting: @meetings.first, created_by: user)
      end

      context 'when pagination is not applied' do
        before { post '/v1/meetings/notes/search', params: request_params.to_json, headers: headers }

        it 'returns the correct notes response' do
          json = response.parsed_body
          note_ids = json['content'].map { |n| n['id'] }

          expected_ids = @meetings[0].notes.map(&:id)
          expected_ids.push(@meetings[1].notes.map(&:id)).flatten!
          expect(note_ids).to match_array(expected_ids)
        end

        it 'returns correct response for default pagination' do
          json = response.parsed_body
          expect(json['page']['size']).to eq(1000)
        end
      end

      context 'when pagination is applied' do
        before { post "/v1/meetings/notes/search?page=#{page}&size=#{size}", params: request_params.to_json, headers: headers }

        context 'when the page number is 1' do
          let(:page) { 1 }

          context "and the number of elements doesn't exceed the page size" do
            let(:size) { 4 }

            it 'returns correct number of notes in the response' do
              expect(response.parsed_body['content'].count).to eq(3)
            end

            it 'returns correct notes details in the response' do
              json = response.parsed_body['content']
              note_ids = json.map { |m| m['id'] }

              expected_ids = @meetings[0].notes.map(&:id)
              expected_ids.push(@meetings[1].notes.map(&:id)).flatten!
              expect(note_ids).to match_array(expected_ids)
            end

            it 'returns correct pagination details in the response' do
              json = response.parsed_body
              expect(json['page']['no']).to eq(page)
              expect(json['page']['size']).to eq(size)

              expect(json['totalElements']).to eq(3)
              expect(json['totalPages']).to eq(1)
              expect(json['first']).to eq(true)
              expect(json['last']).to eq(true)
            end
          end

          context 'and the number of elements exceeds the page size' do
            let(:size) { 2 }

            it 'returns correct number of notes in the response' do
              expect(response.parsed_body['content'].count).to eq(2)
            end

            it 'returns correct notes details in the response' do
              json = response.parsed_body['content']
              note_ids = json.map { |cl| cl['id'] }

              expected_ids = [@meetings[0].notes.first.id]
              expected_ids.push(@meetings[1].notes.first.id)
              expect(note_ids).to match_array(expected_ids)
            end

            it 'returns correct pagination details in the response' do
              json = response.parsed_body
              expect(json['page']['no']).to eq(page)
              expect(json['page']['size']).to eq(size)

              expect(json['totalElements']).to eq(3)
              expect(json['totalPages']).to eq(2)
              expect(json['first']).to eq(true)
              expect(json['last']).to eq(false)
            end
          end
        end

        context 'and the page number is other than 1' do
          let(:page) { 2 }

          context "and the number of elements doesn't exceed the page size" do
            let(:size) { 2 }

            it 'returns correct number of notes in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
            end

            it 'returns correct notes details in the response' do
              json = response.parsed_body['content']
              note_ids = json.map { |cl| cl['id'] }

              expected_ids = [@meetings[0].notes.last.id]
              expect(note_ids).to match_array(expected_ids)
            end

            it 'returns correct pagination details in the response' do
              json = response.parsed_body
              expect(json['page']['no']).to eq(page)
              expect(json['page']['size']).to eq(size)

              expect(json['totalElements']).to eq(3)
              expect(json['totalPages']).to eq(2)
              expect(json['first']).to eq(false)
              expect(json['last']).to eq(true)
            end
          end

          context 'and the number of elements exceeds the page size' do
            let(:size) { 1 }

            it 'returns correct number of notes in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
            end

            it 'returns correct notes details in the response' do
              json = response.parsed_body['content']
              note_ids = json.map { |cl| cl['id'] }

              expected_ids = [@meetings[1].notes.first.id]
              expect(note_ids).to match_array(expected_ids)
            end

            it 'returns correct pagination details in the response' do
              json = response.parsed_body
              expect(json['page']['no']).to eq(page)
              expect(json['page']['size']).to eq(size)

              expect(json['totalElements']).to eq(3)
              expect(json['totalPages']).to eq(3)
              expect(json['first']).to eq(false)
              expect(json['last']).to eq(false)
            end
          end
        end
      end

      context 'when user is not authorised' do
        before { post "/v1/meetings/notes/search?page=1&size=10", params: request_params.to_json, headers: header_with_invalid_token }

        it 'returns correct error code' do
          expect(json['errorCode']).to match(ErrorCode.unauthorized)
        end
      end
    end
  end
end
