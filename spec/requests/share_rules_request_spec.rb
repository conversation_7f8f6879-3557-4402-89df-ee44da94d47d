# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRulesController, type: :request do
  let(:user) { create(:user) }
  let(:another_user) { create(:user, tenant_id: user.tenant_id) }
  let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id) }
  let(:auth_data) { ParseToken.call(token.token).result }
  let(:headers) { valid_headers(token) }
  let(:header_with_invalid_token) { invalid_headers }
  let(:token_without_meeting_update_all) { build(:auth_token, :without_meeting_read_all_and_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

  describe '#show' do
    let(:share_rule) { create(:share_rule, created_by: user, tenant_id: user.tenant_id) }

    context 'when valid request' do
      it 'returns serialized share rule' do
        get "/v1/meetings/share-rules/#{share_rule.id}", headers: headers

        parsed_body = response.parsed_body
        expect(response.status).to eq(200)
        expect(parsed_body['id']).to eq(share_rule.id)
        expect(parsed_body.keys).to match_array(%w[createdAt updatedAt createdBy updatedBy owner recordActions id name description from to shareAllRecords entity childEntities entityShareRuleId systemDefault actions])
      end
    end

    context 'when invalid request' do
      context 'when missing user context' do
        it 'returns unauthorized access' do
          get "/v1/meetings/share-rules/#{share_rule.id}"

          parsed_body = response.parsed_body
          expect(response.status).to eq(401)
          expect(parsed_body.keys).to match_array(%w[errorCode message timestamp])
          expect(parsed_body['errorCode']).to eq('01501005')
          expect(parsed_body['message']).to eq('Unauthorized access.')
        end
      end

      context 'when share rule is not found' do
        it 'returns not found' do
          get "/v1/meetings/share-rules/-1", headers: headers

          parsed_body = response.parsed_body
          expect(response.status).to eq(404)
          expect(parsed_body.keys).to match_array(%w[errorCode message timestamp])
          expect(parsed_body['errorCode']).to eq('01503019')
          expect(parsed_body['message']).to eq('Share rule not found.')
        end
      end

      context 'when user cannot read share rule' do
        before do
          allow_any_instance_of(Auth::Data).to receive(:can_access?).with('shareRule', 'read_all').and_return(false)
          allow_any_instance_of(Auth::Data).to receive(:can_access?).with('shareRule', 'read').and_return(true)
          share_rule.update(created_by: create(:user, tenant_id: user.tenant_id))
        end

        it 'returns unauthorized access' do
          get "/v1/meetings/share-rules/#{share_rule.id}", headers: headers

          parsed_body = response.parsed_body
          expect(response.status).to eq(401)
          expect(parsed_body.keys).to match_array(%w[errorCode message timestamp])
          expect(parsed_body['errorCode']).to eq('01501005')
          expect(parsed_body['message']).to eq('Unauthorized access.')
        end
      end
    end
  end

  describe '#share' do
    context 'when user context is not present' do
      it 'returns unauthorized error' do
        post '/v1/meetings/123/share', params: share_meeting_params(213, 123)

        expect(response.code).to eq('401')
        expect(response.parsed_body['errorCode']).to eq('01501005')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end

    context 'when user is context is present' do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
          headers: {
          'Authorization'=>'Bearer '+ token.token
           }
        ).to_return(
          status: 200,
          body: [
            {
              "id": 213,
              "name": "Jane Doe",
              "email": {"primary": true,"value": "<EMAIL>"}
            }
          ].to_json, headers: {}
        )

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
          headers: {
          'Authorization'=>'Bearer '+ token.token
           }
        ).to_return(
          status: 200,
          body: [
            {
              "id": 123,
              "name": "Jane Doe",
              "email": {"primary": true,"value": "<EMAIL>"}
            }
          ].to_json, headers: {}
        )
      end

      context 'and actions other than allowed are passed' do
        before do
          @meeting = create(:meeting, owner: user, created_by: create(:user, tenant_id: user.tenant_id), time_zone: nil, tenant_id: user.tenant_id)
        end

        it 'raises invalid actions error' do
          post "/v1/meetings/#{@meeting.id}/share", headers: headers, params: share_meeting_params(213, 123, {actions: { email: true } })

          expect(response.code).to eq('422')
          expect(response.parsed_body['errorCode']).to eq('01503014')
          expect(response.parsed_body['message']).to eq('Actions are invalid')
        end
      end

      context 'From and To user are same' do
        before do
          @meeting = create(:meeting, owner: user, created_by: create(:user, tenant_id: user.tenant_id), time_zone: nil, tenant_id: user.tenant_id)
        end

        it 'raises error' do
          post "/v1/meetings/#{@meeting.id}/share", headers: headers, params: share_meeting_params(123, 123)

          expect(response.code).to eq('422')
          expect(response.parsed_body['errorCode']).to eq('01503018')
          expect(response.parsed_body['message']).to eq('From and To users cannot be same.')
        end
      end

      context 'and meeting is not found' do
        it 'raises not found error' do
          post '/v1/meetings/123/share', headers: headers, params: share_meeting_params(213, 123)

          expect(response.code).to eq('404')
          expect(response.parsed_body['errorCode']).to eq('01502001')
          expect(response.parsed_body['message']).to eq('Meeting not found.')
        end
      end

      context 'and meeting is found' do
        context 'and target user already owns meeting' do
          before do
            @meeting = create(:meeting, owner: user, created_by: create(:user, tenant_id: user.tenant_id), time_zone: nil, tenant_id: user.tenant_id)
            post "/v1/meetings/#{@meeting.id}/share", headers: headers, params: share_meeting_params(@meeting.owner_id, @meeting.created_by_id)
          end

          it 'raises error' do
            expect(response.code).to eq('422')
            expect(response.parsed_body['errorCode']).to eq('01503015')
            expect(response.parsed_body['message']).to eq('You cannot share Entity to Owner of that Entity.')
          end
        end

        context 'but user does not have permission on meeting' do
          before do
            @meeting = create(:meeting, owner: another_user, time_zone: nil, tenant_id: user.tenant_id)

            stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
              headers: {
              'Authorization'=>'Bearer '+ token_without_meeting_update_all.token
               }
            ).to_return(
              status: 200,
              body: [
                {
                  "id": 213,
                  "name": "Jane Doe",
                  "email": {"primary": true,"value": "<EMAIL>"}
                }
              ].to_json, headers: {}
            )

            stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
              headers: {
              'Authorization'=>'Bearer '+ token_without_meeting_update_all.token
               }
            ).to_return(
              status: 200,
              body: [
                {
                  "id": 123,
                  "name": "Jane Doe",
                  "email": {"primary": true,"value": "<EMAIL>"}
                }
              ].to_json, headers: {}
            )
          end

          it 'raises forbidden error' do
            post(
              "/v1/meetings/#{@meeting.id}/share",
              headers: valid_headers(token_without_meeting_update_all),
              params: share_meeting_params(@meeting.owner_id, 123),
            )

            expect(response.code).to eq('401')
            expect(response.parsed_body['errorCode']).to eq('01501005')
            expect(response.parsed_body['message']).to eq('You do not have access to this meeting.')
          end
        end

        context 'and user have permission on meeting' do
          before do
            @meeting = create(:meeting, owner: user, time_zone: nil, tenant_id: user.tenant_id)
            create(:user, id: 213, tenant_id: user.tenant_id)
            create(:user, id: 123, tenant_id: user.tenant_id)
          end

          context 'but share rule for same meeting and users exists' do
            before do
              @existing_share_rule = create(:share_rule, from_id: @meeting.owner_id, to_id: 123, tenant_id: user.tenant_id, meeting_id: @meeting.id)
            end

            it 'returns error' do
              post "/v1/meetings/#{@meeting.id}/share", headers: headers, params: share_meeting_params(@meeting.owner_id, 123)
              expect(response.code).to eq('422')
              expect(response.parsed_body['errorCode']).to eq('01503016')
              expect(response.parsed_body['message']).to eq('This meeting is already shared with users/team with selected permissions')
              expect(response.parsed_body['id']).to eq(@existing_share_rule.id.to_s)
            end
          end

          it 'creates share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times
            post "/v1/meetings/#{@meeting.id}/share", headers: headers, params: share_meeting_params(nil, 123)

            expect(response).to be_ok
            expect(response.parsed_body['actions']).to eq({
              "read" => true,
              "update" => false,
              "delete" => false,
              "email" => false,
              "sms" => false,
              "task" => false,
              "note" => false,
              "meeting" => false,
              "document" => false,
              "deleteAll" => false,
              "quotation" => false,
              "reshare" => false,
              "reassign" => false
            })
            expect(response.parsed_body['entity']['id']).to eq(@meeting.id)
            expect(response.parsed_body['from']['id']).to eq(user.id)
            expect(response.parsed_body['from']['type']).to eq('USER')
            expect(response.parsed_body['to']['id']).to eq(123)
            expect(response.parsed_body['to']['type']).to eq('USER')
          end
        end
      end
    end
  end

  describe '#share_all' do
    context 'when user context is not present' do
      it 'returns unauthorized error' do
        post '/v1/meetings/share', params: share_meeting_params(213, 123)

        expect(response.code).to eq('401')
        expect(response.parsed_body['errorCode']).to eq('01501005')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end

    context 'when user is context is present' do
      context 'and actions other than allowed are passed' do
        it 'raises invalid actions error' do
          post "/v1/meetings/share", headers: headers, params: share_meeting_params(213, 123, {actions: { email: true } })

          expect(response.code).to eq('422')
          expect(response.parsed_body['errorCode']).to eq('01503014')
          expect(response.parsed_body['message']).to eq('Actions are invalid')
        end
      end

      context 'but user does not have update_all permission on meeting' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=213").with(
            headers: {
            'Authorization'=>'Bearer '+ token_without_meeting_update_all.token
             }
          ).to_return(
            status: 200,
            body: [
              {
                "id": 213,
                "name": "Jane Doe",
                "email": {"primary": true,"value": "<EMAIL>"}
              }
            ].to_json, headers: {}
          )

          stub_request(:get, "http://localhost:8081/v1/users/summary?id=123").with(
            headers: {
            'Authorization'=>'Bearer '+ token_without_meeting_update_all.token
             }
          ).to_return(
            status: 200,
            body: [
              {
                "id": 123,
                "name": "Jane Doe",
                "email": {"primary": true,"value": "<EMAIL>"}
              }
            ].to_json, headers: {}
          )
        end
        it 'raises forbidden error' do
          post(
            "/v1/meetings/share",
            headers: valid_headers(token_without_meeting_update_all),
            params: share_meeting_params(213, 123),
          )

          expect(response.code).to eq('401')
          expect(response.parsed_body['errorCode']).to eq('01501005')
          expect(response.parsed_body['message']).to eq('You do not have access to this meeting.')
        end
      end

      context 'and user have permission' do
        before do
          create(:user, id: 213, tenant_id: user.tenant_id)
          create(:user, id: 123, tenant_id: user.tenant_id)
        end

        context 'but share rule for same meeting and users exists' do
          before do
            @existing_share_rule = create(:share_rule, from_id: 213, to_id: 123, tenant_id: user.tenant_id, share_all_records: true)
          end

          it 'returns error' do
            post "/v1/meetings/share", headers: headers, params: share_meeting_params(213, 123)
            expect(response.code).to eq('422')
            expect(response.parsed_body['errorCode']).to eq('01503016')
            expect(response.parsed_body['message']).to eq('This meeting is already shared with users/team with selected permissions')
            expect(response.parsed_body['id']).to eq(@existing_share_rule.id.to_s)
          end
        end

        it 'creates share rule' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleCreatedV2)).exactly(1).times
          post "/v1/meetings/share", headers: headers, params: share_meeting_params(213, 123)

          expect(response).to be_ok
          expect(response.parsed_body['actions']).to eq({
            "read" => true,
            "update" => false,
            "delete" => false,
            "email" => false,
            "sms" => false,
            "task" => false,
            "note" => false,
            "meeting" => false,
            "document" => false,
            "deleteAll" => false,
            "quotation" => false,
            "reshare" => false,
            "reassign" => false
          })
          expect(response.parsed_body['entity']).to eq(nil)
          expect(response.parsed_body['shareAllRecords']).to eq(true)
          expect(response.parsed_body['from']['id']).to eq(213)
          expect(response.parsed_body['from']['type']).to eq('USER')
          expect(response.parsed_body['to']['id']).to eq(123)
          expect(response.parsed_body['to']['type']).to eq('USER')
        end
      end
    end
  end

  describe '#search' do
    context 'when valid request' do
      before do
        @share_rule = create(:share_rule, tenant_id: user.tenant_id, created_by: user, updated_by: user)
        @another_share_rule = create(:share_rule, tenant_id: another_user.tenant_id, created_by: another_user, updated_by: another_user)
      end

      it 'returns serialized response' do
        post '/v1/meetings/share-rules/search', headers: headers, params: {}

        parsed_body = response.parsed_body
        expect(parsed_body['content'].count).to eq(2)
        expect(parsed_body['number']).to eq(1)
        expect(parsed_body['size']).to eq(10)
        expect(parsed_body['totalElements']).to eq(2)
        expect(parsed_body['totalPages']).to eq(1)
      end
    end

    context 'when invalid request' do
      context 'when unauthorized user' do
        it 'returns error code' do
          post '/v1/meetings/share-rules/search', headers: header_with_invalid_token, params: {}

          parsed_body = response.parsed_body
          expect(parsed_body['errorCode']).to eq('01501005')
          expect(parsed_body['message']).to eq('Unauthorized access.')
        end
      end

      context 'when invalid filter applied' do
        let(:filter_params) { { jsonRule: { rules: [{ field: 'createdAt', type: 'date', operator: 'not_equal', value: '2023-12-22T18:30:00.000Z' }], condition: 'AND', valid: true } }.to_json }

        it 'returns error code with message' do
          post '/v1/meetings/share-rules/search', headers: headers, params: filter_params

          parsed_body = response.parsed_body
          expect(parsed_body['errorCode']).to eq('01503001')
          expect(parsed_body['message']).to eq('Invalid rule.')
        end
      end

      context 'when invalid sort params' do
        let(:filter_params) { { sort: 'createdBy,asc' }.to_json }

        it 'returns error code with message' do
          post '/v1/meetings/share-rules/search', headers: headers, params: filter_params

          parsed_body = response.parsed_body
          expect(parsed_body['errorCode']).to eq('01503001')
          expect(parsed_body['message']).to eq('Invalid sortable field or order.')
        end
      end
    end
  end

  describe '#update' do
    context 'when requested for particular meeting' do
      context 'when user context is not present' do
        it 'returns unauthorized error' do
          put '/v1/meetings/123/share/123', params: update_share_meeting_params(123)

          expect(response.code).to eq('401')
          expect(response.parsed_body['errorCode']).to eq('01501005')
          expect(response.parsed_body['message']).to eq('Unauthorized access.')
        end
      end

      context 'when user is context is present' do
        context 'and share rule is not present' do
          it 'returns error' do
            put '/v1/meetings/123/share/123', headers: headers, params: update_share_meeting_params(123)

            expect(response.code).to eq('404')
            expect(response.parsed_body['errorCode']).to eq('01503019')
            expect(response.parsed_body['message']).to eq('Share rule not found.')
          end
        end

        context 'when share rule is present' do
          context 'and current user is not creator of that share rule' do
            before do
              @share_rule = create(:share_rule, tenant_id: user.tenant_id)
            end

            it 'returns error' do
              put(
                "/v1/meetings/123/share/#{@share_rule.id}",
                headers: valid_headers(build(:auth_token, :without_share_rule_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)),
                params: update_share_meeting_params(@share_rule.id)
              )

              expect(response.code).to eq('401')
              expect(response.parsed_body['errorCode']).to eq('01501005')
              expect(response.parsed_body['message']).to eq('Unauthorized access.')
            end
          end

          context 'when user can update share rule' do
            before do
              @share_rule = create(:share_rule, created_by: user)
              @meeting = create(:meeting, owner: user, time_zone: nil, tenant_id: user.tenant_id)
            end

            context 'and actions other than allowed are passed' do
              it 'returns error' do
                put "/v1/meetings/#{@meeting.id}/share/#{@share_rule.id}", headers: headers, params: update_share_meeting_params(@share_rule.id, { actions: { email: true } })

                expect(response.code).to eq('422')
                expect(response.parsed_body['errorCode']).to eq('01503014')
                expect(response.parsed_body['message']).to eq('Actions are invalid')
              end
            end

            context 'and share rule with same config is not present' do
              it 'creates share rule' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2)).exactly(1).times
                put "/v1/meetings/#{@meeting.id}/share/#{@share_rule.id}", headers: headers, params: update_share_meeting_params(@share_rule.id, { id: @meeting.id })

                expect(response).to be_ok
                expect(response.parsed_body['actions']).to eq({
                  "read" => true,
                  "update" => false,
                  "delete" => false,
                  "email" => false,
                  "sms" => false,
                  "task" => false,
                  "note" => false,
                  "meeting" => false,
                  "document" => false,
                  "deleteAll" => false,
                  "quotation" => false,
                  "reshare" => false,
                  "reassign" => false
                })
                expect(response.parsed_body['name']).to eq('sharing meeting updated')
                expect(response.parsed_body['entity']['id']).to eq(@meeting.id)
                expect(response.parsed_body['from']['id']).to eq(user.id)
                expect(response.parsed_body['from']['type']).to eq('USER')
                expect(response.parsed_body['to']['id']).to eq(another_user.id)
                expect(response.parsed_body['to']['type']).to eq('USER')
              end
            end
          end
        end
      end
    end

    context 'when requested for all meetings' do
      context 'when user context is not present' do
        it 'returns unauthorized error' do
          put '/v1/meetings/share/123', params: update_share_meeting_params(123)

          expect(response.code).to eq('401')
          expect(response.parsed_body['errorCode']).to eq('01501005')
          expect(response.parsed_body['message']).to eq('Unauthorized access.')
        end
      end

      context 'when user is context is present' do
        context 'and share rule is not present' do
          it 'returns error' do
            put '/v1/meetings/share/123', headers: headers, params: update_share_meeting_params(123)

            expect(response.code).to eq('404')
            expect(response.parsed_body['errorCode']).to eq('01503019')
            expect(response.parsed_body['message']).to eq('Share rule not found.')
          end
        end

        context 'when share rule is present' do
          context 'and current user is not creator of that share rule' do
            before do
              @share_rule = create(:share_rule, tenant_id: user.tenant_id)
            end

            it 'returns error' do
              put(
                "/v1/meetings/share/#{@share_rule.id}",
                headers: valid_headers(build(:auth_token, :without_share_rule_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)),
                params: update_share_meeting_params(@share_rule.id)
              )

              expect(response.code).to eq('401')
              expect(response.parsed_body['errorCode']).to eq('01501005')
              expect(response.parsed_body['message']).to eq('Unauthorized access.')
            end
          end

          context 'when user can update share rule' do
            before do
              @share_rule = create(:share_rule, created_by: user, share_all_records: true)
              @meeting = create(:meeting, id: (@share_rule.id + 1), owner: user, time_zone: nil, tenant_id: user.tenant_id)
            end

            context 'and actions other than allowed are passed' do
              it 'returns error' do
                put "/v1/meetings/#{@meeting.id}/share/#{@share_rule.id}", headers: headers, params: update_share_meeting_params(@share_rule.id, { actions: { email: true } })

                expect(response.code).to eq('422')
                expect(response.parsed_body['errorCode']).to eq('01503014')
                expect(response.parsed_body['message']).to eq('Actions are invalid')
              end
            end

            context 'and share rule with same config is not present' do
              it 'updates share rule' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleUpdatedV2)).exactly(1).times
                put "/v1/meetings/share/#{@share_rule.id}", headers: headers, params: update_share_meeting_params(@share_rule.id, {})

                expect(response.status).to eq(200)
                expect(response.parsed_body['actions']).to eq({
                  "read" => true,
                  "update" => false,
                  "delete" => false,
                  "email" => false,
                  "sms" => false,
                  "task" => false,
                  "note" => false,
                  "meeting" => false,
                  "document" => false,
                  "deleteAll" => false,
                  "quotation" => false,
                  "reshare" => false,
                  "reassign" => false
                })
                expect(response.parsed_body['name']).to eq('sharing meeting updated')
                expect(response.parsed_body['entity']).to eq(nil)
                expect(response.parsed_body['shareAllRecords']).to eq(true)
                expect(response.parsed_body['from']['id']).to eq(user.id)
                expect(response.parsed_body['from']['type']).to eq('USER')
                expect(response.parsed_body['to']['id']).to eq(another_user.id)
                expect(response.parsed_body['to']['type']).to eq('USER')
              end
            end
          end
        end
      end
    end
  end

  describe '#destroy' do
    context 'when user context is not present' do
      it 'returns unauthorized error' do
        delete '/v1/meetings/share-rules/123'

        expect(response.code).to eq('401')
        expect(response.parsed_body['errorCode']).to eq('01501005')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end

    context 'when user context is present' do
      context 'and share rule is not present' do
        it 'returns error' do
          delete '/v1/meetings/share-rules/123', headers: headers

          expect(response.code).to eq('404')
          expect(response.parsed_body['errorCode']).to eq('01503019')
          expect(response.parsed_body['message']).to eq('Share rule not found.')
        end
      end

      context 'when share rule is present' do
        context 'and current user is not creator of that share rule' do
          before do
            @share_rule = create(:share_rule, tenant_id: user.tenant_id)
          end

          it 'returns error' do
            delete(
              "/v1/meetings/share-rules/#{@share_rule.id}",
              headers: valid_headers(build(:auth_token, :without_share_rule_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)),
            )

            expect(response.code).to eq('401')
            expect(response.parsed_body['errorCode']).to eq('01501005')
            expect(response.parsed_body['message']).to eq('Unauthorized access.')
          end
        end

        context 'when user can delete share rule' do
          before do
            @share_rule = create(:share_rule, created_by: user)
          end

          it 'deletes share rule' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ShareRuleDeletedV2)).exactly(1).times
            expect do
              delete "/v1/meetings/share-rules/#{@share_rule.id}", headers: headers
            end.to change { ShareRule.count }.by(-1)
          end
        end
      end
    end
  end

  private

  def share_meeting_params(from_id, to_id, params = {})
    {
      "actions": params[:actions] || {
        "read": true,
        "update": false,
        "delete": false,
        "email": false,
        "call": false,
        "note": false,
        "sms": false,
        "task": false
      },
      "name": "sharing meeting",
      "description": "sharing meeting",
      "from": {
        "id": from_id,
        "type": params[:from_type] || "USER"
      },
      "to": {
        "id": to_id,
        "type": params[:to_type] || "USER"
      }
    }.to_json
  end

  def update_share_meeting_params(share_rule_id, params = {})
    {
      "id": share_rule_id,
      "share_rule_id": share_rule_id,
      "actions": params[:actions] || {
        "read": true,
        "update": false,
        "delete": false,
        "email": false,
        "call": false,
        "note": false,
        "sms": false,
        "task": false
      },
      "name": "sharing meeting updated",
      "description": "sharing meeting updated",
      "from": {
        "id": params[:from_id] || user.id,
        "type": params[:from_type] || "USER"
      },
      "to": {
        "id": params[:to_id] || another_user.id,
        "type": params[:to_type] || "USER"
      }
    }.to_json
  end
end
