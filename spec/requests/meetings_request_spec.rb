require 'rails_helper'
require 'bunny-mock'

RSpec.describe "Meetings Management", type: :request do
  # create test user
  let!(:user) { create(:user)}
  let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
  let!(:third_user) { create(:user, tenant_id: user.tenant_id) }
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_token_2) { build(:auth_token, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name ) }
  let!(:auth_data) { ParseToken.call(valid_auth_token.token).result }
  let!(:participant) { build(:user_look_up, entity_id: another_user.id, tenant_id: user.tenant_id, name: another_user.name) }
  let!(:invalid_auth_token) { build(:auth_token_invalid) }
  # set headers for authorization

  let!(:valid_auth_token_meeting_without_read_all_update_all_permission) { build(:auth_token, :without_meeting_read_all_and_update_all_permission, user_id: third_user.id, tenant_id: third_user.tenant_id, username: third_user.name) }
  let!(:headers_meeting_without_read_all_update_all_permission) { valid_headers(valid_auth_token_meeting_without_read_all_update_all_permission) }

  let!(:valid_auth_token_meeting_with_read_all_and_without_update_all_permission) { build(:auth_token, :without_meeting_update_all_permission, user_id: third_user.id, tenant_id: third_user.tenant_id, username: third_user.name) }
  let!(:headers_meeting_without_update_all_permission) { valid_headers(valid_auth_token_meeting_with_read_all_and_without_update_all_permission) }

  let!(:valid_auth_token_without_meeting_delete_permission) { build(:auth_token, :without_meeting_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers_without_meeting_delete_permission) { valid_headers(valid_auth_token_without_meeting_delete_permission) }

  let(:headers) { valid_headers(valid_auth_token) }
  let(:headers_for_another_user) { valid_headers(valid_auth_token_2) }
  let(:header_with_invalid_token) { invalid_headers }
  # set test valid and invalid credentials
  let(:meeting_standalone) do
    {
      "title": "my meeting",
      "description": "I want to discuss how to enable meetings in kylas",
      "from": (Time.current + 5.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "to": (Time.current + 5.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "allDay": true,
      "medium": OFFLINE,
      "location": "Pune, Balewadi",
      "timezone": {
        "id": 5,
        "name": "Asia/Calcutta"
      },
      "locationCoordinate": {
        "lat": 18.5204, "lon": 73.8567
      },
    }.to_json
  end
  let(:rsvp_notify_payload) do
    {
      "notifyOrganiser": true,
      "rsvpMessage": nil,
      "rsvpResponse": "YES"
    }.to_json
  end
  let(:rsvp_no_notify_payload) do
    {
      "notifyOrganiser": false,
      "rsvpMessage": nil,
      "rsvpResponse": "NO"
    }.to_json
  end

  let(:public_rsvp_notify_payload) do
    {
      "pid": participant.public_id,
      "rsvpMessage": nil,
      "rsvpResponse": "YES"
    }.to_json
  end

  let(:public_rsvp_invalid_participant_payload) do
    {
      "pid": "12121",
      "rsvpMessage": nil,
      "rsvpResponse": "YES"
    }.to_json
  end

  let(:meeting_invalid_standalone) do
    {
      "title": "my meeting",
      "description": "I want to discuss how to enable meetings in kylas",
      "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "allDay": true,
      "medium": OFFLINE,
      "location": "Pune, Balewadi",
      "participants": [
        {
          "entity": LOOKUP_USER,
          "id": 24,
          "name": "John"
        },
        {
          "entity": LOOKUP_LEAD,
          "id": 20,
          "name": "Jane"
        },
        {
          "entity": LOOKUP_CONTACT,
          "id": 10,
          "name": "Jane doe"
        }
      ],
      "outcome": "",
      "related_to": [
        {
          "entity": LOOKUP_LEAD,
          "id": 22,
          "name": "John Doe"
        },
        {
          "entity": LOOKUP_CONTACT,
          "id": 22,
          "name": "Jane Doe",
        },
        {
          "entity": LOOKUP_DEAL,
          "id": 22,
          "name": "5 year service contract"
        }
      ]
    }.to_json
  end

  let(:meeting_with_participants) do
    {
      "title": "my meeting",
      "description": "I want to discuss how to enable meetings in kylas",
      "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
      "allDay": true,
      "medium": OFFLINE,
      "location": "Pune, Balewadi",
      "timezone": {
        "id": 5,
        "name": "Asia/Calcutta"
      },
      "participants": [
        {
          "entity": LOOKUP_USER,
          "id": 24,
          "name": "John",
          "email": '<EMAIL>'
        },
        {
          "entity": LOOKUP_LEAD,
          "id": 20,
          "name": "Jane"
        },
        {
          "entity": LOOKUP_CONTACT,
          "id": 10,
          "name": "Jane doe",
          "email": "<EMAIL>"
        }
      ],
      "outcome": "",
      "relatedTo": [
        {
          "entity": LOOKUP_LEAD,
          "id": 22,
          "name": "John Doe"
        },
        {
          "entity": LOOKUP_CONTACT,
          "id": 22,
          "name": "Jane Doe",
        },
        {
          "entity": LOOKUP_DEAL,
          "id": 22,
          "name": "5 year service contract"
        },
        {
          "entity": LOOKUP_COMPANY,
          "id": 22,
          "name": "Test Company"
        }
      ]
    }.to_json
  end

  let(:meeting_with_user_participants) do
    {
      "meeting": {
        "title": "my first meeting",
        "description": "I want to discuss how to enable meetings in kylas",
        "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
        "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
        "allDay": true,
        "medium": OFFLINE,
        "location": "Pune, Balewadi",
        "participants": [
          {
            "entity": LOOKUP_USER,
            "id": another_user.id,
            "name": another_user.name
          }
        ],
        "outcome": ""
      }
    }.to_json
  end

  let(:user_id_by_team_payload) do
    {
      "fields": [],
      "jsonRule": {
        "id": 'teams',
        "field": 'teams',
        "type": 'long',
        "input": nil,
        "operator": 'equal',
        "value": '249',
        "data": nil,
        "property": nil,
        "primaryField": nil,
        "condition": nil,
        "not": nil,
        "rules": nil,
        "group": false
      }
    }.to_json
  end

  def stub_user_summary_requests(user, email = '<EMAIL>')
     stub_request(:get, "http://localhost:8081/v1/users/summary?id=24,#{user.id}").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
        to_return(status: 200, body: '[{"id": 24,"name": "John User","email": {"primary": true,"value": "<EMAIL>"}},{"id": ' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "' + email + '"}}]', headers: {})
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "' + email + '"}}]', headers: {})
  end

  let(:meeting_to_import) do
    {
      "jobId": 1,
      "meeting": {
        "title": "check With PickList",
        "description": "With Custom PickList for Meeting",
        "allDay": false,
        "timezone": {
          "id": 372,
          "name": "Asia/Calcutta"
        },
        "location": "Pune",
        "medium": "OFFLINE",
        "customFieldValues": {
          "cfMeetingCustom": {
            "id": 325305,
            "name": "chcek2"
          }
        },
        "status": "scheduled",
        "relatedToLeads": [
          "<EMAIL>","<EMAIL>"],
        "relatedToContacts": [
          "<EMAIL>","<EMAIL>"],
        "relatedToCompanies": [
        "<EMAIL>","<EMAIL>"],
        "participants": [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
        ],
        "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
        "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
        "owner": "<EMAIL>"
      }
    }.to_json
  end

  def stub_entity_search_request(emails:, entity_type:, response:)
    rules =
      emails.map do |email|
        {
          id: 'multi_field',
          field: 'multi_field',
          type: 'multi_field',
          input: 'multi_field',
          operator: 'multi_field',
          value: email
        }
      end

    entity_request_body = {
      fields: entity_type == LOOKUP_COMPANY ? %w[id firstName lastName emails ownedBy] : %w[id firstName lastName emails ownerId],
      jsonRule: {
        rules: rules,
        condition: 'OR',
        valid: true
      }
    }

    stub_request(:post, "#{SERVICE_SEARCH}/v1/search/#{entity_type}?page=0&size=100&sort=updatedAt,desc").
      with(
        body: entity_request_body,
      headers: {
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        'Content-Type'=>'application/json',
      }).
      to_return(status: 200, body: response.to_json, headers: {})
  end

  def stub_lead_search_request
    lead_request_body = {
      fields: %w[id firstName lastName emails ownerId],
      jsonRule: {
        rules: [
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          },
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          }
        ],
        condition: 'OR',
        valid: true
      }
    }

    lead_response = {
      content: [
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'john',
          lastName: 'lead',
          id: 1,
          ownerId: 123
        },
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'tony',
          lastName: 'lead',
          id: 2,
          ownerId: 234
        },
      ]
    }

    stub_request(:post, "#{SERVICE_SEARCH}/v1/search/lead?page=0&size=100&sort=updatedAt,desc").
    with(
      body: lead_request_body,
     headers: {
      'Authorization'=>"Bearer #{valid_auth_token.token}",
      'Content-Type'=>'application/json',
     }).
    to_return(status: 200, body: lead_response.to_json, headers: {})
  end

  def stub_contact_search_request
    contact_request_body = {
      fields: %w[id firstName lastName emails ownerId],
      jsonRule: {
        rules: [
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          },
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          }
        ],
        condition: 'OR',
        valid: true
      }
    }

    contact_response = {
      content: [
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'john',
          lastName: 'contact',
          id: 1,
          ownerId: 123
        },
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'tony',
          lastName: 'contact',
          id: 2,
          ownerId: 234
        },
      ]
    }

    stub_request(:post, "#{SERVICE_SEARCH}/v1/search/contact?page=0&size=100&sort=updatedAt,desc").
    with(
      body: contact_request_body,
     headers: {
      'Authorization'=>"Bearer #{valid_auth_token.token}",
      'Content-Type'=>'application/json',
     }).
    to_return(status: 200, body: contact_response.to_json, headers: {})
  end

  def stub_company_search_request
    company_request_body = {
      fields: %w[id firstName lastName emails ownedBy],
      jsonRule: {
        rules: [
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          },
          {
            id: 'multi_field',
            field: 'multi_field',
            type: 'multi_field',
            input: 'multi_field',
            operator: 'multi_field',
            value: '<EMAIL>'
          }
        ],
        condition: 'OR',
        valid: true
      }
    }

    company_response = {
      content: [
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'john',
          lastName: 'company',
          id: 1,
          ownerId: 123
        },
        {
          emails: [
            { type: 'OFFICE', value: '<EMAIL>', primary:true }
          ],
          firstName: 'tony',
          lastName: 'company',
          id: 2,
          ownerId: 234
        },
      ]
    }

    stub_request(:post, "#{SERVICE_SEARCH}/v1/search/company?page=0&size=100&sort=updatedAt,desc").
    with(
      body: company_request_body,
     headers: {
      'Authorization'=>"Bearer #{valid_auth_token.token}",
      'Content-Type'=>'application/json',
     }).
    to_return(status: 200, body: company_response.to_json, headers: {})
  end

  before do
    create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true)

    create(:field, internal_name: 'createdAt', tenant_id: user.tenant_id, active: true, is_sortable: true)
    create(:field, internal_name: 'checkedInAt', tenant_id: user.tenant_id, active: true, is_sortable: true, field_type: 'DATETIME_PICKER')

    stub_user_summary_requests(user)

    stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=20&includeConverted=true").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
        to_return(status: 200, body: [{"id": 20,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=20,22&includeConverted=true").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: [{"id": 20,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]},
                                    {"id": 22,"name": "John Lead","emails": [{"primary": false,"value": nil}]} ].to_json, headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=20,22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: [{"id": 20,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]},
                                    {"id": 22,"name": "John Lead","emails": [{"primary": false,"value": nil}]} ].to_json, headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: [{"id": 22,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=10").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 10,"name": "Jane Contact","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=10").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 10,"name": "test deal"}]', headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 22,"name": "Jane Contact","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=10,22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 10,"name": "Jane Contact","emails": [{"primary": false,"value": "<EMAIL>"}]},
              {"id": 22,"name": "John Contact","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})
    stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 22,"name": "Jane Deal","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})

    stub_request(:get, "http://localhost:8083/v1/summaries/companies?view=meeting&id=22").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '[{"id": 22,"name": "Test Company"}]', headers: {})

    stub_request(:get, "http://localhost:8081/v1/api-keys/meeting-rsvp").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
      to_return(status: 200, body: '{"apiKey": "testtoken"}', headers: {})

    connection = BunnyMock.new
    @channel = connection.start.channel
    @exchange = @channel.topic MEETING_EXCHANGE
    @queue = @channel.queue "meeting.user.participant.added"
    @queue.bind @exchange, routing_key: "meeting.user.participant.added"
    allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return( @queue)
  end

  context 'When request is invalid' do
    before { post '/v1/meetings/', params: meeting_standalone, headers: header_with_invalid_token }
    it 'returns a failure message' do
      expect(json['errorCode']).to match(ErrorCode.unauthorized)
    end
  end

  context "#create" do

    before do
      stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage").
        with(headers: { 'Authorization' => "Bearer #{valid_auth_token.token}" }).
        to_return(status: 200, body: { records: { used: 50, total: 100 } }.to_json)
    end

    context "standalone meeting" do
      before { post '/v1/meetings/', params: meeting_standalone, headers: headers }
       it "returns 201 create with the meeting id" do
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:created)
      end

      it "captures all the meeting data" do
        meeting_id = JSON(response.body)['id']
        meeting = Meeting.find(meeting_id)
        expect(meeting.as_json) == meeting_standalone
        expect(meeting.time_zone.id) == JSON(meeting_standalone)["timezone"]["id"]
        expect(meeting.time_zone.name) == JSON(meeting_standalone)["timezone"]["name"]
      end

      it "sets the current_user as the owner" do
        meeting_id = JSON(response.body)['id']
        meeting = Meeting.find(meeting_id)
        expect(meeting.owner).to eql user
      end

      it "sets the current_user as created_by & updated_by" do
        meeting_id = JSON(response.body)['id']
        meeting = Meeting.find(meeting_id)
        expect(meeting.created_by).to eql user
        expect(meeting.updated_by).to eql user
      end

      it 'raises events' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).once
        post '/v1/meetings/', params: meeting_standalone, headers: headers
        expect(@queue.message_count).to eq(2)
      end
    end

    context "meeting with participants and related_to" do
      it "returns 201 json response with the meeting id" do
        post '/v1/meetings/', params: meeting_with_participants, headers: headers
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:created)
      end

      context 'raising events' do
        context 'when meeting scheduled on lead' do
          it "raises correct number of events on message bus" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(2).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToDeal)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToCompany)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(3).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
            post '/v1/meetings/', params: meeting_with_participants, headers: headers
          end

          it 'gives correct message count' do
            post '/v1/meetings/', params: meeting_with_participants, headers: headers
            expect(@queue.message_count).to eq(15)
          end
        end

        context 'when meeting scheduled on contact/deal' do
          let(:entity) { [LOOKUP_CONTACT, LOOKUP_DEAL].sample }
          let(:meeting_with_participants) do
            {
              "title": "my meeting",
              "description": "I want to discuss how to enable meetings in kylas",
              "from": (Time.current + 2.days).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
              "to": (Time.current + 2.days + 1.hour).strftime("%Y-%m-%dT%H:%M:%S.%3N"),
              "allDay": true,
              "medium": OFFLINE,
              "location": "Pune, Balewadi",
              "timezone": {
                "id": 5,
                "name": "Asia/Calcutta"
              },
              "participants": [
                {
                  "entity": LOOKUP_USER,
                  "id": 24,
                  "name": "John",
                  "email": '<EMAIL>'
                }
              ],
              "outcome": "",
              "relatedTo": [{
                "entity": entity,
                "id": 10,
                "name": "test entity",
                "email": "<EMAIL>"
              }]
            }.to_json
          end

          it "raises correct number of events on message bus" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(1).times if entity == LOOKUP_DEAL
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(1).times if entity == LOOKUP_CONTACT
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToDeal)).exactly(1).times if entity == LOOKUP_DEAL
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCreated)).exactly(1).times
            post '/v1/meetings/', params: meeting_with_participants, headers: headers
          end

          it 'gives correct message count' do
            post '/v1/meetings/', params: meeting_with_participants, headers: headers

            message_count = entity == LOOKUP_DEAL ? 6 : 5
            expect(@queue.message_count).to eq(message_count)
          end
        end

      end

      context "captures all data" do
        before do
          post '/v1/meetings/', params: meeting_with_participants, headers: headers
          meeting_id = JSON(response.body)['id']
          @meeting = Meeting.find(meeting_id)
        end

        it 'has all fields data' do
          expect(@meeting.as_json) == JSON(meeting_with_participants)
        end

        it 'has the correct timezone data' do
          expect(@meeting.time_zone.id) == JSON(meeting_with_participants)["timezone"]["id"]
          expect(@meeting.time_zone.name) == JSON(meeting_with_participants)["timezone"]["name"]
        end

        it 'has correct number of participants' do
          expect(@meeting.participants.count) == JSON(meeting_with_participants)["participants"].count
        end

        it 'saves the correct name of the user participant' do
          user_participant = @meeting.participants.select{|x| x.is_a_user?}.first
          expect(user_participant.name).to be == "John User"
        end

        it 'saves the correct name of the lead participant' do
          user_participant = @meeting.participants.select{|x| x.is_a_lead?}.first
          expect(user_participant.name).to be == "Jane Lead"
        end

        it 'saves the correct name of the contact participant' do
          user_participant = @meeting.participants.select{|x| x.is_a_contact?}.first
          expect(user_participant.name).to be == "Jane Contact"
        end

        it 'saves the correct name of the lead related_to entity' do
          user_participant = @meeting.related_to.select{|x| x.is_a_lead?}.first
          expect(user_participant.name).to be == "John Lead"
        end

        it 'saves the correct name of the contact related_to entity' do
          user_participant = @meeting.related_to.select{|x| x.is_a_contact?}.first
          expect(user_participant.name).to be == "John Contact"
        end

        it 'saves the correct name of the deal related_to entity' do
          user_participant = @meeting.related_to.select{|x| x.is_a_deal?}.first
          expect(user_participant.name).to be == "Jane Deal"
        end
      end

      it "sets the current_user as the owner" do
        post '/v1/meetings/', params: meeting_with_participants, headers: headers
        meeting_id = JSON(response.body)['id']
        meeting = Meeting.find(meeting_id)
        expect(meeting.owner).to eql user
      end

      it "sets the current_user as created_by & updated_by" do
        post '/v1/meetings/', params: meeting_with_participants, headers: headers
        meeting_id = JSON(response.body)['id']
        meeting = Meeting.find(meeting_id)
        expect(meeting.created_by).to eql user
        expect(meeting.updated_by).to eql user
      end

      context 'when creating cancelled meeting' do
        it 'should create cancelled meeting and raise appropriate events' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          post '/v1/meetings/', headers: headers, params: JSON.parse(meeting_standalone).merge(
            status: CANCELLED
          ).to_json
          meeting = Meeting.find(json['id'])
          expect(meeting.status).to eq(CANCELLED)
          expect(meeting.cancelled_at.present?).to be(true)
          expect(meeting.cancelled_by_id).to eq(user.id)
          expect(meeting.conducted_at.present?).to be(false)
          expect(meeting.conducted_by_id).to eq(nil)
          expect(meeting.owner_id).to eq(user.id)
          expect(meeting.all_day).to be(true)
        end
      end

      context 'when creating conducted meeting' do
        it 'should create conducted meeting and raise no events' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          post '/v1/meetings/', headers: headers, params: JSON.parse(meeting_standalone).merge(
            status: CONDUCTED
          ).to_json
          meeting = Meeting.find(json['id'])
          expect(meeting.status).to eq(CONDUCTED)
          expect(meeting.cancelled_at.present?).to be(false)
          expect(meeting.cancelled_by_id).to eq(nil)
          expect(meeting.conducted_at.present?).to be(true)
          expect(meeting.conducted_by_id).to eq(user.id)
          expect(meeting.owner_id).to eq(user.id)
          expect(meeting.all_day).to be(true)
        end
      end

      context 'when creating missed meeting' do
        it 'should create missed meeting and raise no events' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          post '/v1/meetings/', headers: headers, params: JSON.parse(meeting_standalone).merge(
            status: SCHEDULED,
            from: (DateTime.now - 1.month),
            to: (DateTime.now - 1.month + 1.day)
          ).to_json
          meeting = Meeting.find(json['id'])
          expect(meeting.status).to eq(MISSED)
          expect(meeting.cancelled_at.present?).to be(false)
          expect(meeting.cancelled_by_id).to eq(nil)
          expect(meeting.conducted_at.present?).to be(false)
          expect(meeting.conducted_by_id).to eq(nil)
          expect(meeting.owner_id).to eq(user.id)
          expect(meeting.all_day).to be(true)
          expect(meeting.missed?).to be(true)
        end
      end

      context 'when creating scheduled meeting' do
        it 'should create scheduld meeting and raise appropriate events' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
          post '/v1/meetings/', headers: headers, params: JSON.parse(meeting_standalone).merge(
            status: SCHEDULED,
            from: (DateTime.now),
            to: (DateTime.now + 1.hour)
          ).to_json
          meeting = Meeting.find(json['id'])
          expect(meeting.status).to eq(SCHEDULED)
          expect(meeting.cancelled_at.present?).to be(false)
          expect(meeting.cancelled_by_id).to eq(nil)
          expect(meeting.conducted_at.present?).to be(false)
          expect(meeting.conducted_by_id).to eq(nil)
          expect(meeting.owner_id).to eq(user.id)
          expect(meeting.all_day).to be(true)
          expect(meeting.missed?).to be(false)
        end
      end

      context 'when creating meeting without medium' do
        it 'does add default value to meeting medium as offline' do
          expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
          expect(ParticipantAddedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
          expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times

          meeting_payload = JSON.parse(meeting_standalone)
          meeting_payload.delete('medium')
          post '/v1/meetings/', headers: headers, params: meeting_payload.to_json
          expect(response.status).to eq(201)
          meeting = Meeting.find(json['id'])
          expect(meeting.status).to eq(SCHEDULED)
          expect(meeting.medium).to eq(OFFLINE)
          expect(meeting.cancelled_at.present?).to be(false)
          expect(meeting.cancelled_by_id).to eq(nil)
          expect(meeting.conducted_at.present?).to be(false)
          expect(meeting.conducted_by_id).to eq(nil)
          expect(meeting.owner_id).to eq(user.id)
          expect(meeting.all_day).to be(true)
          expect(meeting.missed?).to be(false)
        end
      end
    end
  end

  context "#show" do
    before do
      stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
        to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})
    end

    context "valid request" do
      context 'when user is owner' do
        before do
          @meeting = create(:meeting, owner: user)
          get "/v1/meetings/#{@meeting.id}", headers: headers
        end

        it "returns json with 200 ok" do
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end

        it "returns meeting json" do
          expect(response.body) == {
            title: @meeting.title,
            description: @meeting.description,
            from: @meeting.from,
            to: @meeting.to,
            allDay: @meeting.all_day,
            location: @meeting.location,
            participants: @meeting.participants.collect{|x| {id: x.entity_id, entity: x.entity_type, name: x.name, email: x.email} },
            relatedTo: @meeting.related_to.collect{|x| {id: x.entity_id, entity: x.entity_type, name: x.name} },
            owner: {id: @meeting.owner_id, name: @meeting.owner.name},
            timezone: { id: @meeting.time_zone.entity_id, entity: @meeting.time_zone.entity_type, name: @meeting.time_zone.name },
            createdBy: {id: @meeting.created_by_id, name: @meeting.created_by.name},
            updatedBy: {id: @meeting.updated_by_id, name: @meeting.updated_by.name},
            status: 'scheduled',
            recordActions: {read: true, update: true},
            geofenceConfig: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil }
          }
        end
      end

      context 'when user is not owner' do
        before do
          @meeting = create(:meeting, owner: user)
          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{third_user.id}/geofence").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token_meeting_with_read_all_and_without_update_all_permission.token
            }).
          to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})

          get "/v1/meetings/#{@meeting.id}", headers: headers_meeting_without_update_all_permission
        end

        context 'and has read all access' do
          it 'should return  meeting' do
            expect(json['id']).to eq(@meeting.id)
            expect(json['owner']['id']).to eq(user.id)
            expect(GetSecurityContext.call.result.user_id).to eq(third_user.id)
          end
        end
      end
    end

    context "invalid requests" do
      before do
        meeting = create(:meeting, owner: another_user)
        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token_meeting_without_read_all_update_all_permission.token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end

        get "/v1/meetings/#{meeting.id}", headers: headers_meeting_without_read_all_update_all_permission
      end

      it "returns json with 401 unauthorized" do
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  context "#search" do
    let(:request_params) { {"fields": ["id", "title", "from", "to", "owner", "participants", "recordActions"] }.to_json }
    let(:page_params) { "page=1&size=2" }

    context 'When request is invalid' do
      before { post '/v1/meetings/search?' + page_params, params: request_params, headers: header_with_invalid_token }
      it 'returns a failure message' do
        expect(json['errorCode']).to match(ErrorCode.unauthorized)
      end
    end

    context 'when user is not owner and does not have read all permission and is participant in meeting' do
      let(:basic_auth_token) do
        build(
          :auth_token,
          :without_meeting_read_all_and_update_all_permission,
          user_id: another_user.id,
          tenant_id: another_user.tenant_id,
          username: another_user.name
        )
      end

      before do
        participant = build(:user_look_up)
        participant.tenant_id = another_user.tenant_id
        participant.entity = "user_#{another_user.id}"
        @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
        @meeting.participants = [participant]
        @meeting.save

        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{basic_auth_token.token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end
      end

      it 'returns that meeting' do
        post(
          "/v1/meetings/search/",
          params: request_params,
          headers: valid_headers(basic_auth_token)
        )
      end
    end

    context 'when user is not owner and does not have read all permission and some meetings are shared' do
      let(:basic_auth_token) do
        build(
          :auth_token,
          :without_meeting_read_all_and_update_all_permission,
          user_id: user.id,
          tenant_id: user.tenant_id,
          username: user.name
        )
      end

      before do
        participant = build(:user_look_up)
        participant.tenant_id = another_user.tenant_id
        participant.entity = "user_#{another_user.id}"
        team = Team.create(name: 'team 1', tenant_id: another_user.tenant_id, user_ids: [user.id])
        @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
        @meeting.participants = [participant]
        @meeting.save
        @shared_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
        @another_shared_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: third_user)
        @shared_meeting2 = create(:meeting, tenant_id: another_user.tenant_id, owner: third_user)
        create(
          :share_rule,
          tenant_id: another_user.tenant_id,
          from_id: another_user.id,
          to_id: user.id,
          meeting_id: @shared_meeting.id
        )
        create(
          :share_rule,
          tenant_id: another_user.tenant_id,
          from_id: third_user.id,
          to_id: user.id,
          meeting_id: @another_shared_meeting.id
        )
        create(
          :share_rule,
          tenant_id: another_user.tenant_id,
          from_id: third_user.id,
          to_type: 'TEAM',
          to_id: team.id,
          meeting_id: @shared_meeting2.id
        )
        @un_shared_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
        @another_un_shared_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: third_user)

        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{basic_auth_token.token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end
      end

      it 'returns shared meetings as well' do
        post(
          "/v1/meetings/search/",
          params: request_params,
          headers: valid_headers(basic_auth_token)
        )

        expect(response.parsed_body['totalElements']).to eq(4)
        expect(response.parsed_body['content'].map { |c| c['id'] }).to match_array([@meeting.id, @shared_meeting.id, @another_shared_meeting.id, @shared_meeting2.id])
      end
    end

    context 'when user is not owner and does not have read all permission and all meetings are shared' do
      let(:basic_auth_token) do
        build(
          :auth_token,
          :without_meeting_read_all_and_update_all_permission,
          user_id: user.id,
          tenant_id: user.tenant_id,
          username: user.name
        )
      end

      before do
        participant = build(:user_look_up)
        participant.tenant_id = another_user.tenant_id
        participant.entity = "user_#{another_user.id}"
        @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
        @meeting.participants = [participant]
        @meeting.save
        @shared_meeting1 = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
        @un_shared_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
        @shared_meeting2 = create(:meeting, tenant_id: another_user.tenant_id, owner: third_user)
        @shared_meeting3 = create(:meeting, tenant_id: another_user.tenant_id, owner: third_user)
        create(
          :share_rule,
          tenant_id: another_user.tenant_id,
          from_id: another_user.id,
          to_id: user.id,
          meeting_id: @shared_meeting1.id
        )
        create(
          :share_rule,
          tenant_id: another_user.tenant_id,
          from_id: third_user.id,
          to_id: user.id,
          share_all_records: true
        )

        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{basic_auth_token.token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end
      end

      it 'returns shared meetings as well' do
        post(
          "/v1/meetings/search/",
          params: request_params,
          headers: valid_headers(basic_auth_token)
        )

        expect(response.parsed_body['totalElements']).to eq(4)
        expect(response.parsed_body['content'].map { |c| c['id'] }).to match_array([@meeting.id, @shared_meeting1.id, @shared_meeting2.id, @shared_meeting3.id])
      end
    end

    context 'when user has read all permission and' do
      context 'and filter is applied on specific invitee other then current user' do
        before do
          participant = build(:user_look_up, tenant_id: another_user.tenant_id, entity: "user_#{another_user.id}")

          @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
          @meeting.participants = [participant]
          @meeting.save

          @another_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
          another_participant = build(:user_look_up, tenant_id: another_user.tenant_id, entity: "user_#{third_user.id}")
          @another_meeting.participants = [another_participant]
          @another_meeting.save
        end

        it 'only returns meetings having given invitee as participant' do
          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "participants",
                    "field": "participants",
                    "type": "participants_lookup",
                    "value": {
                      "id": another_user.id,
                      "entity": "user"
                    }
                  }
                ],
                "valid": true
              }
            }.to_json,
            headers: headers
          )
          expect(response_body.count).to eq(1)
          expect(response_body.first['id']).to eq(@meeting.id)
        end
      end

      context 'and filter is applied on specific invitee who is current user' do
        before do
          participant = build(:user_look_up, tenant_id: another_user.tenant_id, entity: "user_#{user.id}")

          @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
          @meeting.participants = [participant]
          @meeting.save

          @another_meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: user)
          another_participant = build(:user_look_up, tenant_id: another_user.tenant_id, entity: "user_#{third_user.id}")
          @another_meeting.participants = [another_participant]
          @another_meeting.save
        end

        it 'only returns meetings having given invitee as participant' do
          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "participants",
                    "field": "participants",
                    "type": "participants_lookup",
                    "value": {
                      "id": user.id,
                      "entity": "user"
                    }
                  }
                ],
                "valid": true
              }
            }.to_json,
            headers: headers
          )
          expect(response_body.count).to eq(1)
          expect(response_body.first['id']).to eq(@meeting.id)
        end
      end
    end

    context 'for user fields' do
      context 'and filter is applied on createdByFields with team property' do
        before do
          Meeting.destroy_all

          create(:meeting, tenant_id: user.tenant_id, created_by: user)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [1, user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "createdByFields",
                    "field": "createdByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "createdBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose createdBy user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
        end
      end

      context 'and filter is applied on updatedByFields with team property' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user, updated_by: another_user, title: 'this-meet')
          create(:meeting, tenant_id: user.tenant_id, created_by: user, updated_by: user)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, another_user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "updatedByFields",
                    "field": "updatedByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "updatedBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose updatedBy user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on conductedByFields with team property' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user, conducted_by: another_user, title: 'this-meet')
          create(:meeting, tenant_id: user.tenant_id, created_by: user, conducted_by: user)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, another_user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "conductedByFields",
                    "field": "conductedByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "conductedBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose conductedBy user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on ownerFields with team property' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user, owner: another_user, title: 'this-meet')
          create(:meeting, tenant_id: user.tenant_id, created_by: user, owner: user)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, another_user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "ownerFields",
                    "field": "ownerFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "owner",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose owner user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on cancelledByFields with team property' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user, cancelled_by: another_user, title: 'this-meet')
          create(:meeting, tenant_id: user.tenant_id, created_by: user, cancelled_by: user)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, another_user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "cancelledByFields",
                    "field": "cancelledByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "cancelledBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose cancelledBy user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on cancelledByFields with team property with empty team' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user, cancelled_by: another_user)
          create(:meeting, tenant_id: user.tenant_id, created_by: user, cancelled_by: user)
          create(:meeting, tenant_id: user.tenant_id, created_by: user, cancelled_by: nil)

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "cancelledByFields",
                    "field": "cancelledByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "cancelledBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'does not return any meetings' do
          expect(response.parsed_body['totalElements']).to eq(0)
        end
      end

      context 'and filter is applied on organizerFields with team property' do
        before do
          create(:meeting, tenant_id: user.tenant_id, created_by: user)
          this_meeting = create(:meeting, tenant_id: user.tenant_id, created_by: user, title: 'this-meet')

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, this_meeting.owner_id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "organizerFields",
                    "field": "organizerFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "organizer",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose organizer user is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on participantFields with team property' do
        before do
          participant = build(:user_look_up)
          participant.tenant_id = user.tenant_id
          participant.entity = "user_#{user.id}"
          meeting = create(:meeting, tenant_id: user.tenant_id, created_by: user)
          meeting.participants << participant
          meeting.save

          another_participant = build(:user_look_up)
          another_participant.tenant_id = user.tenant_id
          another_participant.entity = "user_#{another_user.id}"
          another_meeting = create(:meeting, tenant_id: user.tenant_id, created_by: user, title: 'this-meet')
          another_meeting.participants << another_participant
          another_meeting.save

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, another_user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "participantFields",
                    "field": "participantFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "participants",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose particpant users is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('this-meet')
        end
      end

      context 'and filter is applied on checkedInOutBy with team property' do
        before do
          @non_checked_in_meeting = create(:meeting, owner_id: user.id, title: 'Non Checked In Meeting', tenant_id: user.tenant_id)

          @meeting_checked_in_by_user = create(:meeting, owner_id: user.id, title: 'Checked in By User', tenant_id: user.tenant_id)
          create(:meeting_attendance, meeting_id: @meeting_checked_in_by_user.id, user_id: user.id)

          @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
          participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
          @meeting_checked_in_by_other_user.participants << participant
          @meeting_checked_in_by_other_user.save!
          create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)


          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id").with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json'
            },
            body: user_id_by_team_payload,
          ).to_return(status: 200, body: [11, user.id].to_json)

          post(
            '/v1/meetings/search',
            params: {
              "jsonRule": {
                "condition": "AND",
                "rules": [
                  {
                    "operator": "equal",
                    "id": "checkedInOutByFields",
                    "field": "checkedInOutByFields",
                    "type": "long",
                    "value": 249,
                    "primaryField": "checkedInOutBy",
                    "property": "teams"
                  }
                ],
                "valid": true
              }
            },
            headers: {
              Authorization: valid_auth_token.token
            }
          )
        end

        it 'returns meetings whose particpant users is present in team with given operator' do
          expect(response.parsed_body['totalElements']).to eq(1)
          expect(response.parsed_body.dig('content', 0, 'title')).to eq('Checked in By User')
        end
      end
    end

    context "when user is not owner of the meeting and is not participant in another user's meeting" do
      context 'when user does not have read all permission' do
        context 'when user is participant' do
          before do
            participant = build(:user_look_up)
            participant.tenant_id = another_user.tenant_id
            participant.entity = "user_#{another_user.id}"
            @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
            @meeting.participants << participant
            @meeting.save

            [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
              stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
                .with(
                  headers: {
                    Authorization: "Bearer #{valid_auth_token_meeting_without_read_all_update_all_permission.token}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
            end
          end

          it "returns json with 200 ok" do
            post "/v1/meetings/search/", params: request_params, headers: headers_meeting_without_read_all_update_all_permission
            expect(response.content_type).to eq("application/json; charset=utf-8")
            expect(response).to have_http_status(:ok)
          end

          it "returns empty meetings json for other users" do
            post "/v1/meetings/search/", params: request_params, headers: headers_meeting_without_read_all_update_all_permission
            meetings = response_body

            expect(meetings).to match_array([])
          end
        end

        context 'when user is organiser' do
          before do
            participant = build(:user_look_up)
            participant.tenant_id = another_user.tenant_id
            participant.entity = "user_#{another_user.id}"
            @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
            @meeting.participants << participant
            @meeting.save

            [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
              stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
                .with(
                  headers: {
                    Authorization: "Bearer #{valid_auth_token_2.token}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
            end

            it 'returns meetings for organiser' do
              post "/v1/meetings/search/", params: request_params, headers: headers_for_another_user
              meetings = response_body
              meeting = meetings.first

              expect(meeting['id']).to eq(@meeting.id)
              expect(meeting['tenantId']).to eq(@meeting.tenant_id)
              expect(meeting['owner']['name']).to eq(another_user.name)
            end
          end
        end

        context 'when user is entity owner' do
          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_COMPANY].each do |entity_type|
            context "when related entity is of type #{entity_type}" do
              before do
                participant = build("#{entity_type}_look_up", entity_id: 100001, owner_id: third_user.id, tenant_id: another_user.tenant_id)
                @meeting = create(:meeting, tenant_id: another_user.tenant_id, owner: another_user)
                @meeting.participants = [participant]
                @meeting.save

                [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
                  stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
                    .with(
                      headers: {
                        Authorization: "Bearer #{valid_auth_token_meeting_without_read_all_update_all_permission.token}"
                      }
                    )
                    .to_return(status: 200, body: { accessByOwners: {}, accessByRecords: {} }.to_json)
                end
              end

              it 'returns meeting' do
                post "/v1/meetings/search/", params: request_params, headers: headers_meeting_without_read_all_update_all_permission
                meeting = response_body.first

                expect(meeting['id']).to eq(@meeting.id)
                expect(meeting['tenantId']).to eq(@meeting.tenant_id)
                expect(meeting['owner']['name']).to eq(another_user.name)
              end
            end
          end
        end
      end

      context 'when user has read all permission' do
        before do
          participant = build(:user_look_up)
          participant.tenant_id = third_user.tenant_id
          participant.entity = "user_#{third_user.id}"
          @meeting = create(:meeting, tenant_id: third_user.tenant_id, owner: third_user)
          @meeting.participants << participant
          @meeting.save

          post "/v1/meetings/search/", params: request_params, headers: headers
        end

        it "returns json with 200 ok" do
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end

        it "returns meetings json" do
          meetings = response_body
          meeting = meetings.first

          expect(meeting['id']).to eq(@meeting.id)
          expect(meeting['tenantId']).to eq(@meeting.tenant_id)
          expect(meeting['updatedBy']['name']).to eq(third_user.name)
          expect(meeting['status']).to eq(SCHEDULED)
          expect(meeting['createdBy']['name']).to eq(third_user.name)
          expect(meeting['participants'].first['id']).to eq(@meeting.participants.first.entity.split('_').last.to_i)
        end
      end

      context 'when user has read all permission and share rule is present with read permissions' do
        before do
          participant = build(:user_look_up)
          participant.tenant_id = third_user.tenant_id
          participant.entity = "user_#{third_user.id}"
          @meeting = create(:meeting, tenant_id: third_user.tenant_id, owner: third_user)
          @meeting.participants << participant
          @meeting.save
          create(:share_rule, tenant_id: @meeting.tenant_id, meeting_id: @meeting.id, to_id: user.id, actions: { read: true, update: false })

          post "/v1/meetings/search/", params: request_params, headers: headers
        end

        it "returns correct record actions" do
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['content'][0]['recordActions']).to eq({ "read" => true, "update" => true })
        end

        it "returns meetings json" do
          meetings = response_body
          meeting = meetings.first

          expect(meeting['id']).to eq(@meeting.id)
          expect(meeting['tenantId']).to eq(@meeting.tenant_id)
          expect(meeting['updatedBy']['name']).to eq(third_user.name)
          expect(meeting['status']).to eq(SCHEDULED)
          expect(meeting['createdBy']['name']).to eq(third_user.name)
          expect(meeting['participants'].first['id']).to eq(@meeting.participants.first.entity.split('_').last.to_i)
        end
      end
    end

    context "when user is owner of the meeting" do
      before do
        participant = build(:user_look_up)
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        @meeting.participants << participant
        @meeting.save
        post "/v1/meetings/search/", params: request_params, headers: headers
      end

      it "returns json with 200 ok" do
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:ok)
      end

      it "returns correct meeting" do
        meetings = response_body
        expect(meetings.count).to eq 1
        expect(meetings.first['id']).to eq @meeting.id
        expect(meetings.first['owner']['id']).to eq user.id
      end

      it "has correct record actions" do
        meetings = response_body
        meeting = meetings.first

        expect(meeting['recordActions']['read']).to be true
        expect(meeting['recordActions']['update']).to be true
      end
    end

    context 'when user is owner and not participant in meeting and does not have read_all permission' do
      let(:basic_auth_token) do
        build(
          :auth_token,
          :without_meeting_read_all_and_update_all_permission,
          user_id: user.id,
          tenant_id: user.tenant_id,
          username: user.name
        )
      end

      before do
        participant = build(:user_look_up)
        participant.tenant_id = another_user.tenant_id
        participant.entity = "user_#{another_user.id}"
        @meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        @meeting.participants = [participant]
        @meeting.save
        @another_meeting = create(:meeting, tenant_id: user.tenant_id, owner: another_user)
        another_participant = build(:user_look_up, tenant_id: user.tenant_id, entity: "user_#{user.id}")
        @another_meeting.participants << another_participant
        @another_meeting.save
        third_meeting = create(:meeting, tenant_id: user.tenant_id, owner: another_user)
        third_meeting.participants = [participant]
        third_meeting.save

        [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
            .with(
              headers: {
                Authorization: "Bearer #{basic_auth_token.token}"
              }
            )
            .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
        end

        post(
          "/v1/meetings/search/",
          params: request_params,
          headers: valid_headers(basic_auth_token)
        )
      end

      it "returns json with 200 ok" do
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:ok)
      end

      it "returns correct meeting" do
        meetings = response_body
        expect(meetings.count).to eq 2
        expect(meetings.map { |meeting| meeting['id'] }).to match_array([@meeting.id, @another_meeting.id])
      end

      it "has correct record actions" do
        meetings = response_body
        meeting = meetings.find { |meeting| meeting['id'] == @meeting.id }
        another_meeting = meetings.find { |meeting| meeting['id'] == @another_meeting.id }

        expect(meeting['recordActions']['read']).to be true
        expect(meeting['recordActions']['update']).to be true
        expect(another_meeting['recordActions']['read']).to be true
        expect(another_meeting['recordActions']['update']).to be false
      end
    end

    context "when user is participant in the meeting organized by another user" do
      context 'and user has read all and update all permission' do
        before do
          @meeting = create(:meeting, tenant_id: user.tenant_id, owner: another_user)
          @meeting.save

          post "/v1/meetings/search/", params: request_params, headers: headers
        end

        it "returns json with 200 ok" do
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end

        it "returns correct meeting" do
          meetings = response_body
          meeting = meetings.first

          expect(meetings.count).to eq 1
          expect(meeting['title']).to eq @meeting.title
          expect(meeting['participants'].count).to eq 1
          expect(meeting['participants'].first['id']).to eq another_user.id
        end

        it "has correct record actions" do
          meetings = response_body
          meeting = meetings.first

          expect(meeting['recordActions']['read']).to be true
          expect(meeting['recordActions']['update']).to be true
        end
      end

      context 'and user does not have update all permission' do
        before do
          participant = build(:user_look_up)
          participant.name = third_user.name
          participant.tenant_id = third_user.tenant_id
          participant.entity = "user_#{third_user.id}"
          @meeting = create(:meeting, tenant_id: third_user.tenant_id, owner: another_user)
          @meeting.participants << participant
          @meeting.save

          post "/v1/meetings/search/", params: request_params, headers: headers_meeting_without_update_all_permission
        end

        it "returns json with 200 ok" do
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end

        it "returns correct meeting" do
          meetings = response_body
          meeting = meetings.first

          expect(meetings.count).to eq 1
          expect(meeting['title']).to eq @meeting.title
          expect(meeting['participants'].count).to eq 2

          expect(meeting['participants'].collect{ |a| a['name'] }).to include third_user.name
        end

        it "has correct record actions" do
          meetings = response_body
          meeting = meetings.first

          expect(meeting['recordActions']['read']).to be true
          expect(meeting['recordActions']['update']).to be false
        end
      end
    end

    context "when limited fields are requested" do
      let(:request_params) { { "fields": ["id", "title", "from"], "jsonRule": "null" }.to_json }

      before do
        participant = build(:user_look_up)
        participant.name = user.name
        participant.tenant_id = user.tenant_id
        participant.entity = "user_#{user.id}"
        meeting = create(:meeting, tenant_id: user.tenant_id, owner: another_user)
        meeting.participants << participant

        post "/v1/meetings/search/", params: request_params, headers: headers
      end

      it "returns only requested fields in response" do
        pending "This is not implemented yet"
        meetings = response_body

        expect(meetings.first.keys).to eq(["id", "title", "from"])
      end
    end

    context "when pagination is applied" do
      let(:number_of_meetings) { 3 }
      let(:request_params)     { { "fields": ["id", "title", "from"], "jsonRule": "null" }.to_json }
      let(:page_params)        { "page=#{page_no}&size=#{records_per_page}" }

      before do
        @meetings = create_meetings(number_of_meetings)

        post "/v1/meetings/search?" + page_params , params: request_params, headers: headers
      end

      context "and page number is 1" do
        let(:page_no) { 1 }

        context "and number of meetings are greater than per page size" do
          let(:records_per_page) { 2 }

          it "returns correct number of meetings in response" do
            meetings = response_body

            expect(meetings.count).to eq(records_per_page)
          end

          it "returns correct 'totalElements' count" do
            expect(response.parsed_body['totalElements']).to eq(number_of_meetings)
          end

          it "returns correct meetings" do
            meetings = response_body
            meetings_ids = meetings.map{|m| m['id']}

            expect(meetings_ids).to match_array(@meetings.first(records_per_page).map(&:id))
          end

          it "returns correct pagination details in response" do
            page_details = response.parsed_body['page']

            expect(page_details['no']).to eq(page_no)
            expect(page_details['size']).to eq(records_per_page)
          end

          it "returns correct 'totalPages, first and last' details in response" do
            other_page_details = response.parsed_body

            expect(other_page_details['totalPages']).to eq(2)
            expect(other_page_details['first']).to be true
            expect(other_page_details['last']).to be false
          end
        end

        context "and number of meetings are less than per page size" do
          let(:records_per_page) { 5 }

          it "returns correct number of meetings in response" do
            meetings = response_body

            expect(meetings.count).to eq(number_of_meetings)
          end

          it "returns correct 'totalElements' count" do
            expect(response.parsed_body['totalElements']).to eq(number_of_meetings)
          end

          it "returns correct meetings" do
            meetings = response_body
            meetings_ids = meetings.map{|m| m['id']}

            expect(meetings_ids).to match_array(@meetings.map(&:id))
          end

          it "returns correct pagination details in response" do
            page_details = response.parsed_body['page']

            expect(page_details['no']).to eq(page_no)
            expect(page_details['size']).to eq(records_per_page)
          end

          it "returns correct 'totalPages, first and last' details in response" do
            other_page_details = response.parsed_body

            expect(other_page_details['totalPages']).to eq(1)
            expect(other_page_details['first']).to be true
            expect(other_page_details['last']).to be true
          end
        end
      end

      context "and page number is other than 1" do
        let(:page_no)            { 2 }
        let(:number_of_meetings) { 5 }

        context "and number of meetings are greater than per page size" do
          let(:records_per_page) { 2 }

          it "returns correct number of meetings in response" do
            meetings = response_body

            expect(meetings.count).to eq(records_per_page)
          end

          it "returns correct 'totalElements' count" do
            expect(response.parsed_body['totalElements']).to eq(number_of_meetings)
          end

          it "returns correct meetings" do
            meetings = response_body
            meetings_ids = meetings.map{|m| m['id']}
            page2_meetings_ids = @meetings[records_per_page, records_per_page].map(&:id)

            expect(meetings_ids).to match_array(page2_meetings_ids)
          end

          it "returns correct pagination details in response" do
            page_details = response.parsed_body['page']

            expect(page_details['no']).to eq(page_no)
            expect(page_details['size']).to eq(records_per_page)
          end

          it "returns correct 'totalPages, first and last' details in response" do
            other_page_details = response.parsed_body

            expect(other_page_details['totalPages']).to eq(3)
            expect(other_page_details['first']).to be false
            expect(other_page_details['last']).to be false
          end
        end

        context "and number of meetings are less than per page size" do
          let(:records_per_page) { 3 }

          it "returns correct remaining number of meetings in response" do
            meetings = response_body

            expect(meetings.count).to eq(2)
          end

          it "returns correct 'totalElements' count" do
            expect(response.parsed_body['totalElements']).to eq(number_of_meetings)
          end

          it "returns correct meetings" do
            meetings = response_body
            meetings_ids = meetings.map{|m| m['id']}
            page2_meetings_ids = @meetings[records_per_page, records_per_page].map(&:id)

            expect(meetings_ids).to match_array(page2_meetings_ids)
          end

          it "returns correct pagination details in response" do
            page_details = response.parsed_body['page']

            expect(page_details['no']).to eq(page_no)
            expect(page_details['size']).to eq(records_per_page)
          end

          it "returns correct 'totalPages, first and last' details in response" do
            other_page_details = response.parsed_body

            expect(other_page_details['totalPages']).to eq(2)
            expect(other_page_details['first']).to be false
            expect(other_page_details['last']).to be true
          end
        end
      end
    end

    context "when fields are passed", desc: 'Currently support for only id title' do
      let(:number_of_meetings) { 3 }
      let(:request_params)     { { "jsonRule": nil, "fields": ["id", "title"] }.to_json }

      before { @meetings = create_meetings(number_of_meetings) }

      context "when column sort is requested on standard field" do
        context 'when sorting column is from' do
          before { post "/v1/meetings/search?#{sort_params}", params: request_params, headers: headers }

          context "and sort direction is ascending" do
            let(:sort_params) { "sort=from,asc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.first.id)
              expect(meetings_ids.last).to eq(@meetings.last.id)
              expect(meetings.first).to eq({ 'id' => @meetings.first.id, 'title' => @meetings.first.title })
            end
          end

          context "and sort direction is descending" do
            let(:sort_params) { "sort=from,desc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.last.id)
              expect(meetings_ids.last).to eq(@meetings.first.id)
              expect(meetings.first).to eq({ 'id' => @meetings.last.id, 'title' => @meetings.last.title })
            end
          end
        end
      end

      context "when column sort is requested on custom field" do
        let(:field) { create(:custom_field, tenant_id: user.tenant_id, is_sortable: true, field_type: 'NUMBER') }
        let(:sort_params) { "sort=#{field.internal_name},asc" }

        before do
          @meetings.last.update!(custom_field_values: { field.internal_name => 123 })
          post "/v1/meetings/search?#{sort_params}", params: request_params, headers: headers
        end

        it "returns meetings in correct sequence" do
          meetings = response_body
          meetings_ids = meetings.map { |m| m['id'] }

          expect(meetings_ids.first).to eq(@meetings.last.id)
          expect(meetings_ids.last).to eq(@meetings.second.id)
          expect(meetings.first).to eq({ 'id' => @meetings.last.id, 'title' => @meetings.last.title })
        end
      end
    end

    context "when sorting is applied" do
      let(:number_of_meetings) { 3 }
      let(:request_params)     { { "jsonRule": "null" }.to_json }

      before { @meetings = create_meetings(number_of_meetings) }

      context "when column sort is requested on standard field" do
        context 'when sorting column is from' do
          before { post "/v1/meetings/search?" + sort_params, params: request_params, headers: headers }

          context "and sort direction is ascending" do
            let(:sort_params) { "sort=from,asc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.first.id)
              expect(meetings_ids.last).to eq(@meetings.last.id)
            end
          end

          context "and sort direction is descending" do
            let(:sort_params) { "sort=from,desc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.last.id)
              expect(meetings_ids.last).to eq(@meetings.first.id)
            end
          end

          context "and sort column is not specified" do
            let(:sort_params) { "sort=,asc" }

            it "returns a failure message" do
              expect(json['errorCode']).to match(ErrorCode.invalid)
            end
          end

          context "and sort direction is not specified" do
            let(:sort_params) { "sort=from" }

            it "returns a failure message" do
              expect(json['errorCode']).to match(ErrorCode.invalid)
            end
          end
        end

        context 'when sorting column is createdAt' do
          before { post "/v1/meetings/search?" + created_at_params, params: request_params, headers: headers }

          context "when createAt direction is ascending" do
            let(:created_at_params) { "sort=createdAt,asc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.first.id)
              expect(meetings_ids.last).to eq(@meetings.last.id)
            end
          end

          context "when createdAt direction is descending" do
            let(:created_at_params) { "sort=createdAt,desc" }

            it "returns meetings in correct sequence" do
              meetings = response_body
              meetings_ids = meetings.map { |m| m['id'] }

              expect(meetings_ids.first).to eq(@meetings.last.id)
              expect(meetings_ids.last).to eq(@meetings.first.id)
            end
          end

          context "when sort column is not specified" do
            let(:created_at_params) { "sort=,asc" }

            it "returns a failure message" do
              expect(json['errorCode']).to match(ErrorCode.invalid)
            end
          end

          context "when sort direction is not specified" do
            let(:created_at_params) { "sort=createdAt" }

            it "returns a failure message" do
              expect(json['errorCode']).to match(ErrorCode.invalid)
            end
          end
        end
      end

      context "when column sort is requested on custom field" do
        let(:field) { create(:custom_field, tenant_id: user.tenant_id, is_sortable: true, field_type: 'NUMBER') }
        let(:sort_params) { "sort=#{field.internal_name},asc" }

        before do
          @meetings.last.update!(custom_field_values: { field.internal_name => 123 })
          post "/v1/meetings/search?" + sort_params, params: request_params, headers: headers
        end

        it "returns meetings in correct sequence" do
          meetings = response_body
          meetings_ids = meetings.map { |m| m['id'] }

          expect(meetings_ids.first).to eq(@meetings.last.id)
          expect(meetings_ids.last).to eq(@meetings.second.id)
        end
      end
    end

    describe "jsonRule filter" do
      let(:number_of_meetings) { 3 }
      let(:expected_meetings_count) { 1 }

      context 'is applied on standard field' do
        let(:request_params) {{ "jsonRule": { "condition": "AND", "rules": [valid_rule] }}.to_json}

        context "when the field is string field" do
          before do
            @meetings = create_meetings(number_of_meetings)
            @meetings.last.location = "Balewadi"
            @meetings.last.save

            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'equal_to' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_equal, value: "balewadi") }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'
          end

          context "and the 'not_equal_to' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_equal, value: "balewadi") }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'contains' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_contains, value: "balewa") }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'
          end

          context "and the 'not_contains' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_contains, value: "balewa") }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'begins_with' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_begins_with, value: "bale") }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'
          end

          context "and the 'is_empty' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_is_empty) }

            before do
              @meetings.last.location = ""
              @meetings.last.save

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'in' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_in, value: 'value1,balewadi') }

            before do
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'not_in' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_in, value: 'value1,balewadi') }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end
        end

        context "when the field is date field" do
          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'greater' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_greater, field: 'from', value: (@meetings.last.from - 2.minutes)) }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples "a json filter"
          end

          context "and the 'less' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_less, field: 'from', value: (@meetings.first.from + 2.minutes)) }

            before do
              @expected_meetings_ids = [@meetings.first.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_equal, field: 'from', value: @meetings.first.from) }

            before do
              @expected_meetings_ids = [@meetings.first.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'greter_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_date_greater_or_equal, field: 'from', value: @meetings[1].from) }

            before do
              @expected_meetings_ids = @meetings.last(expected_meetings_count).map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'less_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_date_less_or_equal, field: 'from', value: @meetings[1].from) }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'between' operator is applied" do
            let(:expected_meetings_count) { 1 }
            let(:start_date) { @meetings[1].from - 5.minutes }
            let(:end_date) { @meetings[1].from + 5.minutes }
            let(:valid_rule) { build(:json_rule_date_between, field: 'from', value: [start_date, end_date]) }

            before do
              @expected_meetings_ids = [@meetings[1].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'not_between' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:start_date) { @meetings[1].from - 5.minutes }
            let(:end_date) { @meetings[1].from + 5.minutes }
            let(:valid_rule) { build(:json_rule_date_not_between, field: 'from', value: [start_date, end_date]) }

            before do
              @expected_meetings_ids = [@meetings.first.id, @meetings.last.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'is_not_null' operator is applied" do
            let(:expected_meetings_count) { 3 }
            let(:valid_rule) { build(:json_rule_date_is_not_null, field: 'from') }

            before do
              @expected_meetings_ids = @meetings.map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end
        end

        ['checked_in_at', 'checked_out_at'].each do |field|
          context "when field is #{field}" do
            before do
              @checked_in_at_time = (DateTime.now - 5.minutes).strftime('%Y-%m-%d %H:%M:%S')
              @checked_out_at_time = DateTime.now.strftime('%Y-%m-%d %H:%M:%S')
              @meeting = create(:meeting, owner: user)
              create(:meeting_attendance, meeting_id: @meeting.id, checked_in_at: @checked_in_at_time, checked_out_at: @checked_out_at_time)
              @other_meeting = create(:meeting, owner: user)
              create(:meeting_attendance, meeting_id: @other_meeting.id, checked_in_at: DateTime.now.strftime('%Y-%m-%d %H:%M:%S'), checked_out_at: (DateTime.now + 5.minutes).strftime('%Y-%m-%d %H:%M:%S'))
            end

            context 'when operator is equal' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_equal, type: 'date', value: eval("@#{field}_time"), field: field) }
              let(:expected_meetings_count) { 1 }

              before do
                @expected_meetings_ids = [@meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is not_equal' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_not_equal, type: 'date', value: eval("@#{field}_time"), field: field) }
              let(:expected_meetings_count) { 1 }

              before do
                @expected_meetings_ids = [@other_meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is greater' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_greater, type: 'date', value: eval("@#{field}_time"), field: field) }
              let(:expected_meetings_count) { 1 }

              before do
                @expected_meetings_ids = [@other_meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is greater_or_equal' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_greater_or_equal, type: 'date', value: eval("@#{field}_time"), field: field) }
              let(:expected_meetings_count) { 2 }

              before do
                @expected_meetings_ids = [@meeting.id, @other_meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is less' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_less, type: 'date', value: eval("@#{field}_time").to_datetime + 2.seconds, field: field) }
              let(:expected_meetings_count) { 1 }

              before do
                @expected_meetings_ids = [@meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is less or equal' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_less_or_equal, type: 'date', value: eval("@#{field}_time"), field: field) }
              let(:expected_meetings_count) { 1 }

              before do
                @expected_meetings_ids = [@meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is between' do
              let(:valid_rule) { build(
                :json_rule_meeting_attendance_between,
                type: 'date',
                value: [(eval("@#{field}_time").to_datetime - 10.minutes).strftime('%Y-%m-%d %H:%M:%S'), (DateTime.now + 10.minutes).strftime('%Y-%m-%d %H:%M:%S')], field: field)
              }
              let(:expected_meetings_count) { 2 }

              before do
                @expected_meetings_ids = [@meeting.id, @other_meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is not_between' do
              let(:valid_rule) { build(
                :json_rule_meeting_attendance_not_between,
                type: 'date',
                value: [(eval("@#{field}_time").to_datetime - 10.minutes).strftime('%Y-%m-%d %H:%M:%S'), (DateTime.now + 10.minutes).strftime('%Y-%m-%d %H:%M:%S')], field: field)
              }
              let(:expected_meetings_count) { 0 }

              before do
                @expected_meetings_ids = []
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is is_null' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_is_null, type: 'date', field: 'checked_out_at', field: field) }
              let(:expected_meetings_count) { 2 }

              before do
                @expected_meetings_ids = [@meeting.id, @other_meeting.id]
                MeetingAttendance.update_all(checked_out_at: nil, checked_in_at: nil)
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when operator is is_not_null' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_is_not_null, type: 'date', field: field) }
              let(:expected_meetings_count) { 2 }

              before do
                @expected_meetings_ids = [@meeting.id, @other_meeting.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context 'when sorting is applied in checkedInAt' do
              let(:valid_rule) { build(:json_rule_meeting_attendance_is_not_null, type: 'date', field: field) }

              context 'when sorting order is asc' do
                it 'returns in correct order' do
                  post "/v1/meetings/search?sort=checkedInAt,asc" , params: request_params, headers: headers
                  meeting_ids = response_body.map{|m| m['id']}
                  expect(meeting_ids).to eq([@meeting.id, @other_meeting.id])
                  expect(response_body.first['userAttendances'].first['isCheckedInOutsideGeofence']).to eq(false)
                  expect(response_body.first['userAttendances'].first['isCheckedOutOutsideGeofence']).to eq(false)
                end
              end

              context 'when sorting order is desc' do
                it 'returns in correct order' do
                  post "/v1/meetings/search?sort=checkedInAt,desc" , params: request_params, headers: headers
                  meeting_ids = response_body.map{|m| m['id']}
                  expect(meeting_ids).to eq([@other_meeting.id, @meeting.id])
                end
              end
            end
          end
        end

        context 'when field is checked_in_out_by' do
          before do
            @non_checked_in_meeting = create(:meeting, owner_id: user.id, title: 'Non Checked In Meeting', tenant_id: user.tenant_id)

            @meeting_checked_in_by_user = create(:meeting, owner_id: user.id, title: 'Checked in By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_user.id, user_id: user.id)

            @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
            participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
            @meeting_checked_in_by_other_user.participants << participant
            @meeting_checked_in_by_other_user.save!
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)
          end

          context 'when operator is equal' do
            let(:valid_rule) { build(:checked_in_out_by_equal, value: user.id) }
            let(:expected_meetings_count) { 1 }

            before do
              @expected_meetings_ids = [@meeting_checked_in_by_user.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context 'when operator is not equal' do
            let(:valid_rule) { build(:checked_in_out_by_not_equal, value: user.id) }
            let(:expected_meetings_count) { 2 }

            before do
              @expected_meetings_ids = [@meeting_checked_in_by_other_user.id, @non_checked_in_meeting.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context 'when operator is in' do
            let(:valid_rule) { build(:checked_in_out_by_in, value: [user.id, another_user.id]) }
            let(:expected_meetings_count) { 2 }

            before do
              @expected_meetings_ids = [@meeting_checked_in_by_user.id, @meeting_checked_in_by_other_user.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context 'when operator is not_in' do
            let(:valid_rule) { build(:checked_in_out_by_not_in, value: [user.id]) }
            let(:expected_meetings_count) { 2 }

            before do
              @expected_meetings_ids = [@meeting_checked_in_by_other_user.id, @non_checked_in_meeting.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context 'when operator is is_null' do
            let(:valid_rule) { build(:checked_in_out_by_is_null) }
            let(:expected_meetings_count) { 1 }

            before do
              @expected_meetings_ids = [@non_checked_in_meeting.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context 'when operator is is_not_null' do
            let(:valid_rule) { build(:checked_in_out_by_is_not_null) }
            let(:expected_meetings_count) { 2 }

            before do
              @expected_meetings_ids = [@meeting_checked_in_by_user.id, @meeting_checked_in_by_other_user.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end
        end

        context "when the field is boolean field" do
          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_boolean_equal, value: true) }

            before do
              @meetings.last.all_day = true
              @meetings.last.save

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end
        end

        context "when the field is participant look_up field" do
          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
          end

          [LOOKUP_USER, LOOKUP_LEAD, LOOKUP_CONTACT].each do |participant|
            context "when the entity is #{participant} look up" do
              context "and the 'equal' operator is applied" do
                let(:valid_rule) { build(:json_rule_participants_lookup_equal, value: { entity: participant, id: @participant.entity_id }) }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{participant}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{participant}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[0].id, @meetings[1].id)
                end
              end

              context "and the 'not_equal' operator is applied" do
                let(:valid_rule) { build(:json_rule_participants_lookup_not_equal, value: { entity: participant, id: @participant.entity_id }) }
                let(:expected_meetings_count) { 2 }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{participant}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{participant}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save
                  @expected_meetings_ids = @meetings.map(&:id) - @expected_meetings_ids

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings.last.id)
                end
              end

              context "and the 'in' operator is applied" do
                let(:valid_rule) { build(:json_rule_participants_lookup_in, value: [{ entity: participant, id: @participant.entity_id }]) }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{participant}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{participant}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[0].id, @meetings[1].id)
                end
              end

              context "and the 'not_in' operator is applied" do
                let(:valid_rule) { build(:json_rule_participants_lookup_not_in, value: [{ entity: participant, id: @participant.entity_id }]) }
                let(:expected_meetings_count) { 2 }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{participant}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{participant}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  @expected_meetings_ids = @meetings.map(&:id) - @expected_meetings_ids

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings.last.id)
                end
              end
            end
          end

          context 'when the pagination is applied' do
            let(:valid_rule) { build(:json_rule_participants_lookup_equal, value: { entity: 'contact', id: @contact.entity.split('_').last }) }

            context "and the page number is 1" do
              let(:expected_meetings_count) { 2 }

              before do
                @contact = create(:contact_look_up)
                @expected_meetings_ids = @meetings.first(2).map(&:id)
                @meetings.each{ |m| m.participants << @contact }
                post "/v1/meetings/search?page=1&size=2" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end

            context "and the page number is other than 1" do
              let(:expected_meetings_count) { 1 }

              before do
                @contact = create(:contact_look_up)
                @expected_meetings_ids = @meetings.last(1).map(&:id)
                @meetings.each{ |m| m.participants << @contact }
                post "/v1/meetings/search?page=2&size=2" , params: request_params, headers: headers
              end

              include_examples "a json filter"
            end
          end
        end

        context 'when the field is one of associated entity' do
          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
          end

          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_COMPANY].each do |entity_type|
            context "when the entity is #{entity_type} look up" do
              context "and the 'equal' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "equal",
                    type: "long",
                    value: @participant.entity_id
                  )
                }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{entity_type}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[0].id, @meetings[1].id)
                end
              end

              context "and the 'not_equal' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "not_equal",
                    type: "long",
                    value: @participant.entity_id
                  )
                }
                let(:expected_meetings_count) { 2 }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{entity_type}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save
                  @expected_meetings_ids = @meetings.map(&:id) - @expected_meetings_ids

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings.last.id)
                end
              end

              context "and the 'in' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "in",
                    type: "long",
                    value: [@participant.entity_id]
                  )
                }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{entity_type}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[0].id, @meetings[1].id)
                end
              end

              context "and the 'not_in' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "not_in",
                    type: "long",
                    value: [@participant.entity_id]
                  )
                }
                let(:expected_meetings_count) { 2 }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{entity_type}_#{another_user_participant.id}"
                  @meetings[1].participants << @another_participant
                  @meetings[1].save

                  @expected_meetings_ids = @meetings.map(&:id) - @expected_meetings_ids

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings.last.id)
                end
              end

              context "and the 'is_not_null' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "is_not_null",
                    type: "long",
                    value: nil
                  )
                }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[0].id, @meetings[1].id)
                end
              end

              context "and the 'is_null' operator is applied" do
                let(:valid_rule) {
                  build(
                    :json_rule,
                    field: "associated_#{entity_type.pluralize}",
                    operator: "is_null",
                    type: "long",
                    value: nil
                  )
                }
                let(:expected_meetings_count) { 2 }

                before do
                  user_participant = create(:user, tenant_id: user.tenant_id)
                  @participant = build(:user_look_up)
                  @participant.name = user_participant.name
                  @participant.tenant_id = user_participant.tenant_id
                  @participant.entity = "#{entity_type}_#{user_participant.id}"
                  @meetings.last.participants << @participant
                  @meetings.last.save

                  post "/v1/meetings/search" , params: request_params, headers: headers
                  @expected_meetings_ids = @meetings.first(2).map(&:id)
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings[2].id)
                end
              end
            end
          end
        end

        context "when the field is related look_up field" do
          let(:expected_meetings_count) { 2 }

          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = @meetings.last(expected_meetings_count).map(&:id)
          end

          [LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_LEAD, LOOKUP_COMPANY].each do |related_entity|
            context "when the entity is #{related_entity} look up" do
              context "and the 'equal' operator is applied" do
                let(:valid_rule) { build(:json_rule_related_lookup_equal, value: { entity: related_entity, id: @related_entity.entity_id }) }

                before do
                  @related_entity = build("#{related_entity}_look_up", tenant_id: user.tenant_id)
                  @meetings.last.related_to << @related_entity
                  @meetings.last.save

                  another_user_participant = create(:user, tenant_id: user.tenant_id)
                  @another_participant = build(:user_look_up)
                  @another_participant.name = another_user_participant.name
                  @another_participant.tenant_id = another_user_participant.tenant_id
                  @another_participant.entity = "#{related_entity}_#{another_user_participant.id}"
                  @meetings[1].participants = [@another_participant]
                  @meetings[1].related_to << @related_entity
                  @meetings[1].save
                  # &includeConverted=true&view=meeting").
                  stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=#{@related_entity.entity_id}&includeConverted=true").
                    with(
                      headers: {
                        'Authorization'=>'Bearer '+ valid_auth_token.token
                      }).
                      to_return(status: 200, body: '[{"id":' + @related_entity.entity_id.to_s + ',"name": "Jane lead","emails": [{"primary": true,"value": "' + @related_entity.email.to_s + '"}]}]', headers: {})

                      stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=#{@related_entity.entity_id}").
                        with(
                          headers: {
                            'Authorization'=>'Bearer '+ valid_auth_token.token
                          }).
                          to_return(status: 200, body: '[{"id":' + @related_entity.entity_id.to_s + ',"name": "Jane Deal","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})

                          stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=#{@related_entity.entity_id}").
                            with(
                              headers: {
                                'Authorization'=>'Bearer '+ valid_auth_token.token
                              }).
                              to_return(status: 200, body: '[{"id":' + @related_entity.entity_id.to_s + ',"name": "Jane contact","emails": [{"primary": true,"value": "' + @related_entity.email.to_s + '"}]}]', headers: {})

                              stub_request(:get, "http://localhost:8083/v1/summaries/companies?view=meeting&id=#{@related_entity.entity_id}").
                                with(
                                  headers: {
                                    'Authorization'=>'Bearer '+ valid_auth_token.token
                                  }).
                                  to_return(status: 200, body: '[{"id":' + @related_entity.entity_id.to_s + ',"name": "Jane Company","emails": [{"primary": true,"value": "<EMAIL>"}]}]', headers: {})

                                  post "/v1/meetings/search" , params: request_params, headers: headers
                end

                include_examples "a json filter"

                it "doesn't include unnecessary meetings" do
                  meetings = response_body
                  meetings_ids = meetings.map{|m| m['id']}

                  expect(meetings_ids).not_to include(@meetings.first.id)
                end
              end
            end
          end

          context "when invalid entity is passed" do
            let(:valid_rule) { build(:json_rule_related_lookup_equal, value: { entity: ['invalid', 'user'].sample, id: @related_entity.entity_id }) }

            before do
              @related_entity = build(:lead_look_up, tenant_id: user.tenant_id)
              @meetings.last.related_to << @related_entity
              @meetings.last.save

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            it "raises invalid data error" do
              expect(json['errorCode']).to match(ErrorCode.invalid)
            end
          end
        end

        context "when the field is long field" do
          before do
            @meetings = create_meetings(number_of_meetings)

            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_long_equal, value: @meetings.last.id) }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'
          end

          context "and the 'not_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_not_equal, value: @meetings.last.id) }

            before do
              @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'greater' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_greater, value: @meetings.first.id) }

            before do
              @expected_meetings_ids = [@meetings[1].id, @meetings[2].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'greater_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_greater_or_equal, value: @meetings[1].id) }

            before do
              @expected_meetings_ids = [@meetings[1].id, @meetings[2].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'less' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_less, value: @meetings[2].id) }

            before do
              @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'less_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_less_or_equal, value: @meetings[1].id) }

            before do
              @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'in' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_long_in, value: "#{@meetings[0].id},#{@meetings[1].id}".split(',')) }

            before do
              @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'not_in' operator is applied" do
            let(:expected_meetings_count) { 1 }
            let(:valid_rule) { build(:json_rule_long_not_in, value: "#{@meetings[0].id},#{@meetings[1].id}") }

            before do
              @expected_meetings_ids = [@meetings[2].id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'between' operator is applied" do
            let(:expected_meetings_count) { 3 }
            let(:valid_rule) { build(:json_rule_long_between, value: [@meetings[0].id, @meetings[2].id]) }

            before do
              @expected_meetings_ids = @meetings.map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end

          context "and the 'not_between' operator is applied" do
            let(:expected_meetings_count) { 0 }
            let(:valid_rule) { build(:json_rule_long_not_between, value: [@meetings[0].id, @meetings[2].id]) }

            before do
              @expected_meetings_ids = []
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'
          end
        end

        context 'when an invalid rule is applied' do
          before do
            @meetings = create_meetings(number_of_meetings)
            @meetings.last.location = "Balewadi"
            @meetings.last.save
          end

          context "and an invalid operator is applied" do
            let(:valid_rule) { build(:json_rule_string_equal, operator: 'invalid', value: "balewadi") }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            it 'should return unprocessable entity status' do
              expect(response.status).to be(422)
              expect(json['errorCode']).to eq('01503001')
            end
          end
        end
      end

      context 'is applied on custom field' do
        let(:custom_field) { create(:custom_field, tenant_id: user.tenant_id, field_type: field_type, is_filterable: true) }
        let(:request_params) do
          valid_rule.field = "customFieldValues.#{custom_field.internal_name}"
          { "jsonRule": { "condition": "AND", "rules": [valid_rule] }}.to_json
        end

        context "when the field is string field" do
          let(:field_type) { 'TEXT_FIELD' }

          before do
            @meetings = create_meetings(number_of_meetings)
            @meetings.last.custom_field_values = { custom_field.internal_name => 'Balewadi' }
            @meetings.last.save

            @meetings.first(2).each do |meeting|
              meeting.custom_field_values = { custom_field.internal_name => 'Baner' }
              meeting.save
            end

            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_equal, value: "balewadi", is_custom_field: true) }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Balewadi")
            end
          end

          context "and the 'not_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_equal, value: "balewadi", is_custom_field: true) }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Baner")
            end
          end

          context "and the 'contains' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_contains, value: "balewa", is_custom_field: true) }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Balewadi")
            end
          end

          context "and the 'not_contains' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_contains, value: "balewa") }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Baner")
            end
          end

          context "and the 'begins_with' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_begins_with, value: "bale") }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Balewadi")
            end
          end

          context "and the 'is_empty' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_is_empty) }

            before do
              @meetings.last.custom_field_values = {}
              @meetings.last.save

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq(nil)
            end
          end

          context "and the 'in' operator is applied" do
            let(:valid_rule) { build(:json_rule_string_in, value: 'value1,balewadi') }

            before do
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Balewadi")
            end
          end

          context "and the 'not_in' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_string_not_in, value: 'value1,balewadi') }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples 'a json filter'

            it 'should return meetings' do
              expect(json['content'].first.dig('customFieldValues', custom_field.internal_name)).to eq("Baner")
            end
          end
        end

        context "when the field is boolean field" do
          let(:field_type) { 'CHECKBOX' }

          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_boolean_equal, value: true, type: 'string') }

            before do
              @meetings.last.custom_field_values = { custom_field.internal_name => true }
              @meetings.last.save

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'not_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_boolean_not_equal, value: true, type: 'string') }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)

              @meetings.last.custom_field_values = { custom_field.internal_name => true }
              @meetings.last.save

              @meetings.first(2).each do |meeting|
                meeting.custom_field_values = {  custom_field.internal_name => false }
                meeting.save
              end

              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end
        end

        context "when the field is date field" do
          let(:field_type) { 'DATETIME_PICKER' }

          before do
            @meetings = create_meetings(number_of_meetings)
            @expected_meetings_ids = [@meetings.last.id]
            @date_value = DateTime.now.utc
            @meetings.last.custom_field_values = { custom_field.internal_name => @date_value.to_s }
            @meetings.last.save!
          end

          context "and the 'greater' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_greater, value: (@date_value - 2.minutes)) }

            before{ post "/v1/meetings/search" , params: request_params, headers: headers }

            include_examples "a json filter"
          end

          context "and the 'less' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_less, value: (@date_value + 2.minutes)) }

            before do
              @expected_meetings_ids = [@meetings.last.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'equal' operator is applied" do
            let(:valid_rule) { build(:json_rule_date_equal, value: @date_value.to_s) }

            before do
              @expected_meetings_ids = [@meetings.last.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'greater_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_date_greater_or_equal, value: @date_value - 2.minutes) }

            before do
              @expected_meetings_ids = @meetings.last(expected_meetings_count).map(&:id)
              @meetings.last(expected_meetings_count).each do |meeting|
                meeting.custom_field_values = { custom_field.internal_name => @date_value.to_s }
                meeting.save
              end
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'less_or_equal' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_date_less_or_equal, value: @date_value + 2.minutes) }

            before do
              @expected_meetings_ids = @meetings.last(expected_meetings_count).map(&:id)
              @meetings.last(expected_meetings_count).each do |meeting|
                meeting.custom_field_values = { custom_field.internal_name => @date_value.to_s }
                meeting.save
              end
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'between' operator is applied" do
            let(:expected_meetings_count) { 1 }
            let(:start_date) { @date_value - 5.minutes }
            let(:end_date) { @date_value + 5.minutes }
            let(:valid_rule) { build(:json_rule_date_between, value: [start_date, end_date]) }

            before do
              @expected_meetings_ids = [@meetings.last.id]
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'not_between' operator is applied" do
            let(:expected_meetings_count) { 1 }
            let(:start_date) { @date_value - 5.minutes }
            let(:end_date) { @date_value + 5.minutes }
            let(:valid_rule) { build(:json_rule_date_not_between, value: [start_date, end_date]) }

            before do
              @expected_meetings_ids = [@meetings.first.id]
              @meetings.first.custom_field_values = { custom_field.internal_name => (@date_value - 10.minutes).to_s }
              @meetings.first.save
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'is_null' operator is applied" do
            let(:expected_meetings_count) { 2 }
            let(:valid_rule) { build(:json_rule_date_is_null) }

            before do
              @expected_meetings_ids = @meetings.first(expected_meetings_count).map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end

          context "and the 'is_not_null' operator is applied" do
            let(:expected_meetings_count) { 1 }
            let(:valid_rule) { build(:json_rule_date_is_not_null) }

            before do
              @expected_meetings_ids = @meetings.last(expected_meetings_count).map(&:id)
              post "/v1/meetings/search" , params: request_params, headers: headers
            end

            include_examples "a json filter"
          end
        end

        %w[long picklist].each do |type|
          context "when the field is #{type} field" do
            let(:field_type) { { long: 'NUMBER', picklist: 'PICK_LIST' }[type.to_sym] }

            before do
              @type = type
              @meetings = create_meetings(number_of_meetings)

              @expected_meetings_ids = [@meetings.last.id]
              @custom_value1 = (type == 'picklist' ? { id: 120, name: 'CPV' } : 1232.567)
              @custom_value2 = (type == 'picklist' ? { id: 121, name: 'CPV' } : 1233.567)
              @custom_value3 = (type == 'picklist' ? { id: 123, name: 'CPV' } : 1234.567)
              @meetings.first.custom_field_values = { custom_field.internal_name => @custom_value1 }
              @meetings.first.save

              @meetings.last.custom_field_values = { custom_field.internal_name => @custom_value2 }
              @meetings.last.save
              @meetings.each.with_index(1) do |meeting, index|
                meeting.custom_field_values = { custom_field.internal_name => eval("@custom_value#{index}") }
                meeting.save
              end
            end

            def get(value)
              @type == 'picklist' ? value[:id] : value
            end

            context "and the 'equal' operator is applied" do
              let(:valid_rule) { build(:json_rule_long_equal, value: get(@custom_value3)) }

              before{ post "/v1/meetings/search" , params: request_params, headers: headers }

              include_examples 'a json filter'
            end

            context "and the 'not_equal' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_not_equal, value: get(@custom_value3)) }

              before do
                @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'greater' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_greater, value: get(@custom_value1)) }

              before do
                @expected_meetings_ids = [@meetings[1].id, @meetings.last.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'greater_or_equal' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_greater_or_equal, value: get(@custom_value2)) }

              before do
                @expected_meetings_ids = [@meetings[1].id, @meetings.last.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'less' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_less, value: get(@custom_value3)) }

              before do
                @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'less_or_equal' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_less_or_equal, value: get(@custom_value2)) }

              before do
                @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'in' operator is applied" do
              let(:expected_meetings_count) { 2 }
              let(:valid_rule) { build(:json_rule_long_in, value: "#{get(@custom_value1)},#{get(@custom_value2)}".split(',')) }

              before do
                @expected_meetings_ids = [@meetings[0].id, @meetings[1].id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'not_in' operator is applied" do
              let(:expected_meetings_count) { 1 }
              let(:valid_rule) { build(:json_rule_long_not_in, value: "#{get(@custom_value1)},#{get(@custom_value2)}") }

              before do
                @expected_meetings_ids = [@meetings.last.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'between' operator is applied" do
              let(:expected_meetings_count) { 3 }
              let(:valid_rule) { build(:json_rule_long_between, value: [get(@custom_value1), get(@custom_value3)]) }

              before do
                @expected_meetings_ids = @meetings.map(&:id)
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end

            context "and the 'not_between' operator is applied" do
              let(:expected_meetings_count) { 1 }
              let(:valid_rule) { build(:json_rule_long_not_between, value: [get(@custom_value1), get(@custom_value2)]) }

              before do
                @expected_meetings_ids = [@meetings.last.id]
                post "/v1/meetings/search" , params: request_params, headers: headers
              end

              include_examples 'a json filter'
            end
          end
        end
      end
    end
  end

  context "#update" do
    before do
      @meeting = create(:meeting, owner: user, time_zone:nil)
    end

    context 'valid meeting update request with no scheduled time change' do
      it 'should update it' do
        @update_params = {}
        @update_params[:id] = @meeting.id
        @update_params[:title] = 'New Title'
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        @update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        @update_params[:participants] = [
          {
            "entity": 'user',
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          }
        ]
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_DEAL,
            "id": 24,
            "name": "5 year service contract"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 20,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 24,
            "name": "Jane doe"
          }
        ]

        @update_params[:locationCoordinate] = {
          "lat": 18.5204,
          "lon": 73.8567
        }

        stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=20,24&includeConverted=true").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 20,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]},{"id": 24,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=24").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Jane Contact","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=24").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Test Deal","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).exactly(2).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers

        meeting = response.parsed_body
        related_to_entities = meeting['relatedTo'].map{ |r| "#{r['entity']}_#{r['id']}"}

        expect(meeting["title"]).to eq('New Title')
        expect(related_to_entities).to match_array(['deal_24', 'lead_24', 'contact_24', 'lead_20'])
        expect( DateTime.parse(meeting['from']).utc.round(2)).to eq( @meeting.from.round(2))
        expect( DateTime.parse(meeting['to']).utc.round(2)).to eq( @meeting.to.round(2))
        expect(meeting['locationCoordinate']).to eq({"lat"=>18.5204, "lon"=>73.8567})
      end
    end

    context 'valid meeting update request with change in scheduled time' do
      it 'should publish meeting rescheduled event correctly' do
        lead_lookup = build(:lead_look_up, tenant_id: user.tenant_id, email:"<EMAIL>")
        @meeting.participants << lead_lookup
        @meeting.related_to << lead_lookup

        @update_params = {}
        @update_params[:id] = @meeting.id
        @update_params[:title] = 'New Title'
        @update_params[:from] = Time.now + 5.minutes
        @update_params[:to] = Time.now + 1.hour
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        @update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        @update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": lead_lookup.entity_id,
            "name": lead_lookup.name
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 24,
            "name": "Jane doe"
          }
        ]
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_DEAL,
            "id": 24,
            "name": "5 year service contract"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": lead_lookup.entity_id,
            "name": lead_lookup.name
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 24,
            "name": "Jane doe"
          }
        ]

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": user.id,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}} ].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=24,#{lead_lookup.entity_id}&includeConverted=true").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}, {"id": lead_lookup.entity_id,"name": lead_lookup.name,"emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=24&includeConverted=true").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=24").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Jane Contact","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=24").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": 24,"name": "Test Deal","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(2).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers
      end
    end

    context "with participants added" do
      it 'should Publish event to add participants' do
        update_params = {}
        update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John",
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 20,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 22,
            "name": "Lead Jane"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 10,
            "name": "Jane doe"
          }
        ]

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},24").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": user.id, "entity": LOOKUP_USER,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}}, {"id": 24, "entity": LOOKUP_USER, "name": "John", "email": {"primary": true,"value": "<EMAIL>"}}].to_json, headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).twice
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(4).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["participants"].count).to eq(5)
      end
    end

    context "with contact as participant added" do
      it 'publishes events correctly' do
        update_params = {}
        update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John",
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 10,
            "name": "Jane doe"
          }
        ]

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},24").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": user.id, "entity": LOOKUP_USER,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}}, {"id": 24, "entity": LOOKUP_USER, "name": "John", "email": {"primary": true,"value": "<EMAIL>"}}].to_json, headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(2).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledRelatedToEntity)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["participants"].count).to eq(3)
      end
    end

    context "with some participants removed" do
      before do
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.participants << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.save
      end

      it 'should Publish event to remove participants' do
        update_params = {}
        update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John",
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 20,
            "name": "Jane"
          }
        ]

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},24").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: [{"id": user.id, "entity": LOOKUP_USER,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}}, {"id": 24, "entity": LOOKUP_USER, "name": "John", "email": {"primary": true,"value": "<EMAIL>"}}].to_json, headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(2).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["participants"].count).to eq(3)
      end
    end

    context "with organizer removed" do
      before do
        updated_params = {
          id: @meeting.id,
          participants: []
        }

        put "/v1/meetings/#{@meeting.id}", headers: headers, params: updated_params.to_json
      end

      it 'should return invalid error code' do
        expect(json['errorCode']).to eq('01503001')
      end
    end

    context "when related_to entity is already present and tried to remove user participant entity" do
      before do
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John User")
        @meeting.related_to << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.save

        stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
        with(
          headers: {
            "Authorization" => "Bearer #{valid_auth_token.token}",
            'Accept'=>'application/json',
            'Content-Type'=>'application/json'
          }).
          to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: 1, tenant_id: @meeting.tenant_id, name: "Jane Contact")
        ])
        allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
          build(:contact_look_up, entity_id: 10, tenant_id: @meeting.tenant_id, name: "Jane Doe")
        ])
      end

      it 'should Publish event to remove participants for that contact' do
        update_params = {}
        update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": "John Contact"
          }
        ]
        update_params[:relatedTo] = [
          {
            "entity": LOOKUP_CONTACT,
            "id": 10,
            "name": "John Contact",
            "email": "<EMAIL>"
          }
        ]
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
      end
    end

    context "when participant entity is already present and tried to remove user participant entity" do
      before do
        @meeting.participants << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John User")
        @meeting.save
        stub_user_summary_requests(user, @meeting.organizer.email)
      end

      it 'should Publish event to remove participants for that contact' do
        update_params = {}
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:id] = @meeting.id
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": "John Contact",
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 10,
            "name": "John Contact",
            "email": "<EMAIL>"
          }
        ]
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
      end
    end

    context "when user participant entity is already present and tried to remove other participant entity" do
      before do
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 24, name: "John User")
        @meeting.participants << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.save
      end

      it 'should Publish event to remove participants for that contact' do
        update_params = {}
        update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John User",
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": "John Contact",
            "email": "<EMAIL>"
          }
        ]
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingReScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times
        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
      end
    end

    context "when entities are added in related_to" do
      before do
        @update_params = {}
        @update_params[:id] = @meeting.id
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        @update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        @update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John",
            "email": "<EMAIL>"
          }
        ]
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_DEAL,
            "id": 24,
            "name": "5 year service contract"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 24,
            "name": "Jane doe"
          },
          {
            "entity": LOOKUP_COMPANY,
            "id": 24,
            "name": "Test Company"
          }
        ]

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},24").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: [{"id": user.id, "entity": LOOKUP_USER,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}}, {"id": 24, "entity": LOOKUP_USER, "name": "John", "email": {"primary": true,"value": "<EMAIL>"}}].to_json, headers: {})

            stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&id=24&includeConverted=true").
              with(
                headers: {
                  'Authorization'=>'Bearer '+ valid_auth_token.token
                }).
                to_return(status: 200, body: [{"id": 24,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=24").
                  with(
                    headers: {
                      'Authorization'=>'Bearer '+ valid_auth_token.token
                    }).
                    to_return(status: 200, body: [{"id": 24,"name": "Jane Contact","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                    stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=24").
                      with(
                        headers: {
                          'Authorization'=>'Bearer '+ valid_auth_token.token
                        }).
                        to_return(status: 200, body: [{"id": 24,"name": "Test Deal","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                        stub_request(:get, "http://localhost:8083/v1/summaries/companies?view=meeting&id=24").
                          with(
                            headers: {
                              'Authorization'=>'Bearer '+ valid_auth_token.token
                            }).
                            to_return(status: 200, body: [{"id": 24,"name": "Test Company"}].to_json, headers: {})
      end

      it 'adds related_to entities correctly' do
        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers
        meeting = response.parsed_body
        related_to_entities = meeting['relatedTo'].map{ |r| "#{r['entity']}_#{r['id']}"}

        expect(meeting["relatedTo"].count).to eq(4)
        expect(related_to_entities).to match_array(['deal_24', 'lead_24', 'contact_24', 'company_24'])
      end

      it 'publishes event to add related_to' do
        @update_params[:id] = @meeting.id
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_LEAD,
            "id": 20,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 10,
            "name": "Jane doe"
          }
        ]

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToDeal)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["relatedTo"].count).to eq(2)
      end
    end

    context "when some related_to entities are removed" do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},24").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: [{"id": user.id, "entity": LOOKUP_USER,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}}, {"id": 24, "entity": LOOKUP_USER, "name": "John", "email": {"primary": true,"value": "<EMAIL>"}}].to_json, headers: {})

            stub_request(:get, "http://localhost:8083/v1/summaries/leads?view=meeting&includeConverted=true&id=24").
              with(
                headers: {
                  'Authorization'=>'Bearer '+ valid_auth_token.token
                }).
                to_return(status: 200, body: [{"id": 24,"name": "Jane Lead","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                stub_request(:get, "http://localhost:8083/v1/summaries/deals?view=meeting&id=24").
                  with(
                    headers: {
                      'Authorization'=>'Bearer '+ valid_auth_token.token
                    }).
                    to_return(status: 200, body: [{"id": 24,"name": "Test Deal","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                    stub_request(:get, "http://localhost:8083/v1/summaries/contacts?view=meeting&id=24").
                      with(
                        headers: {
                          'Authorization'=>'Bearer '+ valid_auth_token.token
                        }).
                        to_return(status: 200, body: [{"id": 24,"name": "Test Contact","emails": [{"primary": true,"value": nil}]}].to_json, headers: {})

                        @meeting.related_to << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
                        @meeting.related_to << create(:company_look_up, tenant_id: @meeting.tenant_id, entity_id: 11, name: "Test Company")
                        @meeting.save
      end

      it 'removes related_to entities correctly' do
        @update_params = {}
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        @update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        @update_params[:id] = @meeting.id
        @update_params[:participants] = [
          {
            "entity": 'user',
            "id": user.id,
            "name": user.name,
            "email": '<EMAIL>'
          }
        ]
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_DEAL,
            "id": 24,
            "name": "5 year service contract"
          },
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          }
        ]

        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers
        meeting = response.parsed_body
        related_to_entities = meeting['relatedTo'].map{ |r| "#{r['entity']}_#{r['id']}"}

        expect(meeting["relatedTo"].count).to eq(2)
        expect(related_to_entities).to match_array(['deal_24', 'lead_24'])
      end

      it 'publishes event to remove related_to' do
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John User")
        @meeting.save

        @update_params = {}
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        @update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        @update_params[:id] = @meeting.id
        @update_params[:participants] = [
          {
            "entity": LOOKUP_USER,
            "id": user.id,
            "name": user.name,
            "email": "<EMAIL>"
          },
          {
            "entity": LOOKUP_USER,
            "id": 24,
            "name": "John",
            "email": "<EMAIL>"
          }
        ]
        @update_params[:relatedTo] = [
          {
            "entity": LOOKUP_LEAD,
            "id": 24,
            "name": "Jane"
          },
          {
            "entity": LOOKUP_CONTACT,
            "id": 24,
            "name": "Jane doe"
          }
        ]

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToLead)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).twice
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingRelatedToDeal)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduled)).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingScheduledWithParticipant)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        put "/v1/meetings/#{@meeting.id}", params: @update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["relatedTo"].count).to eq(2)
      end
    end

    context 'when user is not organiser and has update all permission' do
      before do
        new_params = {'title' => 'Some new title'}.merge(participants: [{id: user.id, entity: 'user', name: user.name, email: "<EMAIL>"}])
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        new_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token_2.token
            }).
            to_return(status: 200, body: [{"id": user.id,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}} ].to_json, headers: {})

            stub_request(:get, "http://localhost:8081/v1/api-keys/meeting-rsvp").
              with(
                headers: {
                  'Authorization'=>'Bearer '+ valid_auth_token_2.token
                }).
                to_return(status: 200, body: ''.to_json, headers: {})

                put "/v1/meetings/#{@meeting.id}", params: new_params.to_json, headers: headers_for_another_user
      end

      it 'should update meeting' do
        expect(json['id']).to eq(@meeting.id)
        expect(json['title']).to eq('Some new title')
        expect(json['participants'].count).to eq(1)
        expect(json['owner']['id']).to eq(user.id)
        expect(json['updatedBy']['id']).to eq(another_user.id)
        expect(json['recordActions']['read']).to be(true)
        expect(json['recordActions']['update']).to be(true)
        expect(json['status']).to eq(@meeting.status)
      end
    end

    context 'when user is not organiser and does not have update all permission' do
      before do
        new_params = {'title' => 'A very new title'}.merge(participants: [{id: user.id, entity: 'user', name: user.name, email: "<EMAIL>"}])

        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
              'Authorization' => 'Bearer '+ valid_auth_token_meeting_with_read_all_and_without_update_all_permission.token
            }).
            to_return(status: 200, body: [{"id": user.id,"name": "#{user.name}","email": {"primary": true,"value": "<EMAIL>"}} ].to_json, headers: {})

            stub_request(:get, "http://localhost:8081/v1/api-keys/meeting-rsvp").
              with(
                headers: {
                  'Authorization'=>'Bearer '+ valid_auth_token_meeting_with_read_all_and_without_update_all_permission.token
                }).
                to_return(status: 200, body: ''.to_json, headers: {})

                put "/v1/meetings/#{@meeting.id}", params: new_params.to_json, headers: headers_meeting_without_update_all_permission
      end

      it 'should raise forbidden error and return unauthorised error code' do
        expect(json['errorCode']).to eq('01501005')
      end
    end
    context "when meeting marked as conducted" do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},101").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: [{"id": user.id,"name": user.name,"email": {"primary": true,"value": "<EMAIL>"}, "entity": 'user'}, {
              "id": 101, "name": "John Doe", "entity": 'user', "email": { "primary": true, "value": "<EMAIL>"}
            }].to_json, headers: {})
      end

      it 'should mark meeting as conducted' do
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantAddedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingReScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        update_params = {
          status: CONDUCTED,
          participants: [
            {
              id: user.id,
              name: user.name,
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            },
            {
              id: 101,
              name: "John Doe",
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            }
          ]
        }
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        put "/v1/meetings/#{@meeting.id}", headers: headers, params: update_params.to_json
        expect(json['id']).to eq(@meeting.id)
        expect(json['conductedAt'].present?).to be(true)
        expect(json['conductedBy']['id']).to eq(user.id)
        expect(json['cancelledAt'].present?).to be(false)
        expect(json['cancelledBy']).to eq(nil)
        expect(json['status']).to eq(CONDUCTED)
      end
    end

    context "when meeting marked as cancelled" do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},101").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: [{"id": user.id,"name": user.name,"email": {"primary": true,"value": "<EMAIL>"}, "entity": 'user'}, {
              "id": 101, "name": "John Doe", "entity": 'user', "email": { "primary": true, "value": "<EMAIL>"}
            }].to_json, headers: {})
      end

      it 'should mark meeting as cancelled' do
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(1).times
        expect(ParticipantAddedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingReScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        update_params = {
          status: CANCELLED,
          participants: [
            {
              id: user.id,
              name: user.name,
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            },
            {
              id: 101,
              name: "John Doe",
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            }
          ]
        }
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        put "/v1/meetings/#{@meeting.id}", headers: headers, params: update_params.to_json
        expect(json['id']).to eq(@meeting.id)
        expect(json['conductedAt'].present?).to be(false)
        expect(json['conductedBy']).to eq(nil)
        expect(json['cancelledAt'].present?).to be(true)
        expect(json['cancelledBy']['id']).to eq(user.id)
        expect(json['status']).to eq(CANCELLED)
      end
    end

    context "when meeting marked as scheduled" do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},101").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
            to_return(status: 200, body: [{"id": user.id,"name": user.name,"email": {"primary": true,"value": "<EMAIL>"}, "entity": 'user'}, {
              "id": 101, "name": "John Doe", "entity": 'user', "email": { "primary": true, "value": "<EMAIL>"}
            }].to_json, headers: {})
      end

      it 'should mark meeting as scheduled' do
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantAddedEventPublisher).to receive(:call).exactly(1).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(1).times
        expect(MeetingReScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        update_params = {
          status: SCHEDULED,
          participants: [
            {
              id: user.id,
              name: user.name,
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            },
            {
              id: 101,
              name: "John Doe",
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            }
          ]
        }
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        put "/v1/meetings/#{@meeting.id}", headers: headers, params: update_params.to_json
        expect(json['id']).to eq(@meeting.id)
        expect(json['conductedAt'].present?).to be(false)
        expect(json['conductedBy']).to eq(nil)
        expect(json['cancelledAt'].present?).to be(false)
        expect(json['cancelledBy']).to eq(nil)
        expect(json['status']).to eq(SCHEDULED)
      end

      it 'should mark meeting as missed if time has passed' do
        expect(MeetingCancelledEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).with(instance_of(Meeting)).exactly(0).times
        expect(ParticipantAddedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingReScheduledEventPublisher).to receive(:call).exactly(0).times
        expect(ParticipantRemovedEventPublisher).to receive(:call).exactly(0).times
        expect(MeetingScheduledRelatedToEntityPublisher).to receive(:call).exactly(0).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).exactly(1).times

        update_params = {
          status: SCHEDULED,
          participants: [
            {
              id: user.id,
              name: user.name,
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            },
            {
              id: 101,
              name: "John Doe",
              entity: LOOKUP_USER,
              email: "<EMAIL>"
            }
          ],
          from: (DateTime.now - 1.month),
          to: (DateTime.now.utc - 1.month + 1.hour)
        }
        @timezone = create(:timezone_look_up, tenant_id: user.tenant_id)
        update_params['timezone'] = {id: @timezone.entity_id, name: @timezone.name}
        put "/v1/meetings/#{@meeting.id}", headers: headers, params: update_params.to_json
        expect(json['id']).to eq(@meeting.id)
        expect(json['conductedAt'].present?).to be(false)
        expect(json['conductedBy']).to eq(nil)
        expect(json['cancelledAt'].present?).to be(false)
        expect(json['cancelledBy']).to eq(nil)
        expect(json['status']).to eq(MISSED)
      end
    end

    context 'invalid meeting update request' do
      it 'should not update it and return error code' do
        update_params = {}
        update_params[:id] = @meeting.id
        update_params[:title] = nil
        put "/v1/meetings/#{@meeting.id}", params: update_params.to_json, headers: headers
        meeting = response.parsed_body
        expect(meeting["errorCode"]).to eq('01503001')
      end
    end

    context 'with unauthorized user' do
      it 'should return unauthorized error code' do
        meeting = create(:meeting, owner: another_user)
        put "/v1/meetings/#{meeting.id}", headers: headers_meeting_without_update_all_permission
        expect(response.parsed_body['errorCode']).to eq('01501005')
      end
    end
  end

  context "#conduct" do
    context 'when request is valid' do
      context 'when user is meeting owner' do
        before do
          @meeting = create(:meeting, owner: user)
        end

        it 'should conduct meeting' do
          post "/v1/meetings/#{@meeting.id}/conduct", headers: headers
          expect(json['id']).to eq(@meeting.id)
          expect(json['tenantId']).to eq(@meeting.tenant_id)
          expect(json['title']).to eq(@meeting.title)
          expect(json['createdBy']['id']).to eq(user.id)
          expect(json['updatedBy']['id']).to eq(user.id)
          expect(json['conductedAt'].present?).to be(true)
          expect(json['conductedBy']['id']).to eq(user.id)
          expect(json['status']).to eq(CONDUCTED)
          expect(json['recordActions']['update']).to be(true)
        end

        it 'should raise appropriate events' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          post "/v1/meetings/#{@meeting.id}/conduct", headers: headers
        end
      end

      context 'when user is not meeting owner but has read all access' do
        context 'and update all access' do
          let(:headers_with_update_all_access) {
            valid_headers(
              build(
                :auth_token,
                user_id: another_user.id,
                tenant_id: another_user.tenant_id,
                username: another_user.name,
              )
            )
          }

          before do
            @meeting = create(:meeting, owner: user)
            post "/v1/meetings/#{@meeting.id}/conduct", headers: headers_with_update_all_access
          end

          it 'should conduct meeting' do
            expect(json['id']).to eq(@meeting.id)
            expect(json['tenantId']).to eq(@meeting.tenant_id)
            expect(json['title']).to eq(@meeting.title)
            expect(json['createdBy']['id']).to eq(user.id)
            expect(json['updatedBy']['id']).to eq(another_user.id)
            expect(json['conductedAt'].present?).to be(true)
            expect(json['conductedBy']['id']).to eq(another_user.id)
            expect(json['status']).to eq(CONDUCTED)
            expect(json['recordActions']['update']).to be(true)
          end
        end

        context 'but not update all access' do
          let(:access_token) { SecureRandom.uuid }
          let(:headers_without_update_all_access) {
            valid_headers(
              build(
                :auth_token,
                user_id: another_user.id,
                tenant_id: another_user.tenant_id,
                username: another_user.name,
                expiry: (Time.current + 1.hour).to_i,
                access_token: access_token,
                token: JWT.encode(
                  {
                    iss: 'sell',
                    data: {
                      "expiresIn" => 43199,
                      "expiry" => (Time.current + 1.hour).to_i,
                      "tokenType" => "bearer",
                      "accessToken" => access_token,
                      "permissions" => [
                        {
                          "id" => 7,
                          "name" =>  "meeting",
                          "description" =>  "has access to team resource",
                          "limits" =>  -1,
                          "units" =>  "count",
                          "action" =>  {
                            "read" => true,
                            "write" => true,
                            "update" => true,
                            "delete" => false,
                            "email" => false,
                            "call" => false,
                            "sms" => false,
                            "task" => false,
                            "note" => false,
                            "readAll" => true,
                            "updateAll" => false,
                            "deleteAll" => false
                          }
                        }
                      ],
                      "userId" => another_user.id,
                      "username" => another_user.name,
                      "tenantId" => another_user.tenant_id
                    }
                  },
                  nil,
                  'none'
                )
              )
            )
          }

          before do
            @meeting = create(:meeting, owner: user)
            post "/v1/meetings/#{@meeting.id}/conduct", headers: headers_without_update_all_access
          end

          it 'should raise unauthorized error and return error code' do
            expect(json['errorCode']).to eq('01501005')
            expect(response.status).to eq(401)
          end
        end
      end
    end

    context 'when request is invalid' do
      before do
        @meeting = create(:meeting, owner: user)
        post "/v1/meetings/#{@meeting.id}/conduct", headers: header_with_invalid_token
      end

      context 'with invalid token' do
        it 'should return forbidden unauthorized error code' do
          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to eq(401)
        end
      end

      context 'with cancelled meeting' do
        before do
          @meeting.update(status: CANCELLED)
          post "/v1/meetings/#{@meeting.id}/conduct", headers: headers
        end

        it 'should return invalid data error code' do
          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to eq(422)
        end
      end
    end
  end

  context "#cancel" do
    before do
      @meeting = create(:meeting, owner: user)
      owner_lookup = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name, email:"<EMAIL>")
      @meeting.participants << owner_lookup
    end

    context 'valid meeting cancel request' do
      it 'should cancel it' do
        post "/v1/meetings/#{@meeting.id}/cancel", headers: headers
        meeting = response.parsed_body
        expect(meeting["status"]).to eq(CANCELLED)
        expect(meeting['cancelledBy']['id']).to eq(user.id)
        expect(meeting['cancelledBy']['name']).to eq(user.name)
        expect(meeting['cancelledAt'].present?).to be(true)
        expect(meeting['updatedBy']['id']).to eq(user.id)
      end
    end

    context 'meeting with participant' do
      before do
        @meeting.participants << create(:contact_look_up, tenant_id: @meeting.tenant_id, entity_id: 10, name: "John Contact")
        @meeting.participants << create(:user_look_up, tenant_id: @meeting.tenant_id, entity_id: 11, name: "John User")
        @meeting.save!
      end

      it 'should raise appropriate events' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToContact)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).thrice
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        post "/v1/meetings/#{@meeting.id}/cancel", headers: headers
        meeting = response.parsed_body
        expect(meeting["status"]).to eq(CANCELLED)
        expect(meeting['cancelledBy']['id']).to eq(user.id)
        expect(meeting['cancelledBy']['name']).to eq(user.name)
        expect(meeting['cancelledAt'].present?).to be(true)
        expect(meeting['updatedBy']['id']).to eq(user.id)
      end
    end

    context 'when user is not owner or participant and has update all access' do
      it 'should return meeting' do
        post "/v1/meetings/#{@meeting.id}/cancel", headers: headers_for_another_user
        expect(json['id']).to eq(@meeting.id)
        expect(json['status']).to eq(CANCELLED)
        expect(json['owner']['id']).to eq(user.id)
        expect(json['createdBy']['id']).to eq(user.id)
        expect(json['updatedBy']['id']).to eq(another_user.id)
        expect(json['participants'].count).to eq(2)
        expect(json['recordActions']['update']).to be(true)
      end
    end

    context 'when user is not owner or participant and does not have update all access' do
      it 'should return unauthorized error code' do
        post "/v1/meetings/#{@meeting.id}/cancel", headers: headers_meeting_without_update_all_permission
        expect(json['errorCode']).to eq('01501005')
      end
    end

    context 'invalid meeting cancel request' do
      before do
        @meeting.update(status: CANCELLED)
      end
      it 'should not cancel it' do
        post "/v1/meetings/#{@meeting.id}/cancel", headers: headers
        meeting = response.parsed_body
        expect(meeting["errorCode"]).to eq('01503001')
      end
    end

    context 'with unauthorized user' do
      it 'should return unauthorized error code' do
        meeting = create(:meeting, owner: another_user)
        post "/v1/meetings/#{meeting.id}/cancel", headers: headers_meeting_without_update_all_permission
        expect(response.parsed_body['errorCode']).to eq('01501005')
      end
    end
  end

  context "#checkin" do
    before do
      @params_hash = {
        location: "Baner, Pune",
        checkedInDetails: {
          latitude: '18.559658',
          longitude: '73.779938'
        }
      }
    end

    context 'valid request' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).once
        @meeting = create(:meeting, owner: user)
        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
        to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})
        
        post "/v1/meetings/#{@meeting.id}/checkin", headers: headers, params: @params_hash.to_json

      end

      it 'should checkin meeting' do
        expect(json).to eq({})
        expect(response.status).to eq(200)

        meeting_attendance = @meeting.reload.meeting_attendances.find_by(user_id: user.id)
        expect(meeting_attendance.checked_in?).to be(true)
        expect(meeting_attendance.checked_in_latitude).to eq(@params_hash.dig(:checkedInDetails, :latitude))
        expect(meeting_attendance.checked_in_longitude).to eq(@params_hash.dig(:checkedInDetails, :longitude))
        expect(meeting_attendance.checked_out?).to be(false)
        expect(@meeting.location).to eq(@meeting.location)
      end
    end

    context 'invalid request' do
      context 'when unauthorised user checks in' do
        before do
          meeting = create(:meeting, owner: user)
          post "/v1/meetings/#{meeting.id}/checkin", headers: headers_for_another_user, params: @params_hash.to_json
        end

        it 'should raise forbidden error and return unauthorised error code' do
          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when user checks in on already checked in meeting' do
        before do
          meeting = create(:meeting, owner: user)
          create(:meeting_checked_in, meeting: meeting)
          post "/v1/meetings/#{meeting.id}/checkin", headers: headers, params: @params_hash.to_json
        end

        it 'should raise invalid error' do
          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end
    end
  end

  context "#checkout" do
    before do
      @params_hash = {
        checkedOutDetails: {
          latitude: '18.559658',
          longitude: '73.779938'
        }
      }
    end

    context 'valid request' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedOut)).once
        @meeting = create(:meeting, owner: user)
        create(:meeting_checked_in, meeting: @meeting)

        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
        with(
          headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
          }).
          to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})

        post "/v1/meetings/#{@meeting.id}/checkout", headers: headers, params: @params_hash.to_json
      end

      it 'should check out meeting' do
        expect(json).to eq({})
        expect(response.status).to eq(200)

        meeting_attendance = @meeting.reload.meeting_attendances.find_by(user_id: user.id)
        expect(meeting_attendance.checked_in?).to be(true)
        expect(meeting_attendance.checked_out_latitude).to eq(@params_hash.dig(:checkedOutDetails, :latitude))
        expect(meeting_attendance.checked_out_longitude).to eq(@params_hash.dig(:checkedOutDetails, :longitude))
        expect(meeting_attendance.checked_out?).to be(true)
      end
    end

    context 'invalid request' do
      context 'when unauthorised user checks out' do
        before do
          meeting = create(:meeting, owner: user)
          create(:meeting_checked_in, meeting: meeting)
          post "/v1/meetings/#{meeting.id}/checkout", headers: headers_for_another_user, params: @params_hash.to_json
        end

        it 'should return unauthorised error code' do
          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when user checks out an already checked out meeting' do
        before do
          meeting = create(:meeting, owner: user)
          create(:meeting_attendance, meeting: meeting)
          post "/v1/meetings/#{meeting.id}/checkout", headers: headers, params: @params_hash.to_json
        end

        it 'should return invalid data' do
          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end
    end
  end

  context "#rsvp" do
    before do
      @meeting = create(:meeting, owner: user)
      owner = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
      @meeting.participants << owner
      @meeting.participants << participant
    end

    context 'valid meeting rsvp request' do
      it "returns 201 create with the meeting id" do
        post "/v1/meetings/#{@meeting.id}/rsvp", params: rsvp_notify_payload, headers: headers_for_another_user
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:created)
      end

      it "user RSVP is captured successfully" do
        post "/v1/meetings/#{@meeting.id}/rsvp", params: rsvp_notify_payload, headers: headers_for_another_user
        rsvp_info = participant.meeting_look_ups.where(meeting: @meeting).first
        expect(rsvp_info.rsvp_response).to eq("YES")
        expect(rsvp_info.rsvp_message).to be_falsey
      end

      it "should fire meeting rsvp updated event if notifyOrganizer is true" do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        post "/v1/meetings/#{@meeting.id}/rsvp", params: rsvp_notify_payload, headers: headers_for_another_user
      end

      it "should not fire meeting rsvp updated event if notifyOrganizer is true" do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).exactly(0).times
        post "/v1/meetings/#{@meeting.id}/rsvp", params: rsvp_no_notify_payload, headers: headers_for_another_user
      end
    end
  end

  context "#p_rsvp" do
    before do
      @meeting = create(:meeting, owner: user, tenant_id: user.tenant_id)
      @meeting.participants << participant
    end

    context 'valid public meeting rsvp request' do
      it "returns 201 create with the meeting details" do
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_notify_payload, headers: headers_for_another_user
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(:created)
        expect(JSON(response.body)).to be == MeetingSerializer.call(@meeting, @meeting.participant_look_ups[1]).result

      end

      it "user RSVP is captured successfully" do
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_notify_payload, headers: headers_for_another_user
        rsvp_info = participant.meeting_look_ups.where(meeting: @meeting).first
        expect(rsvp_info.rsvp_response).to eq("YES")
        expect(rsvp_info.rsvp_message).to be_falsey
      end

      it "should fire meeting rsvp updated event" do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_notify_payload, headers: headers_for_another_user
      end
    end

    context 'invalid public meeting rsvp request' do
      it "returns 404 when participant is invalid" do
        allow(Time).to receive(:now).and_return(Time.now.utc)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).exactly(0).times
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_invalid_participant_payload, headers: headers_for_another_user
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(404)
        expect(response.body).to be == ({"errorCode"=>"01501006", "message"=>"Participant not found.", "timestamp"=>Time.now.utc}).to_json
      end

      it "returns 422 when meeting has expired" do
        allow(Time).to receive(:now).and_return(Time.now.utc)
        @meeting.from = Time.now - 10.days
        @meeting.save(validate:false)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).exactly(0).times
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_notify_payload, headers: headers_for_another_user
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(422)
        expect(response.body).to be == ({"errorCode"=>"01503002", "message"=>"Meeting expired.", "timestamp"=>Time.now.utc}).to_json
      end

      it "returns 422 when meeting has cancelled" do
        allow(Time).to receive(:now).and_return(Time.now.utc)
        @meeting.from = Time.now + 10.days
        @meeting.to = @meeting.from + 1.hour
        @meeting.status = CANCELLED
        @meeting.save(validate:false)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingParticipantRsvpUpdated)).exactly(0).times
        post "/v1/meetings/#{@meeting.public_id}/p_rsvp", params: public_rsvp_notify_payload, headers: headers_for_another_user
        expect(response.content_type).to eq("application/json; charset=utf-8")
        expect(response).to have_http_status(422)
        expect(response.body).to be == ({"errorCode"=>"01503003", "message"=>"Meeting cancelled.", "timestamp"=>Time.now.utc}).to_json
      end
    end
  end

  context "#show" do
    context 'valid meeting show request' do
      before do
        @meeting = create(:meeting, owner: user)
        owner = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
        @meeting.participants << owner
        @meeting.participants << participant

        @meeting1 = create(:meeting, owner: user)
        owner = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
        @meeting1.participants << owner
        user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
        another_user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
      end

      context 'Success' do
        it "returns response when request is from owner of the meeting" do
          get "/v1/meetings/#{@meeting.id}", headers: headers
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end

        it "returns response when request is from participant of the meeting" do
          get "/v1/meetings/#{@meeting.id}", headers: headers_for_another_user
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:ok)
        end
      end

      context 'Failure' do
        it "returns error response when request is from non-participant of the meeting" do
          get "/v1/meetings/#{@meeting1.id}", headers: headers_meeting_without_read_all_update_all_permission
          expect(response.content_type).to eq("application/json; charset=utf-8")
          expect(response).to have_http_status(:unauthorized)
        end
      end
    end

    context 'when meeting is shared with current user' do
      before do
        @meeting = create(:meeting, owner: third_user)
        create(:share_rule, tenant_id: @meeting.tenant_id, meeting_id: @meeting.id, to_id: user.id, actions: { read: true, update: false })
        user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
      end

      it 'returns meeting with proper record actions' do
        get(
          "/v1/meetings/#{@meeting.id}",
          headers: valid_headers(
            build(
              :auth_token,
              :without_meeting_read_all_and_update_all_permission,
              user_id: user.id,
              tenant_id: user.tenant_id,
              username: user.name
            )
          )
        )

        expect(response.parsed_body['recordActions']).to eq({'read'=>true, 'update'=>false})
      end

      context 'and user has update all permission' do
        it 'returns correct record actions' do
          get(
            "/v1/meetings/#{@meeting.id}",
            headers: headers
          )

          expect(response.parsed_body['recordActions']).to eq({'read'=>true, 'update'=>true})
        end
      end
    end

    context 'when owner is not participant' do
      before do
        @meeting = create(:meeting, owner: user)
        owner = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
        participant = build(:user_look_up, tenant_id: user.tenant_id, entity: "user_#{another_user.id}")

        @meeting.participants = [participant]
        @meeting.save
        user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
      end

      it 'returns success' do
        get(
          "/v1/meetings/#{@meeting.id}",
          headers: valid_headers(
            build(
              :auth_token,
              :without_meeting_read_all_and_update_all_permission,
              user_id: user.id,
              tenant_id: user.tenant_id,
              username: user.name
            )
          )
        )
      end
    end
  end

  context "#destroy" do
    context "With Valid Permission" do
      context "When user have delete permission" do
        context "Meeting is not cancelled already" do
          before do
            @meeting = create(:meeting, owner: user)
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
            @meeting.participants << [invitee]
            @meeting.related_to << related_entity
          end

          it 'should delete the meeting' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
            expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

            delete "/v1/meetings/#{@meeting.id}", headers: headers

            expect(Meeting.count).to eq(0)
            expect(MeetingLookUp.count).to eql(0)
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
            expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

            delete "/v1/meetings/#{@meeting.id}", headers: headers

            expect(Meeting.count).to eq(0)
            expect(Note.count).to eq(0)
            expect(MeetingLookUp.count).to eql(0)
          end
        end

        context "Meeting is cancelled already" do
          before do
            @meeting = create(:meeting, owner: user)
            owner_lookup = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name, email:"<EMAIL>")
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
            @meeting.participants << [ owner_lookup, invitee]
            @meeting.related_to << related_entity
            @meeting.update(status: 'cancelled')
          end

          it 'should delete the meeting' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
            expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

            delete "/v1/meetings/#{@meeting.id}", headers: headers

            expect(Meeting.count).to eq(0)
            expect(MeetingLookUp.count).to eql(0)
          end
        end
      end

      context "When user have delete permission and its past meeting" do
        before do
          @meeting = build(:meeting, owner: user, from: DateTime.yesterday - 2.hour, to: DateTime.yesterday - 1.hour)
          @meeting.save(validate: false)

          owner_lookup = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name, email:"<EMAIL>")
          related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
          invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
          @meeting.participants << [ owner_lookup, invitee]
          @meeting.related_to << related_entity
        end

        it "should delete the meeting but shouldn't call cancel event" do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
          expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

          delete "/v1/meetings/#{@meeting.id}", headers: headers

          expect(Meeting.count).to eq(0)
          expect(MeetingLookUp.count).to eql(0)
        end
      end

      context "When user have delete all permission" do
        before do
          @meeting = create(:meeting, owner: another_user)
          related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
          invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
          @meeting.participants << [invitee]
          @meeting.related_to << related_entity
        end

        it 'should delete the meeting' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
          expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

          delete "/v1/meetings/#{@meeting.id}", headers: headers

          expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
          expect(MeetingLookUp.count).to eql(0)
        end

        it 'should delete the meeting along with notes associated' do
          @note1 = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
          @note2 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).once
          expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

          delete "/v1/meetings/#{@meeting.id}", headers: headers

          expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
          expect(Note.find_by(id: @note1.id)).to eq(nil)
          expect(Note.find_by(id: @note2.id)).to eq(nil)
          expect(MeetingLookUp.count).to eql(0)
        end
      end
    end

    context "With Invalid Permission" do
      context "When user dont have delete permission" do
        before do
          @meeting = create(:meeting, owner: user)
          related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
          invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
          @meeting.participants << [invitee]
          @meeting.related_to << related_entity
        end

        it "shouldn't delete the meeting" do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
          expect(PublishUsageJob).not_to receive(:perform_later)

          delete "/v1/meetings/#{@meeting.id}", headers: headers_without_meeting_delete_permission

          expect(Meeting.count).to eq(1)
          expect(MeetingLookUp.count).to eql(3)
        end
      end

      context "When user dont have delete all permission" do
        before do
          @meeting = create(:meeting, owner: another_user)
          related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
          invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
          @meeting.participants << [invitee]
          @meeting.related_to << related_entity
        end

        it "shouldn't delete the meeting" do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(0).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
          expect(PublishUsageJob).not_to receive(:perform_later)

          delete "/v1/meetings/#{@meeting.id}", headers: headers_without_meeting_delete_permission

          expect(Meeting.find_by(id: @meeting.id)).to eq(@meeting)
          expect(MeetingLookUp.count).to eql(3)
        end
      end
    end

    context "#bulkDelete" do
      context "With Valid Permission" do
        context "When user have delete permission" do
          context "Meeting is not cancelled already" do
            before do
              @meeting = create(:meeting, owner: user)
              related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
              invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
              @meeting.participants << [invitee]
              @meeting.related_to << related_entity

              @meeting_2 = create(:meeting, owner: user)
              related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
              invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
              @meeting_2.participants << [invitee]
              @meeting_2.related_to << related_entity

              @meeting_params = { ids: [@meeting.id, @meeting_2.id] }.to_json
            end

            it 'should delete the meetings' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
              expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

              delete "/v1/meetings/delete", headers: headers, params: @meeting_params

              expect(Meeting.count).to eq(0)
              expect(MeetingLookUp.count).to eq(0)

              expect(json['response']).to match_array([
                {"entityId" => @meeting.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id},
                {"entityId" => @meeting_2.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id}
              ])
              expect(json['successCount']).to eq 2
              expect(json['errorCount']).to eq 0
            end

            it 'should delete the meeting along with notes associated' do
              @note1 = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
              @note2 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
              expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

              delete "/v1/meetings/delete", params: @meeting_params, headers: headers

              expect(Meeting.count).to eq(0)
              expect(Note.count).to eq(0)
              expect(MeetingLookUp.count).to eq(0)

              expect(json['response']).to match_array([
                {"entityId" => @meeting.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id},
                {"entityId" => @meeting_2.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id}
              ])
              expect(json['successCount']).to eq 2
              expect(json['errorCount']).to eq 0
            end
          end

          context "One of the meeting is cancelled already" do
            before do
              @meeting = create(:meeting, owner: user)
              owner_lookup = build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name, email:"<EMAIL>")
              related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
              invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
              @meeting.participants << [ owner_lookup, invitee]
              @meeting.related_to << related_entity
              @meeting.update(status: 'cancelled')

              @meeting_2 = create(:meeting, owner: user)
              related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
              invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
              @meeting_2.participants << [invitee]
              @meeting_2.related_to << related_entity

              @meeting_params = { ids: [@meeting.id, @meeting_2.id] }.to_json
            end

            it 'should delete the meeting' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).twice
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
              expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

              delete "/v1/meetings/delete", params: @meeting_params, headers: headers

              expect(Meeting.count).to eq(0)
              expect(MeetingLookUp.count).to eq(0)

              expect(json['response']).to match_array([
                {"entityId" => @meeting.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id},
                {"entityId" => @meeting_2.id, "ownerId" => user.id, "result" => "success", "tenantId" => user.tenant_id}
              ])
              expect(json['successCount']).to eq 2
              expect(json['errorCount']).to eq 0
            end
          end
        end

        context "When user have delete all permission" do
          before do
            @meeting = create(:meeting, owner: another_user)
            owner_lookup = build(:user_look_up, entity_id: another_user.id, tenant_id: another_user.tenant_id, name: another_user.name, email:"<EMAIL>")
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
            @meeting.participants << [ invitee]
            @meeting.related_to << related_entity

            @meeting_2 = create(:meeting, owner: another_user)
            owner_lookup = build(:user_look_up, entity_id: another_user.id, tenant_id: another_user.tenant_id, name: another_user.name, email:"<EMAIL>")
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
            @meeting_2.participants << [ invitee]
            @meeting_2.related_to << related_entity

            @meeting_params = { ids: [@meeting.id, @meeting_2.id] }.to_json
          end

          it 'should delete the meeting' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishUsageJob).to receive(:perform_later).with(another_user.tenant_id).once

            delete "/v1/meetings/delete", params: @meeting_params, headers: headers

            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eq(0)
            expect(json['response']).to match_array([
              {"entityId" => @meeting.id, "ownerId" => another_user.id, "result" => "success", "tenantId" => user.tenant_id},
              {"entityId" => @meeting_2.id, "ownerId" => another_user.id, "result" => "success", "tenantId" => user.tenant_id}
            ])
            expect(json['successCount']).to eq 2
            expect(json['errorCount']).to eq 0
          end

          it 'should delete the meeting along with notes associated' do
            @note1 = create(:note, created_by_id: user.id, meeting_id: @meeting.id)
            @note2 = create(:note, created_by_id: another_user.id, meeting_id: @meeting.id)

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(2).times
            expect(PublishUsageJob).to receive(:perform_later).with(another_user.tenant_id).once

            delete "/v1/meetings/delete", params: @meeting_params, headers: headers

            expect(Meeting.find_by(id: @meeting.id)).to eq(nil)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(nil)
            expect(Note.find_by(id: @note1.id)).to eq(nil)
            expect(Note.find_by(id: @note2.id)).to eq(nil)
            expect(MeetingLookUp.count).to eq(0)

            parsed_json = JSON.parse(response.body)
            expect(parsed_json['response']).to match_array([
              { entityId: @meeting.id, ownerId: another_user.id, result: "success", tenantId: user.tenant_id }.as_json,
              { entityId: @meeting_2.id, ownerId: another_user.id, result: "success", tenantId: user.tenant_id }.as_json
            ])
            expect(parsed_json['successCount']).to eq(2)
            expect(parsed_json['errorCount']).to eq(0)
          end
        end
      end
      context "With Invalid Permission" do
        context "When user dont have delete permission" do
          before do
            @meeting = create(:meeting, owner: user)
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
            @meeting.participants << [ invitee]
            @meeting.related_to << related_entity

            @meeting_2 = create(:meeting, owner: user)
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: user.tenant_id, name: 'invitee')
            @meeting_2.participants << [ invitee]
            @meeting_2.related_to << related_entity

            @meeting_params = { ids: [@meeting.id, @meeting_2.id] }.to_json
          end

          it "shouldn't delete the meeting" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
            expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

            delete "/v1/meetings/delete", params: @meeting_params, headers: headers_without_meeting_delete_permission

            expect(Meeting.count).to eq(2)
            expect(MeetingLookUp.count).to eq(6)

            expect(json['response']).to match_array([
              {"entityId" => @meeting.id, "ownerId" => user.id, "result" => "error", "tenantId" => user.tenant_id},
              {"entityId" => @meeting_2.id, "ownerId" => user.id, "result" => "error", "tenantId" => user.tenant_id}
            ])
            expect(json['successCount']).to eq 0
            expect(json['errorCount']).to eq 2
          end
        end

        context "When user dont have delete all permission" do
          before do
            @meeting = create(:meeting, owner: another_user)
            owner_lookup = build(:user_look_up, entity_id: another_user.id, tenant_id: another_user.tenant_id, name: another_user.name, email:"<EMAIL>")
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
            @meeting.participants << [ invitee]
            @meeting.related_to << related_entity

            @meeting_2 = create(:meeting, owner: another_user)
            owner_lookup = build(:user_look_up, entity_id: another_user.id, tenant_id: another_user.tenant_id, name: another_user.name, email:"<EMAIL>")
            related_entity = build(:deal_look_up, entity_id: 12, tenant_id: another_user.tenant_id, name: "John Deal")
            invitee = build(:user_look_up, entity_id: 15, tenant_id: another_user.tenant_id, name: 'invitee')
            @meeting_2.participants << [ invitee]
            @meeting_2.related_to << related_entity

            @meeting_params = { ids: [@meeting.id, @meeting_2.id] }.to_json
          end

          it "shouldn't delete the meeting" do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUnrelatedToDeal)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelled)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCancelledWithParticipant)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata)).exactly(0).times
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingDeleted)).exactly(0).times
            expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id).once

            delete "/v1/meetings/delete", params: @meeting_params, headers: headers_without_meeting_delete_permission

            expect(Meeting.find_by(id: @meeting.id)).to eq(@meeting)
            expect(Meeting.find_by(id: @meeting_2.id)).to eq(@meeting_2)
            expect(MeetingLookUp.count).to eq(6)
            expect(json['response']).to match_array([
              {"entityId" => @meeting.id, "ownerId" => another_user.id, "result" => "error", "tenantId" => user.tenant_id},
              {"entityId" => @meeting_2.id, "ownerId" => another_user.id, "result" => "error", "tenantId" => user.tenant_id}
            ])
            expect(json['successCount']).to eq 0
            expect(json['errorCount']).to eq 2
          end
        end
      end
    end
  end

  context "#external-lookup" do
    let(:query_params) { "q=test" }

    context 'When request is invalid' do
      before { get '/v1/meetings/external-lookup?' + query_params, headers: header_with_invalid_token }
      it 'returns a failure message' do
        expect(json['errorCode']).to match(ErrorCode.unauthorized)
      end
    end

    context 'valid request' do
      before do
        create(:look_up, entity: LOOKUP_EXTERNAL, email: '<EMAIL>', tenant_id: user.tenant_id)
      end

      before { get '/v1/meetings/external-lookup?' + query_params, headers: headers }

      it 'returns matched lookups' do
        expect(json.size).to eq 1
        result = json[0]
        expect(result["email"]).to eq '<EMAIL>'
      end
    end
  end

  context '#lookup' do
    let(:request_url) { get "/v1/meetings/lookup#{query_params}", headers: request_headers }

    context 'when valid request' do
      let(:request_headers) { headers }

      context 'when query is present' do
        let(:query_params) { '?q=defg' }

        before do
          create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
          @meetings = create_list(:meeting, 5, title: 'ABCDEFGHIJ', tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)
        end

        it 'returns first 5 matching meetings' do
          request_url

          expect(json['content'].map{ |meeting| meeting['id'] }).to match_array(@meetings.map(&:id))
          expect(json['content'].map { |meeting| meeting['name'] }).to match_array(@meetings.map(&:title))
          expect(json['content'].first).to eq({ 'id' => @meetings.first.id, 'name' => @meetings.first.title })
          expect(json['page']).to eq({ 'no' => 1, 'size' => 5 })
          expect(json['totalElements']).to eq(5)
        end

        context 'when view is checkin' do
          let(:query_params) { '?q=user&view=checkin&timezone=Asia/Calcutta' }

          before do
            create(:meeting, owner_id: user.id, title: 'Meeting user dint checkin', tenant_id: user.tenant_id)
            create(:meeting, owner_id: another_user.id, title: 'Meeting user not invited', tenant_id: another_user.id)

            @meeting_checked_in_by_user = create(:meeting, owner_id: user.id, title: 'Checked out By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_user.id, user_id: user.id)

            @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
            participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
            @meeting_checked_in_by_other_user.participants << participant
            @meeting_checked_in_by_other_user.save!
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)
            user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
          end

          it 'returns meetings eligible for checkin' do
            request_url
            meeting = Meeting.find(response.parsed_body['content'][0]['id'])
            expect(response.parsed_body['totalElements']).to eq(3)
            expect(response.parsed_body['content'].map { |meeting| meeting['name'] }).to match_array(['Meeting user dint checkin', 'Checked In By Another User', 'Checked out By User'])
            expect(response.parsed_body['content'][0]['geofenceConfig']).to eq({"fieldSalesEnabled"=>false, "meetingCheckInCheckOut"=>nil})
            expect(response.parsed_body['content'][0]['locationCoordinate']).to eq({"lat"=> meeting.location_latitude.to_f, "lon"=> meeting.location_longitude.to_f})
          end
        end
      end

      context 'when query is blank' do
        let(:query_params){ nil }

        before do
          @meetings = create_list(:meeting, 5, tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
          create_list(:meeting, 5, title: 'ABCDEFGHIJ', tenant_id: user.tenant_id, owner: user, from: 10.minutes.ago)
        end

        it 'returns top 5 meetings accessible to user' do
          request_url

          expect(json['content'].map{ |meeting| meeting['id'] }).to match_array(@meetings.map(&:id))
          expect(json['content'].map { |meeting| meeting['name'] }).to match_array(@meetings.map(&:title))
          expect(json['content'].first).to eq({ 'id' => @meetings.first.id, 'name' => @meetings.first.title })
          expect(json['page']).to eq({ 'no' => 1, 'size' => 5 })
          expect(json['totalElements']).to eq(10)
        end

        context 'when view is checkin' do
          let(:query_params) { '?&view=checkin&timezone=Asia/Calcutta' }

          before do
            create(:meeting, owner_id: user.id, title: 'Meeting user dint checkin', tenant_id: user.tenant_id)
            create(:meeting, owner_id: another_user.id, title: 'Meeting user not invited', tenant_id: another_user.id)

            @meeting_checked_in_by_user = create(:meeting, owner_id: user.id, title: 'Checked in By User', tenant_id: user.tenant_id)
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_user.id, user_id: user.id)

            @meeting_checked_in_by_other_user = create(:meeting, owner_id: user.id, title: 'Checked In By Another User', tenant_id: user.tenant_id)
            participant = build(:user_look_up, tenant_id: user.tenant_id, entity_id: another_user.id, name: another_user.name)
            @meeting_checked_in_by_other_user.participants << participant
            @meeting_checked_in_by_other_user.save!
            create(:meeting_attendance, meeting_id: @meeting_checked_in_by_other_user.id, user_id: another_user.id)

            stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
            with(
              headers: {
                'Authorization'=>'Bearer '+ valid_auth_token.token
              }).
            to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})
          end

          it 'returns meetings eligible for checkin' do
            request_url

            expect(response.parsed_body['totalElements']).to eq(13)
          end
        end
      end
    end

    context 'when invalid request' do
      let(:request_headers) { invalid_headers }
      let(:query_params) { '?q=defg' }

      it 'returns error' do
        request_url

        expect(json['errorCode']).to eq('01501005')
        expect(json['message']).to eq('Unauthorized access.')
      end
    end
  end

  context '#import' do
    before do
      other_field = create(:custom_field, tenant_id: user.tenant_id, display_name: 'Meeting Custom', field_type: 'PICK_LIST')
      picklist = create(:picklist, tenant_id: user.tenant_id, internal_name: other_field.internal_name, field: other_field)
      create(:picklist_value, tenant_id: user.tenant_id, internal_name: 'chcek2', picklist: picklist, id: 325305)

      allow(Time).to receive(:now).and_return(Time.now.utc)

      stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage").
        with(headers: { 'Authorization' => "Bearer #{valid_auth_token.token}" }).
        to_return(status: 200, body: { records: { used: 50, total: 100 } }.to_json)
    end

    context 'when request is invalid' do
      it 'raises unauthorized user error' do
        post '/v1/meetings/import', params: meeting_to_import, headers: header_with_invalid_token
        expect(JSON(json)).to eq({
          errorCode: '01501005',
          message: 'Unauthorized access.',
          timestamp: Time.now
        }.to_json)
      end
    end

    context 'when meeting is not offline' do
      it 'returns invalid meeting medium error' do
        meeting_to_import['medium'] = GOOGLE_PROVIDER
        post '/v1/meetings/import', params: meeting_to_import, headers: headers
        expect(JSON(json)).to eq({
          errorCode: '01503001',
          message: 'Invalid meeting medium.',
          timestamp: Time.now
        }.to_json)
      end
    end

    context 'when email is invalid' do
      context 'and that email is from relatedToLeads' do
        it 'returns invalid email error for lead' do
          invalid_lead_email = JSON.parse(meeting_to_import)
          invalid_lead_email['meeting']['relatedToLeads'] = %w[<EMAIL> john]
          post '/v1/meetings/import', params: invalid_lead_email.to_json, headers: headers
          expect(JSON(json)).to eq({
            errorCode: '01503001',
            message: "Invalid email - 'john' in relatedToLeads.",
            timestamp: Time.now
          }.to_json)
        end
      end

      context 'and that email is from relatedToContacts' do
        it 'returns invalid email error for contact' do
          invalid_contact_email = JSON.parse(meeting_to_import)
          invalid_contact_email['meeting']['relatedToContacts'] = %w[<EMAIL> john]
          post '/v1/meetings/import', params: invalid_contact_email.to_json, headers: headers
          expect(JSON(json)).to eq({
            errorCode: '01503001',
            message: "Invalid email - 'john' in relatedToContacts.",
            timestamp: Time.now
          }.to_json)
        end
      end

      context 'and that email is from relatedToCompanies' do
        it 'returns invalid email error for company' do
          invalid_company_email = JSON.parse(meeting_to_import)
          invalid_company_email['meeting']['relatedToCompanies'] = %w[<EMAIL> john]
          post '/v1/meetings/import', params: invalid_company_email.to_json, headers: headers
          expect(JSON(json)).to eq({
            errorCode: '01503001',
            message: "Invalid email - 'john' in relatedToCompanies.",
            timestamp: Time.now
          }.to_json)
        end
      end

      context 'and that email is from participants' do
        it 'returns invalid email error for participant' do
          invalid_participant_email = JSON.parse(meeting_to_import)
          invalid_participant_email['meeting']['participants'] = %w[<EMAIL> john]
          post '/v1/meetings/import', params: invalid_participant_email.to_json, headers: headers
          expect(JSON(json)).to eq({
            errorCode: '01503001',
            message: "Invalid email - 'john' in participants.",
            timestamp: Time.now
          }.to_json)
        end
      end

      context 'and that email is of owner' do
        it 'returns invalid email error for owner' do
          invalid_owner_email = JSON.parse(meeting_to_import)
          invalid_owner_email['meeting']['owner'] = 'john'
          post '/v1/meetings/import', params: invalid_owner_email.to_json, headers: headers
          expect(JSON(json)).to eq({
            errorCode: '01503001',
            message: "Invalid email - 'john' in owner.",
            timestamp: Time.now
          }.to_json)
        end
      end
    end

    context 'when no lead found for given email in realtedToLeads' do
      it 'returns invalid email with error code' do
        lead_request_body = {
          fields: %w[id firstName lastName emails ownerId],
          jsonRule: {
            rules: [
              {
                id: 'multi_field',
                field: 'multi_field',
                type: 'multi_field',
                input: 'multi_field',
                operator: 'multi_field',
                value: '<EMAIL>'
              },
              {
                id: 'multi_field',
                field: 'multi_field',
                type: 'multi_field',
                input: 'multi_field',
                operator: 'multi_field',
                value: '<EMAIL>'
              },
              {
                id: 'multi_field',
                field: 'multi_field',
                type: 'multi_field',
                input: 'multi_field',
                operator: 'multi_field',
                value: '<EMAIL>'
              }
            ],
            condition: 'OR',
            valid: true
          }
        }

        lead_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'lead',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'lead',
              id: 2,
              ownerId: 234
            },
          ]
        }

        stub_request(:post, "#{SERVICE_SEARCH}/v1/search/lead?page=0&size=100&sort=updatedAt,desc").
          with(
            body: lead_request_body,
            headers: {
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: lead_response.to_json, headers: {})

        invalid_lead_email = JSON.parse(meeting_to_import)
        invalid_lead_email['meeting']['relatedToLeads'] << '<EMAIL>'
        post '/v1/meetings/import', params: invalid_lead_email.to_json, headers: headers
        expect(JSON(json)).to eq({
          errorCode: '01503001',
          message: "lead with emails '<EMAIL>' not found.",
          timestamp: Time.now.utc
        }.to_json)
      end
    end

    context 'when no contact found for given email in realtedToContacts' do
      it 'returns invalid email with error code' do
        lead_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'lead',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'lead',
              id: 2,
              ownerId: 234
            },
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

        contact_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'contact',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'contact',
              id: 2,
              ownerId: 234
            },
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

        invalid_contact_email = JSON.parse(meeting_to_import)
        invalid_contact_email['meeting']['relatedToContacts'] << '<EMAIL>'
        post '/v1/meetings/import', params: invalid_contact_email.to_json, headers: headers
        expect(JSON(json)).to eq({
          errorCode: '01503001',
          message: "contact with emails '<EMAIL>' not found.",
          timestamp: Time.now.utc
        }.to_json)
      end
    end

    context 'when no company found for given email in realtedToCompanies' do
      it 'returns invalid email with error code' do
        lead_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'lead',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'lead',
              id: 2,
              ownerId: 234
            },
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

        contact_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'contact',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'contact',
              id: 2,
              ownerId: 234
            },
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

        company_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'company',
              id: 1,
              ownerId: 123
            },
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'tony',
              lastName: 'company',
              id: 2,
              ownerId: 234
            },
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL> <EMAIL>], entity_type: LOOKUP_COMPANY, response: company_response)

        invalid_company_email = JSON.parse(meeting_to_import)
        invalid_company_email['meeting']['relatedToCompanies'] << '<EMAIL>'
        post '/v1/meetings/import', params: invalid_company_email.to_json, headers: headers
        expect(JSON(json)).to eq({
          errorCode: '01503001',
          message: "company with emails '<EMAIL>' not found.",
          timestamp: Time.now.utc
        }.to_json)
      end
    end

    context 'when replacing participants' do
      it 'entities are searched in sequence of user, contact and lead and events are published' do
        stub_lead_search_request
        stub_contact_search_request
        stub_company_search_request

        user_rule = { emailIds: ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']}
        user_response = [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'user',
              id: 1
            }
          ]
        
        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
          with(
            body: user_rule.to_json,
          headers: {
            'Authorization': "Bearer #{valid_auth_token.token}",
            'Content-Type': 'application/json',
          }
        ).to_return(status: 200, body: user_response.to_json, headers: {})

        contact_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'contact',
              id: 1,
              ownerId: 123
            }
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

        lead_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'lead',
              id: 1,
              ownerId: 123
            }
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)

        user_rule = { emailIds: ['<EMAIL>'] }

        user_response = [
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'user',
              id: 1
          ]

        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
          with(
            body: user_rule,
          headers: {
            'Authorization': "Bearer #{valid_auth_token.token}"
          }
        ).to_return(status: 200, body: user_response.to_json, headers: {})

        post '/v1/meetings/import', params: meeting_to_import, headers: headers

        expect(json).to eq({
          "importStatus"=>"CREATED",
          "meetingId"=>Meeting.last.id,
          "importMessage"=>"Meeting created successfully"
        })
      end
    end

    context 'when validating owner' do
      before do
        stub_lead_search_request
        stub_contact_search_request
        stub_company_search_request

        user_rule = {emailIds: ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']}
        user_response = [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'user',
              id: 1
            }
          ]

        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
          with(
            body: user_rule.to_json,
          headers: {
            'Authorization': "Bearer #{valid_auth_token.token}",
            'Content-Type': 'application/json',
          }).
        to_return(status: 200, body: user_response.to_json, headers: {})

        contact_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'contact',
              id: 1,
              ownerId: 123
            }
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL> <EMAIL>], entity_type: LOOKUP_CONTACT, response: contact_response)

        lead_response = {
          content: [
            {
              emails: [
                { type: 'OFFICE', value: '<EMAIL>', primary:true }
              ],
              firstName: 'john',
              lastName: 'lead',
              id: 1,
              ownerId: 123
            }
          ]
        }
        stub_entity_search_request(emails: %w[<EMAIL> <EMAIL>], entity_type: LOOKUP_LEAD, response: lead_response)
      end

      context 'if owner email is invalid' do
        it 'raises invalid email error' do
          user_rule = {emailIds: ['<EMAIL>']}

          user_response = []

          stub_request(:post, "#{SERVICE_IAM}/v1/users/search-by-email").
            with(
              body: user_rule.to_json,
            headers: {
              'Authorization': "Bearer #{valid_auth_token.token}",
              'Content-Type': 'application/json',
            }
          ).to_return(status: 200, body: user_response.to_json, headers: {})

          post '/v1/meetings/import', params: meeting_to_import, headers: headers
          expect(JSON(json)).to eq({
            "errorCode"=>"01503001",
            "message"=>"Owner user with emails '<EMAIL>' not found.",
            "timestamp"=> Time.now.utc
          }.to_json)
        end
      end

      context 'if owner email is not present' do
        it 'adds current user as owner' do
          meeting_without_owner = JSON.parse(meeting_to_import)
          meeting_without_owner['meeting']['owner'] = nil

          post '/v1/meetings/import', params: meeting_without_owner.to_json, headers: headers
          expect(json).to eq({
            "importStatus"=>"CREATED",
            "meetingId"=>Meeting.last.id,
            "importMessage"=>"Meeting created successfully"
          })
          expect(Meeting.last.owner_id).to eq(user.id)
        end
      end
    end
  end

  context '#bulk_checkout' do
    context 'when invalid request' do
      let(:request_headers) { invalid_headers }

      it 'returns error' do
        post '/v1/meetings/bulk-checkout', headers: request_headers, params: {}

        expect(json['errorCode']).to eq('01501005')
        expect(json['message']).to eq('Unauthorized access.')
      end
    end

    context 'with valid headers' do
      let(:request_headers) { headers }

      before do
        @meeting_with_other_owner = create(:meeting, tenant_id: user.tenant_id, title: 'Meeting with other owner')
        @meeting_without_attendance = create(:meeting, tenant_id: user.tenant_id, owner: user, title: 'Meeting without checkin')
        @already_checkedout_meeting = create(:meeting, tenant_id: user.tenant_id, owner: user, title: 'Checkedout Meeting')
        create(:meeting_attendance, meeting: @already_checkedout_meeting)
        @previously_checked_in_meeting = create(:meeting, tenant_id: user.tenant_id, owner: user)
        create(:meeting_checked_in, meeting: @previously_checked_in_meeting)

        stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
      with(
        headers: {
          'Authorization'=>'Bearer '+ valid_auth_token.token
        }).
        to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})
      end

      it 'returns failed and passed meeting updates' do
        params = {
          'latitude': 18.581730,
          'longitude': 73.760761,
          'time': '2023-12-22T09:59:06.409Z',
          'meetingIds': [
            404,
            @meeting_with_other_owner.id,
            @meeting_without_attendance.id,
            @already_checkedout_meeting.id,
            @previously_checked_in_meeting.id
          ]
        }.to_json

        post '/v1/meetings/bulk-checkout', headers: request_headers, params: params

        expect(response.parsed_body['successful_meetings']).to match_array([
          {
            'id' => @previously_checked_in_meeting.id,
            'name' => @previously_checked_in_meeting.title
          }
        ])

        expect(response.parsed_body['invalid_meetings']).to match_array([
          {
            'id' => 404,
            'name' => nil,
            'message' => 'Meeting not found.'
          },
          {
            'id' => @meeting_with_other_owner.id,
            'name' => 'Meeting with other owner',
            'message' => 'you do not have access to checkout this meeting'
          },
          {
            'id' => @meeting_without_attendance.id,
            'name' => 'Meeting without checkin',
            'message' => 'Cannot checkout without checking in.'
          },
          {
            'id' => @already_checkedout_meeting.id,
            'name' => 'Checkedout Meeting',
            'message' => 'Invalid meeting action.'
          }
        ])
      end
    end
  end

  context '#access' do
    let(:basic_auth_token) do
      build(
        :auth_token,
        :without_meeting_read_all_and_update_all_permission,
        user_id: user.id,
        tenant_id: user.tenant_id,
        username: user.name
      )
    end
    let(:access_meeting) { create(:meeting, tenant_id: user.tenant_id, owner: user) }
    let(:shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: another_user) }
    let(:another_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }
    let(:shared_meeting2) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }
    let(:un_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: another_user) }
    let(:another_un_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }

    before do
      team = Team.create(name: 'team 1', tenant_id: user.tenant_id, user_ids: [user.id])
      access_meeting.participants = [participant]
      access_meeting.save
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: another_user.id,
        to_id: user.id,
        meeting_id: shared_meeting.id
      )
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: third_user.id,
        to_id: user.id,
        meeting_id: another_shared_meeting.id
      )
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: third_user.id,
        to_type: 'TEAM',
        to_id: team.id,
        meeting_id: shared_meeting2.id
      )

      [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
        stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
          .with(
            headers: {
              Authorization: "Bearer #{basic_auth_token.token}"
            }
          )
          .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
      end
    end

    it 'returns read accessible meetings' do
      post(
          "/v1/meetings/access/",
          params: [access_meeting.id, shared_meeting.id, another_shared_meeting.id, shared_meeting2.id, un_shared_meeting.id, another_un_shared_meeting.id].to_json,
          headers: valid_headers(basic_auth_token)
        )

      expect(response.status).to eq(200)
      expect(response.parsed_body['entityIds']).to match_array([
        access_meeting.id,
        shared_meeting.id,
        another_shared_meeting.id,
        shared_meeting2.id
      ])
    end
  end

  context '#access_related_to_entity' do
    let(:basic_auth_token) do
      build(
        :auth_token,
        :without_meeting_read_all_and_update_all_permission,
        user_id: user.id,
        tenant_id: user.tenant_id,
        username: user.name
      )
    end
    let(:access_meeting) { create(:meeting, tenant_id: user.tenant_id, owner: user) }
    let(:shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: another_user) }
    let(:another_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }
    let(:shared_meeting2) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }
    let(:un_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: another_user) }
    let(:another_un_shared_meeting) { create(:meeting, tenant_id: another_user.tenant_id, owner: third_user) }
    let(:look_up_lead) { create(:look_up, tenant_id: user.tenant_id, owner_id: user.id, entity_type: LOOKUP_LEAD, entity_id: 123) }

    before do
      team = Team.create(name: 'team 1', tenant_id: user.tenant_id, user_ids: [user.id])
      access_meeting.participants = [participant]
      access_meeting.related_to = [look_up_lead]
      access_meeting.save
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: another_user.id,
        to_id: user.id,
        meeting_id: shared_meeting.id
      )
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: third_user.id,
        to_id: user.id,
        meeting_id: another_shared_meeting.id
      )
      create(
        :share_rule,
        tenant_id: another_user.tenant_id,
        from_id: third_user.id,
        to_type: 'TEAM',
        to_id: team.id,
        meeting_id: shared_meeting2.id
      )

      [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].each do |entity|
        stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/MEETING")
          .with(
            headers: {
              Authorization: "Bearer #{basic_auth_token.token}"
            }
          )
          .to_return(status: 200, body: { accessByOwners: { "1001" => {} }, accessByRecords: { "123" => { read: true, meeting: true } } }.to_json)
      end
    end

    it 'returns read accessible meetings' do
      get(
          "/v1/meetings/access?relatedToEntityId=123&relatedToEntityType=lead",
          headers: valid_headers(basic_auth_token)
        )

      expect(response.status).to eq(200)
      expect(response.parsed_body['entityIds']).to match_array([
        access_meeting.id
      ])
    end
  end

  private

  def response_body
    response.parsed_body["content"]
  end

  def create_meetings(number_of_meetings)
    participant = build(:user_look_up)
    participant.name = user.name
    participant.tenant_id = user.tenant_id
    participant.entity = "user_#{user.id}"
    meetings = create_list(:meeting, number_of_meetings, tenant_id: user.tenant_id, owner: another_user)

    meetings.each_with_index do |m, index|
      m.participants << participant
      m.created_at = (10 - index).days.ago
      m.from = (5 + index).hours.from_now.strftime("%Y-%m-%d %H:%M")
      m.to = (6 + index).hours.from_now.strftime("%Y-%m-%d %H:%M")
      m.location = "meeting location " + (index + 1).to_s
      m.save!
    end
    meetings
  end
end
