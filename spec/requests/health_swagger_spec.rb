require 'swagger_helper'

RSpec.describe 'Health API', type: :request do

  path '/v48348807127D18DF/meetings/health' do
    get 'meeting from database' do
      tags 'Meetings'

      let(:meeting) { create(:meeting) }

      before do
        expect(ENV).to receive(:[]).with('TENANT_ID').and_return(meeting.tenant_id)
      end


      response '200', 'Database is up' do
        run_test!
      end

      response '404', 'Entity not present' do
        before { Meeting.delete_all }

        run_test!
      end

      response '503', 'Database is down' do
        before { expect(Meeting).to receive(:find_by).and_raise(PG::ConnectionBad) }

        run_test!
      end
    end
  end
end
