# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Webhooks', type: :request do
  describe '#microsoft' do
    context 'when requested' do
      it 'successfully returns plain text with validation parameters' do
        post '/v1/meetings/webhooks/microsoft?validationToken=SomeValidationToken'

        expect(response.code).to eq('200')
        expect(response.body).to eq('SomeValidationToken')
        expect(response.headers['Content-Type']).to eq('text/plain; charset=utf-8')
      end
    end
  end
end
