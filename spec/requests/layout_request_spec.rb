require 'rails_helper'

RSpec.describe "Layout", type: :request do
  let!(:user)             { create(:user)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let!(:headers)          { valid_headers(valid_auth_token) }

  describe '#index' do
    context 'when user is authorized' do
      before do
        thread = Thread.current
        thread[:auth] = auth_data
        CreateMeetingFieldsForTenant.call(auth_data.tenant_id, user.id)
      end

      context 'with create view name' do
        let(:url) { '/v1/meetings/layout?view=create' }

        it 'should return create view layout' do
          get url, headers: headers

          expect(json.keys).to eql(['layoutItems'])
          expect(json['layoutItems'].map { |section| section['item']['heading'] }).to eql(['Basic Info'])

          layout_items = json['layoutItems'].first['layoutItems']

          expect(layout_items.count).to be >= 13
          expect(layout_items.map do |item|
                   item['item']['internalName']
                 end.first(15)).to eql(%w[title allDay from to timezone status owner organizer participants relatedTo medium
                                          providerLink location description locationCoordinate])
          expect(layout_items.map do |item|
                   item['item']['type']
                 end.first(15)).to eql(%w[TEXT_FIELD TOGGLE DATETIME_PICKER DATETIME_PICKER PICK_LIST PICK_LIST LOOK_UP MEETING_ORGANIZER MEETING_INVITEES ENTITY_LOOKUP PICK_LIST URL TEXT_FIELD RICH_TEXT GPS_COORDINATES])

          sortable = layout_items.select { |item| item['item']['type'] == 'LOOK_UP' }.map { |item| item['item']['sortable'] }
          expect(sortable.uniq).to eql([false]) if sortable.present?

          related_entities = layout_items.select { |item| item['item']['type'] == 'ENTITY_LOOKUP' }.first['item']['pickLists']
          expect(related_entities.count).to eql(4)
          expect(related_entities.map { |item| item['lookupUrl'] }).to match_array([RELATED_LEAD_LOOKUP_URL, RELATED_DEAL_LOOKUP_URL, RELATED_CONTACT_LOOKUP_URL, RELATED_COMPANY_LOOKUP_URL])
          expect(related_entities.map { |item| item['displayName'] }).to match_array(["LEAD", "DEAL", "CONTACT", "COMPANY"])

          timezones = layout_items.select { |item| item['item']['internalName'] == 'timezone' }.first['item']['pickLists']
          expect(timezones.count).to be >= 435
          expect(timezones.map { |timezone| timezone['systemDefault'] }.count).to eql(435)
        end
      end

      context 'with edit view name' do
        let(:url) { '/v1/meetings/layout?view=edit' }

        it 'should return edit view layout' do
          get url, headers: headers
          expect(json.keys).to eql(['layoutItems'])
          expect(json['layoutItems'].map { |section| section['item']['heading'] }).to eql(['Basic Info', 'Internals', 'Check In & Check Out Internals'])

          basic_info_layout_items = json['layoutItems'].first['layoutItems']
          expect(basic_info_layout_items.count).to be >= 13
          expect(basic_info_layout_items.map do |item|
                   item['item']['internalName']
                 end.first(14)).to eql(%w[title allDay from to timezone status owner organizer participants
                                          relatedTo medium providerLink location description])
          expect(basic_info_layout_items.map do |item|
                   item['item']['type']
                 end.first(14)).to eql(%w[TEXT_FIELD TOGGLE DATETIME_PICKER DATETIME_PICKER PICK_LIST
                                          PICK_LIST LOOK_UP MEETING_ORGANIZER MEETING_INVITEES ENTITY_LOOKUP PICK_LIST URL
                                          TEXT_FIELD RICH_TEXT])

          sortable = basic_info_layout_items.select do |item|
                       item['item']['type'] == 'LOOK_UP'
                     end.map { |item| item['item']['sortable'] }
          expect(sortable.uniq).to eql([false]) if sortable.present?

          status_entities = basic_info_layout_items.select { |item| item['item']['type'] == 'PICK_LIST' && item['item']['internalName'] == 'status' }.first['item']['pickLists']
          expect(status_entities.count).to eql(4)
          expect(status_entities.map { |item| item['displayName'] }).to match_array(["Scheduled", "Missed", "Conducted", "Cancelled"])
          expect(status_entities.map { |item| item['name'] }).to match_array([SCHEDULED, MISSED, CONDUCTED, CANCELLED])

          related_entities = basic_info_layout_items.select { |item| item['item']['type'] == 'ENTITY_LOOKUP' }.first['item']['pickLists']
          expect(related_entities.count).to eql(4)
          expect(related_entities.map { |item| item['displayName'] }).to match_array(["LEAD", "DEAL", "CONTACT", "COMPANY"])
          expect(related_entities.map { |item| item['lookupUrl'] }).to match_array([RELATED_LEAD_LOOKUP_URL, RELATED_DEAL_LOOKUP_URL, RELATED_CONTACT_LOOKUP_URL, RELATED_COMPANY_LOOKUP_URL])

          timezones = basic_info_layout_items.select { |item| item['item']['internalName'] == 'timezone' }.first['item']['pickLists']
          expect(timezones.count).to be >= 435
          expect(timezones.map { |timezone| timezone['systemDefault'] == true }.count).to eql(435)

          internals_layout_items = json['layoutItems'].second['layoutItems']
          expect(internals_layout_items.count).to eql(9)

          sortable = internals_layout_items.select do |item|
                       item['item']['type'] == 'LOOK_UP'
                     end.map { |item| item['item']['sortable'] }
          expect(sortable.uniq).to eql([false]) if sortable.present?

          expect(internals_layout_items.map { |item| item['item']['internalName'] }).to eql(["createdBy", "createdAt", "updatedBy", "updatedAt", "conductedBy", "conductedAt", "cancelledBy", "cancelledAt", "importedBy"])
          expect(internals_layout_items.map { |item| item['item']['type'] }).to eql(["LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP", "DATETIME_PICKER", "LOOK_UP"])

          checkin_checkout_internals = json['layoutItems'].last['layoutItems']
          expect(checkin_checkout_internals.count).to eql(6)
          expect(checkin_checkout_internals.map { |item| item['item']['internalName'] }).to eql(["checkedInAt", "checkedInLatitude", "checkedInLongitude", "checkedOutAt", "checkedOutLatitude", "checkedOutLongitude"])
          expect(checkin_checkout_internals.map { |item| item['item']['type'] }).to eql(["DATETIME_PICKER", "TEXT_FIELD", "TEXT_FIELD", "DATETIME_PICKER", "TEXT_FIELD", "TEXT_FIELD"])
        end
      end

      context 'with invalid view name' do
        let(:url) { '/v1/meetings/layout?view=bogus' }

        it 'should return not found error code' do
          get url, headers: headers
          expect(json['errorCode']).to eql('01502001')
        end
      end

      context 'with layout list' do
        let(:url) { '/v1/meetings/layout/list' }

        it 'should return list layout' do
          get url, headers: headers

          expect(json.keys).to eq(%w[leftNav pageConfig defaultConfig])
          expect(json['defaultConfig']['fields']).to eq(%w[title allDay from to timezone status participants organizer
                                                           relatedTo medium location description recordActions])
          expect(json['pageConfig']['actionConfig'].keys).to eq(%w[search filter create importItems columnSelector])

          table_config = json['pageConfig']['tableConfig']
          expect(table_config.keys).to eq(%w[fetchURL searchService recordClickAction columns clickActionUrl])
          expect(table_config['fetchURL']).to eq('/search/meetings')
          expect(table_config['searchService']).to eq('search')
          expect(table_config['recordClickAction']).to eq('VIEW')
          expect(table_config['columns'].count).to be >= 26
          expect(table_config['columns'].select { |field| field['id'] == 'tenant_id' }.any?).to eq(false)
        end
      end
    end

    context 'when the user is not authorized' do
      context 'with create view name' do
        let(:url) { '/v1/meetings/layout?view=create' }

        it 'returns the error code' do
          get url, headers: invalid_headers
          expect(json['errorCode']).to eq(ErrorCode.unauthorized)
        end
      end

      context 'with edit view name' do
        let(:url) { '/v1/meetings/layout?view=edit' }

        it 'returns the error code' do
          get url, headers: invalid_headers
          expect(json['errorCode']).to eq(ErrorCode.unauthorized)
        end
      end

      context 'with invalid view name' do
        let(:url) { '/v1/meetings/layout?view=bogus' }

        it 'returns the error code' do
          get url, headers: invalid_headers
          expect(json['errorCode']).to eq(ErrorCode.unauthorized)
        end
      end

      context 'with layout list' do
        let(:url) { '/v1/meetings/layout/list' }

        it 'returns the error code' do
          get url, headers: invalid_headers
          expect(json['errorCode']).to eq(ErrorCode.unauthorized)
        end
      end
    end
  end
end
