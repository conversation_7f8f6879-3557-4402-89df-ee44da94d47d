# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Webhook", type: :request do
  describe '#microsoft' do
    context 'when validation token is present' do
      it 'returns same token with successfully with text/plain type' do
        post '/v1/meetings/webhooks/microsoft?validationToken=opaqueTokenCreatedByMicrosoftGraph'

        expect(@response.code).to eq('200')
        expect(@response.body).to eq('opaqueTokenCreatedByMicrosoftGraph')
        expect(@response.content_type).to eq('text/plain; charset=utf-8')
      end
    end

    context 'when any event is present' do
      it 'adds processor job with respective params' do
        event_params = { "value": [
          {
            "id": "lsgTZMr9KwAAA",
            "subscriptionId": "{subscription_guid}",
            "subscriptionExpirationDateTime": "2016-03-19T22:11:09.952Z",
            "clientState": "secretClientValue",
            "changeType": "created",
            "resource": "users/{user_guid}@{tenant_guid}/messages/{long_id_string}",
            "tenantId":  "84bd8158-6d4d-4958-8b9f-9d6445542f95",
            "resourceData":
            {
              "@odata.type": "#Microsoft.Graph.Message",
              "@odata.id": "Users/{user_guid}@{tenant_guid}/Messages/{long_id_string}",
              "@odata.etag": "W/\"CQAAABYAAADkrWGo7bouTKlsgTZMr9KwAAAUWRHf\"",
              "id": "{long_id_string}"
            }
          }
        ] }

        expect(MicrosoftWebhookProcessorJob).to receive(:perform_later).once
        post '/v1/meetings/webhooks/microsoft', params: event_params

        expect(@response.code).to eq('200')
      end
    end
  end
end
