require 'rails_helper'

RSpec.describe "Fields", type: :request do
  let!(:user)             { create(:user) }
  let!(:another_user)     { create(:user, tenant_id: user.tenant_id) }
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name ) }
  let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let!(:headers)          { valid_headers(valid_auth_token) }
  let!(:headers_without_custom_field)  { valid_headers(another_valid_token) }

  before { allow(PublishEvent).to receive(:call) }

  describe '#create' do
    let(:params) {
      {
        description: 'This is custom field',
        displayName: 'Custom Picklist Field',
        filterable: true,
        sortable: false,
        required: false,
        type: 'PICK_LIST',
        pickLists: [
          {
            id: nil,
            name: nil,
            displayName: 'Picklist Value 1'
          },
          {
            id: nil,
            name: nil,
            displayName: 'Picklist Value 2'
          }
        ]
      }
    }

    context 'valid request' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldCreated)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingFieldCreatedV2)).once
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingLayoutUpdated)).once
        post '/v1/meetings/fields', headers: headers, params: params.to_json
      end

      it 'should create field correctly' do
        expect(json.keys).to match_array(%w[id name displayName type description standard sortable filterable internal required active tenantId pickLists createdAt updatedAt])
        expect(response.status).to be(201)

        expect(json['name']).to start_with('cf')
        expect(json['type']).to eq('PICK_LIST')
        expect(json['internal']).to be(false)
        expect(json['standard']).to be(false)
        expect(json['sortable']).to be(false)
        expect(json['filterable']).to be(true)
        expect(json['required']).to be(false)

        expect(json['pickLists'].count).to be(2)
        json['pickLists'].each do |picklist_value|
          expect(picklist_value['name']).to start_with('cpv')
          expect(picklist_value['disabled']).to be(false)
          expect(picklist_value['systemDefault']).to be(false)
        end
      end
    end

    context 'invalid request' do
      context 'when unauthorised user' do
        before { post '/v1/meetings/fields', headers: invalid_headers, params: params.to_json }

        it 'should return unauthorised error code' do
          expect(response.status).to be(401)
          expect(json['errorCode']).to eq('01501005')
        end
      end

      context 'when user does not have custom field write' do
        before { post '/v1/meetings/fields', headers: headers_without_custom_field, params: params.to_json }

        it 'should return unauthorised error code' do
          expect(response.status).to be(401)
          expect(json['errorCode']).to eq('01501005')
        end
      end

      context 'when invalid field' do
        before { post '/v1/meetings/fields', headers: headers, params: params.merge(displayName: '').to_json }

        it 'should return unauthorised error code' do
          expect(response.status).to be(422)
          expect(json['errorCode']).to eq(ErrorCode.cannot_create_entity)
        end
      end
    end
  end

  %w[activate deactivate].each do |action|
    describe "##{action}" do
      context 'valid request' do
        before do
          @field = create(
            :field,
            is_standard: false,
            active: (action == 'deactivate'),
            created_by: another_user,
            updated_by: another_user,
            tenant_id: user.tenant_id
          )

          put "/v1/meetings/fields/#{@field.id}/#{action}", headers: headers
        end

        it 'should perform action successfully' do
          expect(response.status).to be(200)
          expect(@field.reload.active).to be(action == 'activate')
          expect(@field.updated_by_id).to be(user.id)
        end
      end

      context 'invalid request' do
        before do
          @field = create(
            :field,
            is_standard: false,
            active: (action == 'deactivate'),
            created_by: another_user,
            updated_by: another_user,
            tenant_id: user.tenant_id
          )
        end

        context 'when invalid token' do
          before { put "/v1/meetings/fields/#{@field.id}/#{action}", headers: invalid_headers }

          it 'should raise authentication error' do
            expect(json['errorCode']).to eq('01501005')
            expect(response.status).to be(401)
          end
        end

        context 'when user does not have permission to update custom field' do
          before { put "/v1/meetings/fields/#{@field.id}/#{action}", headers: headers_without_custom_field }

          it 'should return unauthorised error' do
            expect(json['errorCode']).to eq('01501005')
            expect(response.status).to be(401)
          end
        end

        context 'when invalid field id' do
          before do
            put "/v1/meetings/fields/#{@field.id + 1}/#{action}", headers: headers
          end

          it 'should raise invalid error' do
            expect(json['errorCode']).to eq('01503001')
            expect(response.status).to be(422)
          end
        end

        context 'when system field' do
          before do
            @field.update(is_standard: true)
            put "/v1/meetings/fields/#{@field.id}/#{action}", headers: headers
          end

          it 'should raise invalid error' do
            expect(json['errorCode']).to eq('01503001')
            expect(response.status).to be(422)
          end
        end
      end
    end
  end

  describe '#index' do
    context 'when user is authorized' do
      before do
        @fields = create_list(:field, 3, tenant_id: user.tenant_id, is_standard: false, created_by: user, updated_by: user)
        @fields.last(1).each do |f|
          f.is_standard = true
          f.internal_name = f.internal_name.gsub('cf_', '')
          f.save
        end
      end

      context 'when only custom fields are requested' do
        let(:url) { '/v1/meetings/fields?custom-only=true' }

        before { get url, headers: headers }

        it 'returns correct fields' do
          expect(json.map{|f| f['id']}).to match_array(@fields.map(&:id).first(2))
        end

        it 'returns the correct fields in the response' do
          expect(json.first.keys).to match_array(['id', 'name', 'displayName', 'type', 'description',
            'standard', 'sortable', 'filterable', 'internal', 'required', 'active', 'tenantId', 'picklist',
             'createdAt', 'updatedAt'])
        end

        it 'returns in the correct order' do
          expect(json.map{|f| f['id']}).to eq(@fields.map(&:id).first(2))
        end
      end

      context 'when all fields are requested' do
        let(:url) { '/v1/meetings/fields' }

        before { get url, headers: headers }

        it 'returns correct fields' do
          expect(json.map{|f| f['id']}).to match_array(@fields.map(&:id))
        end

        it 'returns the correct fields in the response' do
          expect(json.first.keys).to match_array(['id', 'name', 'displayName', 'type', 'description',
            'standard', 'sortable', 'filterable', 'internal', 'required', 'active', 'tenantId', 'picklist',
             'createdAt', 'updatedAt'])
        end

        it 'returns in the correct order' do
          expect(json.map{|f| f['id']}).to eq(@fields.map(&:id))
        end
      end

      context 'and field type for one of the field is picklist' do
        let(:url) { '/v1/meetings/fields' }

        before do
          @field = @fields.last
          @field.update(field_type: 'PICK_LIST')
          @picklist = create(:picklist, field: @field, tenant_id: @field.tenant_id)
          @picklist_value1 = FactoryBot.create(:picklist_value, picklist: @picklist, tenant_id: @picklist.tenant_id)
          FactoryBot.create(:picklist_value, picklist: @picklist, tenant_id: @picklist.tenant_id)
          get url, headers: headers
        end

        it 'sends picklist details correctly' do
          pl = json.third['picklist']
          expect(pl['displayName']).to eq(@picklist.display_name)
          expect(pl['picklistValues'].count).to eq(2)
          value1 = pl['picklistValues'].first
          expect(value1['id']).to eq(@picklist_value1.id)
          expect(value1['name']).to eq(@picklist_value1.internal_name)
          expect(value1['displayName']).to eq(@picklist_value1.display_name)
          expect(value1['disabled']).to eq(@picklist_value1.disabled)
          expect(value1['systemDefault']).to eq(@field.is_standard? && @field.internal_name == 'timezone')
        end
      end
    end

    context 'when the user is not authorized' do
      let(:url)     { '/v1/meetings/fields' }

      it 'returns the error code' do
        get url, headers: invalid_headers
        expect(json['errorCode']).to eq(ErrorCode.unauthorized)
      end
    end
  end

  describe '#create_fields' do
    context 'when user is authorised' do
      let(:url) { "/v1/meetings/fields/create_fields?tenantId=#{user.tenant_id}&userId=#{user.id}&name=#{user.name}" }

      it 'should create fields for new tenant' do
        post url, headers: headers
        expect(json['message']).to eq('Fields Created for Tenant')
      end

      it 'should not create duplicate fields for tenant' do
        post url, headers: headers
        expect(json['message']).to eq('Fields Created for Tenant')

        post url, headers: headers
        expect(json['message']).to eq('Fields Existing for Tenant')
      end
    end

    context 'when the user is not authorized' do
      let(:url) { "/v1/meetings/fields/create_fields?tenantId=#{user.tenant_id}&userId=#{user.id}&name=#{user.name}" }

      it 'returns the error code' do
        post url, headers: invalid_headers
        expect(json['errorCode']).to eq(ErrorCode.unauthorized)
      end
    end
  end

  describe '#update_fields' do
    context 'when user is authorised' do
      let(:url) { "/v1/meetings/fields/update_fields?fromTenantId=1&toTenantId=500" }

      context 'for existing tenants' do
        before do
          from_tenant_id = 1
          to_tenant_id = 100
          (from_tenant_id..to_tenant_id).each do |tenant_id|
            field = create(:field, internal_name: 'medium', display_name: 'Medium', field_type: 'ENTITY_PICKLIST',
                                   tenant_id: tenant_id, created_by_id: user.id, updated_by_id: user.id)
            picklist = create(:picklist, display_name: 'Medium Picklist', internal_name: 'medium_picklist',
                                         tenant_id: tenant_id, field_id: field.id)
            picklist.picklist_values.create(internal_name: 'MICROSOFT', display_name: 'MS Teams',
                                            tenant_id: tenant_id, picklist_id: picklist.id)
          end
          @tenant_count = (from_tenant_id..to_tenant_id).count
        end

        it 'should create picklist values for tenant' do
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'MS Teams').count).to eq(@tenant_count)
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'Outlook Calendar').count).to eq(0)

          expect(Rails.logger).to receive(:info).with("UpdateMeetingFieldsForTenant medium picklist value microsoft updated for Tenants 1 to 500 count #{@tenant_count}")
          expect(Rails.logger).to receive(:info).exactly(4).times

          post url, headers: headers
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'MS Teams').count).to eq(0)
          expect(PicklistValue.where(internal_name: 'MICROSOFT', display_name: 'Outlook Calendar').count).to eq(@tenant_count)
        end
      end

      context 'for new tenants' do
        it 'should not create picklist values for new tenant' do
          expect(Field.where(tenant_id: user.tenant_id).count).to eq(0)
          expect(Picklist.where(tenant_id: user.tenant_id).count).to eq(0)
          expect(PicklistValue.where(tenant_id: user.tenant_id).count).to eq(0)

          post url, headers: headers
          expect(Field.where(tenant_id: user.tenant_id).count).to eq(0)
          expect(Picklist.where(tenant_id: user.tenant_id).count).to eq(0)
          expect(PicklistValue.where(tenant_id: user.tenant_id).count).to eq(0)
        end
      end
    end

    context 'when the user is not authorized' do
      let(:url) { "/v1/meetings/fields/update_fields?tenantId=#{user.tenant_id}&userId=#{user.id}&name=#{user.name}" }

      it 'returns the error code' do
        post url, headers: invalid_headers
        expect(json['errorCode']).to eq(ErrorCode.unauthorized)
      end
    end
  end

  describe '#show' do
    before do
      @fields = create_list(:field, 3, tenant_id: user.tenant_id, is_standard: true, created_by: user, updated_by: user)
      @fields.first(2).each do |f|
        f.is_standard = false
        f.save
      end
    end

    context 'when user is authorized' do
      context 'when specific field is requested' do
        before do
          @field = @fields.last
          @field.update(field_type: 'PICK_LIST')
          picklist = create(:picklist, field: @field, tenant_id: @field.tenant_id)
          FactoryBot.create(:picklist_value, picklist: picklist, tenant_id: picklist.tenant_id)
          FactoryBot.create(:picklist_value, picklist: picklist, tenant_id: picklist.tenant_id)
        end

        it 'should return field data' do
          get "/v1/meetings/fields/#{@field.id}", headers: headers

          expect(json.keys).to eq(['field', 'fieldConfig'])
          expect(json['field']['picklist']).to eq(PicklistSerializer.call(@field.picklist).result)

          {'id' => 'id', 'name' => 'internal_name', 'displayName' => 'display_name', 'type' => 'field_type',
            'standard' => 'is_standard', 'sortable' => 'is_sortable', 'filterable' => 'is_filterable',
            'internal' => 'is_internal', 'required' => 'is_required', 'active' => 'active'}.each do |key, attr|
              expect(json['field'][key]).to eql(@field.send(attr))
            end
        end

        it 'should return picklist values in sorted order' do
          get "/v1/meetings/fields/#{@field.id}", headers: headers

          picklist_values = json['field']['picklist']['picklistValues']
          expect(picklist_values.map{|value| value['id']}).to eq(@field.picklist.picklist_values.order(:id).map(&:id))
        end

        it 'should return fieldConfig also if field standard and required' do
          @field.update(is_standard: true, is_required: true)
          get "/v1/meetings/fields/#{@field.id}", headers: headers

          expect(json['fieldConfig']['name']).to eql(@field.internal_name)
          expect(json['fieldConfig']['standard']).to eq(true)
          expect(json['fieldConfig']['required']).to eq(true)
        end

        it 'should not return fieldConfig if field not standard' do
          @field.update(is_standard: false, is_required: true, internal_name: "cf_#{@field.internal_name}")
          get "/v1/meetings/fields/#{@field.id}", headers: headers

          expect(json['fieldConfig']).to eql(nil)
        end

        it 'should not return fieldConfig if field not standard' do
          @field.update(is_standard: true, is_required: false)
          get "/v1/meetings/fields/#{@field.id}", headers: headers

          expect(json['fieldConfig']).to eql(nil)
        end
      end
    end

    context 'when user is not authorized' do
      it 'returns the unauthorized error code' do
        field = FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user)
        url = "/v1/meetings/fields/#{field.id}"

        get url, headers: invalid_headers
        expect(json['errorCode']).to eq(ErrorCode.unauthorized)
      end
    end
  end

  describe '#update' do
    before do
      @field = FactoryBot.create(:field, field_type: 'TEXT_FIELD', tenant_id: user.tenant_id, created_by: user, updated_by: user)
    end

    let(:url) { "/v1/meetings/fields/#{@field.id}" }

    context 'when user is authorized' do
      # TODO Add support to update rest of the attributes
      it 'should update only display name and description' do
        params = {
          "picklist": nil,
          "displayName": "Time(From)",
          "description": "Updated description",
          "filterable": true,
          "sortable": false,
          "standard": true,
          "required": false,
          "type": "TEXT_FIELD"
        }.to_json

        put url, headers: headers, params: params

        expect(json['displayName']).to eq('Time(From)')
        expect(json['description']).to eq('Updated description')

        # Internal Name should not change
        expect(json['name']).to eq(@field.internal_name)
      end

      it 'should return invalid status if updated field not valid' do
        params = {
          "picklist": nil,
          "displayName": "",
          "description": "Updated description",
          "filterable": true,
          "sortable": false,
          "standard": true,
          "required": false,
          "type": "TEXT_FIELD"
        }.to_json

        put url, headers: headers, params: params

        expect(json['errorCode']).to eq('01503001')
        expect(response.status).to eq(422)
      end
    end

    context 'when the user is not authorized' do
      it 'returns the error code' do
        put url, headers: invalid_headers
        expect(json['errorCode']).to eq(ErrorCode.unauthorized)
      end
    end
  end
end
