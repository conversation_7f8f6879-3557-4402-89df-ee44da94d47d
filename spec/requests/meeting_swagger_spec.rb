require 'swagger_helper'

RSpec.describe 'Meeting API', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }
  let(:meeting)          { build(:meeting) }

  path '/v1/meetings' do
    post 'Creates a Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :meeting, in: :body, schema: {
        type: :object,
        properties: {
          title: { type: :string },
          description: { type: :string },
          from: { type: :time },
          to: { type: :time },
          all_day: { type: :boolean },
          location: { type: :string },
          related_to: { type: :object },
          participants: { type: :object },
          medium: { type: :string }
        },
        required: %w[title from to all_day]
      }

      parameter({
                  in: :header,
                  type: :string,
                  name: :Authorization,
                  required: true,
                  description: 'Client token'
                })

      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage").
          with(headers: { 'Authorization' => "Bearer #{valid_auth_token.token}" })
          .to_return(status: 200, body: { records: { used: 50, total: 100 } }.to_json)
      end

      response '201', 'meeting created' do
        let(:meeting) { build(:meeting) }
        before do
          allow(PublishEvent).to receive(:call)
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}")
            .with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token.token}"
              })
            .to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})

          stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})
        end
        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '422', 'invalid request' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).to_return(body: '', status: 404)
        end

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}' do

    get 'Retrieves a Meeting' do
      tags 'Meetings'
      produces 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meeting found' do
        schema type: :object,
          properties: {
            title: { type: :string },
            description: { type: :string },
            from: {type: :datetime},
            to: {type: :datetime},
            all_day: {type: :boolean},
            location: {type: :string},
            related_to: {type: :object},
            participants: {type: 'array', items: {type: :object}}
          }

        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        before do
          user.update(geofence_config: { fieldSalesEnabled: false, meetingCheckInCheckOut: nil })
        end
        run_test!
      end

      response '404', 'meeting not found' do
        let(:id) { 'invalid' }

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}' do
    let(:id) { FactoryBot.create(:meeting, owner: user).id }
    put 'Update a Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :meeting, in: :body, schema: {
        type: :object,
        properties: {
          id: {type: :string},
          title: { type: :string },
          description: { type: :string },
          from: {type: :time},
          to: {type: :time},
          all_day: {type: :boolean},
          location: {type: :string},
          related_to: {type: :object},
          participants: {type: :object},
        },
        required: ['title', 'from', 'to', 'all_day']
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meeting updated' do
        let(:meet) { create(:meeting, owner: user) }
        let(:meeting) do
          meet.as_json.merge(
            participants: [
              {
                id: meet.organizer.entity_id,
                name: meet.organizer.name,
                entity: 'user',
                email: meet.organizer.email
              }
            ]
          )
        end

        before do
          allow(PublishEvent).to receive(:call)
          allow(GetRsvpToken).to receive_message_chain(:call, :result).and_return('test token')
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return(
            [
              build(
                :user_look_up,
                entity_id: meet.organizer.entity_id,
                tenant_id: meet.organizer.tenant_id,
                name: meet.organizer.name,
                email: meet.organizer.email
              )
            ]
          )
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{meet.organizer.id}")
            .with(
              headers: { 'Authorization' => 'Bearer ' + valid_auth_token.token }
            ).to_return(
              status: 200,
              body: '[{"id":' + meet.organizer.entity_id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]',
              headers: {}
            )

          stub_request(:get, "#{SERVICE_IAM}/v1/users/me")
            .with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token.token}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
              }
            )
            .to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})
        end
        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '422', 'invalid request' do
        let(:meeting) { build(:meeting, title: '') }

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/cancel' do
    post 'Cancel a Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meeting cencelled' do
        before do
          allow(PublishEvent).to receive(:call).with(any_args)
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
        end
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '422', 'invalid request' do
        let(:id) { FactoryBot.create(:meeting, owner: user, id: 1).update(status: CANCELLED); 1}
        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/conduct' do
    post 'Mark meeting as conducted' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

    response '200', 'Meeting conducted' do
        before do
          allow(PublishEvent).to receive(:call)
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
        end
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '422', 'invalid request' do
        let(:id) { FactoryBot.create(:meeting, owner: user, id: 1).update(status: CANCELLED); 1}
        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/checkin' do
    post 'Check in Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :check_in_payload, in: :body, schema: {
        type: :object,
        properties: {
          location: {type: :string},
          checkedInDetails: {
            type: :object,
            properties: {
              latitude: {type: :string},
              longitude: {type: :string}
            }
          }
        }
      }


      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:check_in_payload) {
        {
          location: "Baner, Pune",
          checkedInDetails: {
            latitude: '18.559658',
            longitude: '73.779938'
          }
        }
      }
      let(:id) { create(:meeting, owner: user).id }

      response '200', 'Meeting Checked In' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedIn)).once

          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
            with(
                headers: {
                    'Authorization'=>'Bearer '+ valid_auth_token.token
                }).
            to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})
        end

        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid Request' do
        let(:meeting) { Meeting.find(id) }
        before { FactoryBot.create(:meeting_checked_in, meeting: meeting) }

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/checkout' do
    post 'Check out Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :check_out_payload, in: :body, schema: {
        type: :object,
        properties: {
          checkedOutDetails: {
            type: :object,
            properties: {
              latitude: {type: :string},
              longitude: {type: :string}
            }
          }
        }
      }


      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:check_out_payload) {
        {
          checkedOutDetails: {
            latitude: '18.559658',
            longitude: '73.779938'
          }
        }
      }
      let(:id) { create(:meeting, owner: user).id }

      response '200', 'Meeting Checked Out' do
        let(:meeting) { Meeting.find(id) }
        before do
          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/#{user.id}/geofence").
          with(
            headers: {
              'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: { meetingCheckInCheckOut: nil }.to_json, headers: {})

          FactoryBot.create(:meeting_checked_in, meeting: meeting)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated)).once
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MeetingCheckedOut)).once
        end

        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid Request' do
        let(:meeting) { Meeting.find(id) }
        before { FactoryBot.create(:meeting_attendance, meeting: meeting) }

        run_test!
      end
    end
  end

  path '/v1/meetings/search' do
    post 'Searches meetings' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :sort, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      before { create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true) }

      response '200', 'Meeting Searched' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:sort) { 'from,asc' }
        let(:jsonRule) { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "string",
                                                                   field: "location", value: "Balewadi" }]}}}

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:page)          { '1' }
        let(:size)          { '2' }
        let(:sort)          { 'from,asc' }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "string",
                                                                   field: "location", value: "Balewadi" }]}}}

        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/rsvp' do
    post 'Rsvp Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :rsvp, in: :body, schema: {
          type: :object,
          properties: {
              rsvpResponse: { type: :string },
              rsvpMessage: { type: :string },
              notifyOrganiser: { type: :boolean }
          },
          required: [ 'rsvpResponse', 'rsvpMessage', 'notifyOrganiser' ]
      }
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'Meeting Rsvp' do
        let(:invitee){ create(:user, id: 999, tenant_id: user.tenant_id)}
        let(:valid_token) { build(:auth_token, user_id: invitee.id, tenant_id: invitee.tenant_id, username: invitee.name).token }
        let(:Authorization)    { valid_token }
        before do
          allow(PublishEvent).to receive(:call).with(instance_of(Event::MeetingUpdated))
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{invitee.id}").
              with(
                  headers: {
                      'Authorization'=>'Bearer '+ valid_token
                  }).
              to_return(status: 200, body: '[{"id":' + invitee.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
        end
        let(:rsvp){ { rsvpResponse: "YES", rsvpMessage: "Coming", notifyOrganiser: false  }}
        let(:id) do
          meeting = FactoryBot.create(:meeting, owner: user)
          meeting.participants << build(:user_look_up, entity_id: invitee.id, tenant_id: invitee.tenant_id, name: invitee.name)
          meeting.id
        end
        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:rsvp){ { rsvpResponse: "YES", rsvpMessage: "Coming", notifyOrganiser: false  }}
        let(:id) { 12 }
        run_test!
      end

      response '422', 'invalid request' do
        let(:rsvp){ { rsvpMessage: "Coming", notifyOrganiser: false  }}
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end
    end
  end

  path '/v1/meetings/{id}/p_rsvp' do
    post 'Public Rsvp Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :rsvp, in: :body, schema: {
          type: :object,
          properties: {
              rsvpResponse: { type: :string },
              rsvpMessage: { type: :string },
              pid: { type: :string }
          },
          required: [ 'rsvpResponse', 'rsvpMessage', 'pid' ]
      }
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'Meeting Rsvp' do
        let(:invitee){ create(:user, id: 999, tenant_id: user.tenant_id)}
        let(:valid_token) { build(:auth_token, user_id: invitee.id, tenant_id: invitee.tenant_id, username: invitee.name).token }
        let(:Authorization)    { valid_token }
        before do
          allow(PublishEvent).to receive(:call)
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{invitee.id}").
              with(
                  headers: {
                      'Authorization'=>'Bearer '+ valid_token
                  }).
              to_return(status: 200, body: '[{"id":' + invitee.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
        end
        let(:rsvp){ { rsvpResponse: "YES", rsvpMessage: "Coming", pid: 'public-id' }}
        let(:id) do
          meeting = FactoryBot.create(:meeting, owner: user, public_id: 'public-id')
          meeting.participants << build(:user_look_up, entity_id: invitee.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          meeting.participants << build(:user_look_up, entity_id: user.id, tenant_id: invitee.tenant_id, name: invitee.name, public_id: 'public-id')
          meeting.public_id
        end
        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:rsvp){ { rsvpResponse: "YES", rsvpMessage: "Coming", pid: 'public-id' }}
        let(:id) { 12 }
        run_test!
      end

      response '422', 'invalid request' do
        let(:rsvp){ { rsvpResponse: "NEVER", rsvpMessage: "Coming", pid: 'public-id' }}
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end
    end
  end

  path '/v1/meetings/{id}' do
    delete 'Delete a Meeting' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meeting deleted' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
          allow(PublishEvent).to receive(:call)
        end
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:meeting, owner: user).id }
        run_test!
      end
    end
  end

  path '/v1/meetings/delete' do
    delete 'Delete bulk meetings' do
      tags 'Meetings'
      consumes 'application/json'
      parameter name: :ids, in: :query, type: :array, collectionFormat: :multi

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meetings deleted' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id}").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: '[{"id":' + user.id.to_s + ',"name": "Jane User","email": {"primary": true,"value": "<EMAIL>"}}]', headers: {})
          allow(PublishEvent).to receive(:call)
        end
        let(:ids) { [ FactoryBot.create(:meeting, owner: user).id ] }

        run_test!
      end

      response '401', 'authentication failed' do
        let(:meeting)       { build(:meeting) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:ids) { [FactoryBot.create(:meeting, owner: user).id] }

        run_test!
      end
    end
  end

  path '/v1/meetings/lookup' do
    get 'Meetings Lookup' do
      tags 'Meetings'
      consumes 'application/json'

      parameter name: :q, in: :query, type: :string
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meetings Lookup Response' do
        let(:q) { 'defg' }

        before do
          create(:field, internal_name: 'from', tenant_id: user.tenant_id, active: true, is_sortable: true)
          create(:field, internal_name: 'title', tenant_id: user.tenant_id, active: true, is_filterable: true)
          create_list(:meeting, 10, title: 'ABCDEFGHIJ', tenant_id: user.tenant_id, owner: user, from: 15.minutes.ago)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:q)       { 'defg' }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/meetings/webhooks/microsoft' do
    post 'Listens to webhook events from microsoft' do
      tags 'Meetings'
      consumes 'text/plain'
      parameter name: :validationToken, in: :query, type: :string

      response '200', 'webhook subscribed' do
        let(:validationToken) { 'opaqueTokenCreatedByMicrosoftGraph' }
        run_test!
      end
    end
  end

  path '/v1/meetings/bulk-checkout' do
    post 'Bulk Checkout' do
      tags 'Meetings'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          latitude: { type: :string },
          longitude: { type: :string },
          meetingIds: { type: :array }
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Checked out' do
        let(:user)             { create(:user)}
        let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
        let(:Authorization)    { valid_auth_token.token }
        let(:params) { {
          "latitude": 18.581730,
          "longitude": 73.760761,
          "meetingIds": [123, 324]
        } }

        run_test!
      end

      response '401', 'Unauthorized' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:params) { {
          "latitude": 18.581730,
          "longitude": 73.760761,
          "meetingIds": [123, 324]
        } }

        run_test!

      end
    end
  end
end
