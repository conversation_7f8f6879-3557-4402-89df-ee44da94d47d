require 'swagger_helper'

RSpec.describe 'Fields API', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }
  let(:'custom-only')    { 'true' }

  before { allow(PublishEvent).to receive(:call) }

  path '/v1/meetings/fields' do
    post 'Create custom field' do
      tags 'Meeting Fields'
      consumes 'application/json'
      parameter name: :field, in: :body, schema: {
        type: :object,
        properties: [
          displayName: { type: :string },
          description: { type: :string },
          type: { type: :string },
          filterable: { type: :boolean },
          sortable: { type: :boolean },
          standard: { type: :boolean },
          required: { type: :boolean },
          pickLists: {
            type: :array,
            items: {
              type: :object,
              properties: [
                displayName: { type: :string }
              ],
              uniqueItems: true
            }
          },
        ],
        required: ['displayName', 'type', 'filterable', 'sortable', 'standard', 'required']
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })


      let(:field) {
        {
          description: 'This is custom field',
          displayName: 'Custom Picklist Field',
          filterable: true,
          sortable: false,
          required: false,
          type: 'PICK_LIST',
          standard: false,
          pickLists: [
            {
              id: nil,
              name: nil,
              displayName: 'Picklist Value 1'
            },
            {
              id: nil,
              name: nil,
              displayName: 'Picklist Value 2'
            }
          ]
        }
      }

      response '201', 'Create custom field' do
        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid Field' do
        let(:field) { { displayName: '' } }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields/{id}/activate' do
    put 'Activate Custom Field' do
      tags 'Meeting Fields'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Field Activated' do
        let(:field) { FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user, is_standard: false) }
        let(:id) { field.id }

        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { 101 }

        run_test!
      end

      response '422', 'Invalid Field' do
        let(:id) { 101 }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields/{id}/deactivate' do
    put 'Deactivate Custom Field' do
      tags 'Meeting Fields'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Field Deactivated' do
        let(:field) { FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user, is_standard: false) }
        let(:id) { field.id }

        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { 101 }

        run_test!
      end

      response '422', 'Invalid Field' do
        let(:id) { 101 }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields' do
    get 'List fields' do
      tags 'Meeting Fields'
      consumes 'application/json'
      parameter name: :'custom-only', in: :query, type: :boolean

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Meetings List' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields/{id}' do
    get 'Field' do
      tags 'Get Field'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Get Field' do
        let(:field) { FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user) }
        let(:id) { field.id }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:field) { FactoryBot.create(:field, tenant_id: user.tenant_id, created_by: user, updated_by: user) }
        let(:id) { field.id }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields/{id}' do
    put 'Update fields' do
      tags 'Fields'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :field, in: :body, schema: {
        type: :object,
        properties: [
          displayName: { type: :string },
          picklist: { type: :object },
          description: { type: :string },
          type: { type: :string },
          filterable: { type: :boolean },
          sortable: { type: :boolean },
          standard: { type: :boolean },
          required: { type: :boolean }
        ],
        required: ['displayName', 'description']
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Update Fields' do
        let(:field) { FieldsSerializer.call([FactoryBot.create(:field, field_type: 'TEXT_FIELD', tenant_id: user.tenant_id, created_by: user, updated_by: user)]).result.first }
        let(:id) { field['id'] }

        before do
          stub_request(:put, "http://localhost:3000/v1/meetings/fields/#{field['id']}")
            .with(
              headers: {
                'Authorization' => valid_auth_token.token,
                'CONTENT_TYPE' => 'application/json'
              },
              body: field.to_json
            )
            .to_return(body: '', status: 200)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:field) { FieldsSerializer.call([FactoryBot.create(:field, field_type: 'TEXT_FIELD', created_by: user, updated_by: user)]).result.first }
        let(:id) { field['id'] }

        before do
          stub_request(:put, "http://localhost:3000/v1/meetings/fields/#{field['id']}")
            .with(
              headers: {
                'Authorization' => 'Bearer '+ send(:Authorization),
                'CONTENT_TYPE' => 'application/json'
              },
              body: field.to_json
            )
            .to_return(body: '', status: 401)
        end

        run_test!
      end

      response '404', 'Field Not Found' do
        let(:field) { 'invalid' }
        let(:id) { 1 }

        run_test!
      end
    end
  end

  path '/v1/meetings/fields/create_fields' do
    post 'Create Fields' do
      tags 'Create Fields for Existing Tenants Migration'
      consumes 'application/json'
      parameter name: :tenantId, in: :query, type: :string
      parameter name: :userId, in: :query, type: :string
      parameter name: :name, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:tenantId) { 123 }
      let(:userId) { 456 }
      let(:name) { 'Tony' }

      before do
        allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
          build(:user_look_up, entity_id: @user_id, tenant_id: @tenant_id, name: "Jane Doe")
        ])
      end

      response '200', 'Create Fields' do
        run_test!
      end
    end
  end

  path '/v1/meetings/fields/update_fields' do
    post 'Update Fields' do
      tags 'Update Fields for Existing Tenants Migration'
      consumes 'application/json'
      parameter name: :fromTenantId, in: :query, type: :string
      parameter name: :toTenantId, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:fromTenantId) { 1 }
      let(:toTenantId) { 500 }

      response '200', 'Update Fields' do
        run_test!
      end
    end
  end
end
