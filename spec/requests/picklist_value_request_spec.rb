require 'rails_helper'

RSpec.describe "Picklist Value", type: :request do
  let(:tenant_id) { 101 }
  let(:user) { create(:user, tenant_id: tenant_id) }
  let(:field) { create(:custom_field, tenant_id: tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
  let(:picklist) { create(:picklist, field: field, tenant_id: tenant_id) }
  let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: tenant_id) }
  let(:valid_auth_token)    { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:another_valid_token) { build(:auth_token, :without_custom_field, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers)           { valid_headers(valid_auth_token) }
  let(:headers_without_custom_field)   { valid_headers(another_valid_token) }

  before { allow(PublishEvent).to receive(:call) }

  context '#update' do
    let(:picklist_value) { picklist_values.first }
    let(:params) { picklist_value.as_json.slice(*%w[id name disabled]).merge(displayName: 'Updated Name') }
    let(:url) { "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_value.id}" }

    context 'valid request' do
      context 'Valid display name to be updated' do
        it 'should update picklist value' do
          put url, headers: headers, params: params.to_json

          expect(response.status).to be(200)
          picklist_value_hash = response.parsed_body
          expect(picklist_value_hash['displayName']).not_to eq(picklist_value.display_name)
          expect(picklist_value_hash['displayName']).to eq('Updated Name')
          expect(field.reload.updated_by_id).to eq(user.id)
        end
      end
    end

    context 'invalid request' do
      context 'when invalid token' do
        it 'should raise authentication error' do
          put url, headers: invalid_headers, params: params.to_json

          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when user does not have permission to update custom field' do
        it 'should return unauthorised error' do
          put url, headers: headers_without_custom_field, params: params.to_json

          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when updated display name is blank' do
        it 'should return invalid data' do
          put url, headers: headers, params: params.merge(displayName: '').to_json

          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end

      context 'when standard field' do
        before do
          field.update!(is_standard: true)
          put url, headers: headers, params: params.to_json
        end

        it 'should raise invalid error' do
          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end
    end
  end

  %w[enable disable].each do |action|
    describe "##{action}" do
      before { PicklistValue.where(id: picklist_values.map(&:id)).update_all(disabled: (action == 'enable')) }

      context 'valid request' do
        it 'should perform action successfully' do
          put "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}/#{action}", headers: headers

          expect(response.status).to be(200)
          expect(picklist_values.map(&:reload).map(&:disabled).count((action == 'disable'))).to be(1)
          expect(picklist_values.map(&:disabled).count((action == 'enable'))).to be(4)
          expect(field.updated_by_id).to eq(user.id)
        end
      end

      context 'invalid request' do
        context 'when invalid token' do
          it 'should raise authentication error' do
            put "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}/#{action}", headers: invalid_headers

            expect(json['errorCode']).to eq('01501005')
            expect(response.status).to be(401)
          end
        end

        context 'when user does not have permission to update custom field' do
          it 'should return unauthorised error' do
            put "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}/#{action}", headers: headers_without_custom_field

            expect(json['errorCode']).to eq('01501005')
            expect(response.status).to be(401)
          end
        end

        context 'when invalid picklist id' do
          it 'should raise invalid error' do
            put "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id + 6}/#{action}", headers: headers

            expect(json['errorCode']).to eq('01503001')
            expect(response.status).to be(422)
          end
        end

        context 'when system field' do
          before do
            field.update!(is_standard: true)
            put "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}/#{action}", headers: headers
          end

          it 'should raise invalid error' do
            expect(json['errorCode']).to eq('01503001')
            expect(response.status).to be(422)
          end
        end
      end
    end
  end

  context '#destroy' do
    context 'valid request' do
      context 'No meeting with given picklist value is present' do
        before do
          picklist_values.last(4).each do |picklist_value|
            create(:meeting, tenant_id: user.tenant_id, custom_field_values: { field.internal_name => { id: picklist_value.id, name: picklist_value.display_name } })
          end
        end

        it 'should delete picklist value' do
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}", headers: headers

          expect(response.status).to be(200)
          expect(picklist.picklist_values.count).to be(4)
        end
      end
    end

    context 'invalid request' do
      context 'when invalid token' do
        it 'should raise authentication error' do
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}", headers: invalid_headers

          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when user does not have permission to update custom field' do
        it 'should return unauthorised error' do
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}", headers: headers_without_custom_field

          expect(json['errorCode']).to eq('01501005')
          expect(response.status).to be(401)
        end
      end

      context 'when meetings with picklist value present' do
        before do
          picklist_value = picklist_values.first
          create(:meeting, tenant_id: user.tenant_id, custom_field_values: { field.internal_name => { id: picklist_value.id, name: picklist_value.display_name } })
        end

        it 'should return bad data' do
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}", headers: headers

          expect(json['errorCode']).to eq(ErrorCode.picklist_value_cannot_be_deleted)
          expect(response.status).to be(400)
        end
      end

      context 'when invalid picklist id' do
        it 'should raise invalid error' do
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id + 6}", headers: headers

          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end

      context 'when system field' do
        before do
          field.update!(is_standard: true)
          delete "/v1/meetings/picklist/#{picklist.id}/picklist-value/#{picklist_values.first.id}", headers: headers
        end

        it 'should raise invalid error' do
          expect(json['errorCode']).to eq('01503001')
          expect(response.status).to be(422)
        end
      end
    end
  end
end
