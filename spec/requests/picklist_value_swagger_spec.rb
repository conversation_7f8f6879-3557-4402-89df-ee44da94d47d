require 'swagger_helper'

RSpec.describe 'Picklist Value APIs', type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)    { valid_auth_token.token }

  before { allow(PublishEvent).to receive(:call) }

  path "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}" do
    put "Update Picklist Value" do
      tags 'Custom Picklist Values'
      consumes 'application/json'

      parameter name: :id, in: :path, type: :string
      parameter name: :picklist_id, in: :path, type: :string
      parameter name: :picklist_value_body, in: :body, schema: {
        type: :object,
        required: %w[id name displayName],
        properties: {
          id: { type: :integer, description: 'Picklist Value ID' },
          name: { type: :string, description: 'Picklist Value Internal Name' },
          displayName: { type: :string, description: 'Picklist Value Updated Display Name' },
          disabled: { type: :boolean, description: 'Current Status of Value' }
        }
      }
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', "Custom picklist value updated successfully" do
        let(:field) { create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
        let(:picklist) { create(:picklist, field: field, tenant_id: user.tenant_id) }
        let(:picklist_value) { create(:picklist_value, picklist: picklist, tenant_id: user.tenant_id) }
        let(:id) { picklist_value.id }
        let(:picklist_id) { picklist.id }
        let(:picklist_value_body) { picklist_value.as_json.slice(*%w[id name disabled]).merge(displayName: 'Updated Name') }

        run_test!
      end

      response '401', 'Authentication Failed or Unauthorised user' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { 101 }
        let(:picklist_id) { 100 }
        let(:picklist_value_body) { { id: 101, name: 'Internal Name', displayName: 'Updated Name', disabled: false } }

        run_test!
      end

      response '422', 'Invalid Picklist Value' do
        let(:id) { 101 }
        let(:picklist_id) { 100 }
        let(:picklist_value_body) { { id: 101, name: 'Internal Name', displayName: '', disabled: false } }

        run_test!
      end
    end
  end

  %w[enable disable].each do |action|
    path "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}/#{action}" do
      put "#{action.titleize} Picklist Value" do
        tags 'Custom Picklist Values'
        consumes 'application/json'

        parameter name: :id, in: :path, type: :string
        parameter name: :picklist_id, in: :path, type: :string
        parameter({
          :in => :header,
          :type => :string,
          :name => :Authorization,
          :required => true,
          :description => 'Client token'
        })

        response '200', "Custom Picklist Value #{action.titleize}d successfully" do
          let(:field) { create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
          let(:picklist) { create(:picklist, field: field, tenant_id: user.tenant_id) }
          let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: user.tenant_id) }
          let(:id) { picklist_values.first.id }
          let(:picklist_id) { picklist.id }

          run_test!
        end

        response '401', 'Authentication Failed or Unauthorised user' do
          let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
          let(:id) { 101 }
          let(:picklist_id) { 100 }

          run_test!
        end

        response '422', 'Invalid Picklist Value' do
          let(:id) { 101 }
          let(:picklist_id) { 100 }

          run_test!
        end
      end
    end
  end

  path "/v1/meetings/picklist/{picklist_id}/picklist-value/{id}" do
    delete "Delete Picklist Value" do
      tags 'Custom Picklist Values'
      consumes 'application/json'

      parameter name: :id, in: :path, type: :string
      parameter name: :picklist_id, in: :path, type: :string
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', "Custom Picklist Value deleted successfully" do
        let(:field) { create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
        let(:picklist) { create(:picklist, field: field, tenant_id: user.tenant_id) }
        let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: user.tenant_id) }
        let(:id) { picklist_values.first.id }
        let(:picklist_id) { picklist.id }

        run_test!
      end

      response '400', 'Picklist value associated with meeting' do
        let(:field) { create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST') }
        let(:picklist) { create(:picklist, field: field, tenant_id: user.tenant_id) }
        let(:picklist_values) { create_list(:picklist_value, 5, picklist: picklist, tenant_id: user.tenant_id) }
        let(:picklist_value) { picklist_values.first }
        let(:id) { picklist_value.id }
        let(:picklist_id) { picklist.id }
        before { create(:meeting, tenant_id: user.tenant_id, custom_field_values: { field.internal_name => { id: picklist_value.id, name: picklist_value.display_name } }) }

        run_test!
      end

      response '401', 'Authentication Failed or Unauthorised user' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { 101 }
        let(:picklist_id) { 100 }

        run_test!
      end

      response '422', 'Invalid Picklist Value' do
        let(:id) { 101 }
        let(:picklist_id) { 100 }

        run_test!
      end
    end
  end
end
