FactoryBot.define do
  factory :look_up do
    transient do
      entity_id { (1..100).to_a.sample(1).first }
      entity_type { LOOKUP_TYPES[rand(5)] }
    end
    entity { "#{entity_type}_#{entity_id}"}
    name { Faker::Name.name }
    public_id { SecureRandom.uuid }
    tenant_id { (1..100).to_a.sample(1).first }
    owner_id { [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_COMPANY].include?(entity_type) ? 123 : nil }

    trait :lead do
      entity_type { 'lead' }
      name { Faker::Name.name }
      email {Faker::Internet.email}
    end

    trait :deal do
      entity_type { 'deal' }
      name { Faker::Restaurant.name }
    end

    trait :contact do
      entity_type { 'contact' }
      name { Faker::FunnyName.name }
      email {Faker::Internet.email}
    end

    trait :company do
      entity_type { 'company' }
      name { Faker::Company.name }
    end

    trait :user do
      entity_type { 'user' }
      name { Faker::Name.name }
      email {Faker::Internet.email}
    end

    trait :external do
      entity_type { 'external' }
      name { Faker::Name.name }
      email {Faker::Internet.email}
    end

    trait :time_zone do
      entity_type { 'timezone' }
      name { ActiveSupport::TimeZone.all.collect{|x| x.name}[rand(150)] }
    end

    factory :lead_look_up, traits: [:lead]
    factory :deal_look_up, traits: [:deal]
    factory :contact_look_up, traits: [:contact]
    factory :external_look_up, traits: [:external]
    factory :company_look_up, traits: [:company]
    factory :user_look_up, traits: [:user]
    factory :timezone_look_up, traits: [:time_zone]
  end
end
