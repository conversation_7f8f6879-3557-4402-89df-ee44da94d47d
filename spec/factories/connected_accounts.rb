# frozen_string_literal: true

FactoryBot.define do
  factory :connected_account do
    tenant_id { rand(100) }
    access_token { SecureRandom.uuid }
    expires_at { (Time.now + 1.hour).to_i }
    email { Faker::Internet.email }
    provider_name { GOOGLE_PROVIDER }
    active { true }
    calendar_id { SecureRandom.uuid }
    sync_type { { kylas_to_calendar: true, calendar_to_kylas: true } }
    user
  end
end
