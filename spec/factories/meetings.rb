# frozen_string_literal: true

FactoryBot.define do
  factory :meeting do
    title { "Read#{Faker::Movies::HarryPotter.book}" }
    description { Faker::Lorem.paragraph }
    from { Faker::Time.between(from: DateTime.now + 1, to: DateTime.now + 10) }
    to { from + 1.hour }
    all_day { false }
    location { Faker::Movies::HarryPotter.location }
    association :owner, factory: :user
    tenant_id { owner.tenant_id }
    created_by { owner }
    updated_by { owner }
    related_to { [] }
    participants { [] }
    time_zone { build(:timezone_look_up) }
    public_id { SecureRandom.uuid }
    medium { OFFLINE }
    location_latitude { Faker::Address.latitude }
    location_longitude { Faker::Address.longitude }
    transient do 
      organizer_email {'<EMAIL>'}
      organizer_name {'test'}
    end  
    after(:create) do |meeting, evaluator|
      if meeting.participant_look_ups.exists?
        meeting.participant_look_ups.first.update(organizer: true)
      else
        meeting.participants << build(:look_up, entity: "#{LOOKUP_USER}_#{meeting.owner.id}", tenant_id: meeting.tenant_id, email: ( evaluator.organizer_email || Faker::Internet.email), name: evaluator.organizer_name)
        meeting.participant_look_ups.last.update(organizer: true)
      end
    end

    factory :meeting_all_day do
      all_day { true }
      to { from.in_time_zone(time_zone.name).end_of_day.utc }
    end

    transient do
      related_lead { false }
      related_deal { false }
      related_contact { false }
      related_company { false }
      participant_lead { false }
      participant_deal { false }
      participant_contact { false }
      participant_company { false }
      participant_user { false }
    end

    factory :meeting_with_associated_entities do
      after(:build) do |meeting, evaluator|
        meeting.related_to << build(:lead_look_up) if evaluator.related_lead
        meeting.related_to << build(:deal_look_up) if evaluator.related_deal
        meeting.related_to << build(:contact_look_up) if evaluator.related_contact
        meeting.related_to << build(:company_look_up) if evaluator.related_company
        meeting.participants << build(:lead_look_up) if evaluator.participant_lead
        meeting.participants << build(:contact_look_up) if evaluator.participant_contact
        meeting.participants << build(:user_look_up) if evaluator.participant_user
      end

      after(:create) do |meeting, evaluator|
        meeting.related_to << build(:lead_look_up) if evaluator.related_lead

        meeting.participants << build(:user_look_up) if evaluator.participant_user
      end
    end
  end
end
