FactoryBot.define do
  factory :auth_data, class: 'Auth::Data' do
    expires_in { 43199 }
    expiry { (Time.now + 1.day).to_i }
    token_type { 'Bearer' }
    user_id { rand(1000) }
    access_token { SecureRandom.uuid }
    username { Faker::Internet.email }
    tenant_id { rand(100) }
    permissions { build_list(:auth_permission_meeting, 1)}
    trait :note do
      permissions { build_list(:auth_permission_note, 1)}
    end

    trait :note_with_read_all_true do
      permissions  { build_list(:auth_permission_note_with_read_all_true, 1) }
    end

    trait :note_with_delete_true do
      permissions { build_list(:auth_permission_note_with_delete, 1)}
    end

    trait :note_without_read do
      permissions { build_list(:auth_permission_note_read_false, 1)}
    end

    trait :note_without_write do
      permissions { build_list(:auth_permission_note_write_false, 1)}
    end

    trait :note_without_delete do
      permissions { build_list(:auth_permission_note_delete_false, 1)}
    end

    trait :note_without_delete_all do
      permissions { build_list(:auth_permission_note_delete_all_false, 1)}
    end

    trait :meeting_without_delete do
      permissions { build_list(:auth_permission_meeting_delete_false, 1)}
    end

    trait :meeting_with_delete do
      permissions { build_list(:auth_permission_meeting_delete_true, 1)}
    end

    trait :meeting_without_read_all do
      permissions { build_list(:auth_permission_meeting_with_read_all_false, 1)}
    end

    trait :meeting_without_update_all do
      permissions { build_list(:auth_permission_meeting_with_update_all_false, 1)}
    end

    trait :share_rule_with_create do
      permissions { build_list(:auth_permission_share_rule_with_create, 1) }
    end

    trait :share_rule_without_create do
      permissions { build_list(:auth_permission_share_rule_without_create, 1) }
    end
  end
end
