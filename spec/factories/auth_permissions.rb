FactoryBot.define do
  factory :auth_permission, class: 'Auth::Permission' do
    id { rand(10000) }
    name { ['lead', 'deal', 'contact', 'meeting', 'task', 'note'].sample }
    description { Faker::Lorem.sentence}
    limits { rand(1000)}
    units { 'count' }
    action { build(:auth_permission_action)}

    trait :meeting do
      name { 'meeting' }
      action { build(:auth_permission_action, read:true, read_all: true, update: true, update_all: true) }
    end
    trait :lead do
      name { 'lead' }
    end
    trait :deal do
      name { 'deal' }
    end
    trait :task do
      name { 'task' }
    end
    trait :note do
      name { 'note' }
      action { build(:auth_permission_action, read: true, write: true)}
    end

    trait :note_with_read_all_true do
      name { 'note' }
      action { build(:auth_permission_action, read: true, write: true, read_all: true) }
    end

    trait :note_with_delete_true do
      name { 'note' }
      action { build(:auth_permission_action, delete: true, delete_all: true)}
    end

    trait :note_with_read_false do
      name { 'note' }
      action { build(:auth_permission_action, read: false, read_all:false, write: true)}
    end
    trait :note_with_write_false do
      name { 'note' }
      action { build(:auth_permission_action, write: false)}
    end
    trait :contact do
      name { 'contact' }
    end

    trait :meeting_with_delete_false do
      name { 'meeting' }
      action { build(:auth_permission_action, delete: false, delete_all: false)}
    end

    trait :meeting_with_delete_true do
      name { 'meeting' }
      action { build(:auth_permission_action, delete: true, delete_all: true)}
    end

    trait :meeting_with_read_all_false do
      name { 'meeting' }
      action { build(:auth_permission_action, read:true, read_all: false, update: true, reshare: true, update_all: false)}
    end

    trait :meeting_with_update_all_false do
      name { 'meeting' }
      action { build(:auth_permission_action, read:true, read_all: true, update: true, update_all: false)}
    end

    trait :note_with_delete_false do
      name { 'note' }
      action { build(:auth_permission_action, delete: false, delete_all: false)}
    end

    trait :note_with_delete_all_false do
      name { 'note' }
      action { build(:auth_permission_action, delete_all: false)}
    end

    trait :share_rule_with_create do
      name { 'shareRule' }
      action { build(:auth_permission_action, write: true, update: true, delete: true, read_all: false, update_all: false, delete_all: true) }
    end

    trait :share_rule_without_create do
      name { 'shareRule' }
      action { build(:auth_permission_action, write: false, update: false, update_all: false, delete_all: false) }
    end

    factory :auth_permission_lead, traits: [:lead]
    factory :auth_permission_contact, traits: [:contact]
    factory :auth_permission_deal, traits: [:deal]
    factory :auth_permission_meeting, traits: [:meeting]
    factory :auth_permission_task, traits: [:task]
    factory :auth_permission_note, traits: [:note]
    factory :auth_permission_note_with_read_all_true, traits: [:note_with_read_all_true]
    factory :auth_permission_note_with_delete, traits: [:note_with_delete_true]
    factory :auth_permission_note_read_false, traits: [:note_with_read_false]
    factory :auth_permission_note_write_false, traits: [:note_with_write_false]
    factory :auth_permission_meeting_delete_false, traits: [:meeting_with_delete_false]
    factory :auth_permission_meeting_delete_true, traits: [:meeting_with_delete_true]
    factory :auth_permission_meeting_with_read_all_false, traits: [:meeting_with_read_all_false]
    factory :auth_permission_meeting_with_update_all_false, traits: [:meeting_with_update_all_false]
    factory :auth_permission_note_delete_all_false, traits: [:note_with_delete_all_false]
    factory :auth_permission_note_delete_false, traits: [:note_with_delete_false]
    factory :auth_permission_share_rule_with_create, traits: [:share_rule_with_create]
    factory :auth_permission_share_rule_without_create, traits: [:share_rule_without_create]
  end
end
