FactoryBot.define do
  factory :meeting_attendance do
    checked_in_at { DateTime.now.utc }
    checked_in_latitude { "#{rand(90)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    checked_in_longitude { "#{rand(180)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    checked_out_at { DateTime.now.utc + 1.hour }
    checked_out_latitude { "#{rand(90)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    checked_out_longitude { "#{rand(180)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    association :meeting, factory: :meeting
    user_id { meeting.owner_id }
  end

  factory :meeting_checked_in, class: 'MeetingAttendance' do
    checked_in_at { DateTime.now.utc }
    checked_in_latitude { "#{rand(90)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    checked_in_longitude { "#{rand(180)}.#{rand(9_999_999).to_s.rjust(7, '0')}" }
    association :meeting, factory: :meeting
    user_id { meeting.owner_id }
  end
end
