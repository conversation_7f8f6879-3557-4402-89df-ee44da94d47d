FactoryBot.define do
  factory :auth_permission_action, class: 'Auth::PermissionAction' do
    read  { [true].sample }
    write  { [true, false].sample }
    update  { [true, false].sample }
    delete  { [true, false].sample }
    email  { [true, false].sample }
    call  { [true, false].sample }
    sms  { [true, false].sample }
    task  { [true, false].sample }
    note  { [true, false].sample }
    read_all  { [true, false].sample }
    update_all  { [true, false].sample }
    delete_all  { [true, false].sample }
  end
end

