# frozen_string_literal: true

FactoryBot.define do
  factory :share_rule do
    name { 'Share Rule' }
    description { 'Description' }
    association :created_by, factory: :user
    updated_by { created_by }
    tenant_id { created_by.tenant_id }
    actions { { read: true, update: false } }
    share_all_records { false }
    meeting_id { share_all_records ? nil : create(:meeting, owner: created_by).id }
    from_id { create(:user, tenant_id: created_by.tenant_id).id }
    from_type { 'USER' }
    to_id { create(:user, tenant_id: created_by.tenant_id).id }
    to_type { 'USER' }
    system_default { false }
  end
end
