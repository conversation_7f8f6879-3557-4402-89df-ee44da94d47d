FactoryBot.define do
  factory :json_rule do
    operator { "equal" }
    field { "allDay" }
    value { "true" }
    type { "boolean" }
    primary_field { nil }
    property { nil }
    is_custom_field { false }
    meeting_or_share_rule_model { 'Meeting' }

    trait :string do
      field { "location" }
      type { "string" }
      value { "lorem ipsum" }
    end

    trait :boolean do
      field { "allDay" }
      type { "boolean" }
      value { true }
    end

    trait :long do
      field { "id" }
      type { "long" }
      value { 123456 }
    end

    trait :date do
      field { ["from", "to"].sample }
      type { "date" }
      value { "2020-09-03T10:30:00.000Z" }
    end

    trait :lookup do
      field { 'participants' }
      type { "participants_lookup" }
      value { { entity: "user", id: 1 } }
    end

    trait :organizer_lookup do
      field { 'organizer' }
      type { "organizer_lookup" }
      value { { entity: "user", id: 1 } }
    end

    trait :related_lookup do
      field { 'related_to' }
      type { "related_lookup" }
      value { { entity: "lead", id: 1 } }
    end

    trait :associated_lookup do
      field { 'associated_to' }
      type { "associated_lookup" }
      value { { entity: "lead", id: 1 } }
    end

    trait :meeting_attendance do
      field { 'checked_in_at' }
      type { 'meeting_attendance' }
      value { '2020-09-03 10:30:00' }
    end

    trait :checked_in_out_by do
      field { 'checked_in_out_by' }
      type { 'long' }
      value { 1 }
    end

    trait :equal do
      operator { "equal" }
    end

    trait :not_equal do
      operator { "not_equal" }
    end
    trait :contains do
      operator { "contains" }
    end
    trait :not_contains do
      operator { "not_contains" }
    end
    trait :in do
      operator { "in" }
      value { 'value1, value2' }
    end
    trait :not_in do
      operator { "not_in" }
      value { 'value1, value2' }
    end
    trait :is_empty do
      operator { "is_empty" }
    end
    trait :is_not_empty do
      operator { "is_not_empty" }
    end
    trait :begins_with do
      operator { "begins_with" }
    end
    trait :greater do
      operator { "greater" }
    end
    trait :greater_or_equal do
      operator { "greater_or_equal" }
    end
    trait :less do
      operator { "less" }
    end
    trait :less_or_equal do
      operator { "less_or_equal" }
    end
    trait :between do
      operator { "between" }
    end
    trait :not_between do
      operator { "not_between" }
    end
    trait :is_not_null do
      operator { "is_not_null" }
    end
    trait :is_null do
      operator { "is_null" }
    end

    after(:build) do |rule|
      if rule.is_custom_field
        rule.field[0] = rule.field[0].upcase
        rule.field = "cf#{rule.field}"
      end
    end

    factory :json_rule_string_equal, traits: [:string, :equal]
    factory :json_rule_string_not_equal, traits: [:string, :not_equal]
    factory :json_rule_string_contains, traits: [:string, :contains]
    factory :json_rule_string_not_contains, traits: [:string, :not_contains]
    factory :json_rule_string_begins_with, traits: [:string, :begins_with]
    factory :json_rule_string_is_empty, traits: [:string, :is_empty]
    factory :json_rule_string_is_null, traits: [:string, :is_null]
    factory :json_rule_string_is_not_empty, traits: [:string, :is_not_empty]
    factory :json_rule_string_is_not_null, traits: [:string, :is_not_null]
    factory :json_rule_string_in, traits: [:string, :in]
    factory :json_rule_string_not_in, traits: [:string, :not_in]

    factory :json_rule_boolean_equal, traits: [:boolean, :equal]
    factory :json_rule_boolean_not_equal, traits: [:boolean, :not_equal]

    factory :json_rule_date_greater, traits: [:date, :greater]
    factory :json_rule_date_equal, traits: [:date, :equal]
    factory :json_rule_date_less, traits: [:date, :less]
    factory :json_rule_date_greater_or_equal, traits: [:date, :greater_or_equal]
    factory :json_rule_date_less_or_equal, traits: [:date, :less_or_equal]
    factory :json_rule_date_between, traits: [:date, :between]
    factory :json_rule_date_not_between, traits: [:date, :not_between]
    factory :json_rule_date_is_null, traits: [:date, :is_null]
    factory :json_rule_date_is_not_null, traits: [:date, :is_not_null]

    factory :json_rule_participants_lookup_equal, traits: [:lookup, :equal]
    factory :json_rule_participants_lookup_not_equal, traits: [:lookup, :not_equal]
    factory :json_rule_participants_lookup_in, traits: [:lookup, :in]
    factory :json_rule_participants_lookup_not_in, traits: [:lookup, :not_in]

    factory :json_rule_organizer_lookup_equal, traits: [:organizer_lookup, :equal]
    factory :json_rule_organizer_lookup_not_equal, traits: [:organizer_lookup, :not_equal]
    factory :json_rule_organizer_lookup_in, traits: [:organizer_lookup, :in]
    factory :json_rule_organizer_lookup_not_in, traits: [:organizer_lookup, :not_in]

    factory :json_rule_related_lookup_equal, traits: [:related_lookup, :equal]
    factory :json_rule_associated_lookup_equal, traits: [:associated_lookup, :equal]
    factory :json_rule_associated_lookup_not_equal, traits: [:associated_lookup, :not_equal]
    factory :json_rule_associated_lookup_in, traits: [:associated_lookup, :in]
    factory :json_rule_associated_lookup_not_in, traits: [:associated_lookup, :not_in]
    factory :json_rule_associated_lookup_is_not_null, traits: [:associated_lookup, :is_not_null]

    factory :json_rule_long_equal, traits: [:long, :equal]
    factory :json_rule_long_not_equal, traits: [:long, :not_equal]
    factory :json_rule_long_is_null, traits: [:long, :is_null]
    factory :json_rule_long_is_not_null, traits: [:long, :is_not_null]
    factory :json_rule_long_greater, traits: [:long, :greater]
    factory :json_rule_long_greater_or_equal, traits: [:long, :greater_or_equal]
    factory :json_rule_long_less, traits: [:long, :less]
    factory :json_rule_long_less_or_equal, traits: [:long, :less_or_equal]
    factory :json_rule_long_in, traits: [:long, :in]
    factory :json_rule_long_not_in, traits: [:long, :not_in]
    factory :json_rule_long_between, traits: [:long, :between]
    factory :json_rule_long_not_between, traits: [:long, :not_between]

    factory :json_rule_meeting_attendance_equal, traits: [:meeting_attendance, :equal]
    factory :json_rule_meeting_attendance_not_equal, traits: [:meeting_attendance, :not_equal]
    factory :json_rule_meeting_attendance_greater, traits: [:meeting_attendance, :greater]
    factory :json_rule_meeting_attendance_greater_or_equal, traits: [:meeting_attendance, :greater_or_equal]
    factory :json_rule_meeting_attendance_less, traits: [:meeting_attendance, :less]
    factory :json_rule_meeting_attendance_less_or_equal, traits: [:meeting_attendance, :less_or_equal]
    factory :json_rule_meeting_attendance_between, traits: [:meeting_attendance, :between]
    factory :json_rule_meeting_attendance_not_between, traits: [:meeting_attendance, :not_between]
    factory :json_rule_meeting_attendance_is_null, traits: [:meeting_attendance, :is_null]
    factory :json_rule_meeting_attendance_is_not_null, traits: [:meeting_attendance, :is_not_null]

    factory :checked_in_out_by_equal, traits: [:checked_in_out_by, :equal]
    factory :checked_in_out_by_not_equal, traits: [:checked_in_out_by, :not_equal]
    factory :checked_in_out_by_in, traits: [:checked_in_out_by, :in]
    factory :checked_in_out_by_not_in, traits: [:checked_in_out_by, :not_in]
    factory :checked_in_out_by_is_null, traits: [:checked_in_out_by, :is_null]
    factory :checked_in_out_by_is_not_null, traits: [:checked_in_out_by, :is_not_null]
  end
end
