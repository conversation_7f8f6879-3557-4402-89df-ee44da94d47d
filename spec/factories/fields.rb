FactoryBot.define do
  factory :field do
    field_type { ['TEXT_FIELD', 'PICK_LIST', 'TOGGLE', 'DATETIME_PICKER', 'NUMBER', 'ENTITY_LOOKUP', 'LOOK_UP', 'RICH_TEXT', 'MEETING_INVITEES'].sample }
    description { 'Lorem ipsum dolor sit amet.' }
    is_standard { true }
    is_sortable { SORTABLE_FIELD_TYPES.include?(field_type) ? true : false }
    is_filterable { [true, false].sample }
    is_internal { [true, false].sample }
    is_required { [true, false].sample }
    active { [true, false].sample }
    tenant_id { rand(100) }
    display_name { Faker::Name.name }
    internal_name do
      if is_standard?
        display_name.camelize(:lower).tr('[]_@#!%()\-=\;><,{}~./?"*^$+\': \\', '')
      else
        "#{('cf' + display_name).camelize(:lower).tr('[]_@#!%()\-=\;><,{}~./?"*^$+\': \\', '')}"
      end
    end
    association :created_by, factory: :user
    association :updated_by, factory: :user

    factory :custom_field do
      field_type { ['TEXT_FIELD', 'TOGGLE', 'DATETIME_PICKER', 'NUMBER', 'PARAGRAPH_TEXT'].sample }
      description { 'Lorem ipsum dolor sit amet.' }
      is_standard { false }
      is_sortable { SORTABLE_FIELD_TYPES.include?(field_type) ? true : false }
      is_filterable { [true, false].sample }
      is_internal { false }
      is_required { [true, false].sample }
      active { true }
      tenant_id { rand(100) }
      display_name { Faker::Name.name }
      internal_name { "#{('cf' + display_name).camelize(:lower).tr('[]_@#!%()\-=\;><,{}~./?"*^$+\': \\', '')}" }
      association :created_by, factory: :user
      association :updated_by, factory: :user
    end
  end
end
