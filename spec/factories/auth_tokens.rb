FactoryBot.define do
  lead_permission = {
    "id" => 4,
    "name" => "lead",
    "description" => "has access to lead resource",
    "limits" => -1,
    "units" => "count",
    "action" => {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => true,
      "note" => true,
      "readAll" => true,
      "updateAll" => true
    }
  }

  share_rule_permission = {
    "id" => 4,
    "name" => "shareRule",
    "description" => "has access to ShareRule resource",
    "limits" => -1,
    "units" => "count",
    "action" => {
      "read": true,
      "write": true,
      "update": true,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "document": false,
      "readAll": true,
      "updateAll": true,
      "deleteAll": true,
      "quotation": false,
      "reshare": false,
      "reassign": false
    }
  }

  share_rule_permission_without_read_all_update_all = {
    "id" => 4,
    "name" => "shareRule",
    "description" => "has access to ShareRule resource",
    "limits" => -1,
    "units" => "count",
    "action" => {
      "read": true,
      "write": true,
      "update": true,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "document": false,
      "readAll": false,
      "updateAll": false,
      "deleteAll": true,
      "quotation": false,
      "reshare": false,
      "reassign": false
    }
  }

  meeting_permission = {
    "id" => 7,
    "name" =>  "meeting",
    "description" =>  "has access to team resource",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true,
      "deleteAll" => true
    }
  }

  meeting_without_delete_permission = {
    "id" => 7,
    "name" =>  "meeting",
    "description" =>  "has access to team resource",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => false,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true,
      "deleteAll" => false
    }
  }

  meeting_without_read_all_and_update_all_permission = {
    "id" => 7,
    "name" =>  "meeting",
    "description" =>  "has access to team resource",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => false,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => false,
      "updateAll" => false,
      "deleteAll" => false
    }
  }

  meeting_with_read_all_and_without_update_all_permission = {
    "id" => 7,
    "name" =>  "meeting",
    "description" =>  "has access to team resource",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => false,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => false,
      "deleteAll" => false
    }
  }

  note_write_true_permission = {
    "id" => 8,
    "name" =>  "note",
    "description" =>  "has access to note",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true,
      "deleteAll" => true
    }
  }

  note_write_false_permission = {
    "id" => 8,
    "name" =>  "note",
    "description" =>  "has access to note",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => false,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true
    }
  }

  note_delete_false_permission = {
    "id" => 8,
    "name" =>  "note",
    "description" =>  "has access to note",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => false,
      "update" => true,
      "delete" => false,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true,
      "deleteAll" => false
    }
  }

  note_delete_all_false_permission = {
    "id" => 8,
    "name" =>  "note",
    "description" =>  "has access to note",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => false,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => true,
      "updateAll" => true,
      "deleteAll" => false
    }
  }

  note_read_false_permission = {
    "id" => 8,
    "name" =>  "note",
    "description" =>  "has access to note",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => false,
      "write" => false,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => false,
      "updateAll" => true
    }
  }

  custom_field_permission = {
    "id" => 9,
    "name" =>  "customField",
    "description" =>  "has access to custom fields",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => false,
      "updateAll" => false
    }
  }

  custom_field_without_permission = {
    "id" => 9,
    "name" =>  "customField",
    "description" =>  "has access to custom fields",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => false,
      "write" => false,
      "update" => false,
      "delete" => false,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => false,
      "updateAll" => false
    }
  }

  factory :auth_token, class: 'Auth::Token' do
    transient do
      expiry { (Time.current + 1.hour).to_i }
      user_id { rand(1000) }
      tenant_id { rand(1000) }
      access_token { SecureRandom.uuid }
      username { '<EMAIL>'}
      token_data {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_write_true_permission,
            custom_field_permission,
            share_rule_permission
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_custom_field {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_write_true_permission,
            custom_field_without_permission
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_meeting_delete_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_without_delete_permission,
            note_write_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_meeting_without_read_all_and_update_all_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_without_read_all_and_update_all_permission,
            note_write_true_permission,
            share_rule_permission
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_share_rule_without_read_all_and_update_all_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_without_read_all_and_update_all_permission,
            note_write_true_permission,
            share_rule_permission_without_read_all_update_all
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_meeting_with_read_all_and_without_update_all_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_with_read_all_and_without_update_all_permission,
            note_write_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_note_write_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_write_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_note_delete_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_delete_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_note_delete_all_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_delete_all_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_note_read_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            meeting_permission,
            note_read_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      payload {
        {
          "iss" => "sell",
          "data" => token_data
        }
      }

      payload_without_custom_field_permission {
        {
          'iss' => 'sell',
          'data' => token_data_without_custom_field
        }
      }

      payload_without_meeting_delete_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_meeting_delete_permission
        }
      }

      payload_without_note_write_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_note_write_permission
        }
      }

      payload_without_note_read_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_note_read_permission
        }
      }

      payload_without_note_delete_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_note_delete_permission
        }
      }

      payload_without_note_delete_all_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_note_delete_all_permission
        }
      }

      payload_meeting_without_read_all_and_update_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_meeting_without_read_all_and_update_all_permission
        }
      }

      payload_meeting_with_read_all_and_without_update_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_meeting_with_read_all_and_without_update_all_permission
        }
      }

      payload_share_rule_with_out_read_all_and_without_update_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_share_rule_without_read_all_and_update_all_permission
        }
      }
    end

    token { JWT.encode payload, nil, 'none' }

    trait :without_custom_field do
      token { JWT.encode payload_without_custom_field_permission, nil, 'none' }
    end

    trait :without_note_write_permission do
      token { JWT.encode payload_without_note_write_permission, nil, 'none' }
    end

    trait :without_meeting_delete_permission do
      token { JWT.encode payload_without_meeting_delete_permission, nil, 'none' }
    end

    trait :without_note_read_permission do
      token { JWT.encode payload_without_note_read_permission, nil, 'none' }
    end

    trait :without_note_delete_permission do
      token { JWT.encode payload_without_note_delete_permission, nil, 'none' }
    end

    trait :without_note_delete_all_permission do
      token { JWT.encode payload_without_note_delete_all_permission, nil, 'none' }
    end

    trait :without_meeting_read_all_and_update_all_permission do
      token { JWT.encode payload_meeting_without_read_all_and_update_all_permission, nil, 'none' }
    end

    trait :without_meeting_update_all_permission do
      token { JWT.encode payload_meeting_with_read_all_and_without_update_all_permission, nil, 'none' }
    end

    trait :without_share_rule_update_all_permission do
      token { JWT.encode payload_meeting_with_read_all_and_without_update_all_permission, nil, 'none' }
    end

    factory :auth_token_invalid do
      token { 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJleHBpcmVzSW4iOjQzMTk5LCJleHBpcnkiOjE1NzY0OTM3MTAsInRva2VuVHlwZSI6ImJlYXJlciIsInBlcm1pc3Npb25zIjpbeyJpZCI6NCwibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwid3JpdGUiOnRydWUsInVwZGF0ZSI6dHJ1ZSwiZGVsZXRlIjp0cnVlLCJlbWFpbCI6ZmFsc2UsImNhbGwiOmZhbHNlLCJzbXMiOmZhbHNlLCJ0YXNrIjp0cnVlLCJub3RlIjp0cnVlLCJyZWFkQWxsIjp0cnVlLCJ1cGRhdGVBbGwiOnRydWV9fSx7ImlkIjo3LCJuYW1lIjoidGVhbSIsImRlc2NyaXB0aW9uIjoiaGFzIGFjY2VzcyB0byB0ZWFtIHJlc291cmNlIiwibGltaXRzIGlvbiI6eyJyZWFkIjp0cnVlLCJ3cml0ZSI6dHJ1ZSwidXBkYXRlIjp0cnVlLCJkZWxldGUiOnRydWUsImVtYWlsIjpmYWxzZSwiY2FsbCI6ZmFsc2UsInNtcyI6ZmFsc2UsInRhc2siOmZhbHNlLCJub3RlIjpmYWxzZSwicmVhZEFsbCI6dHJ1ZSwidXBkYXRlQWxsIjp0cnVlfX1dLCJ1c2VySWQiOiIxMiIsInVzZXJ_U3W9DSeom8' }
    end

    factory :auth_token_expired do
      expiry { (Time.now - 1.day).to_i}
      token { JWT.encode payload, nil, 'none' }
    end
  end
end
