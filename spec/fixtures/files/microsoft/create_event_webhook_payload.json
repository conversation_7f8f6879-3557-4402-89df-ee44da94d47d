{"value": [{"subscriptionId": "fa1104bf-4b37-4315-8026-3ac7202b4409", "subscriptionExpirationDateTime": "2023-03-01T13:41:20+00:00", "changeType": "updated", "resource": "Users/f678077d-f779-4e40-a094-3f3da8959984/Events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=", "resourceData": {"@odata.type": "#Microsoft.Graph.Event", "@odata.id": "Users/f678077d-f779-4e40-a094-3f3da8959984/Events/AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA=", "@odata.etag": "W/\"DwAAABYAAACSVVe1z3i9QaaEullonKYkAAGAHGKX\"", "id": "AAMkADhmNjFlYTBjLTg5M2ItNDQ2Mi04MDZjLTc2MjFlYzYzMjViZABGAAAAAACZq6JofmC5RI9nJYwEygQPBwCSVVe1z3i9QaaEullonKYkAAAAAAENAACSVVe1z3i9QaaEullonKYkAAGBPr7OAAA="}, "clientState": "SecretClientState", "tenantId": "46ecb5f0-1227-4755-a101-64d39a05e1c7"}]}