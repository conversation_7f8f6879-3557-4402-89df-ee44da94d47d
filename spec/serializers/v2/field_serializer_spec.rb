# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V2::FieldSerializer do
  describe '#call' do
    context 'when field is present' do
      context 'when field is of type other than picklist' do
        let(:field) { create(:field) }

        it 'returns serialized data for v2 event' do
          serialized_response = described_class.call(field).result

          expect(serialized_response).to eq(
            {
              "id" => field.id,
              "name" => field.internal_name,
              "displayName" => field.display_name,
              "fieldType" => field.field_type,
              "description" => "Lorem ipsum dolor sit amet.",
              "standard" => true,
              "sortable" => field.is_sortable,
              "filterable" => field.is_filterable,
              "internal" => field.is_internal,
              "required" => field.is_required,
              "active" => field.active,
              "tenantId" => field.tenant_id,
              "picklistValues" => nil,
              "createdAt" => field.created_at.iso8601(6),
              "updatedAt" => field.updated_at.iso8601(6),
              "createdBy" => {
                "id" => field.created_by.id,
                "name" => field.created_by.name
              },
              "updatedBy" => {
                "id" => field.updated_by.id,
                "name" => field.updated_by.name
              }
            }
          )
        end
      end

      context 'when field is of type picklist' do
        let(:field) { create(:field, field_type: 'PICK_LIST') }
        let(:picklist) { create(:picklist, field: field, tenant_id: field.tenant_id) }
        let(:picklist_values) { create_list(:picklist_value, 2, picklist: picklist, tenant_id: field.tenant_id) }

        before do
          picklist
          picklist_values
        end

        it 'returns serialized data for v2 event' do
          serialized_response = described_class.call(field).result

          expect(serialized_response.keys).to include('picklistValues')
          expect(serialized_response['picklistValues'].count).to eq(2)
        end
      end
    end

    context 'when field is nil' do
      it 'returns nil' do
        expect(described_class.call(nil).result).to eq(nil)
      end
    end
  end
end
