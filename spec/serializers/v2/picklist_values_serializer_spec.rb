# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V2::PicklistValuesSerializer do
  describe '#call' do
    context 'when picklist values are present' do
      let(:field) { create(:field, field_type: 'PICK_LIST') }
      let(:picklist) { create(:picklist, field: field, tenant_id: field.tenant_id) }
      let(:picklist_values) { create_list(:picklist_value, 2, picklist: picklist, tenant_id: field.tenant_id) }

      it 'returns serialized picklist values' do
        serialized_response = described_class.call(picklist_values).result

        value_one = picklist_values.first
        value_two = picklist_values.last
        expect(serialized_response).to match_array(
          [
            {
              "id" => value_one.id,
              "name" => value_one.internal_name,
              "displayName" => value_one.display_name,
              "disabled" => false,
              "createdAt" => value_one.created_at.iso8601(6),
              "updatedAt" => value_one.updated_at.iso8601(6)
            },
            {
              "id" => value_two.id,
              "name" => value_two.internal_name,
              "displayName" => value_two.display_name,
              "disabled" => false,
              "createdAt" => value_two.created_at.iso8601(6),
              "updatedAt" => value_two.updated_at.iso8601(6)
            }
          ]
        )
      end
    end

    context 'when picklist values are not present' do
      it 'returns nil' do
        expect(described_class.call(nil).result).to eq(nil)
      end
    end
  end
end
