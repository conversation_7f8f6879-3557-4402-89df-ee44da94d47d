# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRule, type: :model do
  describe 'entity definition' do
    describe 'fields & validations' do
      it do
        should validate_presence_of(:tenant_id)
      end

      it do
        should validate_presence_of(:from_id)
      end

      it do
        should validate_presence_of(:to_id)
      end

      it 'should validate value of from_type and to_type' do
        share_rule = build(:share_rule, from_type: 'invalid', to_type: 'invalid')
        expect(share_rule).to be_invalid
        expect(share_rule.errors.messages[:from_type]).to eq(['is not included in the list'])
        expect(share_rule.errors.messages[:to_type]).to eq(['is not included in the list'])
      end

      it 'supports 3 string fields for filter' do
        expect(described_class.string_fields).to be == %w[name from_type to_type]
      end

      it 'supports 1 boolean field for filter' do
        expect(described_class.boolean_fields).to be == ['system_default']
      end

      it 'supports 2 date fields for filter' do
        expect(described_class.date_fields).to be == %w[created_at updated_at]
      end

      it 'supports 5 long double fields for filter' do
        expect(described_class.long_double_fields).to be == %w[from_id to_id meeting_id created_by_id updated_by_id]
      end
    end

    describe '#associations' do
      it { should belong_to(:created_by) }

      it { should belong_to(:updated_by) }
    end
  end

  describe '.for_user' do
    let(:user) { create(:user) }

    context 'when meeting update check is false' do
      context 'when user is present in a team' do
        let(:team) { create(:team, tenant_id: user.tenant_id, user_ids: [user.id]) }

        before { create(:share_rule, tenant_id: user.tenant_id, share_all_records: true, to_id: team.id, to_type: 'TEAM') }

        it 'returns share rules for user' do
          response = described_class.for_user(user.tenant_id, user.id)

          expect(response.count).to eq(1)
          expect(response.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND (\"share_rules\".\"to_id\" = #{user.id} AND \"share_rules\".\"to_type\" = 'USER' OR \"share_rules\".\"to_id\" = #{team.id} AND \"share_rules\".\"to_type\" = 'TEAM')")
        end
      end

      context 'when user is not present in any team' do
        before { create(:share_rule, tenant_id: user.tenant_id, share_all_records: true, to_id: user.id) }

        it 'returns share rules for user' do
          response = described_class.for_user(user.tenant_id, user.id)

          expect(response.count).to eq(1)
          expect(response.to_sql).to eq("SELECT \"share_rules\".* FROM \"share_rules\" WHERE \"share_rules\".\"tenant_id\" = #{user.tenant_id} AND \"share_rules\".\"to_id\" = #{user.id} AND \"share_rules\".\"to_type\" = 'USER'")
        end
      end
    end
  end
end
