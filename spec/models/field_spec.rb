require 'rails_helper'

RSpec.describe Field, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:internal_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:display_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:field_type)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:active)
          .of_type(:boolean)
          .with_options(default: true)
      end
      it do
        should have_db_column(:is_standard)
          .of_type(:boolean)
          .with_options(default: true)
      end
      it do
        should have_db_column(:is_sortable)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:is_filterable)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:is_internal)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:is_required)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:tenant_id)
        .of_type(:integer)
        .with_options(null: false)
      end
      it do
        should have_db_column(:created_by_id)
        .of_type(:integer)
        .with_options(null: false)
      end
      it do
        should have_db_column(:updated_by_id)
        .of_type(:integer)
        .with_options(null: false)
      end
      it do
        should have_db_column(:created_at)
        .of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at)
        .of_type(:datetime)
      end
    end

    describe 'fields & validations' do
      it 'should validate uniqueness of field with tenant' do
        user = FactoryBot.create(:user)
        FactoryBot.create(:field, field_type: 'NUMBER', created_by: user, updated_by: user)
        should validate_presence_of(:internal_name)
        should validate_uniqueness_of(:internal_name).scoped_to(:tenant_id).case_insensitive
      end
      it do
        should validate_presence_of(:display_name)
      end
      it do
        should validate_inclusion_of(:field_type)
        .in_array(%w[TEXT_FIELD TOGGLE DATE_PICKER DATETIME_PICKER PICK_LIST ENTITY_LOOKUP LOOK_UP NUMBER PARAGRAPH_TEXT MEETING_INVITEES ENTITY_PICKLIST URL CHECKBOX RICH_TEXT])
        .with_message("Invalid Field Type")
      end
      it do
        should have_one(:picklist)
        .dependent(:destroy)
      end
      it do
        should validate_presence_of(:tenant_id)
      end
      it do
        should belong_to(:created_by).class_name('User').required(true)
      end
      it do
        should belong_to(:updated_by).class_name('User').required(true)
      end

      it 'should validate is_sortable for custom fields' do
        field = build(:custom_field, field_type: 'PARAGRAPH_TEXT', is_sortable: true)
        expect(field.valid?).to be(false)
        expect(field.errors.messages.values.first).to match_array(['cannot make field with type PARAGRAPH_TEXT sortable'])
      end
    end

    describe 'instance methods' do
      context '#system_default?' do
        it 'should return true if field is standard and is timezone or status' do
          user = create(:user)

          field = create(:field, field_type: 'ENTITY_PICKLIST', internal_name: 'status', is_standard: true, created_by: user, updated_by: user)
          expect(field.system_default?).to be(true)

          field = create(:field, field_type: 'NUMBER', created_by: user, updated_by: user)
          expect(field.system_default?).to be(false)
        end
      end
    end
  end
end
