require 'rails_helper'

RSpec.describe LookUp, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id).
                with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:entity).
                of_type(:string).
                with_options(null: false)
      end
      it do
        should have_db_column(:tenant_id).
                of_type(:integer).
                with_options(null: false)
      end
      it do
        should have_db_column(:name).
                of_type(:string)
      end
      it do
        should have_db_column(:email).
                of_type(:string)
      end
      it do
        should have_db_column(:created_at).
                of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).
                of_type(:datetime)
      end
      it do
        should have_db_column(:public_id).
                of_type(:string)
      end
    end

    describe 'callbacks' do
      context 'after_create - update entity to store id' do
        it 'stores entity as external_id incase of external entity' do
          look_up = build(:look_up, entity: LOOKUP_EXTERNAL)
          look_up.save
          expect(look_up.reload.entity).to be_eql "#{LOOKUP_EXTERNAL}_#{look_up.id}"
        end
      end
    end

    describe 'validations' do
      it do
        should allow_value('lead_1212', 'deal_1000', 'contact_99999999', 'company_1', 'timezone_2').for(:entity)
      end
      it do
        should_not allow_value('TEST_1212', 'LEAD_1000', 'DEAL_400', 'tesst', '1212112', 'timezone').for(:entity)
      end
      it do
        should validate_presence_of(:entity)
      end
      it do
        should validate_presence_of(:public_id)
      end
      it do
        should_not validate_presence_of(:name)
      end
      it 'entity value is not modifiable once created' do
        look_up = create(:look_up)
        look_up.entity = 'LEAD_92888888'
        expect(look_up.save).to be false
      end
      it 'name is always modifyble' do
        look_up = create(:look_up)
        look_up.name = Faker::Name.name
        expect(look_up.save).to be true
      end
    end

    describe 'associations' do
      it do
        should have_many(:meeting_look_ups)
      end
    end

    describe 'instance methods' do
      context '#entity_id' do
        before { @look_up = create(:company_look_up) }
        it 'should return entity id in integer' do
          expect(@look_up.entity_id).to eq(@look_up.entity.split("_")[1].to_i)
          expect(@look_up.entity_id.class).to eq(Integer)
        end
      end

      context '#entity_type' do
        before { @look_up = create(:company_look_up) }
        it 'shold return valid entity type' do
          expect(@look_up.entity_type).to eq(LOOKUP_COMPANY)
        end
      end

      context '#is_a_user?' do
        before { @look_up = create(:user_look_up) }
        it 'shold return valid entity type' do
          expect(@look_up.is_a_user?).to be(true)
        end
      end

      context '#is_a_lead?' do
        before { @look_up = create(:lead_look_up) }
        it 'shold return valid entity type' do
          expect(@look_up.is_a_lead?).to be(true)
        end
      end

      context '#is_a_deal?' do
        before { @look_up = create(:deal_look_up) }
        it 'shold return valid entity type' do
          expect(@look_up.is_a_deal?).to be(true)
        end
      end

      context '#is_a_company?' do
        before { @look_up = create(:company_look_up) }
        it 'shold return valid entity type' do
          expect(@look_up.is_a_company?).to be(true)
        end
      end

      context '#future_scheduled_meeting_time_from_now' do
        before do
          user = create(:user)
          @meeting = create(:meeting, owner: user)
          @meeting.participants << build(:user_look_up, entity_id: user.id, tenant_id: user.tenant_id, name: user.name, email: "<EMAIL>")
          @look_up = LookUp.last
        end
        it 'should return maximum scheduled on date time for the lookup' do
          expect(@look_up.future_scheduled_meeting_time_from_now.round(2)).to eq(@meeting.from.round(2))
        end
      end
    end
  end
end
