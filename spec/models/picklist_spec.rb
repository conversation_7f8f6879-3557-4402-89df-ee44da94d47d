require 'rails_helper'

RSpec.describe Picklist, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:internal_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:display_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:field_id)
          .of_type(:integer)
          .with_options(null: true)
      end
      it do
        should have_db_column(:tenant_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:created_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at)
          .of_type(:datetime)
      end
    end
    describe 'fields & validations' do
      it 'should validate uniqueness of picklist with tenant' do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', created_by: user, updated_by: user)
        FactoryBot.create(:picklist, field: field, tenant_id: field.tenant_id )
        should validate_presence_of(:internal_name)
        should validate_uniqueness_of(:internal_name).scoped_to([:tenant_id])
      end
      it do
        should validate_presence_of(:display_name)
      end
      it do
        should have_many(:picklist_values)
          .class_name('PicklistValue')
          .dependent(:destroy)
      end
      it do
        should belong_to(:field)
      end
      it do
        should validate_presence_of(:tenant_id)
      end

      it "should raise error if tenant id mismatched on create" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', tenant_id: 2, created_by: user, updated_by: user)

        begin
          FactoryBot.create(:picklist, field: field, tenant_id: 3)
        rescue ActiveRecord::ActiveRecordError => e
          expect(e.class).to eql(ActiveRecord::RecordInvalid)
        end
      end

      it "should not have mismatched tenant id for field and picklist on update" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', tenant_id: 2, created_by: user, updated_by: user)
        picklist = FactoryBot.create(:picklist, field: field, tenant_id: 2)
        picklist.tenant_id = 3
        picklist.valid?
        expect(picklist.errors[:tenant_id].first).to eql('value cannot be mismatched between picklist and value.')
      end

      it "should not have field with field type other PICK_LIST" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'DATETIME_PICKER', created_by: user, updated_by: user)
        picklist = FactoryBot.build(:picklist, field: field, tenant_id: field.tenant_id)
        picklist.valid?
        expect(picklist.errors[:field].first).to eql("cannot have picklist for field type other than #{PICKLIST_FIELD_TYPES.join(' or ')}.")
      end
    end
  end
end
