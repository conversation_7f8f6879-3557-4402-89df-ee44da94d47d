require 'rails_helper'

RSpec.describe JsonRule.new({}), type: :model do
  describe 'entity definition' do
    describe 'validations' do
      it do
        should validate_presence_of(:operator)
      end
      it do
        should validate_presence_of(:field)
      end
      it do
        should validate_presence_of(:type)
      end
      it do
        should validate_presence_of(:value)
      end
      it do
        should allow_value('string', 'long', 'double', 'date', 'boolean', 'participants_lookup', 'organizer_lookup').for(:type)
      end
      it do
        should_not allow_value('datetime', 'XYZ').for(:type)
      end

      context 'when json rule for meeting' do
        context 'string fields' do
          it 'allows location field for filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: 'location'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows status field for filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'doesnt allow any other string field for filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: 'description'
            })
            expect(@json_rule).to_not be_valid
          end

          it 'allows equal operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "not_equal",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows contains operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "contains",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_contains operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "not_contains",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "in",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "not_in",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_empty operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "is_empty",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_empty operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "is_not_empty",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows begins_with operator for string filter' do
            @json_rule = JsonRule.new({
              operator: "begins_with",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any random operator for string filter" do
            @json_rule = JsonRule.new({
              operator: "test",
              type: "string",
              value: "lore ipsum",
              field: 'status'
            })
            expect(@json_rule).to_not be_valid
          end
        end

        context 'date fields' do
          it 'allows greater operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "greater",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows equal operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "less",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater_or_equal operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "greater_or_equal",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less_or_equal operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "less_or_equal",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows between operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "between",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_between operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "not_between",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_null operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "is_null",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_null operator for date filter' do
            @json_rule = JsonRule.new({
              operator: "is_not_null",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any invalid operator for date filter" do
            @json_rule = JsonRule.new({
              operator: "test",
              type: "date",
              value: Time.current,
              field: ['from', 'to'].sample
            })
            expect(@json_rule).to_not be_valid
          end

          it "doesn't allow any invalid field for date filter" do
            @json_rule = JsonRule.new({
              operator: "test",
              type: "date",
              value: Time.current,
              field: 'status'
            })
            expect(@json_rule).to_not be_valid
          end
        end

        context 'long fields' do
          it 'allows id field for filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'doesnt allow any other long field for filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "long",
              value: 123456,
              field: 'test'
            })
            expect(@json_rule).to_not be_valid
          end

          it 'allows equal operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "not_equal",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_null operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "is_not_null",
              type: "long",
              value: nil,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_null operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "is_null",
              type: "long",
              value: nil,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "greater",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater_or_equal operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "greater_or_equal",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less_or_equal operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "less_or_equal",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "less",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows between operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "between",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_between operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "not_between",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "in",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for long filter' do
            @json_rule = JsonRule.new({
              operator: "not_in",
              type: "long",
              value: 123456,
              field: 'id'
            })
            expect(@json_rule).to be_valid
          end
        end

        context 'boolean fields' do
          it 'allows equal operator for boolean filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "boolean",
              value: [true, false].sample,
              field: 'allDay'
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any invalid operator for boolean filter" do
            @json_rule = JsonRule.new({
              operator: "greater",
              type: "boolean",
              value: [true, false].sample,
              field: 'allDay'
            })
            expect(@json_rule).to_not be_valid
          end

          it "doesn't allow any invalid field for date filter" do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "date",
              value: [true, false].sample,
              field: 'status'
            })
            expect(@json_rule).to_not be_valid
          end
        end

        context 'participants_lookup fields' do
          it 'allows equal operator for participants_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "participants_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample }.to_json,
              field: 'participants'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for participants_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_equal",
              type: "participants_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample }.to_json,
              field: 'participants'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for participants_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "in",
              type: "participants_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }].to_json,
              field: 'participants'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for participants_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_in",
              type: "participants_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }].to_json,
              field: 'participants'
            })
            expect(@json_rule).to be_valid
          end
        end

        context 'organizer_lookup fields' do
          it 'allows equal operator for organizer_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "organizer_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample }.to_json,
              field: 'organizer'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for organizer_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_equal",
              type: "organizer_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample }.to_json,
              field: 'organizer'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for organizer_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "in",
              type: "organizer_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }].to_json,
              field: 'organizer'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for organizer_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_in",
              type: "organizer_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }].to_json,
              field: 'organizer'
            })
            expect(@json_rule).to be_valid
          end
        end

        context 'related_lookup fields' do
          it 'allows equal operator for related_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "related_lookup",
              value: { id: 1, entity: ["deal", "lead", "contact"].sample }.to_json,
              field: 'related_to'
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow invalid operator for related_lookup filter" do
            @json_rule = JsonRule.new({
              operator: "test",
              type: "related_lookup",
              value: { id: 1, entity: ["deal", "lead", "contact"].sample }.to_json,
              field: 'related_to'
            })
            expect(@json_rule).not_to be_valid
          end

          it "doesn't allow invalid field for related_lookup filter" do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "related_lookup",
              value: { id: 1, entity: ["deal", "lead", "contact"].sample }.to_json,
              field: 'test'
            })
            expect(@json_rule).not_to be_valid
          end
        end

        context 'associated_lookup fields' do
          it 'allows equal operator for associated_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "equal",
              type: "associated_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample },
              field: 'associated_to'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for associated_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_equal",
              type: "associated_lookup",
              value: { id: 1, entity: ["user", "lead", "contact"].sample },
              field: 'associated_to'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for associated_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "in",
              type: "associated_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }],
              field: 'associated_to'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for associated_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "not_in",
              type: "associated_lookup",
              value: [{ id: 1, entity: ["user", "lead", "contact"].sample }, { id: 2, entity: ["user", "lead", "contact"].sample }],
              field: 'associated_to'
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_null operator for associated_lookup filter' do
            @json_rule = JsonRule.new({
              operator: "is_not_null",
              type: "associated_lookup",
              value: 'deal',
              field: 'associated_to'
            })
            expect(@json_rule).to be_valid
          end
        end
      end

      context 'when json rule for share rule' do
        context 'string fields' do
          it 'allows name field for filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows name field for filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'doesnt allow any other string field for filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: 'description'
            })
            expect(@json_rule).to_not be_valid
          end

          it 'allows equal operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_equal",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows contains operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "contains",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_contains operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_contains",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "in",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_in",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_empty operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_empty",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_empty operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_not_empty",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows begins_with operator for string filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "begins_with",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any random operator for string filter" do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "test",
              type: "string",
              value: "lore ipsum",
              field: ShareRule.string_fields.sample
            })
            expect(@json_rule).to_not be_valid
          end
        end

        context 'date fields' do
          it 'allows greater operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "greater",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows equal operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "less",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater_or_equal operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "greater_or_equal",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less_or_equal operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "less_or_equal",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows between operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "between",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_between operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_between",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_null operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_null",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_null operator for date filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_not_null",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any invalid operator for date filter" do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "test",
              type: "date",
              value: Time.current,
              field: ShareRule.date_fields.sample
            })
            expect(@json_rule).to_not be_valid
          end

          it "doesn't allow any invalid field for date filter" do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "test",
              type: "date",
              value: Time.current,
              field: 'status'
            })
            expect(@json_rule).to_not be_valid
          end
        end

        context 'long fields' do
          it 'allows created_by/updated_by field for filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'doesnt allow any other long field for filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "long",
              value: 123456,
              field: 'test'
            })
            expect(@json_rule).to_not be_valid
          end

          it 'allows equal operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_equal operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_equal",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_not_null operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_not_null",
              type: "long",
              value: nil,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows is_null operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "is_null",
              type: "long",
              value: nil,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "greater",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows greater_or_equal operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "greater_or_equal",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less_or_equal operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "less_or_equal",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows less operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "less",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows between operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "between",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_between operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_between",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows in operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "in",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not_in operator for long filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_in",
              type: "long",
              value: 123456,
              field: ShareRule.long_double_fields.sample
            })
            expect(@json_rule).to be_valid
          end
        end

        context 'boolean fields' do
          it 'allows equal operator for boolean filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "equal",
              type: "boolean",
              value: [true, false].sample,
              field: ShareRule.boolean_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it 'allows not equal operator for boolean filter' do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "not_equal",
              type: "boolean",
              value: [true, false].sample,
              field: ShareRule.boolean_fields.sample
            })
            expect(@json_rule).to be_valid
          end

          it "doesn't allow any invalid operator for boolean filter" do
            @json_rule = JsonRule.new({
              meeting_or_share_rule_model: 'ShareRule',
              operator: "greater",
              type: "boolean",
              value: [true, false].sample,
              field: ShareRule.boolean_fields.sample
            })
            expect(@json_rule).to_not be_valid
          end
        end
      end
    end

    describe '.initialize' do
      before do
        @json_rule = JsonRule.new({
          operator: "equal",
          field: "allDay",
          type: "boolean",
          value: "true"
        })
      end

      it do
        expect(@json_rule).to have_attributes(operator: "equal")
      end

      it do
        expect(@json_rule).to have_attributes(field: "all_day")
      end

      it do
        expect(@json_rule).to have_attributes(value: "true")
      end

      it do
        expect(@json_rule).to have_attributes(type: "boolean")
      end
    end
  end
end
