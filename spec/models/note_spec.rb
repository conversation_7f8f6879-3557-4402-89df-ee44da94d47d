require 'rails_helper'

RSpec.describe Note, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id).
          with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:description).
          of_type(:text).with_options(null: false)
      end

      it do
        should have_db_column(:created_by_id).
          of_type(:integer).with_options(null: false)
      end
    end

    describe 'validations' do
      it do
        should validate_presence_of(:description)
      end
      it do
        should validate_presence_of(:created_by)
      end
    end
  end

  describe 'class methods' do
    context '#usage per tenant' do
      it 'will return count of note per tenant' do
        user = create(:user)
        meeting = create(:meeting, owner: user, tenant_id: user.tenant_id)
        FactoryBot.create_list(:note, 5, meeting: meeting, created_by: meeting.owner, tenant_id: user.tenant_id)
        user2 = create(:user)
        meeting = create(:meeting, owner: user2, tenant_id: user2.tenant_id)
        FactoryBot.create_list(:note, 3, meeting: meeting, created_by: meeting.owner, tenant_id: user2.tenant_id)
        data = Note.usage_per_tenant
        expected_response = [{:tenantId=>user.tenant_id, :count=>5, :usageEntity=>"MEETING_NOTE"}, {:tenantId=>user2.tenant_id, :count=>3, :usageEntity=>"MEETING_NOTE"}]
        expect(data).to match_array(expected_response)
      end

      it 'will return count of note per tenant, returns 0' do
        data = Note.usage_per_tenant(111)
        expected_response = [{:tenantId=>111, :count=>0, :usageEntity=>"MEETING_NOTE"}]
        expect(data).to eql expected_response
      end
    end
  end
end
