require 'rails_helper'

RSpec.describe PicklistValue, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:internal_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:display_name)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:disabled)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:picklist_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:tenant_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:created_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at)
          .of_type(:datetime)
      end
    end
    describe 'fields & validations' do
      it do
        field = create(:field, field_type: 'PICK_LIST')
        picklist = create(:picklist, field: field, tenant_id: field.tenant_id)
        create(:picklist_value, picklist: picklist, tenant_id: picklist.tenant_id)
        should validate_presence_of(:internal_name)
        should validate_uniqueness_of(:internal_name).scoped_to(:picklist_id).case_insensitive
      end
      it do
        should validate_presence_of(:display_name)
      end
      it do
        should belong_to(:picklist)
      end
      it do
        should validate_presence_of(:tenant_id)
      end

      it "should not update system default picklist value" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', is_standard: true, created_by: user, updated_by: user)
        picklist = FactoryBot.create(:picklist, field: field, tenant_id: field.tenant_id)
        picklist_value = FactoryBot.create(:picklist_value, picklist: picklist, tenant_id: picklist.tenant_id)
        picklist_value.disabled = true
        expect(picklist_value.save).to eql(false)
        expect(picklist_value.errors[:base].first).to eql('cannot update system default picklist values.')
      end

      it "should raise error if tenant id mismatched on create" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', tenant_id: 2, created_by: user, updated_by: user)
        picklist = FactoryBot.create(:picklist, field: field, tenant_id: 2)
        begin
          FactoryBot.create(:picklist_value, picklist: picklist, tenant_id: 3)
        rescue ActiveRecord::ActiveRecordError => e
          expect(e.class).to eql(ActiveRecord::RecordInvalid)
        end
      end

      it "should not have mismatched tenant id for picklist and value on update" do
        user = FactoryBot.create(:user)
        field = FactoryBot.create(:field, field_type: 'PICK_LIST', tenant_id: 2, created_by: user, updated_by: user)
        picklist = FactoryBot.create(:picklist, field: field, tenant_id: 2)
        picklist_value = FactoryBot.create(:picklist_value, picklist: picklist, tenant_id: 2)
        picklist_value.tenant_id = 3
        picklist_value.valid?
        expect(picklist_value.errors[:tenant_id].first).to eql('value cannot be mismatched between picklist and value.')
      end
    end
  end
end
