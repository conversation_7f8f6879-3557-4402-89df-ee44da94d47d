require 'rails_helper'

RSpec.describe Event::PicklistValueUpdated, type: :model do
  before do
    user = create(:user)
    @field = create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST')
    picklist = create(:picklist, field: @field, tenant_id: user.tenant_id)
    picklist_values = create_list(:picklist_value, 3, picklist: picklist, tenant_id: user.tenant_id)
    picklist_value = picklist_values.first
    old_hash = PicklistValuesSerializer.call([picklist_value], @field.system_default?).result.first
    picklist_value.update(display_name: 'Updated Picklist Value Name')
    new_hash = PicklistValuesSerializer.call([picklist_value], @field.system_default?).result.first
    @event = described_class.new(new_hash, old_hash, user)
  end

  describe 'methods' do
    context '#routing_key' do
      it 'should return correct key' do
        expect(@event.routing_key).to eq('meeting.picklist.value.updated')
      end
    end

    context '#to_json' do
      it 'should return correct payload' do
        payload = JSON.parse(@event.to_json)

        expect(payload.keys).to match_array(%w[newEntity oldEntity metadata])
        expect(payload['newEntity'].keys).to match_array(%w[id name displayName systemDefault disabled])
        expect(payload['oldEntity'].keys).to match_array(%w[id name displayName systemDefault disabled])
        expect(payload['metadata'].keys).to match_array(%w[userId tenantId])
        expect(payload['newEntity']['displayName']).not_to eq(payload['oldEntity']['displayName'])
      end
    end
  end
end
