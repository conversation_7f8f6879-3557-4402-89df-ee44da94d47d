# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::ShareRuleCreatedV2, type: :model do
  let(:share_rule) { create(:share_rule) }
  let(:event) { Event::ShareRuleCreatedV2.new(share_rule) }
  before do
    @user = share_rule.created_by
    auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
    auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
    Thread.current[:auth] = auth_data
  end

  describe 'attributes' do
    it 'should have share_rule attribute' do
      expect(event).to respond_to(:share_rule)
      expect(event.share_rule).to eq(share_rule)
    end
  end

  describe 'methods' do
    it 'should return correct routing key' do
      expect(event.routing_key).to eq('shareRule.created.v2')
    end

    it 'should return json converted payload' do
      share_rule_serialized = ShareRuleSerializer.new(share_rule, payload_for_event: true).call
      expect(event.to_json).to eq({
        'entity' => share_rule_serialized,
        'oldEntity' => nil,
        'metadata' => {
          'tenantId': share_rule.tenant_id,
          'userId': @user.id,
          'entityType': 'SHARE_RULE',
          'entityId': share_rule.id,
          'entityAction': 'CREATED'
        }
      }.to_json)
    end
  end

  describe 'invalidations' do
    context 'when calling a private method' do
      it 'should raise error' do
        expect{ event.as_json }.to raise_error(NoMethodError)
      end
    end
  end
end
