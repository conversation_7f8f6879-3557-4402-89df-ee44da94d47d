require 'rails_helper'

RSpec.describe Event::MeetingFieldUpdated, type: :model do
  before do
    user = create(:user)
    @field = create(:custom_field, tenant_id: user.tenant_id, created_by: user, updated_by: user, field_type: 'PICK_LIST')
    picklist = create(:picklist, field: @field, tenant_id: user.tenant_id)
    create_list(:picklist_value, 3, picklist: picklist, tenant_id: user.tenant_id)
    @event = described_class.new(@field)
  end

  describe 'methods' do
    context '#routing_key' do
      it 'should return correct key' do
        expect(@event.routing_key).to eq('meeting.field.updated')
      end
    end

    context '#to_json' do
      it 'should return correct payload' do
        payload = JSON.parse(@event.to_json)

        expect(payload.keys).to match_array(%w[tenantId fields])
        expect(payload['tenantId']).to eq(@field.tenant_id)
        expect(payload['fields'].count).to be(1)
        expect(payload['fields'].first['picklist'].keys).to include('values')
        expect(payload['fields'].first['picklist'].keys).not_to include('picklistValues')
      end
    end
  end
end
