require 'rails_helper'

RSpec.describe Event::MeetingEntityDisassociated, type: :model do
  let(:meeting) { create(:meeting) }
  let(:lookup) { create(:company_look_up) }
  let(:event) { described_class.new(meeting, lookup) }

  describe 'methods' do
    it 'should return correct routing key' do
      expect(event.routing_key).to eq('meeting.entity.disassociated')
    end

    it 'should return json converted payload' do
      expect(JSON.parse(event.to_json)).to eq({
        "tenantId" => meeting.tenant_id,
        "meetingId" => meeting.id,
        "entity" => "COMPANY",
        "entityId" => lookup.entity_id
      })
    end
  end
end
