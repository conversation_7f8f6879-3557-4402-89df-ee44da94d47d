# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::ShareRuleDeletedV2, type: :model do
  let(:share_rule) { create(:share_rule) }
  let(:share_rule_serialized_data) { ShareRuleSerializer.new(share_rule, payload_for_event: true).call }
  let(:event) { Event::ShareRuleDeletedV2.new(share_rule_serialized_data, share_rule.created_by_id, share_rule.tenant_id) }
  before do
    @user = share_rule.created_by
    auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
    auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
    Thread.current[:auth] = auth_data
  end

  describe 'attributes' do
    it 'should have share_rule_serialized_data attribute' do
      expect(event).to respond_to(:share_rule_serialized_data)
      expect(event.share_rule_serialized_data).to eq(share_rule_serialized_data)
    end
  end

  describe 'methods' do
    it 'should return correct routing key' do
      expect(event.routing_key).to eq('shareRule.deleted.v2')
    end

    it 'should return json converted payload' do
      share_rule_serialized = ShareRuleSerializer.new(share_rule, payload_for_event: true).call
      expect(event.to_json).to eq({
        'entity' => nil,
        'oldEntity' => share_rule_serialized,
        'metadata' => {
          'tenantId': share_rule.tenant_id,
          'userId': @user.id,
          'entityType': 'SHARE_RULE',
          'entityId': share_rule.id,
          'entityAction': 'DELETED'
        }
      }.to_json)
    end
  end

  describe 'invalidations' do
    context 'when calling a private method' do
      it 'should raise error' do
        expect{ event.as_json }.to raise_error(NoMethodError)
      end
    end
  end
end
