require 'rails_helper'

RSpec.describe Event::MeetingCreated, type: :model do
  let(:meeting) { create(:meeting, owner: create(:user)) }
  let(:meeting_all_day) {  create(:meeting, all_day: true, owner: meeting.owner) }
  let(:event) { Event::MeetingCreated.new(meeting, meeting.owner_id) }
  let(:event_all_day) { Event::MeetingCreated.new(meeting_all_day, meeting_all_day.owner_id) }

  before do
    @user = meeting.owner
    Thread.current[:auth] = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
  end

  describe 'attributes' do
    it 'should have meeting attribute' do
      expect(event).to respond_to(:meeting)
      expect(event.meeting).to eq(meeting)
      expect(event_all_day).to respond_to(:meeting)
      expect(event_all_day.meeting).to eq(meeting_all_day)
    end

    it 'should have user_id attribute' do
      expect(event).to respond_to(:user_id)
      expect(event.user_id).to eq(@user.id)
    end
  end

  describe 'methods' do
    it 'should return correct routing key' do
      expect(event.routing_key).to eq('meeting.created.v2')
      expect(event_all_day.routing_key).to eq('meeting.created.v2')
    end

    it 'should return json converted payload' do
      meeting_serialzed = MeetingSerializer.call(meeting, nil, false, nil, true).result
      expect(event.to_json).to eq({
        'entity' => meeting_serialzed,
        'oldEntity' => nil,
        'metadata' => {
          'tenantId': meeting.tenant_id,
          'userId': @user.id,
          'entityType': 'MEETING',
          'entityId': meeting.id,
          'entityAction': 'CREATED'
        }
      }.to_json)

      meeting_all_day_serialized =  MeetingSerializer.call(meeting_all_day, nil, false, nil, true).result
      expect(event_all_day.to_json).to eq({
        'entity' => meeting_all_day_serialized,
        'oldEntity' => nil,
        'metadata' => {
          'tenantId': meeting_all_day.tenant_id,
          'userId': @user.id,
          'entityType': 'MEETING',
          'entityId': meeting_all_day.id,
          'entityAction': 'CREATED'
        }
      }.to_json)

      expect(meeting_serialzed['to'].present?).to be(true)
      expect(meeting.to.present?).to be(true)
      expect(DateTime.parse((meeting_serialzed['to'])).utc.round(2)).to eq(meeting.to.round(2))
      expect(meeting_all_day_serialized['to'].present?).to be(true)
      expect(meeting_all_day.to.present?).to be(true)
    end
  end

  describe 'invalidations' do
    context 'when calling a private method' do
      it 'should raise error' do
        expect{event.as_json}.to raise_error(NoMethodError)
        expect{event_all_day.as_json}.to raise_error(NoMethodError)
      end
    end
  end
end
