require 'rails_helper'

RSpec.describe Event::MeetingUpdated, type: :model do
  before do
    @user = create(:user)
    Thread.current[:auth] = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

    @old_meeting = create(:meeting, owner: @user)
    @new_meeting = Meeting.find(@old_meeting.id)
    @new_meeting.update(status: MISSED)

    @old_meeting = MeetingSerializer.call(@old_meeting, nil, false, nil, true).result
    @event = Event::MeetingUpdated.new(@new_meeting, @old_meeting, @user.id)
  end

  describe 'attributes' do
    it 'should have new meeting attribute' do
      expect(@event).to respond_to(:new_meeting)
      expect(@event.new_meeting).to eq(@new_meeting)
    end

    it 'should have old serialized meeting attribute' do
      expect(@event).to respond_to(:old_serialized_meeting)
      expect(@event.old_serialized_meeting).to eq(@old_meeting)
    end

    it 'should have user_id attribute' do
      expect(@event).to respond_to(:user_id)
      expect(@event.user_id).to eq(@user.id)
    end
  end

  describe 'methods' do
    context 'routing_key' do
      it 'should return correct routing key' do
        expect(@event.routing_key).to eq('meeting.updated.v2')
      end
    end

    context 'to_json' do
      it 'should return json converted payload' do
        payload = @event.to_json
        expect(payload).to eq({
          'entity' => MeetingSerializer.call(@new_meeting, nil, false, nil, true).result,
          'oldEntity' => @old_meeting,
          'metadata' => {
            'tenantId': @new_meeting.tenant_id,
            'userId': @user.id,
            'entityType': 'MEETING',
            'entityId': @new_meeting.id,
            'entityAction': 'UPDATED',
            'workflowId': nil,
            'executedWorkflows': nil,
            'executeWorkflow': true,
            'workflowName': nil
          }
        }.to_json)
        expect(JSON.parse(payload)['entity']['status']).to eq(MISSED)
        expect(JSON.parse(payload)['oldEntity']['status']).to eq(SCHEDULED)
      end

      context 'when workflow metada is present' do
        it 'publishes workflow metadata' do
          metada = {
            "workflowId": "WF_301",
            "executedWorkflows": [
              "WF_309",
              "WF_301"
            ],
            "executeWorkflow": true,
            "workflowName": "Workflow 1"
          }
          @event = Event::MeetingUpdated.new(@new_meeting, @old_meeting, @user.id, metada)
          payload = @event.to_json
          expect(payload).to eq({
            'entity' => MeetingSerializer.call(@new_meeting, nil, false, nil, true).result,
            'oldEntity' => @old_meeting,
            'metadata' => {
              'tenantId': @new_meeting.tenant_id,
              'userId': @user.id,
              'entityType': 'MEETING',
              'entityId': @new_meeting.id,
              'entityAction': 'UPDATED',
              'workflowId': 'WF_301',
              'executedWorkflows': [ "WF_309", "WF_301" ],
              'executeWorkflow': true,
              'workflowName': 'Workflow 1'
            }
          }.to_json)
          expect(JSON.parse(payload)['entity']['status']).to eq(MISSED)
          expect(JSON.parse(payload)['oldEntity']['status']).to eq(SCHEDULED)
        end
      end
    end
  end

  describe 'invalidations' do
    context 'when calling a private method' do
      it 'should raise error' do
        expect{@event.as_json}.to raise_error(NoMethodError)
      end
    end
  end
end
