# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::MeetingFieldUpdatedV2 do
  let(:field) { create(:field) }
  let(:old_serialized_field_data) { V2::FieldSerializer.call(field).result }
  let(:event) { described_class.new(field, old_serialized_field_data) }

  before do
    field.update(display_name: 'Updated Display Name')
  end

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('meeting.field.updated.v2')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      event_payload = JSON.parse(event.to_json)

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['oldEntity']).to eq(old_serialized_field_data)
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => field.tenant_id,
          "userId" => field.updated_by_id,
          "entityType" => "MEETING",
          "entityId" => field.id,
          "entityAction" => "UPDATED"
        }
      )
      expect(event_payload['entity']).to eq(
        {
          "id" => field.id,
          "name" => field.internal_name,
          "displayName" => 'Updated Display Name',
          "fieldType" => field.field_type,
          "description" => "Lorem ipsum dolor sit amet.",
          "standard" => true,
          "sortable" => field.is_sortable,
          "filterable" => field.is_filterable,
          "internal" => field.is_internal,
          "required" => field.is_required,
          "active" => field.active,
          "tenantId" => field.tenant_id,
          "picklistValues" => nil,
          "createdAt" => field.created_at.iso8601(6),
          "updatedAt" => field.updated_at.iso8601(6),
          "createdBy" => {
            "id" => field.created_by.id,
            "name" => field.created_by.name
          },
          "updatedBy" => {
            "id" => field.updated_by.id,
            "name" => field.updated_by.name
          }
        }
      )
    end
  end
end
