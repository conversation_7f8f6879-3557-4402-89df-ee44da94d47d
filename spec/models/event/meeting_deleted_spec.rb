require 'rails_helper'

RSpec.describe Event::MeetingDeleted, type: :model do
  let(:meeting) { create(:meeting, owner: create(:user)) }
  let(:serialized_meeting) { MeetingSerializer.call(meeting, nil, false, meeting.owner, true).result }
  let(:event) { Event::MeetingDeleted.new(serialized_meeting, meeting.owner_id) }

  before do
    @user = meeting.owner
    Thread.current[:auth] = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
  end

  describe 'attributes' do
    it 'should have old_serialized_meeting attribute' do
      expect(event).to respond_to(:old_serialized_meeting)
      expect(event.old_serialized_meeting).to eq(serialized_meeting)
    end

    it 'should have user_id attribute' do
      expect(event).to respond_to(:user_id)
      expect(event.user_id).to eq(@user.id)
    end
  end

  describe 'methods' do
    it 'should return correct routing key' do
      expect(event.routing_key).to eq('meeting.deleted.v2')
    end

    it 'should return json converted payload' do
      expect(event.to_json).to eq({
        'entity' => nil,
        'oldEntity' => serialized_meeting,
        'metadata' => {
          'tenantId': serialized_meeting['tenantId'],
          'userId': @user.id,
          'entityType': 'MEETING',
          'entityId': serialized_meeting['id'],
          'entityAction': 'DELETED'
        }
      }.to_json)
    end
  end

  describe 'payload validations' do
    it 'should have deletedBy and deletedAt key values on old entity' do
      expect(JSON.parse(event.to_json)['oldEntity'].keys).to include(*%w[deletedBy deletedAt])
    end
  end

  describe 'invalidations' do
    context 'when calling a private method' do
      it 'should raise error' do
        expect{event.as_json}.to raise_error(NoMethodError)
      end
    end
  end
end
