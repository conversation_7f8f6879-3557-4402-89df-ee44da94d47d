# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::ShareRuleUpdatedV2, type: :model do
  before do
    @user = create(:user)
    auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
    auth_data.permissions += build(:auth_data, :share_rule_with_create, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name).permissions
    Thread.current[:auth] = auth_data

    @old_share_rule = create(:share_rule, created_by: @user, name: 'share rule')
    @new_share_rule = ShareRule.find(@old_share_rule.id)
    @new_share_rule.update(name: 'updated name')

    @old_share_rule = ShareRuleSerializer.new(@old_share_rule, payload_for_event: true).call
    @event = Event::ShareRuleUpdatedV2.new(@new_share_rule, @old_share_rule)
  end

  describe 'attributes' do
    it 'should have new meeting attribute' do
      expect(@event).to respond_to(:new_share_rule)
      expect(@event.new_share_rule).to eq(@new_share_rule)
    end

    it 'should have old serialized meeting attribute' do
      expect(@event).to respond_to(:old_serialized_share_rule)
      expect(@event.old_serialized_share_rule).to eq(@old_share_rule)
    end
  end

  describe 'methods' do
    context 'routing_key' do
      it 'should return correct routing key' do
        expect(@event.routing_key).to eq('shareRule.updated.v2')
      end
    end

    context 'to_json' do
      it 'should return json converted payload' do
        payload = @event.to_json
        expect(payload).to eq({
          'entity' => ShareRuleSerializer.new(@new_share_rule, payload_for_event: true).call,
          'oldEntity' => @old_share_rule,
          'metadata' => {
            'tenantId': @new_share_rule.tenant_id,
            'userId': @user.id,
            'entityType': 'SHARE_RULE',
            'entityId': @new_share_rule.id,
            'entityAction': 'UPDATED'
          }
        }.to_json)
        expect(JSON.parse(payload)['entity']['name']).to eq('updated name')
        expect(JSON.parse(payload)['oldEntity']['name']).to eq('share rule')
      end
    end
  end
end
