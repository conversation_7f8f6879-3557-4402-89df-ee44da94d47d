# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::MeetingCheckedinBeyondGeofence do
  let(:tenant_id) { 123 }
  let(:user) { create(:user, name: '<PERSON>') }
  let(:meeting) { create(:meeting, title: 'Team Sync') }
  let(:event) { described_class.new(meeting, user, tenant_id) }

  describe '#routing_key' do
    it 'returns the correct routing key' do
      expect(event.routing_key).to eq('meeting.checkedin.beyond.geofence')
    end
  end

  describe '#as_json' do
    subject { event.as_json }

    it 'returns the correct JSON structure' do
      expect(subject).to eq(
        {
          tenantId: tenant_id,
          user: {
            id: user.id,
            name: user.name
          },
          meeting: {
            id: meeting.id,
            title: meeting.title
          }
        }
      )
    end
  end

  describe '#to_json' do
    subject { event.to_json }

    it 'returns the serialized JSON string' do
      expected_json = {
        tenantId: tenant_id,
        user: {
          id: user.id,
          name: user.name
        },
        meeting: {
          id: meeting.id,
          title: meeting.title
        }
      }.to_json

      expect(subject).to eq(expected_json)
    end
  end
end
