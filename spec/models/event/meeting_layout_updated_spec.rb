# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::MeetingLayoutUpdated, type: :model do
  before do
    @event = described_class.new(12)
  end

  describe 'methods' do
    context '#routing_key' do
      it 'should return correct key' do
        expect(@event.routing_key).to eq('meeting.layout.updated')
      end
    end

    context '#to_json' do
      it 'should return correct payload' do
        payload = JSON.parse(@event.to_json)
        expect(payload).to eq({ "layoutType" => nil, "tenantId" => 12 })
      end
    end
  end
end
