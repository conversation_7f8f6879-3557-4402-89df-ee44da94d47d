# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConnectedAccount, type: :model do
  let(:connected_account) { create(:connected_account) }

  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id).with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:tenant_id).of_type(:integer).with_options(null: false)
      end

      it do
        should have_db_column(:access_token).of_type(:string)
      end

      it do
        should have_db_column(:expires_at).of_type(:integer)
      end

      it do
        should have_db_column(:email).of_type(:string)
      end

      it do
        should have_db_column(:created_at).of_type(:datetime)
      end

      it do
        should have_db_column(:updated_at).of_type(:datetime)
      end

      it do
        should have_db_column(:provider_name).of_type(:string)
      end

      it do
        should have_db_column(:active).of_type(:boolean)
      end

      it do
        should have_db_column(:user_id).of_type(:integer)
      end

      it do
        should have_db_column(:calendar_id).of_type(:string)
      end

      it do
        should have_db_column(:sync_type).of_type(:jsonb).with_options(default: { 'calendar_to_kylas' => false, 'kylas_to_calendar' => false })
      end

      it do
        should have_db_index(:user_id)
      end
    end

    describe 'fields & validations' do
      subject do
        user = FactoryBot.create(:user)
        FactoryBot.create(:connected_account, user_id: user.id)
      end

      it do
        should validate_presence_of(:tenant_id)
      end

      it do
        should validate_presence_of(:access_token)
      end

      it do
        should validate_presence_of(:provider_name)
      end

      it do
        should validate_presence_of(:expires_at)
      end

      it do
        should validate_presence_of(:email)
      end

      it do
        should validate_length_of(:calendar_id).is_at_most(320)
      end
    end

    describe '#associations' do
      it do
        should belong_to(:user).with_foreign_key('user_id')
      end
    end

    describe 'google_provider?' do
      it 'returns true when provider is google' do
        acct = create(:connected_account, provider_name: GOOGLE_PROVIDER)
        expect(acct.google_provider?).to eq true
      end

      it 'returns false when provider is not google' do
        acct = create(:connected_account, provider_name: MICROSOFT_TEAMS_PROVIDER)
        expect(acct.google_provider?).to eq false
      end
    end

    describe '#fetch_access_token' do
      def stub_fetch_provider_access_token_request(token, status, response_body)
        stub_request(:get, %r{/v1/calendar-oauth/})
          .with(headers: { Authorization: "Bearer #{token}", Accept: 'application/json' })
          .to_return(status: status, body: response_body.to_json)
      end

      before do
        @user = create(:user)
        @token = build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
        @auth_data = ParseToken.call(@token).result
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
      end

      context 'when access token is valid' do
        it 'returns existing access token only' do
          expect(connected_account.fetch_access_token).to eq(connected_account.access_token)
          expect(connected_account.expires_at).to be > Time.now.to_i
        end
      end

      context 'when access token is invalid' do
        it 'returns new access token from iam service and stores new access token on connected account object' do

          response_body = { 'accessToken' => 'g7UpMwveCD0cIzdG778wuOcwCR5qUnX55ZVwoPC7cnw=', 'expiresAt' => '2022-06-12T11:30:10.607Z' }
          stub_fetch_provider_access_token_request(@token, 200, response_body)
          connected_account.update(expires_at: 1.hour.ago.to_i)
          decrypted_token = connected_account.fetch_access_token
          expect(decrypted_token).to eq("some-access-token")
          expect(connected_account.reload.access_token).to eq(response_body['accessToken'])
          expect(connected_account.reload.expires_at).to eq((DateTime.now + 50.minutes).to_i)
        end
      end
    end 
  end
end
