# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id).with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:name).of_type(:string).with_options(null: false)
      end

      it do
        should have_db_column(:tenant_id).of_type(:integer)
      end

      it do
        should have_db_column(:created_at).of_type(:datetime)
      end

      it do
        should have_db_column(:updated_at).of_type(:datetime)
      end
    end

    describe 'fields & validations' do
      it do
        should validate_presence_of(:name)
      end

      it do
        should validate_presence_of(:tenant_id)
      end
    end

    describe '#associations' do
      it { should have_many(:connected_accounts) }
    end
  end

  describe '#get_geofence_config' do
    let(:user) { create(:user) }

    context 'when geofence_config is present' do
      before do
        expect(FetchGeofenceConfigurationForUser).not_to receive(:call)
      end

      it 'returns the existing geofence_config' do
        user.update(geofence_config: { meetingCheckInCheckOut: { radius: 500, restrictCheckIn: true } })
        expect(user.get_geofence_config).to eq({ "meetingCheckInCheckOut" => { "radius" => 500, "restrictCheckIn" => true } })
      end
    end

    context 'when geofence_config is not present' do
      before do
        @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
        @auth_data = ParseToken.call(@token).result
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token
      end

      context 'when fetch returns a configuration' do
        before do
          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
            headers: { Authorization: "Bearer #{@token}" }
          ).to_return(status: 200, body: { fieldSalesEnabled: true, meetingCheckInCheckOut: { radius: 500, restrictCheckIn: true }}.to_json)
        end

        it 'saves the geofence configuration' do
          expect(user.get_geofence_config).to eq({ "fieldSalesEnabled" => true, "meetingCheckInCheckOut" => { "radius" => 500, "restrictCheckIn" => true } })
          expect(user.reload.geofence_config).to eq({ "fieldSalesEnabled" => true, "meetingCheckInCheckOut" => { "radius" => 500, "restrictCheckIn" => true } })
        end
      end

      context 'when fetch returns nil' do
        before do
          stub_request(:get, "http://localhost:9007/v1/field-sales/executives/9/geofence").with(
            headers: { Authorization: "Bearer #{@token}" }
          ).to_return(status: 200, body: nil.to_json)
        end
          
        it 'returns default configuration if fetch returns nil' do
          expect(user.get_geofence_config).to eq({ "fieldSalesEnabled" => false, "meetingCheckInCheckOut" => nil })
          expect(user.reload.geofence_config).to eq({ "fieldSalesEnabled" => false,"meetingCheckInCheckOut" => nil })
        end
      end
    end
  end
end
