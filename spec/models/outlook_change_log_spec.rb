# frozen_string_literal: true
require 'rails_helper'

RSpec.describe OutlookChangeLog, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:resource_id)
          .of_type(:string)
          .with_options(null: false)
      end

      it do
        should have_db_column(:change_key)
          .of_type(:string)
          .with_options(null: false)
      end
    end

    describe 'validations' do
      it do
        create(:outlook_change_log)
        should validate_uniqueness_of(:resource_id).scoped_to(:change_key)
      end
    end
  end
end
