require 'rails_helper'
require 'bunny-mock'

RSpec.describe Meeting, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:title)
          .of_type(:string)
          .with_options(null: false)
      end
      it do
        should have_db_column(:description)
          .of_type(:string)
      end
      it do
        should have_db_column(:from)
          .of_type(:datetime)
          .with_options(null: false)
      end
      it do
        should have_db_column(:to)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:all_day)
          .of_type(:boolean)
          .with_options(default: false)
      end
      it do
        should have_db_column(:location)
          .of_type(:string)
      end
      it do
        should have_db_column(:tenant_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:time_zone_id)
          .of_type(:integer)
      end
      it do
        should have_db_column(:owner_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:created_by_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:updated_by_id)
          .of_type(:integer)
          .with_options(null: false)
      end
      it do
        should have_db_column(:created_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:conducted_at)
          .of_type(:datetime)
          .with_options(precision: 6)
      end
      it do
        should have_db_column(:conducted_by_id)
          .of_type(:integer)
          .with_options(null: true)
      end
      it do
        should have_db_column(:cancelled_at)
          .of_type(:datetime)
          .with_options(precision: 6)
      end
      it do
        should have_db_column(:cancelled_by_id)
          .of_type(:integer)
          .with_options(null: true)
      end
      it do
        should have_db_column(:status)
          .of_type(:string)
        should have_db_column(:status)
          .with_options(default: 'scheduled')
      end
      it do
        should have_db_column(:public_id)
          .of_type(:string)
      end
      it do
        should have_db_column(:imported_by_id)
          .of_type(:integer)
          .with_options(null: true)
      end

      it do
        should have_db_column(:medium).of_type(:string)
        should have_db_column(:medium).with_options(default: OFFLINE)
      end

      it { should have_db_column(:provider_link).of_type(:string) }
    end

    describe 'validations' do
      it { should validate_presence_of(:title) }

      it { should validate_presence_of(:from) }

      it do
        should validate_inclusion_of(:status)
          .in_array([SCHEDULED, CONDUCTED, CANCELLED, MISSED])
          .with_message('Invalid status')
      end

      it do
        should validate_inclusion_of(:medium)
          .in_array([OFFLINE, GOOGLE_PROVIDER])
          .with_message('Invalid medium')
      end

      it do
        should_not allow_value(nil)
          .for(:all_day)
      end
      it do
        should validate_presence_of(:tenant_id)
      end
      it do
        should validate_presence_of(:owner)
      end
      it do
        should validate_presence_of(:created_by)
      end
      it do
        should validate_presence_of(:updated_by)
      end
      it do
        should validate_presence_of(:public_id)
      end
      it 'past meetings can be updated without changing from/to' do
        meeting = FactoryBot.build(:meeting, from: Time.now - 2.hours, to: Time.now - 1.hour)
        meeting.save(validate: false)
        meeting.description = Faker::Lorem.paragraph
        expect(meeting.valid?).to be true
      end

      it 'supports 2 string fields for filter' do
        expect(Meeting.string_fields).to be == %w[location status title]
      end

      it 'supports 1 boolean field for filter' do
        expect(Meeting.boolean_fields).to be == ['all_day']
      end

      it 'supports 6 date fields for filter' do
        expect(Meeting.date_fields).to be == %w[from to conducted_at cancelled_at created_at
                                                updated_at]
      end

      it 'supports 12 long double fields for filter' do
        expect(Meeting.long_double_fields).to be == %w[id owner created_by updated_by conducted_by
                                                       cancelled_by imported_by created_by_fields updated_by_fields conducted_by_fields
                                                       cancelled_by_fields organizer_fields]
      end

      it 'should accept allowed values for status' do
        meeting = FactoryBot.create(:meeting)
        meeting.status = 'scheduled'
        expect(meeting.valid?).to be true
      end

      it 'should throw error for if status is invalid' do
        meeting = FactoryBot.create(:meeting)
        meeting.status = 'Invalid status'
        expect(meeting.valid?).to be false
        expect(meeting.errors[:status][0]).to be_eql('Invalid status')
      end

      it 'should throw error for if status is empty' do
        meeting = FactoryBot.create(:meeting)
        meeting.status = nil
        expect(meeting.valid?).to be false
        expect(meeting.errors[:status][0]).to be_eql('Invalid status')
      end

      it 'does throw error for if medium is empty' do
        meeting = FactoryBot.create(:meeting)
        meeting.medium = nil
        expect(meeting.valid?).to be false
        expect(meeting.errors[:medium][0]).to be_eql('Invalid medium')
      end

      it 'should not allow to update cancelled meeting' do
        meeting = FactoryBot.create(:meeting)
        meeting.update(status: CANCELLED)
        meeting.title = 'new title'
        expect(meeting.valid?).to be false
        expect(meeting.errors.to_a[0]).to eq('cannot update cancelled meeting')
      end

      context 'duration' do
        it 'to should be set when event is not all_day' do
          meeting = FactoryBot.build(:meeting)
          meeting.to = nil
          expect(meeting.valid?).to be false
          expect(meeting.errors[:to]).to be_truthy
        end
        it 'from should always be before to' do
          meeting = FactoryBot.build(:meeting)
          expect(meeting.valid?).to be true
        end
        it 'from should never be later than to' do
          meeting = FactoryBot.build(:meeting)
          meeting.from = meeting.to + 1.hour
          expect(meeting.valid?).to be false
          expect(meeting.errors[:base]).to be_truthy
        end
      end

      context 'status' do
        context 'as conducted' do
          it 'should have conducted at and conducted by present' do
            meeting = FactoryBot.create(:meeting)
            meeting.status = CONDUCTED
            expect(meeting.valid?).to be(false)
            expect(meeting.errors.messages.values.flatten.first).to eq('cannot have conducted at or conducted by blank for conducted meetings')
          end
        end

        context 'not conducted' do
          it 'should not have conducted at and conducted by present' do
            meeting = FactoryBot.create(:meeting)
            meeting.conducted_at = DateTime.now.utc
            expect(meeting.valid?).to be(false)
            expect(meeting.errors.messages.values.flatten.first).to eq('cannot have conducted at or conducted by for unconducted meetings')
          end
        end
      end
    end
    describe 'associations' do
      it do
        should belong_to(:conducted_by)
          .class_name('User').optional
      end
      it do
        should belong_to(:cancelled_by)
          .class_name('User').optional
      end
      it do
        should have_many(:meeting_attendances).class_name('MeetingAttendance')
      end
      it do
        should have_many(:participant_look_ups)
          .conditions(participant: true)
          .class_name('MeetingLookUp')
      end
      it do
        should have_many(:related_to_look_ups)
          .conditions(related: true)
          .class_name('MeetingLookUp')
      end
      it do
        should have_many(:participants)
          .source('look_up')
          .through('participant_look_ups')
      end
      it do
        should have_many(:related_to)
          .source('look_up')
          .through('related_to_look_ups')
      end
      it do
        should have_many(:notes).dependent(:destroy)
      end
      it do
        should belong_to(:owner)
          .class_name('User')
      end
      it do
        should belong_to(:time_zone)
          .class_name('LookUp')
          .optional(true)
      end
      it do
        should belong_to(:created_by)
          .class_name('User')
      end
      it do
        should belong_to(:updated_by)
          .class_name('User')
      end
    end
  end

  describe 'class methods' do
    context '#usage per tenant' do
      it 'will return count of meeting per tenant' do
        FactoryBot.create_list(:meeting, 5, tenant_id: 11)
        FactoryBot.create_list(:meeting, 3, tenant_id: 12)
        data = Meeting.usage_per_tenant
        expected_response = [{ tenantId: 11, count: 5, usageEntity: 'MEETING' },
                             { tenantId: 12, count: 3, usageEntity: 'MEETING' }]
        expect(data).to match_array(expected_response)
      end

      it 'will return count of meeting per tenant, returns 0' do
        FactoryBot.create_list(:meeting, 5, tenant_id: 11)
        FactoryBot.create_list(:meeting, 3, tenant_id: 12)
        data = Meeting.usage_per_tenant(111)
        expected_response = [{ tenantId: 111, count: 0, usageEntity: 'MEETING' }]
        expect(data).to eql expected_response
      end
    end
  end

  describe 'instance methods' do
    describe '#online?' do
      context 'when meeting medium is google' do
        it 'does return true' do
          meeting = FactoryBot.create(:meeting, from: DateTime.now, to: DateTime.now + 1.hour,
                                                medium: GOOGLE_PROVIDER)
          expect(meeting.online?).to eq(true)
        end
      end

      context 'when meeting is offline' do
        it 'does return false' do
          meeting = FactoryBot.create(:meeting, from: DateTime.now, to: DateTime.now + 1.hour, medium: OFFLINE)
          expect(meeting.online?).to eq(false)
        end
      end
    end

    context '#missed?' do
      it 'should return true or false correctly' do
        meeting = FactoryBot.create(:meeting, from: (DateTime.now - 1.day), to: (DateTime.now - 1.day + 1.hour))
        expect(meeting.missed?).to be(true)

        meeting.from = (DateTime.now + 1.day)
        meeting.to = (DateTime.now + 1.day + 1.hour)
        expect(meeting.missed?).to be(false)

        meeting.status = MISSED
        expect(meeting.missed?).to be(true)
      end
    end
  end

  describe 'callbacks' do
    context '#publish_entity_metadata' do
      before do
        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic MEETING_EXCHANGE

        @queue = @channel.queue 'meeting.update.deal.metaInfo'
        @queue.bind @exchange, routing_key: 'meeting.update.deal.metaInfo'
        allow(RabbitmqConnection).to receive(:get_exchange).with(MEETING_EXCHANGE, 'meeting').and_return(@queue)
        @meeting = create(:meeting)
        related_entity = build("#{LOOKUP_DEAL}_look_up")
        @meeting.related_to << related_entity
        entity = build("#{LOOKUP_DEAL}_look_up")
        @meeting.participants << entity
        @meeting.publish_entity_metadata
      end

      # NOTE: Already there are 2 messages in the queue when entities are pushed in
      # the related to and participants above
      # When update_atttributes is invoked it is adding 2 more messages in the queue
      it 'will raise event if from changed' do
        expect(@queue.message_count).to eq 2
        @meeting.update(from: DateTime.now + 1.hour)
        @meeting.publish_entity_metadata
        expect(@queue.message_count).to eq 4
      end

      it 'will raise event if to changed' do
        @meeting.update(to: DateTime.now + 1.hour)
        expect(@queue.message_count).to eq 2
      end
    end
  end
end
