require 'rails_helper'

RSpec.describe MeetingLookUp, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:id).
          with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:related).
          of_type(:boolean)
      end
      it do
        should have_db_column(:participant).
          of_type(:boolean)
      end
      it do
        should have_db_column(:meeting_id).
          of_type(:integer).
          with_options(null: false)
      end
      it do
        should have_db_column(:look_up_id).
          of_type(:integer).
          with_options(null: false)
      end
      it do
        should have_db_column(:created_at).
          of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).
          of_type(:datetime)
      end
      it do
        should have_db_column(:organizer).
          of_type(:boolean).with_options(default: false)
      end
    end
    describe 'validations' do
      it do
        should validate_presence_of(:look_up)
      end
      it do
        should validate_presence_of(:meeting)
      end

    end
    describe 'associations' do
      it do
        should belong_to(:meeting)
      end
      it do
        should belong_to(:look_up)
      end
    end
  end
end
