require 'rails_helper'
require 'bunny-mock'

RSpec.describe MeetingAttendance, type: :model do
  describe 'entity definition' do
    describe 'db schema' do
      it do
        should have_db_column(:checked_in_at).of_type(:datetime).with_options(precision: 6)
      end
      it do
        should have_db_column(:checked_in_latitude).of_type(:string)
      end
      it do
        should have_db_column(:checked_in_longitude).of_type(:string)
      end
      it do
        should have_db_column(:checked_out_at).of_type(:datetime).with_options(precision: 6)
      end
      it do
        should have_db_column(:checked_out_latitude).of_type(:string)
      end
      it do
        should have_db_column(:checked_out_longitude).of_type(:string)
      end
      it do
        should have_db_column(:meeting_id).of_type(:integer)
      end
      it do
        should have_db_column(:created_at).of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).of_type(:datetime)
      end
      it do
        should have_db_column(:user_id).of_type(:integer)
      end
    end

    describe 'associations' do
      it do
        should belong_to(:meeting)
      end
    end

    describe 'validations' do
      it 'should now allow to check out without checking in' do
        meeting_attendance = create(:meeting_attendance)
        meeting_attendance.checked_in_at = nil
        meeting_attendance.checked_in_latitude = nil
        meeting_attendance.checked_in_longitude = nil
        expect(meeting_attendance.valid?).to be(false)
        expect(meeting_attendance.errors.full_messages.first).to eq('cannot check out without checking in')
      end

      it 'should not allow absence of dependent fields' do
        meeting_attendance = create(:meeting_attendance)
        meeting_attendance.checked_in_latitude = nil
        meeting_attendance.checked_out_longitude = nil
        expect(meeting_attendance.valid?).to be(false)
        expect(meeting_attendance.errors.full_messages.first).to eq('cannot check in without time or location details')
        expect(meeting_attendance.errors.full_messages.second).to eq('cannot check out without time or location details')
      end
    end

    describe 'geofence validation' do
      let(:meeting) { create(:meeting, location_latitude: '18.559658', location_longitude: '73.779938') }
      let(:user) { create(:user) }
      let(:meeting_attendance) { create(:meeting_attendance, meeting: meeting, user: user) }

      context 'set_is_checked_in_outside_geofence' do
        context 'when geofence config is not present' do
          it 'sets is_checked_in_outside_geofence to false' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
            meeting_attendance.set_is_checked_in_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end
        end

        context 'when meeting lat long are not present' do
          it 'sets is_checked_in_outside_geofence to false' do
            meeting.update(location_latitude: nil, location_longitude: nil)
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_in_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end
        end

        context 'when check in location is within geofence' do
          it 'sets is_checked_in_outside_geofence to false' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_in_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(false)
          end
        end

        context 'when check in location is outside geofence' do
          it 'sets is_checked_in_outside_geofence to true' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: false }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_in_outside_geofence(
              { latitude: '18.5670563', longitude: '73.7684087' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_in_outside_geofence).to eq(true)
          end

          it 'raises error when restrictCheckIn is true' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            expect {
              meeting_attendance.set_is_checked_in_outside_geofence(
                { latitude: '18.5670563', longitude: '73.7684087' },
                user,
                meeting
              )
            }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_checkin_location}||#{I18n.t('error.invalid.checkin_location')}")
          end
        end
      end

      context 'set_is_checked_out_outside_geofence' do
        context 'when geofence config is not present' do
          it 'sets is_checked_out_outside_geofence to false' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => nil })
            meeting_attendance.set_is_checked_out_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
          end
        end

        context 'when meeting lat long are not present' do
          it 'sets is_checked_out_outside_geofence to false' do
            meeting.update(location_latitude: nil, location_longitude: nil)
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_out_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
          end
        end

        context 'when check out location is within geofence' do
          it 'sets is_checked_out_outside_geofence to false' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_out_outside_geofence(
              { latitude: '18.559658', longitude: '73.779938' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_out_outside_geofence).to eq(false)
          end
        end

        context 'when check out location is outside geofence' do
          it 'sets is_checked_out_outside_geofence to true' do
            user.update(geofence_config: { "meetingCheckInCheckOut" => { radius: 500, restrictCheckIn: true }, fieldSalesEnabled: true })
            
            meeting_attendance.set_is_checked_out_outside_geofence(
              { latitude: '18.5670563', longitude: '73.7684087' },
              user,
              meeting
            )
            expect(meeting_attendance.is_checked_out_outside_geofence).to eq(true)
          end
        end
      end
    end
  end
end
