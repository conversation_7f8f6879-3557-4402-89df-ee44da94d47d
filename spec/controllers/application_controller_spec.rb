require "rails_helper"

RSpec.describe ApplicationController, type: :controller do

  let(:auth_data) { build(:auth_data) }

  let(:headers) { { 'Authorization' => token_generator(auth_data) } }
  let(:invalid_headers) { { 'Authorization' => nil } }

  describe "#authorize_request" do
    context "when auth token is passed" do
      before { allow(request).to receive(:headers).and_return(headers) }

      # private method authorize_request returns current user
      it "sets the current user" do
        expect(subject.instance_eval { authenticate.as_json }).to eq(auth_data.as_json)
      end
    end

    context "when auth token is not passed" do
      before do
        allow(request).to receive(:headers).and_return(invalid_headers)
      end

      it "raises Unauthorised error" do
        expect { subject.instance_eval { authenticate } }.
          to raise_error(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Unauthorized access.")
      end
    end
  end
end