version: '3.4'

services:
  app: 
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - database
    ports: 
      - "3000:3000"
    env_file: .env
    environment:
      RAILS_ENV: test
  database:
    image: postgres:12.1
    ports:
      - 5432:5432
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
volumes:
  db_data:
      